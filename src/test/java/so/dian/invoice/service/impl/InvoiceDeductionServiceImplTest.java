package so.dian.invoice.service.impl;
import java.util.Date;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.dao.InvoiceSubjectRelationDAO;
import so.dian.invoice.manager.InvoiceOperateLogManager;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.bo.InvoiceQueryBO;
import so.dian.invoice.pojo.dto.InvoiceDeductionDTO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.param.ApplyInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceDeductBatchParam;
import so.dian.invoice.pojo.param.InvoiceDeductOperationParam;
import so.dian.invoice.pojo.param.InvoiceDeductParam;
import so.dian.invoice.pojo.param.InvoiceManualDeductParam;
import so.dian.invoice.pojo.param.InvoiceParam;
import so.dian.invoice.pojo.param.InvoiceRecoverBatchParam;
import so.dian.invoice.pojo.param.InvoiceRecoverOperationParam;
import so.dian.invoice.pojo.param.InvoiceRecoverParam;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.InvoiceService;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InvoiceDeductionServiceImplTest {

    @Mock
    private InvoiceDeductionDAO mockInvoiceDeductionDAO;
    @Mock
    private InvoiceDAO mockInvoiceDAO;
    @Mock
    private InvoiceSubjectRelationDAO mockInvoiceSubjectRelationDAO;
    @Mock
    private InvoiceService mockInvoiceService;
    @Mock
    private InvoiceOperateLogManager mockInvoiceOperateLogManager;
    @Mock
    private AgentEmployeeService mockAgentEmployeeService;

    @InjectMocks private InvoiceDeductionServiceImpl invoiceDeductionServiceImplUnderTest;

    @Test public void testGetInvoiceDeductionList() {
        // Setup
        final InvoiceDeductOperationParam param = new InvoiceDeductOperationParam();
        param.setOperatorId(0L);
        param.setOperatorName("operatorName");
        param.setRemark("remark");

        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> expectedResult = Arrays.asList(invoiceDeductionDO);

        // Configure InvoiceDeductionDAO.selectInvoiceDeductionList(...).
        final InvoiceDeductionDO invoiceDeductionDO1 = new InvoiceDeductionDO();
        invoiceDeductionDO1.setId(0);
        invoiceDeductionDO1.setInvoiceCode("invoiceCode");
        invoiceDeductionDO1.setInvoiceNo("invoiceNo");
        invoiceDeductionDO1.setBusinessNo("businessNo");
        invoiceDeductionDO1.setBusinessType(0);
        invoiceDeductionDO1.setOperateType(0);
        invoiceDeductionDO1.setCreator(0L);
        invoiceDeductionDO1.setCreateName("createName");
        invoiceDeductionDO1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO1);
        when(mockInvoiceDeductionDAO.selectInvoiceDeductionList("businessNo", "invoiceCode", "invoiceNo", 0, 0)).thenReturn(
              invoiceDeductionDOS);

        // Run the test
        final List<InvoiceDeductionDO> result = invoiceDeductionServiceImplUnderTest.getInvoiceDeductionList(param);

    }

    @Test public void testGetInvoiceDeductionList_InvoiceDeductionDAOReturnsNoItems() {
        // Setup
        final InvoiceDeductOperationParam param = new InvoiceDeductOperationParam();
        param.setOperatorId(0L);
        param.setOperatorName("operatorName");
        param.setRemark("remark");

        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> expectedResult = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.selectInvoiceDeductionList("businessNo", "invoiceCode", "invoiceNo", 0, 0)).thenReturn(
              Collections.emptyList());

        // Run the test
        final List<InvoiceDeductionDO> result = invoiceDeductionServiceImplUnderTest.getInvoiceDeductionList(param);

    }

    @Test public void testBatchDeductInvoice() {
        // Setup
        final InvoiceDeductBatchParam params = new InvoiceDeductBatchParam();
        final InvoiceDeductParam invoiceDeductParam = new InvoiceDeductParam();
        invoiceDeductParam.setAmount(new BigDecimal("0.00"));
        invoiceDeductParam.setInvoiceCode("invoiceCode");
        invoiceDeductParam.setInvoiceNo("invoiceNo");
        invoiceDeductParam.setBusinessNo("businessNo");
        invoiceDeductParam.setBusinessType(0);
        invoiceDeductParam.setSubjectName("subjectName");
        invoiceDeductParam.setBuyerName("buyerName");
        invoiceDeductParam.setNeedSubjectRelation(false);
        params.setList(Arrays.asList(invoiceDeductParam));

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<List<InvoiceDeductionDTO>> expectedResult = BizResult.create(Arrays.asList(invoiceDeductionDTO));

        // Configure InvoiceDAO.findByInvoiceNoAndInvoiceCode(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.findByInvoiceNoAndInvoiceCode(
              Arrays.asList(new InvoiceQueryBO("invoiceNo", "invoiceCode")))).thenReturn(invoiceDOS);

        when(mockInvoiceSubjectRelationDAO.getSubjectNameList("relationSubjectName")).thenReturn(Arrays.asList("value"));
        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result = invoiceDeductionServiceImplUnderTest.batchDeductInvoice(params);


    }

    @Test public void testBatchDeductInvoice_InvoiceDAOFindByInvoiceNoAndInvoiceCodeReturnsNoItems() {
        // Setup
        final InvoiceDeductBatchParam params = new InvoiceDeductBatchParam();
        final InvoiceDeductParam invoiceDeductParam = new InvoiceDeductParam();
        invoiceDeductParam.setAmount(new BigDecimal("0.00"));
        invoiceDeductParam.setInvoiceCode("invoiceCode");
        invoiceDeductParam.setInvoiceNo("invoiceNo");
        invoiceDeductParam.setBusinessNo("businessNo");
        invoiceDeductParam.setBusinessType(0);
        invoiceDeductParam.setSubjectName("subjectName");
        invoiceDeductParam.setBuyerName("buyerName");
        invoiceDeductParam.setNeedSubjectRelation(false);
        params.setList(Arrays.asList(invoiceDeductParam));

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<List<InvoiceDeductionDTO>> expectedResult = BizResult.create(Arrays.asList(invoiceDeductionDTO));
        when(mockInvoiceDAO.findByInvoiceNoAndInvoiceCode(
              Arrays.asList(new InvoiceQueryBO("invoiceNo", "invoiceCode")))).thenReturn(Collections.emptyList());
        when(mockInvoiceSubjectRelationDAO.getSubjectNameList("relationSubjectName")).thenReturn(Arrays.asList("value"));
        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result = invoiceDeductionServiceImplUnderTest.batchDeductInvoice(params);

    }

    @Test public void testBatchDeductInvoice_InvoiceSubjectRelationDAOReturnsNoItems() {
        // Setup
        final InvoiceDeductBatchParam params = new InvoiceDeductBatchParam();
        final InvoiceDeductParam invoiceDeductParam = new InvoiceDeductParam();
        invoiceDeductParam.setAmount(new BigDecimal("0.00"));
        invoiceDeductParam.setInvoiceCode("invoiceCode");
        invoiceDeductParam.setInvoiceNo("invoiceNo");
        invoiceDeductParam.setBusinessNo("businessNo");
        invoiceDeductParam.setBusinessType(0);
        invoiceDeductParam.setSubjectName("subjectName");
        invoiceDeductParam.setBuyerName("buyerName");
        invoiceDeductParam.setNeedSubjectRelation(false);
        params.setList(Arrays.asList(invoiceDeductParam));

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<List<InvoiceDeductionDTO>> expectedResult = BizResult.create(Arrays.asList(invoiceDeductionDTO));

        // Configure InvoiceDAO.findByInvoiceNoAndInvoiceCode(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.findByInvoiceNoAndInvoiceCode(
              Arrays.asList(new InvoiceQueryBO("invoiceNo", "invoiceCode")))).thenReturn(invoiceDOS);

        when(mockInvoiceSubjectRelationDAO.getSubjectNameList("relationSubjectName")).thenReturn(Collections.emptyList());
        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result = invoiceDeductionServiceImplUnderTest.batchDeductInvoice(params);

    }

    @Test public void testDeductInvoice1() {
        // Setup
        final InvoiceDeductOperationParam param = new InvoiceDeductOperationParam();
        param.setOperatorId(0L);
        param.setOperatorName("operatorName");
        param.setRemark("remark");

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<InvoiceDeductionDTO> expectedResult = BizResult.create(invoiceDeductionDTO);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        when(mockInvoiceSubjectRelationDAO.getSubjectNameList("relationSubjectName")).thenReturn(Arrays.asList("value"));
        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<InvoiceDeductionDTO> result = invoiceDeductionServiceImplUnderTest.deductInvoice(param);

    }

    @Test public void testDeductInvoice1_InvoiceSubjectRelationDAOReturnsNoItems() {
        // Setup
        final InvoiceDeductOperationParam param = new InvoiceDeductOperationParam();
        param.setOperatorId(0L);
        param.setOperatorName("operatorName");
        param.setRemark("remark");

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<InvoiceDeductionDTO> expectedResult = BizResult.create(invoiceDeductionDTO);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        when(mockInvoiceSubjectRelationDAO.getSubjectNameList("relationSubjectName")).thenReturn(Collections.emptyList());
        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<InvoiceDeductionDTO> result = invoiceDeductionServiceImplUnderTest.deductInvoice(param);

    }
    @Test public void testBatchRecoverInvoice_InvoiceDeductionDAOSelectInvoiceDeductionListReturnsNoItems() {
        // Setup
        final InvoiceRecoverBatchParam param = new InvoiceRecoverBatchParam();
        final InvoiceRecoverParam invoiceRecoverParam = new InvoiceRecoverParam();
        invoiceRecoverParam.setInvoiceCode("invoiceCode");
        invoiceRecoverParam.setInvoiceNo("invoiceNo");
        invoiceRecoverParam.setBusinessNo("businessNo");
        invoiceRecoverParam.setBusinessType(2);
        invoiceRecoverParam.setAmount(new BigDecimal("20.00"));
        param.setList(Arrays.asList(invoiceRecoverParam));

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("10.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        List<InvoiceDeductionDTO> expectedResult = Arrays.asList(invoiceDeductionDTO);

        // Configure InvoiceDAO.findByInvoiceNoAndInvoiceCode(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("20.00"));
        invoiceDO.setUsedAmount(new BigDecimal("10.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.findByInvoiceNoAndInvoiceCode(
              Arrays.asList(new InvoiceQueryBO("invoiceNo", "invoiceCode")))).thenReturn(invoiceDOS);

        when(mockInvoiceDeductionDAO.selectInvoiceDeductionList("businessNo", "invoiceCode", "invoiceNo", 0, 0)).thenReturn(
              Collections.emptyList());
        when(mockInvoiceDAO.recoverInvoice(0, 0, new BigDecimal("0.00"))).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result = invoiceDeductionServiceImplUnderTest.batchRecoverInvoice(param);

    }

    @Test public void testBatchRecoverInvoice() {
        // Setup
        final InvoiceRecoverBatchParam param = new InvoiceRecoverBatchParam();
        final InvoiceRecoverParam invoiceRecoverParam = new InvoiceRecoverParam();
        invoiceRecoverParam.setInvoiceCode("invoiceCode");
        invoiceRecoverParam.setInvoiceNo("invoiceNo");
        invoiceRecoverParam.setBusinessNo("businessNo");
        invoiceRecoverParam.setBusinessType(0);
        invoiceRecoverParam.setAmount(new BigDecimal("0.00"));
        param.setList(Arrays.asList(invoiceRecoverParam));

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<List<InvoiceDeductionDTO>> expectedResult = BizResult.create(Arrays.asList(invoiceDeductionDTO));

        // Configure InvoiceDAO.findByInvoiceNoAndInvoiceCode(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.findByInvoiceNoAndInvoiceCode(
              Arrays.asList(new InvoiceQueryBO("invoiceNo", "invoiceCode")))).thenReturn(invoiceDOS);

        // Configure InvoiceDeductionDAO.selectInvoiceDeductionList(...).
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.selectInvoiceDeductionList("businessNo", "invoiceCode", "invoiceNo", 0, 0)).thenReturn(
              invoiceDeductionDOS);

        when(mockInvoiceDAO.recoverInvoice(0, 0, new BigDecimal("0.00"))).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result = invoiceDeductionServiceImplUnderTest.batchRecoverInvoice(param);

    }

    @Test public void testBatchRecoverInvoice_InvoiceDAOFindByInvoiceNoAndInvoiceCodeReturnsNoItems() {
        // Setup
        final InvoiceRecoverBatchParam param = new InvoiceRecoverBatchParam();
        final InvoiceRecoverParam invoiceRecoverParam = new InvoiceRecoverParam();
        invoiceRecoverParam.setInvoiceCode("invoiceCode");
        invoiceRecoverParam.setInvoiceNo("invoiceNo");
        invoiceRecoverParam.setBusinessNo("businessNo");
        invoiceRecoverParam.setBusinessType(0);
        invoiceRecoverParam.setAmount(new BigDecimal("0.00"));
        param.setList(Arrays.asList(invoiceRecoverParam));

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<List<InvoiceDeductionDTO>> expectedResult = BizResult.create(Arrays.asList(invoiceDeductionDTO));
        when(mockInvoiceDAO.findByInvoiceNoAndInvoiceCode(
              Arrays.asList(new InvoiceQueryBO("invoiceNo", "invoiceCode")))).thenReturn(Collections.emptyList());

        // Configure InvoiceDeductionDAO.selectInvoiceDeductionList(...).
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.selectInvoiceDeductionList("businessNo", "invoiceCode", "invoiceNo", 0, 0)).thenReturn(
              invoiceDeductionDOS);

        when(mockInvoiceDAO.recoverInvoice(0, 0, new BigDecimal("0.00"))).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result = invoiceDeductionServiceImplUnderTest.batchRecoverInvoice(param);

    }



    @Test public void testRecoverInvoice1() {
        // Setup
        final InvoiceRecoverOperationParam param = new InvoiceRecoverOperationParam();
        param.setOperatorId(0L);
        param.setOperatorName("operatorName");
        param.setRemark("remark");

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<InvoiceDeductionDTO> expectedResult = BizResult.create(invoiceDeductionDTO);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Configure InvoiceDeductionDAO.selectInvoiceDeductionList(...).
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.selectInvoiceDeductionList("businessNo", "invoiceCode", "invoiceNo", 0, 0)).thenReturn(
              invoiceDeductionDOS);

        when(mockInvoiceDAO.recoverInvoice(0, 0, new BigDecimal("0.00"))).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<InvoiceDeductionDTO> result = invoiceDeductionServiceImplUnderTest.recoverInvoice(param);

    }

    @Test public void testRecoverInvoice1_InvoiceDeductionDAOSelectInvoiceDeductionListReturnsNoItems() {
        // Setup
        final InvoiceRecoverOperationParam param = new InvoiceRecoverOperationParam();
        param.setOperatorId(0L);
        param.setOperatorName("operatorName");
        param.setRemark("remark");

        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<InvoiceDeductionDTO> expectedResult = BizResult.create(invoiceDeductionDTO);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        when(mockInvoiceDeductionDAO.selectInvoiceDeductionList("businessNo", "invoiceCode", "invoiceNo", 0, 0)).thenReturn(
              Collections.emptyList());
        when(mockInvoiceDAO.recoverInvoice(0, 0, new BigDecimal("0.00"))).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final BizResult<InvoiceDeductionDTO> result = invoiceDeductionServiceImplUnderTest.recoverInvoice(param);

    }

    @Test public void testInvoiceManualDeduct() {
        // Setup
        final InvoiceManualDeductParam param = new InvoiceManualDeductParam();
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setAmount(new BigDecimal("0.00"));
        param.setBusinessNo("businessNo");
        param.setReason("reason");
        param.setCreator(0);
        param.setOperateType(0);

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("100.00"));
        invoiceDO.setUsedAmount(new BigDecimal("0.00"));
        invoiceDO.setPrice(new BigDecimal("100.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);
        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        invoiceDeductionServiceImplUnderTest.invoiceManualDeduct(param);

    }

    @Test public void testGetApplyInvoiceList() {
        // Setup
        final ApplyInvoiceParam param = new ApplyInvoiceParam();
        param.setApplyNo("applyNo");

        final InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setInvoiceId(0);
        invoiceInfoVO.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO.setBusinessTypeCode(0);
        invoiceInfoVO.setBusinessTypeName("businessTypeName");
        invoiceInfoVO.setInvoiceCode("invoiceCode");
        invoiceInfoVO.setInvoiceNo("invoiceNo");
        invoiceInfoVO.setInvoiceDate("invoiceDate");
        invoiceInfoVO.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setInvoiceStatus(0);
        final List<InvoiceInfoVO> expectedResult = Arrays.asList(invoiceInfoVO);

        // Configure InvoiceDeductionDAO.findPayInvoiceByBusinessNo(...).
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.findPayInvoiceByBusinessNo("businessNo", 0)).thenReturn(invoiceDeductionDOS);

        // Configure InvoiceService.getInvoiceInfoList(...).
        final InvoiceInfoVO invoiceInfoVO1 = new InvoiceInfoVO();
        invoiceInfoVO1.setInvoiceId(0);
        invoiceInfoVO1.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO1.setBusinessTypeCode(0);
        invoiceInfoVO1.setBusinessTypeName("businessTypeName");
        invoiceInfoVO1.setInvoiceCode("invoiceCode");
        invoiceInfoVO1.setInvoiceNo("invoiceNo");
        invoiceInfoVO1.setInvoiceDate("invoiceDate");
        invoiceInfoVO1.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO1.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO1.setInvoiceStatus(0);
        final List<InvoiceInfoVO> invoiceInfoVOS = Arrays.asList(invoiceInfoVO1);
        when(mockInvoiceService.getInvoiceInfoList(new InvoiceParam())).thenReturn(invoiceInfoVOS);

        // Run the test
        final List<InvoiceInfoVO> result = invoiceDeductionServiceImplUnderTest.getApplyInvoiceList(param);

    }

    @Test public void testGetApplyInvoiceList_InvoiceDeductionDAOReturnsNoItems() {
        // Setup
        final ApplyInvoiceParam param = new ApplyInvoiceParam();
        param.setApplyNo("applyNo");

        final InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setInvoiceId(0);
        invoiceInfoVO.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO.setBusinessTypeCode(0);
        invoiceInfoVO.setBusinessTypeName("businessTypeName");
        invoiceInfoVO.setInvoiceCode("invoiceCode");
        invoiceInfoVO.setInvoiceNo("invoiceNo");
        invoiceInfoVO.setInvoiceDate("invoiceDate");
        invoiceInfoVO.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setInvoiceStatus(0);
        final List<InvoiceInfoVO> expectedResult = Arrays.asList(invoiceInfoVO);
        when(mockInvoiceDeductionDAO.findPayInvoiceByBusinessNo("businessNo", 0)).thenReturn(Collections.emptyList());

        // Configure InvoiceService.getInvoiceInfoList(...).
        final InvoiceInfoVO invoiceInfoVO1 = new InvoiceInfoVO();
        invoiceInfoVO1.setInvoiceId(0);
        invoiceInfoVO1.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO1.setBusinessTypeCode(0);
        invoiceInfoVO1.setBusinessTypeName("businessTypeName");
        invoiceInfoVO1.setInvoiceCode("invoiceCode");
        invoiceInfoVO1.setInvoiceNo("invoiceNo");
        invoiceInfoVO1.setInvoiceDate("invoiceDate");
        invoiceInfoVO1.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO1.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO1.setInvoiceStatus(0);
        final List<InvoiceInfoVO> invoiceInfoVOS = Arrays.asList(invoiceInfoVO1);
        when(mockInvoiceService.getInvoiceInfoList(new InvoiceParam())).thenReturn(invoiceInfoVOS);

        // Run the test
        final List<InvoiceInfoVO> result = invoiceDeductionServiceImplUnderTest.getApplyInvoiceList(param);

    }

    @Test public void testGetApplyInvoiceList_InvoiceServiceReturnsNoItems() {
        // Setup
        final ApplyInvoiceParam param = new ApplyInvoiceParam();
        param.setApplyNo("applyNo");

        final InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setInvoiceId(0);
        invoiceInfoVO.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO.setBusinessTypeCode(0);
        invoiceInfoVO.setBusinessTypeName("businessTypeName");
        invoiceInfoVO.setInvoiceCode("invoiceCode");
        invoiceInfoVO.setInvoiceNo("invoiceNo");
        invoiceInfoVO.setInvoiceDate("invoiceDate");
        invoiceInfoVO.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setInvoiceStatus(0);
        final List<InvoiceInfoVO> expectedResult = Arrays.asList(invoiceInfoVO);

        // Configure InvoiceDeductionDAO.findPayInvoiceByBusinessNo(...).
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.findPayInvoiceByBusinessNo("businessNo", 0)).thenReturn(invoiceDeductionDOS);

        when(mockInvoiceService.getInvoiceInfoList(new InvoiceParam())).thenReturn(Collections.emptyList());

        // Run the test
        final List<InvoiceInfoVO> result = invoiceDeductionServiceImplUnderTest.getApplyInvoiceList(param);

    }

    @Test public void testGetDeductInvoiceList() {
        // Setup
        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<List<InvoiceDeductionDTO>> expectedResult = BizResult.create(Arrays.asList(invoiceDeductionDTO));

        // Configure InvoiceDeductionDAO.findByBusinessNo(...).
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.findByBusinessNo("businessNo", 0, 0)).thenReturn(invoiceDeductionDOS);

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result =
              invoiceDeductionServiceImplUnderTest.getDeductInvoiceList("businessNo", 0);

    }

    @Test public void testGetDeductInvoiceList_InvoiceDeductionDAOReturnsNoItems() {
        // Setup
        final InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(0);
        invoiceDeductionDTO.setInvoiceCode("invoiceCode");
        invoiceDeductionDTO.setInvoiceNo("invoiceNo");
        invoiceDeductionDTO.setBusinessNo("businessNo");
        invoiceDeductionDTO.setBusinessType(0);
        invoiceDeductionDTO.setOperateType(0);
        invoiceDeductionDTO.setAmount(new BigDecimal("0.00"));
        invoiceDeductionDTO.setReason("reason");
        invoiceDeductionDTO.setCreator(0L);
        invoiceDeductionDTO.setCreateName("createName");
        final BizResult<List<InvoiceDeductionDTO>> expectedResult = BizResult.create(Arrays.asList(invoiceDeductionDTO));
        when(mockInvoiceDeductionDAO.findByBusinessNo("businessNo", 0, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final BizResult<List<InvoiceDeductionDTO>> result =
              invoiceDeductionServiceImplUnderTest.getDeductInvoiceList("businessNo", 0);

    }

    @Test public void testGetInvoiceByBusinessRelation() {
        // Setup
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> expectedResult = Arrays.asList(invoiceDeductionDO);

        // Configure InvoiceDeductionDAO.findTopByInvoiceNoAndInvoiceCode(...).
        final InvoiceDeductionDO invoiceDeductionDO1 = new InvoiceDeductionDO();
        invoiceDeductionDO1.setId(0);
        invoiceDeductionDO1.setInvoiceCode("invoiceCode");
        invoiceDeductionDO1.setInvoiceNo("invoiceNo");
        invoiceDeductionDO1.setBusinessNo("businessNo");
        invoiceDeductionDO1.setBusinessType(0);
        invoiceDeductionDO1.setOperateType(0);
        invoiceDeductionDO1.setCreator(0L);
        invoiceDeductionDO1.setCreateName("createName");
        invoiceDeductionDO1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> invoiceDeductionDOS = Arrays.asList(invoiceDeductionDO1);
        when(mockInvoiceDeductionDAO.findTopByInvoiceNoAndInvoiceCode("invoiceNo", "invoiceCode")).thenReturn(
              invoiceDeductionDOS);

        // Run the test
        final List<InvoiceDeductionDO> result =
              invoiceDeductionServiceImplUnderTest.getInvoiceByBusinessRelation("invoiceNo", "invoiceCode");

    }

    @Test public void testGetInvoiceByBusinessRelation_InvoiceDeductionDAOReturnsNoItems() {
        // Setup
        final InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setId(0);
        invoiceDeductionDO.setInvoiceCode("invoiceCode");
        invoiceDeductionDO.setInvoiceNo("invoiceNo");
        invoiceDeductionDO.setBusinessNo("businessNo");
        invoiceDeductionDO.setBusinessType(0);
        invoiceDeductionDO.setOperateType(0);
        invoiceDeductionDO.setCreator(0L);
        invoiceDeductionDO.setCreateName("createName");
        invoiceDeductionDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceDeductionDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InvoiceDeductionDO> expectedResult = Arrays.asList(invoiceDeductionDO);
        when(mockInvoiceDeductionDAO.findTopByInvoiceNoAndInvoiceCode("invoiceNo", "invoiceCode")).thenReturn(
              Collections.emptyList());

        // Run the test
        final List<InvoiceDeductionDO> result =
              invoiceDeductionServiceImplUnderTest.getInvoiceByBusinessRelation("invoiceNo", "invoiceCode");

    }
}
