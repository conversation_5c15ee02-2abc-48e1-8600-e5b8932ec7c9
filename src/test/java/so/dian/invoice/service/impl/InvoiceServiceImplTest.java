package so.dian.invoice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import so.dian.center.common.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.client.LvyClient;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.dao.InvoiceDetailDAO;
import so.dian.invoice.dao.InvoiceExpressesRelationDAO;
import so.dian.invoice.dao.InvoiceIdentifyRecordDAO;
import so.dian.invoice.dao.InvoiceSubjectRelationDAO;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.enums.InvoiceOperateLogTypeEnum;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;
import so.dian.invoice.internal.CommonInternalService;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.manager.InvoiceOperateLogManager;
import so.dian.invoice.manager.InvoiceValidateStatusManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.dto.InvoiceBatchParam;
import so.dian.invoice.pojo.dto.InvoiceDetailDto;
import so.dian.invoice.pojo.dto.InvoiceDto;
import so.dian.invoice.pojo.dto.KeyValueDto;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.param.AddSingleInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceDeductQueryParam;
import so.dian.invoice.pojo.param.InvoiceExportParam;
import so.dian.invoice.pojo.param.InvoiceFilterTimeParam;
import so.dian.invoice.pojo.param.InvoiceObsoleteParam;
import so.dian.invoice.pojo.param.InvoiceParam;
import so.dian.invoice.pojo.param.InvoiceReviewParam;
import so.dian.invoice.pojo.param.InvoiceSubjectInfoParam;
import so.dian.invoice.pojo.param.InvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvoiceValidateDetailParams;
import so.dian.invoice.pojo.param.InvoiceValidateParams;
import so.dian.invoice.pojo.param.SubjectTypeParam;
import so.dian.invoice.pojo.query.InvoiceFilterTimeQuery;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;
import so.dian.invoice.pojo.vo.InvoiceVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.util.PageData;
import so.dian.invoice.util.ServiceResult;
import so.dian.lvy.pojo.query.WithdrawListQuery;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InvoiceServiceImplTest {

    @Mock
    private InvoiceDAO mockInvoiceDAO;
    @Mock
    private InvoiceDetailDAO mockInvoiceDetailDAO;
    @Mock
    private InvoiceExpressesRelationDAO mockInvoiceExpressesRelationDAO;
    @Mock
    private InvoiceDeductionDAO mockInvoiceDeductionDAO;
    @Mock
    private LvyClient mockLvyClient;
    @Mock
    private AgentEmployeeService mockAgentEmployeeService;
    @Mock
    private InvoiceIdentifyRecordDAO mockRecordDAO;
    @Mock
    private ApplicationContext mockContext;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private InvoiceSubjectRelationDAO mockInvoiceSubjectRelationDAO;
    @Mock
    private CommonInternalService mockCommonInternalService;
    @Mock
    private InvoiceManager mockInvoiceManager;
    @Mock
    private InvoiceOperateLogManager mockInvoiceOperateLogManager;
    @Mock
    private InvoiceValidateStatusManager mockInvoiceValidateStatusManager;

    @InjectMocks private InvoiceServiceImpl invoiceServiceImplUnderTest;

    @Test public void testAddWithDeductInvoice() {
        // Setup
        final InvoiceDto dto = new InvoiceDto();
        dto.setId(0);
        dto.setInvoiceCode("invoiceCode");
        dto.setInvoiceNo("invoiceNo");
        dto.setSupplierNo("supplierNo");
        dto.setSupplierName("supplierName");
        dto.setSubjectType(0);
        dto.setSubjectName("subjectName");
        dto.setSubjectTypeStr("subjectTypeStr");
        dto.setType(0);
        dto.setTypeStr("typeStr");

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        when(mockAgentEmployeeService.getEmployeeNickById(0)).thenReturn("result");
        when(mockInvoiceDAO.insertBatch(Arrays.asList(new InvoiceDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);

        // Run the test
        invoiceServiceImplUnderTest.addWithDeductInvoice(dto);
    }

    @Test public void testAddWithDeductInvoice_AgentEmployeeServiceGetAgentByUserIdReturnsNoItem() {
        // Setup
        final InvoiceDto dto = new InvoiceDto();
        dto.setId(0);
        dto.setInvoiceCode("invoiceCode");
        dto.setInvoiceNo("invoiceNo");
        dto.setSupplierNo("supplierNo");
        dto.setSupplierName("supplierName");
        dto.setSubjectType(0);
        dto.setSubjectName("subjectName");
        dto.setSubjectTypeStr("subjectTypeStr");
        dto.setType(0);
        dto.setTypeStr("typeStr");

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(BizResult.create());
        when(mockAgentEmployeeService.getEmployeeNickById(0)).thenReturn("result");
        when(mockInvoiceDAO.insertBatch(Arrays.asList(new InvoiceDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceDeductionDAO.insertSelective(new InvoiceDeductionDO())).thenReturn(0);

        // Run the test
        invoiceServiceImplUnderTest.addWithDeductInvoice(dto);
    }

    @Test public void testAddInvoiceBatch() {
        // Setup
        final InvoiceBatchParam param = new InvoiceBatchParam();
        param.setBatchNo("batchNo");
        final InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setId(0);
        invoiceDto.setInvoiceCode("invoiceCode");
        invoiceDto.setInvoiceNo("invoiceNo");
        invoiceDto.setSupplierNo("supplierNo");
        invoiceDto.setSupplierName("supplierName");
        invoiceDto.setSubjectType(0);
        invoiceDto.setSubjectName("subjectName");
        invoiceDto.setSubjectTypeStr("subjectTypeStr");
        invoiceDto.setType(0);
        invoiceDto.setTypeStr("typeStr");
        param.setInvoiceDtoList(Arrays.asList(invoiceDto));

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        when(mockAgentEmployeeService.getEmployeeNickById(0)).thenReturn("result");
        when(mockInvoiceDAO.selectCodeAndNoByInvoiceNoList(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockInvoiceDAO.insertBatch(Arrays.asList(new InvoiceDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceOperateLogManager.insertBatch(Arrays.asList(new InvoiceOperateLogBO()))).thenReturn(false);

        // Run the test
        final Set<String> result = invoiceServiceImplUnderTest.addInvoiceBatch(0, param, Arrays.asList("value"));
    }

    @Test public void testAddInvoiceBatch_AgentEmployeeServiceGetAgentByUserIdReturnsNoItem() {
        // Setup
        final InvoiceBatchParam param = new InvoiceBatchParam();
        param.setBatchNo("batchNo");
        final InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setId(0);
        invoiceDto.setInvoiceCode("invoiceCode");
        invoiceDto.setInvoiceNo("invoiceNo");
        invoiceDto.setSupplierNo("supplierNo");
        invoiceDto.setSupplierName("supplierName");
        invoiceDto.setSubjectType(0);
        invoiceDto.setSubjectName("subjectName");
        invoiceDto.setSubjectTypeStr("subjectTypeStr");
        invoiceDto.setType(0);
        invoiceDto.setTypeStr("typeStr");
        param.setInvoiceDtoList(Arrays.asList(invoiceDto));

        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(BizResult.create());
        when(mockAgentEmployeeService.getEmployeeNickById(0)).thenReturn("result");
        when(mockInvoiceDAO.selectCodeAndNoByInvoiceNoList(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockInvoiceDAO.insertBatch(Arrays.asList(new InvoiceDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceOperateLogManager.insertBatch(Arrays.asList(new InvoiceOperateLogBO()))).thenReturn(false);

        // Run the test
        final Set<String> result = invoiceServiceImplUnderTest.addInvoiceBatch(0, param, Arrays.asList("value"));
    }

    @Test public void testAddInvoiceBatch_InvoiceDAOSelectCodeAndNoByInvoiceNoListReturnsNoItems() {
        // Setup
        final InvoiceBatchParam param = new InvoiceBatchParam();
        param.setBatchNo("batchNo");
        final InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setId(0);
        invoiceDto.setInvoiceCode("invoiceCode");
        invoiceDto.setInvoiceNo("invoiceNo");
        invoiceDto.setSupplierNo("supplierNo");
        invoiceDto.setSupplierName("supplierName");
        invoiceDto.setSubjectType(0);
        invoiceDto.setSubjectName("subjectName");
        invoiceDto.setSubjectTypeStr("subjectTypeStr");
        invoiceDto.setType(0);
        invoiceDto.setTypeStr("typeStr");
        param.setInvoiceDtoList(Arrays.asList(invoiceDto));

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        when(mockAgentEmployeeService.getEmployeeNickById(0)).thenReturn("result");
        when(mockInvoiceDAO.selectCodeAndNoByInvoiceNoList(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        when(mockInvoiceDAO.insertBatch(Arrays.asList(new InvoiceDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceOperateLogManager.insertBatch(Arrays.asList(new InvoiceOperateLogBO()))).thenReturn(false);

        // Run the test
        final Set<String> result = invoiceServiceImplUnderTest.addInvoiceBatch(0, param, Arrays.asList("value"));
    }

    @Test public void testGetInvoiceById() {
        // Setup
        final InvoiceDO expectedResult = new InvoiceDO();
        expectedResult.setBuyer("buyer");
        expectedResult.setSubjectName("subjectName");
        expectedResult.setInvoiceNo("invoiceNo");
        expectedResult.setInvoiceCode("invoiceCode");
        expectedResult.setId(0);
        expectedResult.setSupplierNo("supplierNo");
        expectedResult.setSubjectType(0);
        expectedResult.setBillNo("billNo");
        expectedResult.setType(0);
        expectedResult.setRawPrice(new BigDecimal("0.00"));

        // Configure InvoiceDAO.selectByPrimaryKey(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectByPrimaryKey(0)).thenReturn(invoiceDO);

        // Run the test
        final InvoiceDO result = invoiceServiceImplUnderTest.getInvoiceById(0);
    }

    @Test public void testGetInvoiceByInvoiceCodeAndNo() {
        // Setup
        final InvoiceDO expectedResult = new InvoiceDO();
        expectedResult.setBuyer("buyer");
        expectedResult.setSubjectName("subjectName");
        expectedResult.setInvoiceNo("invoiceNo");
        expectedResult.setInvoiceCode("invoiceCode");
        expectedResult.setId(0);
        expectedResult.setSupplierNo("supplierNo");
        expectedResult.setSubjectType(0);
        expectedResult.setBillNo("billNo");
        expectedResult.setType(0);
        expectedResult.setRawPrice(new BigDecimal("0.00"));

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Run the test
        final InvoiceDO result = invoiceServiceImplUnderTest.getInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo");
    }

    @Test public void testGetInvoiceByInvoiceNoList() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> expectedResult = Arrays.asList(invoiceDO);

        // Configure InvoiceDAO.selectInvoiceByInvoiceNoList(...).
        final InvoiceDO invoiceDO1 = new InvoiceDO();
        invoiceDO1.setBuyer("buyer");
        invoiceDO1.setSubjectName("subjectName");
        invoiceDO1.setInvoiceNo("invoiceNo");
        invoiceDO1.setInvoiceCode("invoiceCode");
        invoiceDO1.setId(0);
        invoiceDO1.setSupplierNo("supplierNo");
        invoiceDO1.setSubjectType(0);
        invoiceDO1.setBillNo("billNo");
        invoiceDO1.setType(0);
        invoiceDO1.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO1);
        when(mockInvoiceDAO.selectInvoiceByInvoiceNoList(Arrays.asList("value"))).thenReturn(invoiceDOS);

        // Run the test
        final List<InvoiceDO> result = invoiceServiceImplUnderTest.getInvoiceByInvoiceNoList(Arrays.asList("value"));
    }

    @Test public void testGetInvoiceByInvoiceNoList_InvoiceDAOReturnsNoItems() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> expectedResult = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.selectInvoiceByInvoiceNoList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<InvoiceDO> result = invoiceServiceImplUnderTest.getInvoiceByInvoiceNoList(Arrays.asList("value"));
    }

    @Test public void testInsertInvoiceList() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceList = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.insertBatch(Arrays.asList(new InvoiceDO()))).thenReturn(0);

        // Run the test
        final int result = invoiceServiceImplUnderTest.insertInvoiceList(invoiceList);
    }

    @Test public void testUpdateInvoice() {
        // Setup
        final InvoiceDO invoice = new InvoiceDO();
        invoice.setBuyer("buyer");
        invoice.setSubjectName("subjectName");
        invoice.setInvoiceNo("invoiceNo");
        invoice.setInvoiceCode("invoiceCode");
        invoice.setId(0);
        invoice.setSupplierNo("supplierNo");
        invoice.setSubjectType(0);
        invoice.setBillNo("billNo");
        invoice.setType(0);
        invoice.setRawPrice(new BigDecimal("0.00"));

        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);

        // Run the test
        final int result = invoiceServiceImplUnderTest.updateInvoice(invoice);
    }

    @Test public void testDeleteInvoiceByInvoiceCodeAndNo() {
        // Setup
        final InvoiceObsoleteParam param = new InvoiceObsoleteParam();
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setInvoiceId(0L);

        when(mockInvoiceDAO.updateInvoiceByInvoiceCodeAndNo(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDetailDAO.updateInvoiceDetailByInvoiceCodeAndNo(any(InvoiceDetailDO.class))).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final Boolean result = invoiceServiceImplUnderTest.deleteInvoiceByInvoiceCodeAndNo(param);
    }

    @Test public void testGetInvoiceList() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> expectedResult = Arrays.asList(invoiceDO);

        // Configure InvoiceDAO.selectInvoiceList(...).
        final InvoiceDO invoiceDO1 = new InvoiceDO();
        invoiceDO1.setBuyer("buyer");
        invoiceDO1.setSubjectName("subjectName");
        invoiceDO1.setInvoiceNo("invoiceNo");
        invoiceDO1.setInvoiceCode("invoiceCode");
        invoiceDO1.setId(0);
        invoiceDO1.setSupplierNo("supplierNo");
        invoiceDO1.setSubjectType(0);
        invoiceDO1.setBillNo("billNo");
        invoiceDO1.setType(0);
        invoiceDO1.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO1);
        when(mockInvoiceDAO.selectInvoiceList(new InvoiceParam())).thenReturn(invoiceDOS);

        // Run the test
        final List<InvoiceDO> result = invoiceServiceImplUnderTest.getInvoiceList(param);
    }

    @Test public void testGetInvoiceList_InvoiceDAOReturnsNoItems() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> expectedResult = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.selectInvoiceList(new InvoiceParam())).thenReturn(Collections.emptyList());

        // Run the test
        final List<InvoiceDO> result = invoiceServiceImplUnderTest.getInvoiceList(param);
    }

    @Test public void testGetInvoicePage() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setId(0);
        invoiceDto.setInvoiceCode("invoiceCode");
        invoiceDto.setInvoiceNo("invoiceNo");
        invoiceDto.setSupplierNo("supplierNo");
        invoiceDto.setSupplierName("supplierName");
        invoiceDto.setSubjectType(0);
        invoiceDto.setSubjectName("subjectName");
        invoiceDto.setSubjectTypeStr("subjectTypeStr");
        invoiceDto.setType(0);
        invoiceDto.setTypeStr("typeStr");
        final List<InvoiceDto> expectedResult = Arrays.asList(invoiceDto);

        // Configure InvoiceDAO.selectInvoicePage(...).
        final InvoiceDto invoiceDto1 = new InvoiceDto();
        invoiceDto1.setId(0);
        invoiceDto1.setInvoiceCode("invoiceCode");
        invoiceDto1.setInvoiceNo("invoiceNo");
        invoiceDto1.setSupplierNo("supplierNo");
        invoiceDto1.setSupplierName("supplierName");
        invoiceDto1.setSubjectType(0);
        invoiceDto1.setSubjectName("subjectName");
        invoiceDto1.setSubjectTypeStr("subjectTypeStr");
        invoiceDto1.setType(0);
        invoiceDto1.setTypeStr("typeStr");
        final List<InvoiceDto> invoiceDtos = Arrays.asList(invoiceDto1);
        when(mockInvoiceDAO.selectInvoicePage(new InvoiceParam())).thenReturn(invoiceDtos);

        // Run the test
        final List<InvoiceDto> result = invoiceServiceImplUnderTest.getInvoicePage(param);
    }

    @Test public void testGetInvoicePage_InvoiceDAOReturnsNoItems() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setId(0);
        invoiceDto.setInvoiceCode("invoiceCode");
        invoiceDto.setInvoiceNo("invoiceNo");
        invoiceDto.setSupplierNo("supplierNo");
        invoiceDto.setSupplierName("supplierName");
        invoiceDto.setSubjectType(0);
        invoiceDto.setSubjectName("subjectName");
        invoiceDto.setSubjectTypeStr("subjectTypeStr");
        invoiceDto.setType(0);
        invoiceDto.setTypeStr("typeStr");
        final List<InvoiceDto> expectedResult = Arrays.asList(invoiceDto);
        when(mockInvoiceDAO.selectInvoicePage(new InvoiceParam())).thenReturn(Collections.emptyList());

        // Run the test
        final List<InvoiceDto> result = invoiceServiceImplUnderTest.getInvoicePage(param);
    }

    @Test public void testCount() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        when(mockInvoiceDAO.count(new InvoiceParam())).thenReturn(0);

        // Run the test
        final int result = invoiceServiceImplUnderTest.count(param);
    }

    @Test public void testGetInvoiceDetailByInvoiceCodeAndNo() {
        // Setup

        // Configure InvoiceDetailDAO.selectInvoiceDetailList(...).
        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOS = Arrays.asList(invoiceDetailDO);
        when(mockInvoiceDetailDAO.selectInvoiceDetailList("invoiceCode", "invoiceNo")).thenReturn(invoiceDetailDOS);

        // Run the test
        final List<InvoiceDetailDO> result =
              invoiceServiceImplUnderTest.getInvoiceDetailByInvoiceCodeAndNo("invoiceCode", "invoiceNo");
    }

    @Test public void testGetInvoiceDetailByInvoiceCodeAndNo_InvoiceDetailDAOReturnsNoItems() {
        // Setup
        when(mockInvoiceDetailDAO.selectInvoiceDetailList("invoiceCode", "invoiceNo")).thenReturn(Collections.emptyList());

        // Run the test
        final List<InvoiceDetailDO> result =
              invoiceServiceImplUnderTest.getInvoiceDetailByInvoiceCodeAndNo("invoiceCode", "invoiceNo");
    }

    @Test public void testSaveInvoiceDetail1() {
        // Setup
        final InvoiceDetailDto invoiceDetailDto = new InvoiceDetailDto();
        invoiceDetailDto.setId(0);
        invoiceDetailDto.setInvoiceNo("invoiceNo");
        invoiceDetailDto.setInvoiceCode("invoiceCode");
        invoiceDetailDto.setMaterialName("materialName");
        invoiceDetailDto.setMaterialSpec("materialSpec");
        invoiceDetailDto.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setQuantity(0);
        invoiceDetailDto.setUnit("unit");
        invoiceDetailDto.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setTaxRate(new BigDecimal("0.00"));
        final List<InvoiceDetailDto> invoiceDetailDtoList = Arrays.asList(invoiceDetailDto);

        // Configure InvoiceDetailDAO.selectInvoiceDetailList(...).
        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOS = Arrays.asList(invoiceDetailDO);
        when(mockInvoiceDetailDAO.selectInvoiceDetailList("invoiceCode", "invoiceNo")).thenReturn(invoiceDetailDOS);

        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.updateByPrimaryKeySelective(any(InvoiceDetailDO.class))).thenReturn(0);

        // Run the test
        invoiceServiceImplUnderTest.saveInvoiceDetail("invoiceCode", "invoiceNo", invoiceDetailDtoList);
    }

    @Test public void testSaveInvoiceDetail1_InvoiceDetailDAOSelectInvoiceDetailListReturnsNoItems() {
        // Setup
        final InvoiceDetailDto invoiceDetailDto = new InvoiceDetailDto();
        invoiceDetailDto.setId(0);
        invoiceDetailDto.setInvoiceNo("invoiceNo");
        invoiceDetailDto.setInvoiceCode("invoiceCode");
        invoiceDetailDto.setMaterialName("materialName");
        invoiceDetailDto.setMaterialSpec("materialSpec");
        invoiceDetailDto.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setQuantity(0);
        invoiceDetailDto.setUnit("unit");
        invoiceDetailDto.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setTaxRate(new BigDecimal("0.00"));
        final List<InvoiceDetailDto> invoiceDetailDtoList = Arrays.asList(invoiceDetailDto);
        when(mockInvoiceDetailDAO.selectInvoiceDetailList("invoiceCode", "invoiceNo")).thenReturn(Collections.emptyList());
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.updateByPrimaryKeySelective(any(InvoiceDetailDO.class))).thenReturn(0);

        // Run the test
        invoiceServiceImplUnderTest.saveInvoiceDetail("invoiceCode", "invoiceNo", invoiceDetailDtoList);

        // Verify the results
    }

    @Test public void testSaveInvoiceDetail2() {
        // Setup
        final InvoiceDetailDto invoiceDetailDto = new InvoiceDetailDto();
        invoiceDetailDto.setId(0);
        invoiceDetailDto.setInvoiceNo("invoiceNo");
        invoiceDetailDto.setInvoiceCode("invoiceCode");
        invoiceDetailDto.setMaterialName("materialName");
        invoiceDetailDto.setMaterialSpec("materialSpec");
        invoiceDetailDto.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setQuantity(0);
        invoiceDetailDto.setUnit("unit");
        invoiceDetailDto.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setTaxRate(new BigDecimal("0.00"));
        final List<InvoiceDetailDto> invoiceDetailDtoList = Arrays.asList(invoiceDetailDto);

        // Configure InvoiceDetailDAO.selectInvoiceDetailList(...).
        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOS = Arrays.asList(invoiceDetailDO);
        when(mockInvoiceDetailDAO.selectInvoiceDetailList("invoiceCode", "invoiceNo")).thenReturn(invoiceDetailDOS);

        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.updateByPrimaryKeySelective(any(InvoiceDetailDO.class))).thenReturn(0);

        // Run the test
        invoiceServiceImplUnderTest.saveInvoiceDetail("invoiceCode", "invoiceNo", invoiceDetailDtoList, "oldInvoiceCode",
              "oldInvoiceNo");

        // Verify the results
    }

    @Test public void testSaveInvoiceDetail2_InvoiceDetailDAOSelectInvoiceDetailListReturnsNoItems() {
        // Setup
        final InvoiceDetailDto invoiceDetailDto = new InvoiceDetailDto();
        invoiceDetailDto.setId(0);
        invoiceDetailDto.setInvoiceNo("invoiceNo");
        invoiceDetailDto.setInvoiceCode("invoiceCode");
        invoiceDetailDto.setMaterialName("materialName");
        invoiceDetailDto.setMaterialSpec("materialSpec");
        invoiceDetailDto.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setQuantity(0);
        invoiceDetailDto.setUnit("unit");
        invoiceDetailDto.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setTaxRate(new BigDecimal("0.00"));
        final List<InvoiceDetailDto> invoiceDetailDtoList = Arrays.asList(invoiceDetailDto);
        when(mockInvoiceDetailDAO.selectInvoiceDetailList("invoiceCode", "invoiceNo")).thenReturn(Collections.emptyList());
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockInvoiceDetailDAO.updateByPrimaryKeySelective(any(InvoiceDetailDO.class))).thenReturn(0);

        // Run the test
        invoiceServiceImplUnderTest.saveInvoiceDetail("invoiceCode", "invoiceNo", invoiceDetailDtoList, "oldInvoiceCode",
              "oldInvoiceNo");

        // Verify the results
    }

    @Test public void testInsertInvoiceDetailBatch() {
        // Setup
        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOList = Arrays.asList(invoiceDetailDO);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);

        // Run the test
        final int result = invoiceServiceImplUnderTest.insertInvoiceDetailBatch(invoiceDetailDOList);
    }

    @Test public void testUpdateInvoiceDetailList() {
        // Setup
        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOList = Arrays.asList(invoiceDetailDO);
        when(mockInvoiceDetailDAO.updateByPrimaryKeySelective(any(InvoiceDetailDO.class))).thenReturn(0);

        // Run the test
        final int result = invoiceServiceImplUnderTest.updateInvoiceDetailList(invoiceDetailDOList);
    }

    @Test public void testDownLoadInvoiceDetail() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();

        // Configure InvoiceDAO.selectInvoiceDetailAll(...).
        final InvoiceDetailDto invoiceDetailDto = new InvoiceDetailDto();
        invoiceDetailDto.setId(0);
        invoiceDetailDto.setInvoiceNo("invoiceNo");
        invoiceDetailDto.setInvoiceCode("invoiceCode");
        invoiceDetailDto.setMaterialName("materialName");
        invoiceDetailDto.setMaterialSpec("materialSpec");
        invoiceDetailDto.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setQuantity(0);
        invoiceDetailDto.setUnit("unit");
        invoiceDetailDto.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDto.setTaxRate(new BigDecimal("0.00"));
        final List<InvoiceDetailDto> invoiceDetailDtos = Arrays.asList(invoiceDetailDto);
        when(mockInvoiceDAO.selectInvoiceDetailAll(new InvoiceParam())).thenReturn(invoiceDetailDtos);

        // Run the test
        invoiceServiceImplUnderTest.downLoadInvoiceDetail(param, request, response);

        // Verify the results
    }

    @Test public void testDownLoadInvoiceDetail_InvoiceDAOReturnsNoItems() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();
        when(mockInvoiceDAO.selectInvoiceDetailAll(new InvoiceParam())).thenReturn(Collections.emptyList());

        // Run the test
        invoiceServiceImplUnderTest.downLoadInvoiceDetail(param, request, response);

        // Verify the results
    }

    @Test public void testParse() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOList = Arrays.asList(invoiceDO);
        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOList = Arrays.asList(invoiceDetailDO);
        final Multimap<String, Integer> invoiceNoMultimap = ImmutableMultimap.of("value", 0);
        final JSONObject expectedResult = new JSONObject(0, false);

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        // Run the test
        final JSONObject result =
              invoiceServiceImplUnderTest.parse(Arrays.asList(Arrays.asList("value")), invoiceDOList, invoiceDetailDOList, 0,
                    invoiceNoMultimap);
    }

    @Test public void testParse_AgentEmployeeServiceReturnsNoItem() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOList = Arrays.asList(invoiceDO);
        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOList = Arrays.asList(invoiceDetailDO);
        final Multimap<String, Integer> invoiceNoMultimap = ImmutableMultimap.of("value", 0);
        final JSONObject expectedResult = new JSONObject(0, false);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(BizResult.create());

        // Run the test
        final JSONObject result =
              invoiceServiceImplUnderTest.parse(Arrays.asList(Arrays.asList("value")), invoiceDOList, invoiceDetailDOList, 0,
                    invoiceNoMultimap);
    }

    @Test public void testParseErrorMsg() {
        // Setup
        final Workbook workbook = null;
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOList = Arrays.asList(invoiceDO);
        final JSONObject res = new JSONObject(0, false);
        final Multimap<String, Integer> invoiceNoMultimap = ImmutableMultimap.of("value", 0);
        final JSONObject expectedResult = new JSONObject(0, false);

        // Configure InvoiceDAO.selectInvoiceByInvoiceNoList(...).
        final InvoiceDO invoiceDO1 = new InvoiceDO();
        invoiceDO1.setBuyer("buyer");
        invoiceDO1.setSubjectName("subjectName");
        invoiceDO1.setInvoiceNo("invoiceNo");
        invoiceDO1.setInvoiceCode("invoiceCode");
        invoiceDO1.setId(0);
        invoiceDO1.setSupplierNo("supplierNo");
        invoiceDO1.setSubjectType(0);
        invoiceDO1.setBillNo("billNo");
        invoiceDO1.setType(0);
        invoiceDO1.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO1);
        when(mockInvoiceDAO.selectInvoiceByInvoiceNoList(Arrays.asList("value"))).thenReturn(invoiceDOS);

        // Run the test
        final JSONObject result =
              invoiceServiceImplUnderTest.parseErrorMsg("filePathName", workbook, invoiceDOList, res, invoiceNoMultimap);
    }

    @Test public void testParseErrorMsg_InvoiceDAOReturnsNoItems() {
        // Setup
        final Workbook workbook = null;
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOList = Arrays.asList(invoiceDO);
        final JSONObject res = new JSONObject(0, false);
        final Multimap<String, Integer> invoiceNoMultimap = ImmutableMultimap.of("value", 0);
        final JSONObject expectedResult = new JSONObject(0, false);
        when(mockInvoiceDAO.selectInvoiceByInvoiceNoList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final JSONObject result =
              invoiceServiceImplUnderTest.parseErrorMsg("filePathName", workbook, invoiceDOList, res, invoiceNoMultimap);
    }

    @Test public void testUploadFile() {
        // Setup

        // Run the test
        final String result = invoiceServiceImplUnderTest.uploadFile("filePathName", "fileType");
    }

    @Test public void testDownLoadFile() {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        invoiceServiceImplUnderTest.downLoadFile(request, response, "fileType", "filePathName", "downFileName");

        // Verify the results
    }

    @Test public void testDeleteInvoiceFile() {
        // Setup

        // Run the test
        invoiceServiceImplUnderTest.deleteInvoiceFile("filePathName");

        // Verify the results
    }

    @Test public void testWriteInvoiceFile() {
        // Setup
        final Workbook workbook = null;

        // Run the test
        invoiceServiceImplUnderTest.writeInvoiceFile("filePathName", workbook);

        // Verify the results
    }

    @Test public void testAutoAddBatch() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        when(mockRecordDAO.updateIsRelatedByIds(Arrays.asList(0), 0)).thenReturn(0);
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure InvoiceIdentifyRecordDAO.getListByIds(...).
        final InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        invoiceIdentifyRecordDO.setId(0);
        invoiceIdentifyRecordDO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDO.setInvoiceType(0);
        invoiceIdentifyRecordDO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDO.setCheckCode("checkCode");
        invoiceIdentifyRecordDO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDO.setTotal("total");
        invoiceIdentifyRecordDO.setTax("tax");
        invoiceIdentifyRecordDO.setSeller("seller");
        final List<InvoiceIdentifyRecordDO> invoiceIdentifyRecordDOS = Arrays.asList(invoiceIdentifyRecordDO);
        when(mockRecordDAO.getListByIds(Arrays.asList(0))).thenReturn(invoiceIdentifyRecordDOS);

        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Run the test
        final String result = invoiceServiceImplUnderTest.autoAddBatch(params);
    }

    @Test public void testAutoAddBatch_InvoiceIdentifyRecordDAOGetListByIdsReturnsNoItems() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        when(mockRecordDAO.updateIsRelatedByIds(Arrays.asList(0), 0)).thenReturn(0);
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockRecordDAO.getListByIds(Arrays.asList(0))).thenReturn(Collections.emptyList());
        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Run the test
        final String result = invoiceServiceImplUnderTest.autoAddBatch(params);
    }

    @Test public void testAutoInsertInvoice() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        final InvoiceIdentifyRecordDO identifyRecordDO = new InvoiceIdentifyRecordDO();
        identifyRecordDO.setId(0);
        identifyRecordDO.setInvoiceNo("invoiceNo");
        identifyRecordDO.setInvoiceCode("invoiceCode");
        identifyRecordDO.setInvoiceType(0);
        identifyRecordDO.setInvoiceDate("invoiceDate");
        identifyRecordDO.setCheckCode("checkCode");
        identifyRecordDO.setPretaxAmount("pretaxAmount");
        identifyRecordDO.setTotal("total");
        identifyRecordDO.setTax("tax");
        identifyRecordDO.setSeller("seller");

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        when(mockInvoiceDAO.insertSelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final ServiceResult<String> result = invoiceServiceImplUnderTest.autoInsertInvoice(params, identifyRecordDO);
    }

    @Test public void testAutoInsertInvoice_AgentEmployeeServiceReturnsNoItem() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        final InvoiceIdentifyRecordDO identifyRecordDO = new InvoiceIdentifyRecordDO();
        identifyRecordDO.setId(0);
        identifyRecordDO.setInvoiceNo("invoiceNo");
        identifyRecordDO.setInvoiceCode("invoiceCode");
        identifyRecordDO.setInvoiceType(0);
        identifyRecordDO.setInvoiceDate("invoiceDate");
        identifyRecordDO.setCheckCode("checkCode");
        identifyRecordDO.setPretaxAmount("pretaxAmount");
        identifyRecordDO.setTotal("total");
        identifyRecordDO.setTax("tax");
        identifyRecordDO.setSeller("seller");

        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(BizResult.create());
        when(mockInvoiceDAO.insertSelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final ServiceResult<String> result = invoiceServiceImplUnderTest.autoInsertInvoice(params, identifyRecordDO);
    }

    @Test public void testAutoInsertInvoiceTransactional() {
        // Setup
        final InvoiceIdentifyRecordDO identifyRecordDO = new InvoiceIdentifyRecordDO();
        identifyRecordDO.setId(0);
        identifyRecordDO.setInvoiceNo("invoiceNo");
        identifyRecordDO.setInvoiceCode("invoiceCode");
        identifyRecordDO.setInvoiceType(0);
        identifyRecordDO.setInvoiceDate("invoiceDate");
        identifyRecordDO.setCheckCode("checkCode");
        identifyRecordDO.setPretaxAmount("pretaxAmount");
        identifyRecordDO.setTotal("total");
        identifyRecordDO.setTax("tax");
        identifyRecordDO.setSeller("seller");

        final InvoiceDO invoice = new InvoiceDO();
        invoice.setBuyer("buyer");
        invoice.setSubjectName("subjectName");
        invoice.setInvoiceNo("invoiceNo");
        invoice.setInvoiceCode("invoiceCode");
        invoice.setId(0);
        invoice.setSupplierNo("supplierNo");
        invoice.setSubjectType(0);
        invoice.setBillNo("billNo");
        invoice.setType(0);
        invoice.setRawPrice(new BigDecimal("0.00"));

        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOList = Arrays.asList(invoiceDetailDO);
        final InvoiceOperateLogBO logBO = new InvoiceOperateLogBO();
        logBO.setId(0L);
        logBO.setInvoiceId(0);
        logBO.setType(InvoiceOperateLogTypeEnum.INVOICE_MANUAL_ENTRY);
        logBO.setContent(new HashMap<>());
        logBO.setOperatorId(0L);
        logBO.setOperatorName("operatorName");
        logBO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        logBO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        logBO.setDeleted(false);

        when(mockInvoiceDAO.insertSelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        invoiceServiceImplUnderTest.autoInsertInvoiceTransactional(identifyRecordDO, invoice, invoiceDetailDOList, logBO);

        // Verify the results
    }

    @Test public void testInit() {
        // Setup
        when(mockContext.getBean(InvoiceService.class)).thenReturn(null);

        // Run the test

        // Verify the results
    }

    @Test public void testInit_ApplicationContextThrowsBeansException() {
        // Setup
        when(mockContext.getBean(InvoiceService.class)).thenThrow(BeansException.class);

        // Run the test

        // Verify the results
    }

    @Test public void testAutoAddBatchSingle() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        // Configure InvoiceIdentifyRecordDAO.selectByPrimaryKey(...).
        final InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        invoiceIdentifyRecordDO.setId(0);
        invoiceIdentifyRecordDO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDO.setInvoiceType(0);
        invoiceIdentifyRecordDO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDO.setCheckCode("checkCode");
        invoiceIdentifyRecordDO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDO.setTotal("total");
        invoiceIdentifyRecordDO.setTax("tax");
        invoiceIdentifyRecordDO.setSeller("seller");
        when(mockRecordDAO.selectByPrimaryKey(0)).thenReturn(invoiceIdentifyRecordDO);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Run the test
        final ServiceResult<String> result = invoiceServiceImplUnderTest.autoAddBatchSingle(params);

        // Verify the results
    }

    @Test public void testGetSubjectRelation() {
        // Setup
        final InvoiceSubjectRelationParam param = new InvoiceSubjectRelationParam();
        param.setBizId(0);
        param.setBizType(0);
        param.setInvoiceSubjectName("invoiceSubjectName");
        param.setApplySubjectName("applySubjectName");

        // Configure InvoiceSubjectRelationDAO.selectByBizIdAndBizTypeAndSubjectName(...).
        final InvoiceSubjectRelationDO invoiceSubjectRelationDO = new InvoiceSubjectRelationDO();
        invoiceSubjectRelationDO.setSubjectName("subjectName");
        invoiceSubjectRelationDO.setRelationSubjectName("relationSubjectName");
        invoiceSubjectRelationDO.setId(0);
        invoiceSubjectRelationDO.setBizId(0);
        invoiceSubjectRelationDO.setBizType(0);
        invoiceSubjectRelationDO.setRemark("remark");
        invoiceSubjectRelationDO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceSubjectRelationDO.setCreator(0);
        invoiceSubjectRelationDO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        invoiceSubjectRelationDO.setUpdater(0);
        when(mockInvoiceSubjectRelationDAO.selectByBizIdAndBizTypeAndSubjectName(0, 0, "subjectName")).thenReturn(
              invoiceSubjectRelationDO);

        // Run the test
        final ServiceResult<Boolean> result = invoiceServiceImplUnderTest.getSubjectRelation(param);

        // Verify the results
    }

    @Test public void testGetInvoiceBelongInfo() {
        // Setup

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        // Run the test
        final AgentDTO result = invoiceServiceImplUnderTest.getInvoiceBelongInfo(0);

        // Verify the results
    }

    @Test public void testGetInvoiceBelongInfo_AgentEmployeeServiceReturnsNoItem() {
        // Setup
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(BizResult.create());

        // Run the test
        final AgentDTO result = invoiceServiceImplUnderTest.getInvoiceBelongInfo(0);

        // Verify the results
    }

    @Test public void testQueryWithdrawSubjectNameList() {
        // Setup
        when(mockLvyClient.queryWithdrawSubjectNameList(new WithdrawListQuery())).thenReturn(
              so.dian.commons.eden.entity.BizResult.create(
                    Arrays.asList("value")));

        // Run the test
        final List<String> result = invoiceServiceImplUnderTest.queryWithdrawSubjectNameList("keyword");
    }

    @Test public void testQueryWithdrawSubjectNameList_LvyClientReturnsNoItems() {
        // Setup
        when(mockLvyClient.queryWithdrawSubjectNameList(new WithdrawListQuery())).thenReturn(
              so.dian.commons.eden.entity.BizResult.create(
                    Collections.emptyList()));

        // Run the test
        final List<String> result = invoiceServiceImplUnderTest.queryWithdrawSubjectNameList("keyword");
    }

    @Test public void testQueryWithdrawSubjectNameList_LvyClientReturnsError() {
        // Setup
        when(mockLvyClient.queryWithdrawSubjectNameList(new WithdrawListQuery())).thenReturn(
              so.dian.commons.eden.entity.BizResult.error(ErrorCodeEnum.PARAMS_ERROR));

        // Run the test
        //final List<String> result = invoiceServiceImplUnderTest.queryWithdrawSubjectNameList("keyword");

    }

    @Test public void testCheckInvoiceBelong() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        // Run the test
        invoiceServiceImplUnderTest.checkInvoiceBelong(invoiceDO, 0);
    }

    @Test public void testCheckInvoiceBelong_AgentEmployeeServiceReturnsNoItem() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));

        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(BizResult.create());

        // Run the test
        invoiceServiceImplUnderTest.checkInvoiceBelong(invoiceDO, 0);
    }

    @Test public void testGetInvoiceInfoList() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setInvoiceId(0);
        invoiceInfoVO.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO.setBusinessTypeCode(0);
        invoiceInfoVO.setBusinessTypeName("businessTypeName");
        invoiceInfoVO.setInvoiceCode("invoiceCode");
        invoiceInfoVO.setInvoiceNo("invoiceNo");
        invoiceInfoVO.setInvoiceDate("invoiceDate");
        invoiceInfoVO.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setInvoiceStatus(0);
        final List<InvoiceInfoVO> expectedResult = Arrays.asList(invoiceInfoVO);

        // Configure InvoiceDAO.selectInvoiceList(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.selectInvoiceList(new InvoiceParam())).thenReturn(invoiceDOS);

        // Run the test
        final List<InvoiceInfoVO> result = invoiceServiceImplUnderTest.getInvoiceInfoList(param);
    }

    @Test public void testGetInvoiceInfoList_InvoiceDAOReturnsNoItems() {
        // Setup
        final InvoiceParam param = new InvoiceParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSupplierNo("supplierNo");
        param.setSubjectType(0);
        param.setSubjectName("subjectName");
        param.setSupplierName("supplierName");
        param.setCreator(0);
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");

        final InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setInvoiceId(0);
        invoiceInfoVO.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO.setBusinessTypeCode(0);
        invoiceInfoVO.setBusinessTypeName("businessTypeName");
        invoiceInfoVO.setInvoiceCode("invoiceCode");
        invoiceInfoVO.setInvoiceNo("invoiceNo");
        invoiceInfoVO.setInvoiceDate("invoiceDate");
        invoiceInfoVO.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setInvoiceStatus(0);
        final List<InvoiceInfoVO> expectedResult = Arrays.asList(invoiceInfoVO);
        when(mockInvoiceDAO.selectInvoiceList(new InvoiceParam())).thenReturn(Collections.emptyList());

        // Run the test
        final List<InvoiceInfoVO> result = invoiceServiceImplUnderTest.getInvoiceInfoList(param);
    }

    @Test public void testInvoiceReview() {
        // Setup
        final InvoiceReviewParam param = new InvoiceReviewParam();
        param.setId(0);
        param.setSuggestion("suggestion");
        param.setStatus(2);
        param.setUserId(0);

        final so.dian.commons.eden.entity.BizResult<Boolean> expectedResult = so.dian.commons.eden.entity.BizResult.create(false);

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        // Configure InvoiceManager.getInvoiceById(...).
        final InvoiceBO invoiceBO = new InvoiceBO();
        invoiceBO.setId(0);
        invoiceBO.setInvoiceNo("invoiceNo");
        invoiceBO.setSupplierNo("supplierNo");
        invoiceBO.setInvoiceCode("invoiceCode");
        invoiceBO.setSubjectType(0);
        invoiceBO.setSubjectName("subjectName");
        invoiceBO.setBillNo("billNo");
        invoiceBO.setInvoiceType(0);
        invoiceBO.setRawPrice(new BigDecimal("0.00"));
        invoiceBO.setTaxPrice(new BigDecimal("0.00"));
        invoiceBO.setInvoiceProcessStatusEnum(InvoiceProcessStatusEnum.REVIEW_PASS);
        when(mockInvoiceManager.getInvoiceById(0)).thenReturn(invoiceBO);

        when(mockInvoiceManager.invoiceReview(0, 0, "reviewRemark", InvoiceProcessStatusEnum.ALREADY_ENTRY,
              InvoiceProcessStatusEnum.ALREADY_ENTRY)).thenReturn(false);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final so.dian.commons.eden.entity.BizResult<Boolean> result = invoiceServiceImplUnderTest.invoiceReview(param);
    }

    @Test public void testExportInvoiceList() {
        // Setup
        final InvoiceExportParam param = new InvoiceExportParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSubjectType(0);
        param.setNickName("nickName");
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");
        param.setStartGmtCreate("startGmtCreate");
        param.setEndGmtCreate("endGmtCreate");
        param.setStatus(0);

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        when(mockInvoiceExpressesRelationDAO.listInvoiceIdsByExpressNo("expressNo")).thenReturn(Arrays.asList(0L));
        when(mockInvoiceManager.countInvoiceExportList(new InvoiceExportParam())).thenReturn(0L);

        // Run the test
        final so.dian.commons.eden.entity.BizResult result = invoiceServiceImplUnderTest.exportInvoiceList(param);
    }

    @Test public void testExportInvoiceList_InvoiceExpressesRelationDAOReturnsNoItems() {
        // Setup
        final InvoiceExportParam param = new InvoiceExportParam();
        param.setId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSubjectType(0);
        param.setNickName("nickName");
        param.setStartCreateTime("startCreateTime");
        param.setEndCreateTime("endCreateTime");
        param.setStartGmtCreate("startGmtCreate");
        param.setEndGmtCreate("endGmtCreate");
        param.setStatus(0);

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        when(mockInvoiceExpressesRelationDAO.listInvoiceIdsByExpressNo("expressNo")).thenReturn(Collections.emptyList());
        when(mockInvoiceManager.countInvoiceExportList(new InvoiceExportParam())).thenReturn(0L);

        // Run the test
        final so.dian.commons.eden.entity.BizResult result = invoiceServiceImplUnderTest.exportInvoiceList(param);
    }

    @Test public void testListSubjectName() {
        // Setup
        final InvoiceSubjectInfoParam param = new InvoiceSubjectInfoParam();
        param.setSubjectName("subjectName");
        param.setLoginUserId(0);

        final so.dian.commons.eden.entity.BizResult<List<String>> expectedResult = so.dian.commons.eden.entity.BizResult.create(
              Arrays.asList("value"));

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        when(mockInvoiceManager.listSubjectNameListByParam("subjectName", 0)).thenReturn(Arrays.asList("value"));

        // Run the test
        final so.dian.commons.eden.entity.BizResult<List<String>> result = invoiceServiceImplUnderTest.listSubjectName(param);
    }

    @Test public void testListSubjectName_InvoiceManagerReturnsNoItems() {
        // Setup
        final InvoiceSubjectInfoParam param = new InvoiceSubjectInfoParam();
        param.setSubjectName("subjectName");
        param.setLoginUserId(0);

        final so.dian.commons.eden.entity.BizResult<List<String>> expectedResult = so.dian.commons.eden.entity.BizResult.create(
              Arrays.asList("value"));

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        when(mockInvoiceManager.listSubjectNameListByParam("subjectName", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final so.dian.commons.eden.entity.BizResult<List<String>> result = invoiceServiceImplUnderTest.listSubjectName(param);
    }

    @Test public void testPageInvoiceList() {
        // Setup
        final InvoiceFilterTimeParam param = new InvoiceFilterTimeParam();
        param.setFilterTime(0L);

        final so.dian.commons.eden.entity.BizResult<PageData<InvoiceVO>> expectedResult =
              so.dian.commons.eden.entity.BizResult.create(
                    PageData.create(Arrays.asList()));
        when(mockInvoiceDAO.countInvoice(new InvoiceFilterTimeQuery())).thenReturn(0L);

        // Configure InvoiceManager.listInvoice(...).
        final InvoiceBO invoiceBO = new InvoiceBO();
        invoiceBO.setId(0);
        invoiceBO.setInvoiceNo("invoiceNo");
        invoiceBO.setSupplierNo("supplierNo");
        invoiceBO.setInvoiceCode("invoiceCode");
        invoiceBO.setSubjectType(0);
        invoiceBO.setSubjectName("subjectName");
        invoiceBO.setBillNo("billNo");
        invoiceBO.setInvoiceType(0);
        invoiceBO.setRawPrice(new BigDecimal("0.00"));
        invoiceBO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceBO> invoiceBOS = Arrays.asList(invoiceBO);
        when(mockInvoiceManager.listInvoice(new InvoiceFilterTimeQuery())).thenReturn(invoiceBOS);

        // Run the test

        CurrentUserReq currentUserReq = new CurrentUserReq();
        currentUserReq.setUserId(0);
        currentUserReq.setCurrentRole("");
        currentUserReq.setNickName("");
        currentUserReq.setUserName("");
        currentUserReq.setRoleId(0);
        currentUserReq.setEmail("");
        currentUserReq.setMobile("");
        currentUserReq.setBelongSubjectType(0);
        currentUserReq.setBelongSubjectId(0);


        final so.dian.commons.eden.entity.BizResult<PageData<InvoiceVO>> result =
              invoiceServiceImplUnderTest.pageInvoiceList(param, currentUserReq);
    }

    @Test public void testPageInvoiceList_InvoiceManagerReturnsNoItems() {
        // Setup
        final InvoiceFilterTimeParam param = new InvoiceFilterTimeParam();
        param.setFilterTime(0L);

        final so.dian.commons.eden.entity.BizResult<PageData<InvoiceVO>> expectedResult =
              so.dian.commons.eden.entity.BizResult.create(
                    PageData.create(Arrays.asList()));
        when(mockInvoiceDAO.countInvoice(new InvoiceFilterTimeQuery())).thenReturn(0L);
        when(mockInvoiceManager.listInvoice(new InvoiceFilterTimeQuery())).thenReturn(Collections.emptyList());

        // Run the test

        CurrentUserReq currentUserReq = new CurrentUserReq();
        currentUserReq.setUserId(0);
        currentUserReq.setCurrentRole("");
        currentUserReq.setNickName("");
        currentUserReq.setUserName("");
        currentUserReq.setRoleId(0);
        currentUserReq.setEmail("");
        currentUserReq.setMobile("");
        currentUserReq.setBelongSubjectType(0);
        currentUserReq.setBelongSubjectId(0);

        final so.dian.commons.eden.entity.BizResult<PageData<InvoiceVO>> result =
              invoiceServiceImplUnderTest.pageInvoiceList(param, currentUserReq);
    }

    @Test public void testAddSingleInvoice() {
        // Setup
        final AddSingleInvoiceParam param = new AddSingleInvoiceParam();
        param.setBuyer("buyer");
        param.setDetails("details");
        param.setType(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        param.setMemo("memo");
        param.setPrice("price");
        param.setRawPrice("rawPrice");
        param.setSeller("seller");

        final InvoiceIdentifyRecordDO recordDO = new InvoiceIdentifyRecordDO();
        recordDO.setId(0);
        recordDO.setInvoiceNo("invoiceNo");
        recordDO.setInvoiceCode("invoiceCode");
        recordDO.setInvoiceType(0);
        recordDO.setInvoiceDate("invoiceDate");
        recordDO.setCheckCode("checkCode");
        recordDO.setPretaxAmount("pretaxAmount");
        recordDO.setTotal("total");
        recordDO.setTax("tax");
        recordDO.setSeller("seller");

        final so.dian.commons.eden.entity.BizResult<Boolean> expectedResult = so.dian.commons.eden.entity.BizResult.create(false);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Configure AgentEmployeeService.getAgentByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        final BizResult<AgentDTO> agentDTOBizResult = BizResult.create(agentDTO);
        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(agentDTOBizResult);

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        when(mockInvoiceDAO.insertSelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final so.dian.commons.eden.entity.BizResult<Boolean> result =
              invoiceServiceImplUnderTest.addSingleInvoice(param, recordDO, false);
    }

    @Test public void testAddSingleInvoice_AgentEmployeeServiceGetAgentByUserIdReturnsNoItem() {
        // Setup
        final AddSingleInvoiceParam param = new AddSingleInvoiceParam();
        param.setBuyer("buyer");
        param.setDetails("details");
        param.setType(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        param.setMemo("memo");
        param.setPrice("price");
        param.setRawPrice("rawPrice");
        param.setSeller("seller");

        final InvoiceIdentifyRecordDO recordDO = new InvoiceIdentifyRecordDO();
        recordDO.setId(0);
        recordDO.setInvoiceNo("invoiceNo");
        recordDO.setInvoiceCode("invoiceCode");
        recordDO.setInvoiceType(0);
        recordDO.setInvoiceDate("invoiceDate");
        recordDO.setCheckCode("checkCode");
        recordDO.setPretaxAmount("pretaxAmount");
        recordDO.setTotal("total");
        recordDO.setTax("tax");
        recordDO.setSeller("seller");

        final so.dian.commons.eden.entity.BizResult<Boolean> expectedResult = so.dian.commons.eden.entity.BizResult.create(false);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        when(mockAgentEmployeeService.getAgentByUserId(0L)).thenReturn(BizResult.create());

        // Configure AgentEmployeeService.getEmployeeById(...).
        final AgentEmployeeDTO agentEmployeeDTO = new AgentEmployeeDTO();
        agentEmployeeDTO.setId(0);
        agentEmployeeDTO.setMobile("mobile");
        agentEmployeeDTO.setAgentId(0);
        agentEmployeeDTO.setDepartmentId(0);
        agentEmployeeDTO.setName("name");
        agentEmployeeDTO.setSex(0);
        agentEmployeeDTO.setIdCardNo("idCardNo");
        agentEmployeeDTO.setNickName("nickName");
        agentEmployeeDTO.setEmail("email");
        agentEmployeeDTO.setEmployeeNo("employeeNo");
        when(mockAgentEmployeeService.getEmployeeById(0)).thenReturn(agentEmployeeDTO);

        when(mockInvoiceDAO.insertSelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);
        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);
        when(mockInvoiceOperateLogManager.insert(new InvoiceOperateLogBO())).thenReturn(false);

        // Run the test
        final so.dian.commons.eden.entity.BizResult<Boolean> result =
              invoiceServiceImplUnderTest.addSingleInvoice(param, recordDO, false);
    }

    @Test public void testBatchUpdateInvoiceProcessStatus() {
        // Setup
        when(mockInvoiceDAO.batchUpdateInvoiceProcessStatus(Arrays.asList(0L), 0)).thenReturn(0);

        // Run the test
        final int result = invoiceServiceImplUnderTest.batchUpdateInvoiceProcessStatus(InvoiceProcessStatusEnum.ALREADY_ENTRY,
              Arrays.asList(0L));
    }

    @Test public void testFindByInvoiceDeductQueryParam() {
        // Setup
        final InvoiceDeductQueryParam param = new InvoiceDeductQueryParam();
        param.setInvoiceId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSubjectName("subjectName");
        param.setBusinessType(0);
        param.setRelationSubjectName("relationSubjectName");
        param.setNeedRelate(false);
        param.setRelationBizType(0);
        param.setSubjectNameList(Arrays.asList("value"));
        param.setBuyerName("buyerName");

        final InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setInvoiceId(0);
        invoiceInfoVO.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO.setBusinessTypeCode(0);
        invoiceInfoVO.setBusinessTypeName("businessTypeName");
        invoiceInfoVO.setInvoiceCode("invoiceCode");
        invoiceInfoVO.setInvoiceNo("invoiceNo");
        invoiceInfoVO.setInvoiceDate("invoiceDate");
        invoiceInfoVO.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setInvoiceStatus(0);
        final List<InvoiceInfoVO> expectedResult = Arrays.asList(invoiceInfoVO);

        // Configure InvoiceDAO.findByInvoiceDeductQueryParam(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.findByInvoiceDeductQueryParam(new InvoiceDeductQueryParam())).thenReturn(invoiceDOS);

        // Run the test
        final List<InvoiceInfoVO> result = invoiceServiceImplUnderTest.findByInvoiceDeductQueryParam(param, 1L);
    }

    @Test public void testFindByInvoiceDeductQueryParam_InvoiceDAOReturnsNoItems() {
        // Setup
        final InvoiceDeductQueryParam param = new InvoiceDeductQueryParam();
        param.setInvoiceId(0);
        param.setInvoiceCode("invoiceCode");
        param.setInvoiceNo("invoiceNo");
        param.setSubjectName("subjectName");
        param.setBusinessType(0);
        param.setRelationSubjectName("relationSubjectName");
        param.setNeedRelate(false);
        param.setRelationBizType(0);
        param.setSubjectNameList(Arrays.asList("value"));
        param.setBuyerName("buyerName");

        final InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setInvoiceId(0);
        invoiceInfoVO.setInvoiceSellerName("invoiceSellerName");
        invoiceInfoVO.setBusinessTypeCode(0);
        invoiceInfoVO.setBusinessTypeName("businessTypeName");
        invoiceInfoVO.setInvoiceCode("invoiceCode");
        invoiceInfoVO.setInvoiceNo("invoiceNo");
        invoiceInfoVO.setInvoiceDate("invoiceDate");
        invoiceInfoVO.setTotalAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setUnUseAmount(new BigDecimal("0.00"));
        invoiceInfoVO.setInvoiceStatus(0);
        final List<InvoiceInfoVO> expectedResult = Arrays.asList(invoiceInfoVO);
        when(mockInvoiceDAO.findByInvoiceDeductQueryParam(new InvoiceDeductQueryParam())).thenReturn(Collections.emptyList());

        // Run the test
        final List<InvoiceInfoVO> result = invoiceServiceImplUnderTest.findByInvoiceDeductQueryParam(param, 1L);
    }

    @Test public void testTrimInvoice() {
        // Setup
        final so.dian.commons.eden.entity.BizResult<Integer> expectedResult = so.dian.commons.eden.entity.BizResult.create(0);

        // Configure InvoiceDAO.selectByPrimaryKey(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectByPrimaryKey(0)).thenReturn(invoiceDO);

        when(mockInvoiceDAO.trimInvoice(new InvoiceDO())).thenReturn(0);

        // Configure InvoiceDAO.findBySubjectType(...).
        final InvoiceDO invoiceDO1 = new InvoiceDO();
        invoiceDO1.setBuyer("buyer");
        invoiceDO1.setSubjectName("subjectName");
        invoiceDO1.setInvoiceNo("invoiceNo");
        invoiceDO1.setInvoiceCode("invoiceCode");
        invoiceDO1.setId(0);
        invoiceDO1.setSupplierNo("supplierNo");
        invoiceDO1.setSubjectType(0);
        invoiceDO1.setBillNo("billNo");
        invoiceDO1.setType(0);
        invoiceDO1.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO1);
        when(mockInvoiceDAO.findBySubjectType(0, 0L)).thenReturn(invoiceDOS);

        // Run the test
        final so.dian.commons.eden.entity.BizResult<Integer> result = invoiceServiceImplUnderTest.trimInvoice(0L, 0, 0);
    }

    @Test public void testTrimInvoice_InvoiceDAOFindBySubjectTypeReturnsNoItems() {
        // Setup
        final so.dian.commons.eden.entity.BizResult<Integer> expectedResult = so.dian.commons.eden.entity.BizResult.create(0);

        // Configure InvoiceDAO.selectByPrimaryKey(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectByPrimaryKey(0)).thenReturn(invoiceDO);

        when(mockInvoiceDAO.trimInvoice(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDAO.findBySubjectType(0, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final so.dian.commons.eden.entity.BizResult<Integer> result = invoiceServiceImplUnderTest.trimInvoice(0L, 0, 0);
    }

    @Test public void testInvoiceValidate() {
        // Setup
        final InvoiceBO validateInvoiceBO = new InvoiceBO();
        validateInvoiceBO.setId(0);
        validateInvoiceBO.setInvoiceNo("invoiceNo");
        validateInvoiceBO.setSupplierNo("supplierNo");
        validateInvoiceBO.setInvoiceCode("invoiceCode");
        validateInvoiceBO.setSubjectType(0);
        validateInvoiceBO.setSubjectName("subjectName");
        validateInvoiceBO.setBillNo("billNo");
        validateInvoiceBO.setInvoiceType(0);
        validateInvoiceBO.setRawPrice(new BigDecimal("0.00"));
        validateInvoiceBO.setTaxPrice(new BigDecimal("0.00"));

        final InvoiceValidateStatusDO statusDOForInsert = new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult");
        when(mockInvoiceManager.updateValidationById(0, 0, "validateCode")).thenReturn(false);
        when(mockInvoiceValidateStatusManager.insert(new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"))).thenReturn(
              false);

        // Run the test
    }

    @Test public void testProcessValidateData() {
        // Setup

        // Configure InvoiceDAO.selectValidateInvoice(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.selectValidateInvoice(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              InvoiceIdentifyRecordEnum.IsRealEnum.STAY)).thenReturn(invoiceDOS);

        when(mockInvoiceDAO.updateValidationById(0, 0, "validateCode")).thenReturn(0);

        // Configure InvoiceValidateStatusManager.getAllValidateInvoice(...).
        final List<InvoiceValidateStatusDO> invoiceValidateStatusDOS = Arrays.asList(
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
        when(mockInvoiceValidateStatusManager.getAllValidateInvoice()).thenReturn(invoiceValidateStatusDOS);

        // Run the test
        invoiceServiceImplUnderTest.processValidateData(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test public void testProcessValidateData_InvoiceDAOSelectValidateInvoiceReturnsNoItems() {
        // Setup
        when(mockInvoiceDAO.selectValidateInvoice(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              InvoiceIdentifyRecordEnum.IsRealEnum.STAY)).thenReturn(Collections.emptyList());
        when(mockInvoiceDAO.updateValidationById(0, 0, "validateCode")).thenReturn(0);

        // Configure InvoiceValidateStatusManager.getAllValidateInvoice(...).
        final List<InvoiceValidateStatusDO> invoiceValidateStatusDOS = Arrays.asList(
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
        when(mockInvoiceValidateStatusManager.getAllValidateInvoice()).thenReturn(invoiceValidateStatusDOS);

        // Run the test
        invoiceServiceImplUnderTest.processValidateData(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test public void testProcessValidateData_InvoiceValidateStatusManagerReturnsNoItems() {
        // Setup

        // Configure InvoiceDAO.selectValidateInvoice(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        final List<InvoiceDO> invoiceDOS = Arrays.asList(invoiceDO);
        when(mockInvoiceDAO.selectValidateInvoice(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              InvoiceIdentifyRecordEnum.IsRealEnum.STAY)).thenReturn(invoiceDOS);

        when(mockInvoiceDAO.updateValidationById(0, 0, "validateCode")).thenReturn(0);
        when(mockInvoiceValidateStatusManager.getAllValidateInvoice()).thenReturn(Collections.emptyList());

        // Run the test
        invoiceServiceImplUnderTest.processValidateData(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test public void testListInvoiceIdsByExpressNo() {
        // Setup
        when(mockInvoiceExpressesRelationDAO.listInvoiceIdsByExpressNo("expressNo")).thenReturn(Arrays.asList(0L));

        // Run the test
        final List<Long> result = invoiceServiceImplUnderTest.listInvoiceIdsByExpressNo("expressNo");
    }

    @Test public void testListInvoiceIdsByExpressNo_InvoiceExpressesRelationDAOReturnsNoItems() {
        // Setup
        when(mockInvoiceExpressesRelationDAO.listInvoiceIdsByExpressNo("expressNo")).thenReturn(Collections.emptyList());

        // Run the test
        final List<Long> result = invoiceServiceImplUnderTest.listInvoiceIdsByExpressNo("expressNo");
    }

    @Test public void testFillBusinessNo() {
        // Setup
        final InvoiceDto dto = new InvoiceDto();
        dto.setId(0);
        dto.setInvoiceCode("invoiceCode");
        dto.setInvoiceNo("invoiceNo");
        dto.setSupplierNo("supplierNo");
        dto.setSupplierName("supplierName");
        dto.setSubjectType(0);
        dto.setSubjectName("subjectName");
        dto.setSubjectTypeStr("subjectTypeStr");
        dto.setType(0);
        dto.setTypeStr("typeStr");

        when(mockInvoiceDeductionDAO.selectByInvoiceCodeAndNo("invoiceCode", "invoiceNo", Arrays.asList("value"), 0)).thenReturn(
              Arrays.asList("value"));

        // Run the test
        invoiceServiceImplUnderTest.fillBusinessNo(dto);
    }

    @Test public void testFillBusinessNo_InvoiceDeductionDAOReturnsNoItems() {
        // Setup
        final InvoiceDto dto = new InvoiceDto();
        dto.setId(0);
        dto.setInvoiceCode("invoiceCode");
        dto.setInvoiceNo("invoiceNo");
        dto.setSupplierNo("supplierNo");
        dto.setSupplierName("supplierName");
        dto.setSubjectType(0);
        dto.setSubjectName("subjectName");
        dto.setSubjectTypeStr("subjectTypeStr");
        dto.setType(0);
        dto.setTypeStr("typeStr");

        when(mockInvoiceDeductionDAO.selectByInvoiceCodeAndNo("invoiceCode", "invoiceNo", Arrays.asList("value"), 0)).thenReturn(
              Collections.emptyList());

        // Run the test
        invoiceServiceImplUnderTest.fillBusinessNo(dto);
    }

    @Test public void testGetBusinessNoListByInvoiceCodeAndNo() {
        // Setup
        when(mockInvoiceDeductionDAO.selectByInvoiceCodeAndNo("invoiceCode", "invoiceNo", Arrays.asList("value"), 0)).thenReturn(
              Arrays.asList("value"));

        // Run the test
        final List<String> result =
              invoiceServiceImplUnderTest.getBusinessNoListByInvoiceCodeAndNo("invoiceCode", "invoiceNo", Arrays.asList("value"),
                    0);
    }

    @Test public void testGetBusinessNoListByInvoiceCodeAndNo_InvoiceDeductionDAOReturnsNoItems() {
        // Setup
        when(mockInvoiceDeductionDAO.selectByInvoiceCodeAndNo("invoiceCode", "invoiceNo", Arrays.asList("value"), 0)).thenReturn(
              Collections.emptyList());

        // Run the test
        final List<String> result =
              invoiceServiceImplUnderTest.getBusinessNoListByInvoiceCodeAndNo("invoiceCode", "invoiceNo", Arrays.asList("value"),
                    0);
    }

    @Test public void testGetSubjectTypeList() {
        // Setup
        final SubjectTypeParam subjectTypeParam = new SubjectTypeParam();
        subjectTypeParam.setScmFilter(false);

        // Configure AgentEmployeeService.getAgentDtoByUserId(...).
        final AgentDTO agentDTO = new AgentDTO();
        agentDTO.setAgentId(0L);
        agentDTO.setAgentName("agentName");
        agentDTO.setStatus(0);
        agentDTO.setType(0);
        agentDTO.setManagerId(0);
        agentDTO.setRootDepartmentId(0);
        agentDTO.setResponseMobile("responseMobile");
        when(mockAgentEmployeeService.getAgentDtoByUserId(0)).thenReturn(agentDTO);

        // Run the test
        final List<KeyValueDto> result =
              invoiceServiceImplUnderTest.getSubjectTypeList(UserRoleEnum.EMPTY_ROLE, 0, subjectTypeParam);
    }

    @Test public void testSelectInvoiceByInvoiceCodeAndNo() {
        // Setup
        final InvoiceDO expectedResult = new InvoiceDO();
        expectedResult.setBuyer("buyer");
        expectedResult.setSubjectName("subjectName");
        expectedResult.setInvoiceNo("invoiceNo");
        expectedResult.setInvoiceCode("invoiceCode");
        expectedResult.setId(0);
        expectedResult.setSupplierNo("supplierNo");
        expectedResult.setSubjectType(0);
        expectedResult.setBillNo("billNo");
        expectedResult.setType(0);
        expectedResult.setRawPrice(new BigDecimal("0.00"));

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        // Run the test
        final InvoiceDO result = invoiceServiceImplUnderTest.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo");
    }

    @Test public void testSaveInvoiceAndDetail() {
        // Setup
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));

        final InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
        invoiceDetailDO.setId(0);
        invoiceDetailDO.setInvoiceNo("invoiceNo");
        invoiceDetailDO.setMaterialName("materialName");
        invoiceDetailDO.setMaterialSpec("materialSpec");
        invoiceDetailDO.setUnit("unit");
        invoiceDetailDO.setUnitPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setQuantity(0);
        invoiceDetailDO.setRawPrice(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxRate(new BigDecimal("0.00"));
        invoiceDetailDO.setTaxPrice(new BigDecimal("0.00"));
        final List<InvoiceDetailDO> invoiceDetailDOList = Arrays.asList(invoiceDetailDO);
        when(mockInvoiceDAO.insertSelective(new InvoiceDO())).thenReturn(0);
        when(mockInvoiceDetailDAO.insertBatch(Arrays.asList(new InvoiceDetailDO()))).thenReturn(0);

        // Run the test
        invoiceServiceImplUnderTest.saveInvoiceAndDetail(invoiceDO, invoiceDetailDOList);
    }
}
