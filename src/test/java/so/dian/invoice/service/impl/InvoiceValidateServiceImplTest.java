package so.dian.invoice.service.impl;

import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.List;
import javax.validation.ValidationException;
import javax.validation.Validator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.client.GlorityClient;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceIdentifyRecordDAO;
import so.dian.invoice.enums.InvoiceValidateTypeEnum;
import so.dian.invoice.manager.DingTalkManager;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.dto.OCRQueueDTO;
import so.dian.invoice.pojo.dto.identify.DetailAndExtraDTO;
import so.dian.invoice.pojo.dto.identify.DetailsDTO;
import so.dian.invoice.pojo.dto.identify.ExtraDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceDataDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceIdentifyRecordDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceResponseDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceResultDTO;
import so.dian.invoice.pojo.dto.identify.ValidationDTO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.param.GlorityParam;
import so.dian.invoice.pojo.param.InvoiceValidateDetailParams;
import so.dian.invoice.pojo.param.InvoiceValidateParams;
import so.dian.invoice.service.ImageService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.util.ServiceResult;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InvoiceValidateServiceImplTest {

    @Mock
    private InvoiceIdentifyRecordDAO mockRecordDAO;
    @Mock
    private ImageService mockImageService;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private InvoiceDAO mockInvoiceDAO;
    @Mock
    private InvoiceService mockInvoiceService;
    @Mock
    private InvoiceManager mockInvoiceManager;
    @Mock
    private GlorityClient mockGlorityClient;
    @Mock
    private Validator mockValidator;
    @Mock
    private RedissonClient mockRedisson;
    @Mock
    private DingTalkManager mockDingTalkManager;

    @InjectMocks private InvoiceValidateServiceImpl invoiceValidateServiceImplUnderTest;


    @Test public void testBatchUpload() throws Exception {
        // Setup
        when(mockImageService.uploadFileQCloud(eq("rootName"), eq("originalFilename"), any(byte[].class), eq("contentType"),
              eq(0L))).thenReturn("result");
        when(mockImageService.uploadImageQCloud(eq("rootName"), eq("originalFilename"), any(byte[].class), eq("contentType"),
              eq(0L))).thenReturn("result");
        when(mockRedisson.getQueue("s")).thenReturn(null);

        // Run the test
        final ServiceResult<String> result =
              invoiceValidateServiceImplUnderTest.batchUpload(0, "clientFileName", "content".getBytes(), "contentType", 0);

        // Verify the results
    }

    @Test public void testBatchUpload_ImageServiceUploadFileQCloudThrowsIOException() throws Exception {
        // Setup
        when(mockImageService.uploadFileQCloud(eq("rootName"), eq("originalFilename"), any(byte[].class), eq("contentType"),
              eq(0L))).thenThrow(
              IOException.class);
        when(mockRedisson.getQueue("s")).thenReturn(null);

        // Run the test
        final ServiceResult<String> result =
              invoiceValidateServiceImplUnderTest.batchUpload(0, "clientFileName", "content".getBytes(), "contentType", 0);

        // Verify the results
    }

    @Test public void testBatchUpload_ImageServiceUploadImageQCloudThrowsIOException() throws Exception {
        // Setup
        when(mockImageService.uploadFileQCloud(eq("rootName"), eq("originalFilename"), any(byte[].class), eq("contentType"),
              eq(0L))).thenReturn("result");
        when(mockImageService.uploadImageQCloud(eq("rootName"), eq("originalFilename"), any(byte[].class), eq("contentType"),
              eq(0L))).thenThrow(
              IOException.class);
        when(mockRedisson.getQueue("s")).thenReturn(null);

        // Run the test
        final ServiceResult<String> result =
              invoiceValidateServiceImplUnderTest.batchUpload(0, "clientFileName", "content".getBytes(), "contentType", 0);

        // Verify the results
    }

    @Test public void testBatchUpload_RedissonClientReturnsNoItems() throws Exception {
        // Setup
        when(mockImageService.uploadFileQCloud(eq("rootName"), eq("originalFilename"), any(byte[].class), eq("contentType"),
              eq(0L))).thenReturn("result");
        when(mockImageService.uploadImageQCloud(eq("rootName"), eq("originalFilename"), any(byte[].class), eq("contentType"),
              eq(0L))).thenReturn("result");
        when(mockRedisson.getQueue("s")).thenReturn(Mockito.any());

        // Run the test
        final ServiceResult<String> result =
              invoiceValidateServiceImplUnderTest.batchUpload(0, "clientFileName", "content".getBytes(), "contentType", 0);

        // Verify the results
    }

    @Test public void testHandleOCR() {
        // Setup
        final OCRQueueDTO ocrQueueDTO = new OCRQueueDTO();
        ocrQueueDTO.setImageUrl("imageUrl");
        ocrQueueDTO.setLoginUserId(0);
        ocrQueueDTO.setTimes(0);

        when(mockRecordDAO.insertSelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Run the test
        final boolean result = invoiceValidateServiceImplUnderTest.handleOCR(ocrQueueDTO);

        // Verify the results
        assertTrue(result);
        verify(mockRecordDAO).insertSelective(new InvoiceIdentifyRecordDO());
        verify(mockDingTalkManager).sendWarnMsgAsync("title", "text");
    }

    @Test public void testGetInvoiceIdentifyDetail() {
        // Setup
        final InvoiceIdentifyRecordDTO invoiceIdentifyRecordDTO = new InvoiceIdentifyRecordDTO();
        invoiceIdentifyRecordDTO.setId(0);
        invoiceIdentifyRecordDTO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDTO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDTO.setInvoiceType(0);
        invoiceIdentifyRecordDTO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDTO.setCheckCode("checkCode");
        invoiceIdentifyRecordDTO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDTO.setTotal("total");
        invoiceIdentifyRecordDTO.setTax("tax");
        invoiceIdentifyRecordDTO.setSeller("seller");
        final BizResult<InvoiceIdentifyRecordDTO> expectedResult = BizResult.create(invoiceIdentifyRecordDTO);

        // Run the test
        final BizResult<InvoiceIdentifyRecordDTO> result =
              invoiceValidateServiceImplUnderTest.getInvoiceIdentifyDetail("imageUrl");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test public void testHandleInvoiceValidate1() throws Exception {
        // Setup
        final InvoiceValidateDetailParams params = new InvoiceValidateDetailParams();
        params.setInvoiceNo("invoiceNo");
        params.setCheckCode("checkCode");
        params.setInvoiceCode("invoiceCode");
        params.setPretaxAmount("pretaxAmount");
        params.setInvoiceType(0);
        params.setInvoiceTypeStr("invoiceTypeStr");
        params.setInvoiceDate("invoiceDate");
        params.setId(0);
        params.setTotal("total");
        params.setSeller("seller");

        final ValidationDTO expectedResult = new ValidationDTO();
        expectedResult.setCode("code");
        expectedResult.setMessage("message");

        // Run the test
        final ValidationDTO result = invoiceValidateServiceImplUnderTest.handleInvoiceValidate(params);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = IOException.class) public void testHandleInvoiceValidate1_ThrowsIOException() throws Exception {
        // Setup
        final InvoiceValidateDetailParams params = new InvoiceValidateDetailParams();
        params.setInvoiceNo("invoiceNo");
        params.setCheckCode("checkCode");
        params.setInvoiceCode("invoiceCode");
        params.setPretaxAmount("pretaxAmount");
        params.setInvoiceType(0);
        params.setInvoiceTypeStr("invoiceTypeStr");
        params.setInvoiceDate("invoiceDate");
        params.setId(0);
        params.setTotal("total");
        params.setSeller("seller");

        // Run the test
        invoiceValidateServiceImplUnderTest.handleInvoiceValidate(params);
    }

    @Test public void testList() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        when(mockRecordDAO.getListCount(0, 0, 0L)).thenReturn(0);

        // Configure InvoiceIdentifyRecordDAO.getList(...).
        final InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        invoiceIdentifyRecordDO.setId(0);
        invoiceIdentifyRecordDO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDO.setInvoiceType(0);
        invoiceIdentifyRecordDO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDO.setCheckCode("checkCode");
        invoiceIdentifyRecordDO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDO.setTotal("total");
        invoiceIdentifyRecordDO.setTax("tax");
        invoiceIdentifyRecordDO.setSeller("seller");
        final List<InvoiceIdentifyRecordDO> invoiceIdentifyRecordDOS = Arrays.asList(invoiceIdentifyRecordDO);
        when(mockRecordDAO.getList(0, 0, 0L)).thenReturn(invoiceIdentifyRecordDOS);

        // Run the test
        final ServiceResult<JSONObject> result = invoiceValidateServiceImplUnderTest.list(params);

        // Verify the results
    }

    @Test public void testList_InvoiceIdentifyRecordDAOGetListReturnsNoItems() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        when(mockRecordDAO.getListCount(0, 0, 0L)).thenReturn(0);
        when(mockRecordDAO.getList(0, 0, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final ServiceResult<JSONObject> result = invoiceValidateServiceImplUnderTest.list(params);

        // Verify the results
    }

    @Test public void testDelete() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.delete(params);

        // Verify the results
        verify(mockRecordDAO).updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO());
    }

    @Test public void testBatchDelete() {
        // Setup
        final InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(0);
        params.setLoginRoleId(0);
        params.setOffset(0);
        params.setPageSize(0);
        params.setId(0);
        params.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        params.setList(Arrays.asList(invoiceValidateDetailParams));
        params.setBatchNo("batchNo");
        params.setIsReal(0);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.batchDelete(params);

        // Verify the results
        verify(mockRecordDAO).batchDelete(Arrays.asList(0), 0);
    }

    @Test public void testGetBatchNo() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.getBatchNo();

        // Verify the results
    }

    @Test public void testGetCurrent2TodayEndMillisTime() {
        // Setup

        // Run the test
        final Long result = invoiceValidateServiceImplUnderTest.getCurrent2TodayEndMillisTime();

        // Verify the results
    }

    @Test public void testGetIncr() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final Long result = invoiceValidateServiceImplUnderTest.getIncr("key", 0L);

    }

    @Test public void testBatchValidate() {
        // Setup
        final InvoiceValidateParams invoiceValidateParams = new InvoiceValidateParams();
        invoiceValidateParams.setSubjectType(0);
        invoiceValidateParams.setLoginUserId(0);
        invoiceValidateParams.setLoginRoleId(0);
        invoiceValidateParams.setOffset(0);
        invoiceValidateParams.setPageSize(0);
        invoiceValidateParams.setId(0);
        invoiceValidateParams.setIds(Arrays.asList(0));
        final InvoiceValidateDetailParams invoiceValidateDetailParams = new InvoiceValidateDetailParams();
        invoiceValidateDetailParams.setInvoiceNo("invoiceNo");
        invoiceValidateDetailParams.setCheckCode("checkCode");
        invoiceValidateDetailParams.setInvoiceCode("invoiceCode");
        invoiceValidateDetailParams.setPretaxAmount("pretaxAmount");
        invoiceValidateDetailParams.setInvoiceType(0);
        invoiceValidateDetailParams.setInvoiceTypeStr("invoiceTypeStr");
        invoiceValidateDetailParams.setInvoiceDate("invoiceDate");
        invoiceValidateDetailParams.setId(0);
        invoiceValidateDetailParams.setTotal("total");
        invoiceValidateDetailParams.setSeller("seller");
        invoiceValidateParams.setList(Arrays.asList(invoiceValidateDetailParams));
        invoiceValidateParams.setBatchNo("batchNo");
        invoiceValidateParams.setIsReal(0);

        // Configure InvoiceDAO.selectInvoiceByInvoiceCodeAndNo(...).
        final InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBuyer("buyer");
        invoiceDO.setSubjectName("subjectName");
        invoiceDO.setInvoiceNo("invoiceNo");
        invoiceDO.setInvoiceCode("invoiceCode");
        invoiceDO.setId(0);
        invoiceDO.setSupplierNo("supplierNo");
        invoiceDO.setSubjectType(0);
        invoiceDO.setBillNo("billNo");
        invoiceDO.setType(0);
        invoiceDO.setRawPrice(new BigDecimal("0.00"));
        when(mockInvoiceDAO.selectInvoiceByInvoiceCodeAndNo("invoiceCode", "invoiceNo")).thenReturn(invoiceDO);

        when(mockInvoiceDAO.updateByPrimaryKeySelective(new InvoiceDO())).thenReturn(0);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.batchValidate(invoiceValidateParams);

        // Verify the results
        verify(mockInvoiceDAO).updateByPrimaryKeySelective(new InvoiceDO());
    }

    @Test public void testUpdate() {
        // Setup
        final InvoiceValidateDetailParams params = new InvoiceValidateDetailParams();
        params.setInvoiceNo("invoiceNo");
        params.setCheckCode("checkCode");
        params.setInvoiceCode("invoiceCode");
        params.setPretaxAmount("pretaxAmount");
        params.setInvoiceType(0);
        params.setInvoiceTypeStr("invoiceTypeStr");
        params.setInvoiceDate("invoiceDate");
        params.setId(0);
        params.setTotal("total");
        params.setSeller("seller");

        // Configure InvoiceIdentifyRecordDAO.selectByPrimaryKey(...).
        final InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        invoiceIdentifyRecordDO.setId(0);
        invoiceIdentifyRecordDO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDO.setInvoiceType(0);
        invoiceIdentifyRecordDO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDO.setCheckCode("checkCode");
        invoiceIdentifyRecordDO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDO.setTotal("total");
        invoiceIdentifyRecordDO.setTax("tax");
        invoiceIdentifyRecordDO.setSeller("seller");
        when(mockRecordDAO.selectByPrimaryKey(0)).thenReturn(invoiceIdentifyRecordDO);

        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.update(params);

        // Verify the results
        verify(mockRecordDAO).updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO());
    }

    @Test public void testValidate() {
        // Setup
        final InvoiceValidateDetailParams params = new InvoiceValidateDetailParams();
        params.setInvoiceNo("invoiceNo");
        params.setCheckCode("checkCode");
        params.setInvoiceCode("invoiceCode");
        params.setPretaxAmount("pretaxAmount");
        params.setInvoiceType(0);
        params.setInvoiceTypeStr("invoiceTypeStr");
        params.setInvoiceDate("invoiceDate");
        params.setId(0);
        params.setTotal("total");
        params.setSeller("seller");

        // Configure InvoiceIdentifyRecordDAO.selectByPrimaryKey(...).
        final InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        invoiceIdentifyRecordDO.setId(0);
        invoiceIdentifyRecordDO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDO.setInvoiceType(0);
        invoiceIdentifyRecordDO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDO.setCheckCode("checkCode");
        invoiceIdentifyRecordDO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDO.setTotal("total");
        invoiceIdentifyRecordDO.setTax("tax");
        invoiceIdentifyRecordDO.setSeller("seller");
        when(mockRecordDAO.selectByPrimaryKey(0)).thenReturn(invoiceIdentifyRecordDO);

        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Configure InvoiceManager.getInvoiceById(...).
        final InvoiceBO invoiceBO = new InvoiceBO();
        invoiceBO.setId(0);
        invoiceBO.setInvoiceNo("invoiceNo");
        invoiceBO.setSupplierNo("supplierNo");
        invoiceBO.setInvoiceCode("invoiceCode");
        invoiceBO.setSubjectType(0);
        invoiceBO.setSubjectName("subjectName");
        invoiceBO.setBillNo("billNo");
        invoiceBO.setInvoiceType(0);
        invoiceBO.setRawPrice(new BigDecimal("0.00"));
        invoiceBO.setTaxPrice(new BigDecimal("0.00"));
        when(mockInvoiceManager.getInvoiceById(0)).thenReturn(invoiceBO);

        when(mockValidator.validate(new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"),
              Class.class)).thenReturn(new HashSet<>());

        // Configure GlorityClient.invoiceValidation(...).
        final InvoiceResultDTO invoiceResultDTO = new InvoiceResultDTO();
        invoiceResultDTO.setResult(0);
        invoiceResultDTO.setError("error");
        invoiceResultDTO.setMessage("message");
        final InvoiceResponseDTO response = new InvoiceResponseDTO();
        final InvoiceDataDTO data = new InvoiceDataDTO();
        data.setVersion("version");
        data.setResult(0);
        data.setTimestamp(0L);
        data.setMessage("message");
        data.setId("id");
        data.setSha1("sha1");
        data.setTime_cost("time_cost");
        final DetailAndExtraDTO detailAndExtraDTO = new DetailAndExtraDTO();
        detailAndExtraDTO.setType("type");
        detailAndExtraDTO.setOrientation(0);
        final DetailsDTO details = new DetailsDTO();
        details.setCode("code");
        details.setNumber("number");
        details.setDate("date");
        details.setPretax_amount("pretax_amount");
        details.setTotal("total");
        details.setTax("tax");
        details.setCheck_code("check_code");
        details.setSeller("seller");
        details.setSeller_tax_id("seller_tax_id");
        details.setBuyer("buyer");
        detailAndExtraDTO.setDetails(details);
        final ExtraDTO extra = new ExtraDTO();
        extra.setCheck_code_candidates(Arrays.asList("value"));
        extra.setCheck_code_last_six(Arrays.asList("value"));
        extra.setNumber_order_error(Arrays.asList("value"));
        detailAndExtraDTO.setExtra(extra);
        final ValidationDTO validation = new ValidationDTO();
        validation.setCode("code");
        validation.setMessage("message");
        detailAndExtraDTO.setValidation(validation);
        data.setIdentify_results(Arrays.asList(detailAndExtraDTO));
        response.setData(data);
        invoiceResultDTO.setResponse(response);
        when(mockGlorityClient.invoiceValidation(
              new GlorityParam("app_key", "token", 0L, "code", "number", "pretax_amount","total", "date", "type"))).thenReturn(invoiceResultDTO);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.validate(params);

        // Verify the results
        verify(mockRecordDAO).updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO());
        verify(mockInvoiceService).invoiceValidate(new InvoiceBO(),
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
    }

    @Test public void testValidate_ValidatorReturnsNoItems() {
        // Setup
        final InvoiceValidateDetailParams params = new InvoiceValidateDetailParams();
        params.setInvoiceNo("invoiceNo");
        params.setCheckCode("checkCode");
        params.setInvoiceCode("invoiceCode");
        params.setPretaxAmount("pretaxAmount");
        params.setInvoiceType(0);
        params.setInvoiceTypeStr("invoiceTypeStr");
        params.setInvoiceDate("invoiceDate");
        params.setId(0);
        params.setTotal("total");
        params.setSeller("seller");

        // Configure InvoiceIdentifyRecordDAO.selectByPrimaryKey(...).
        final InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        invoiceIdentifyRecordDO.setId(0);
        invoiceIdentifyRecordDO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDO.setInvoiceType(0);
        invoiceIdentifyRecordDO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDO.setCheckCode("checkCode");
        invoiceIdentifyRecordDO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDO.setTotal("total");
        invoiceIdentifyRecordDO.setTax("tax");
        invoiceIdentifyRecordDO.setSeller("seller");
        when(mockRecordDAO.selectByPrimaryKey(0)).thenReturn(invoiceIdentifyRecordDO);

        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Configure InvoiceManager.getInvoiceById(...).
        final InvoiceBO invoiceBO = new InvoiceBO();
        invoiceBO.setId(0);
        invoiceBO.setInvoiceNo("invoiceNo");
        invoiceBO.setSupplierNo("supplierNo");
        invoiceBO.setInvoiceCode("invoiceCode");
        invoiceBO.setSubjectType(0);
        invoiceBO.setSubjectName("subjectName");
        invoiceBO.setBillNo("billNo");
        invoiceBO.setInvoiceType(0);
        invoiceBO.setRawPrice(new BigDecimal("0.00"));
        invoiceBO.setTaxPrice(new BigDecimal("0.00"));
        when(mockInvoiceManager.getInvoiceById(0)).thenReturn(invoiceBO);

        when(mockValidator.validate(new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"),
              Class.class)).thenReturn(Collections.emptySet());

        // Configure GlorityClient.invoiceValidation(...).
        final InvoiceResultDTO invoiceResultDTO = new InvoiceResultDTO();
        invoiceResultDTO.setResult(0);
        invoiceResultDTO.setError("error");
        invoiceResultDTO.setMessage("message");
        final InvoiceResponseDTO response = new InvoiceResponseDTO();
        final InvoiceDataDTO data = new InvoiceDataDTO();
        data.setVersion("version");
        data.setResult(0);
        data.setTimestamp(0L);
        data.setMessage("message");
        data.setId("id");
        data.setSha1("sha1");
        data.setTime_cost("time_cost");
        final DetailAndExtraDTO detailAndExtraDTO = new DetailAndExtraDTO();
        detailAndExtraDTO.setType("type");
        detailAndExtraDTO.setOrientation(0);
        final DetailsDTO details = new DetailsDTO();
        details.setCode("code");
        details.setNumber("number");
        details.setDate("date");
        details.setPretax_amount("pretax_amount");
        details.setTotal("total");
        details.setTax("tax");
        details.setCheck_code("check_code");
        details.setSeller("seller");
        details.setSeller_tax_id("seller_tax_id");
        details.setBuyer("buyer");
        detailAndExtraDTO.setDetails(details);
        final ExtraDTO extra = new ExtraDTO();
        extra.setCheck_code_candidates(Arrays.asList("value"));
        extra.setCheck_code_last_six(Arrays.asList("value"));
        extra.setNumber_order_error(Arrays.asList("value"));
        detailAndExtraDTO.setExtra(extra);
        final ValidationDTO validation = new ValidationDTO();
        validation.setCode("code");
        validation.setMessage("message");
        detailAndExtraDTO.setValidation(validation);
        data.setIdentify_results(Arrays.asList(detailAndExtraDTO));
        response.setData(data);
        invoiceResultDTO.setResponse(response);
        when(mockGlorityClient.invoiceValidation(
              new GlorityParam("app_key", "token", 0L, "code", "number", "pretax_amount","total", "date", "type"))).thenReturn(invoiceResultDTO);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.validate(params);

        // Verify the results
        verify(mockRecordDAO).updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO());
        verify(mockInvoiceService).invoiceValidate(new InvoiceBO(),
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
    }

    @Test public void testValidate_ValidatorThrowsValidationException() {
        // Setup
        final InvoiceValidateDetailParams params = new InvoiceValidateDetailParams();
        params.setInvoiceNo("invoiceNo");
        params.setCheckCode("checkCode");
        params.setInvoiceCode("invoiceCode");
        params.setPretaxAmount("pretaxAmount");
        params.setInvoiceType(0);
        params.setInvoiceTypeStr("invoiceTypeStr");
        params.setInvoiceDate("invoiceDate");
        params.setId(0);
        params.setTotal("total");
        params.setSeller("seller");

        // Configure InvoiceIdentifyRecordDAO.selectByPrimaryKey(...).
        final InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        invoiceIdentifyRecordDO.setId(0);
        invoiceIdentifyRecordDO.setInvoiceNo("invoiceNo");
        invoiceIdentifyRecordDO.setInvoiceCode("invoiceCode");
        invoiceIdentifyRecordDO.setInvoiceType(0);
        invoiceIdentifyRecordDO.setInvoiceDate("invoiceDate");
        invoiceIdentifyRecordDO.setCheckCode("checkCode");
        invoiceIdentifyRecordDO.setPretaxAmount("pretaxAmount");
        invoiceIdentifyRecordDO.setTotal("total");
        invoiceIdentifyRecordDO.setTax("tax");
        invoiceIdentifyRecordDO.setSeller("seller");
        when(mockRecordDAO.selectByPrimaryKey(0)).thenReturn(invoiceIdentifyRecordDO);

        when(mockRecordDAO.updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO())).thenReturn(0);

        // Configure InvoiceManager.getInvoiceById(...).
        final InvoiceBO invoiceBO = new InvoiceBO();
        invoiceBO.setId(0);
        invoiceBO.setInvoiceNo("invoiceNo");
        invoiceBO.setSupplierNo("supplierNo");
        invoiceBO.setInvoiceCode("invoiceCode");
        invoiceBO.setSubjectType(0);
        invoiceBO.setSubjectName("subjectName");
        invoiceBO.setBillNo("billNo");
        invoiceBO.setInvoiceType(0);
        invoiceBO.setRawPrice(new BigDecimal("0.00"));
        invoiceBO.setTaxPrice(new BigDecimal("0.00"));
        when(mockInvoiceManager.getInvoiceById(0)).thenReturn(invoiceBO);

        when(mockValidator.validate(new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"),
              Class.class)).thenThrow(ValidationException.class);

        // Configure GlorityClient.invoiceValidation(...).
        final InvoiceResultDTO invoiceResultDTO = new InvoiceResultDTO();
        invoiceResultDTO.setResult(0);
        invoiceResultDTO.setError("error");
        invoiceResultDTO.setMessage("message");
        final InvoiceResponseDTO response = new InvoiceResponseDTO();
        final InvoiceDataDTO data = new InvoiceDataDTO();
        data.setVersion("version");
        data.setResult(0);
        data.setTimestamp(0L);
        data.setMessage("message");
        data.setId("id");
        data.setSha1("sha1");
        data.setTime_cost("time_cost");
        final DetailAndExtraDTO detailAndExtraDTO = new DetailAndExtraDTO();
        detailAndExtraDTO.setType("type");
        detailAndExtraDTO.setOrientation(0);
        final DetailsDTO details = new DetailsDTO();
        details.setCode("code");
        details.setNumber("number");
        details.setDate("date");
        details.setPretax_amount("pretax_amount");
        details.setTotal("total");
        details.setTax("tax");
        details.setCheck_code("check_code");
        details.setSeller("seller");
        details.setSeller_tax_id("seller_tax_id");
        details.setBuyer("buyer");
        detailAndExtraDTO.setDetails(details);
        final ExtraDTO extra = new ExtraDTO();
        extra.setCheck_code_candidates(Arrays.asList("value"));
        extra.setCheck_code_last_six(Arrays.asList("value"));
        extra.setNumber_order_error(Arrays.asList("value"));
        detailAndExtraDTO.setExtra(extra);
        final ValidationDTO validation = new ValidationDTO();
        validation.setCode("code");
        validation.setMessage("message");
        detailAndExtraDTO.setValidation(validation);
        data.setIdentify_results(Arrays.asList(detailAndExtraDTO));
        response.setData(data);
        invoiceResultDTO.setResponse(response);
        when(mockGlorityClient.invoiceValidation(
              new GlorityParam("app_key", "token", 0L, "code", "number", "pretax_amount","total", "date", "type"))).thenReturn(invoiceResultDTO);

        // Run the test
        final ServiceResult<String> result = invoiceValidateServiceImplUnderTest.validate(params);

        // Verify the results
        verify(mockRecordDAO).updateByPrimaryKeySelective(new InvoiceIdentifyRecordDO());
        verify(mockInvoiceService).invoiceValidate(new InvoiceBO(),
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
    }

    @Test public void testRequireGlority() {
        // Setup
        final InvoiceBO validateInvoiceBO = new InvoiceBO();
        validateInvoiceBO.setId(0);
        validateInvoiceBO.setInvoiceNo("invoiceNo");
        validateInvoiceBO.setSupplierNo("supplierNo");
        validateInvoiceBO.setInvoiceCode("invoiceCode");
        validateInvoiceBO.setSubjectType(0);
        validateInvoiceBO.setSubjectName("subjectName");
        validateInvoiceBO.setBillNo("billNo");
        validateInvoiceBO.setInvoiceType(0);
        validateInvoiceBO.setRawPrice(new BigDecimal("0.00"));
        validateInvoiceBO.setTaxPrice(new BigDecimal("0.00"));

        final InvoiceValidateStatusDO expectedResult = new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult");
        when(mockValidator.validate(new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"),
              Class.class)).thenReturn(new HashSet<>());

        // Configure GlorityClient.invoiceValidation(...).
        final InvoiceResultDTO invoiceResultDTO = new InvoiceResultDTO();
        invoiceResultDTO.setResult(0);
        invoiceResultDTO.setError("error");
        invoiceResultDTO.setMessage("message");
        final InvoiceResponseDTO response = new InvoiceResponseDTO();
        final InvoiceDataDTO data = new InvoiceDataDTO();
        data.setVersion("version");
        data.setResult(0);
        data.setTimestamp(0L);
        data.setMessage("message");
        data.setId("id");
        data.setSha1("sha1");
        data.setTime_cost("time_cost");
        final DetailAndExtraDTO detailAndExtraDTO = new DetailAndExtraDTO();
        detailAndExtraDTO.setType("type");
        detailAndExtraDTO.setOrientation(0);
        final DetailsDTO details = new DetailsDTO();
        details.setCode("code");
        details.setNumber("number");
        details.setDate("date");
        details.setPretax_amount("pretax_amount");
        details.setTotal("total");
        details.setTax("tax");
        details.setCheck_code("check_code");
        details.setSeller("seller");
        details.setSeller_tax_id("seller_tax_id");
        details.setBuyer("buyer");
        detailAndExtraDTO.setDetails(details);
        final ExtraDTO extra = new ExtraDTO();
        extra.setCheck_code_candidates(Arrays.asList("value"));
        extra.setCheck_code_last_six(Arrays.asList("value"));
        extra.setNumber_order_error(Arrays.asList("value"));
        detailAndExtraDTO.setExtra(extra);
        final ValidationDTO validation = new ValidationDTO();
        validation.setCode("code");
        validation.setMessage("message");
        detailAndExtraDTO.setValidation(validation);
        data.setIdentify_results(Arrays.asList(detailAndExtraDTO));
        response.setData(data);
        invoiceResultDTO.setResponse(response);
        when(mockGlorityClient.invoiceValidation(
              new GlorityParam("app_key", "token", 0L, "code", "number", "pretax_amount","total", "date", "type"))).thenReturn(invoiceResultDTO);

        // Run the test
        final InvoiceValidateStatusDO result = invoiceValidateServiceImplUnderTest.requireGlority(validateInvoiceBO, 0,
              InvoiceValidateTypeEnum.TASK_VERIFICATION);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockInvoiceService).invoiceValidate(new InvoiceBO(),
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
    }

    @Test public void testRequireGlority_ValidatorReturnsNoItems() {
        // Setup
        final InvoiceBO validateInvoiceBO = new InvoiceBO();
        validateInvoiceBO.setId(0);
        validateInvoiceBO.setInvoiceNo("invoiceNo");
        validateInvoiceBO.setSupplierNo("supplierNo");
        validateInvoiceBO.setInvoiceCode("invoiceCode");
        validateInvoiceBO.setSubjectType(0);
        validateInvoiceBO.setSubjectName("subjectName");
        validateInvoiceBO.setBillNo("billNo");
        validateInvoiceBO.setInvoiceType(0);
        validateInvoiceBO.setRawPrice(new BigDecimal("0.00"));
        validateInvoiceBO.setTaxPrice(new BigDecimal("0.00"));

        final InvoiceValidateStatusDO expectedResult = new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult");
        when(mockValidator.validate(new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"),
              Class.class)).thenReturn(Collections.emptySet());

        // Configure GlorityClient.invoiceValidation(...).
        final InvoiceResultDTO invoiceResultDTO = new InvoiceResultDTO();
        invoiceResultDTO.setResult(0);
        invoiceResultDTO.setError("error");
        invoiceResultDTO.setMessage("message");
        final InvoiceResponseDTO response = new InvoiceResponseDTO();
        final InvoiceDataDTO data = new InvoiceDataDTO();
        data.setVersion("version");
        data.setResult(0);
        data.setTimestamp(0L);
        data.setMessage("message");
        data.setId("id");
        data.setSha1("sha1");
        data.setTime_cost("time_cost");
        final DetailAndExtraDTO detailAndExtraDTO = new DetailAndExtraDTO();
        detailAndExtraDTO.setType("type");
        detailAndExtraDTO.setOrientation(0);
        final DetailsDTO details = new DetailsDTO();
        details.setCode("code");
        details.setNumber("number");
        details.setDate("date");
        details.setPretax_amount("pretax_amount");
        details.setTotal("total");
        details.setTax("tax");
        details.setCheck_code("check_code");
        details.setSeller("seller");
        details.setSeller_tax_id("seller_tax_id");
        details.setBuyer("buyer");
        detailAndExtraDTO.setDetails(details);
        final ExtraDTO extra = new ExtraDTO();
        extra.setCheck_code_candidates(Arrays.asList("value"));
        extra.setCheck_code_last_six(Arrays.asList("value"));
        extra.setNumber_order_error(Arrays.asList("value"));
        detailAndExtraDTO.setExtra(extra);
        final ValidationDTO validation = new ValidationDTO();
        validation.setCode("code");
        validation.setMessage("message");
        detailAndExtraDTO.setValidation(validation);
        data.setIdentify_results(Arrays.asList(detailAndExtraDTO));
        response.setData(data);
        invoiceResultDTO.setResponse(response);
        when(mockGlorityClient.invoiceValidation(
              new GlorityParam("app_key", "token", 0L, "code", "number", "pretax_amount","total", "date", "type"))).thenReturn(invoiceResultDTO);

        // Run the test
        final InvoiceValidateStatusDO result = invoiceValidateServiceImplUnderTest.requireGlority(validateInvoiceBO, 0,
              InvoiceValidateTypeEnum.TASK_VERIFICATION);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockInvoiceService).invoiceValidate(new InvoiceBO(),
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
    }

    @Test public void testRequireGlority_ValidatorThrowsValidationException() {
        // Setup
        final InvoiceBO validateInvoiceBO = new InvoiceBO();
        validateInvoiceBO.setId(0);
        validateInvoiceBO.setInvoiceNo("invoiceNo");
        validateInvoiceBO.setSupplierNo("supplierNo");
        validateInvoiceBO.setInvoiceCode("invoiceCode");
        validateInvoiceBO.setSubjectType(0);
        validateInvoiceBO.setSubjectName("subjectName");
        validateInvoiceBO.setBillNo("billNo");
        validateInvoiceBO.setInvoiceType(0);
        validateInvoiceBO.setRawPrice(new BigDecimal("0.00"));
        validateInvoiceBO.setTaxPrice(new BigDecimal("0.00"));

        final InvoiceValidateStatusDO expectedResult = new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
              new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult");
        when(mockValidator.validate(new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"),
              Class.class)).thenThrow(ValidationException.class);

        // Configure GlorityClient.invoiceValidation(...).
        final InvoiceResultDTO invoiceResultDTO = new InvoiceResultDTO();
        invoiceResultDTO.setResult(0);
        invoiceResultDTO.setError("error");
        invoiceResultDTO.setMessage("message");
        final InvoiceResponseDTO response = new InvoiceResponseDTO();
        final InvoiceDataDTO data = new InvoiceDataDTO();
        data.setVersion("version");
        data.setResult(0);
        data.setTimestamp(0L);
        data.setMessage("message");
        data.setId("id");
        data.setSha1("sha1");
        data.setTime_cost("time_cost");
        final DetailAndExtraDTO detailAndExtraDTO = new DetailAndExtraDTO();
        detailAndExtraDTO.setType("type");
        detailAndExtraDTO.setOrientation(0);
        final DetailsDTO details = new DetailsDTO();
        details.setCode("code");
        details.setNumber("number");
        details.setDate("date");
        details.setPretax_amount("pretax_amount");
        details.setTotal("total");
        details.setTax("tax");
        details.setCheck_code("check_code");
        details.setSeller("seller");
        details.setSeller_tax_id("seller_tax_id");
        details.setBuyer("buyer");
        detailAndExtraDTO.setDetails(details);
        final ExtraDTO extra = new ExtraDTO();
        extra.setCheck_code_candidates(Arrays.asList("value"));
        extra.setCheck_code_last_six(Arrays.asList("value"));
        extra.setNumber_order_error(Arrays.asList("value"));
        detailAndExtraDTO.setExtra(extra);
        final ValidationDTO validation = new ValidationDTO();
        validation.setCode("code");
        validation.setMessage("message");
        detailAndExtraDTO.setValidation(validation);
        data.setIdentify_results(Arrays.asList(detailAndExtraDTO));
        response.setData(data);
        invoiceResultDTO.setResponse(response);
        when(mockGlorityClient.invoiceValidation(
              new GlorityParam("app_key", "token", 0L, "code", "number", "pretax_amount","total", "date", "type"))).thenReturn(invoiceResultDTO);

        // Run the test
        final InvoiceValidateStatusDO result = invoiceValidateServiceImplUnderTest.requireGlority(validateInvoiceBO, 0,
              InvoiceValidateTypeEnum.TASK_VERIFICATION);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockInvoiceService).invoiceValidate(new InvoiceBO(),
              new InvoiceValidateStatusDO(0L, 0L, "invoiceNo", "invoiceCode",
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, 0, "validateCode", "validateResult"));
    }
}
