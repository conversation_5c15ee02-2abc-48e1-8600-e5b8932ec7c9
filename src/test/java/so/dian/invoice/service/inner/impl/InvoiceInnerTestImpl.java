package so.dian.invoice.service.inner.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.invoice.dao.InvoiceIdentifyRecordDAO;
import so.dian.invoice.mock.annotation.Mock;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.param.InvoiceValidateParams;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.service.inner.InvoiceInnerTest;
import so.dian.invoice.util.ServiceResult;

/**
 * 发票服务测试类内部接口实现
 *
 * <AUTHOR>
 */
@Service
public class InvoiceInnerTestImpl implements InvoiceInnerTest {

    @Resource
    private InvoiceService invoiceService;

    @Resource
    InvoiceIdentifyRecordDAO recordDAO;

    @Mock(targetClass = "so.dian.hr.api.entity.employee.AgentDTO", paramJson = "{\"agentId\":1,\"type\":0}")
    public boolean getInvoiceBelongInfoTest() {
        AgentDTO agentDTO = invoiceService.getInvoiceBelongInfo(11339);
        return agentDTO != null;
    }

    @Mock(targetClass = "so.dian.hr.api.entity.employee.AgentDTO", paramJson = "{\"agentId\":1,\"type\":0}")
    public boolean autoInsertInvoiceTest() {
        InvoiceValidateParams params = new InvoiceValidateParams();
        params.setSubjectType(0);
        params.setLoginUserId(11339);
        params.setBatchNo("T01");
        InvoiceIdentifyRecordDO identifyRecordDO = recordDAO.selectByPrimaryKey(277);
        ServiceResult<String> result = invoiceService.autoInsertInvoice(params, identifyRecordDO);

        return result.isSuccess();
    }
}
