package so.dian.invoice.mock.aspect;

import com.alibaba.fastjson.JSON;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.annotation.Configuration;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.invoice.mock.annotation.Mock;

/**
 * 数据填充切面
 *
 * <AUTHOR>
 */
@Aspect
@Configuration
public class DataAspect extends BaseAspect {

    @Before("@annotation(so.dian.invoice.mock.annotation.Mock)")
    public void beforeProcess(JoinPoint joinPoint) {
        try {
            // 获取注解实例
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Mock mock = methodSignature.getMethod().getAnnotation(Mock.class);
            // 获取Data对象名称
            String targetClass = mock.targetClass();
            // 获取对象值
            String paramJson = mock.paramJson();
            if (AgentDTO.class.getName().equals(targetClass)) {
                AgentDTO agentDTO = JSON.parseObject(paramJson, AgentDTO.class);
                agentThreadLocal.set(agentDTO);
            } else if (Boolean.class.getName().equals(targetClass)) {
                merchantPermissionThreadLocal.set(Boolean.parseBoolean(paramJson));
            } else {
                return;
            }
        } catch (Exception e) {
        }
    }

}