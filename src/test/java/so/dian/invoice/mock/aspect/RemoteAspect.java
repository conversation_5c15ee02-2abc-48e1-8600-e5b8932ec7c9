package so.dian.invoice.mock.aspect;


import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Configuration;
import so.dian.center.common.entity.BizResult;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.lvy.pojo.dto.InvoiceInfoDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 远程接口mock切面
 *
 * <AUTHOR>
 */
@Aspect
@Configuration
public class RemoteAspect extends BaseAspect {

    @Around("execution(* so.dian.invoice.client.HrClient.getAgentByUserId(..))")
    public BizResult<AgentDTO> execute1(ProceedingJoinPoint thisJoinPoint) {
        AgentDTO agentDTO = agentThreadLocal.get();
        return BizResult.create(agentDTO);
    }

    @Around("execution(* so.dian.contract.api.ContractFeignClient.checkDivideMerchantPermission(..))")
    public BizResult<Boolean> execute2(ProceedingJoinPoint thisJoinPoint) {
        Boolean result = merchantPermissionThreadLocal.get();
        return BizResult.create(result);
    }

    @Around("execution(* so.dian.lvy.api.WithdrawApplyBillApi.getInvoicelist(..))")
    public so.dian.commons.eden.entity.BizResult<List<InvoiceInfoDTO>> execute3(ProceedingJoinPoint thisJoinPoint) {
        return so.dian.commons.eden.entity.BizResult.create(new ArrayList());
    }
}