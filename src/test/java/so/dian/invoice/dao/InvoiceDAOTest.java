package so.dian.invoice.dao;

import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;
import so.dian.invoice.InvoiceApplicationTests;
import so.dian.invoice.pojo.dto.InvoiceDetailDto;
import so.dian.invoice.pojo.dto.InvoiceDto;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.param.InvoiceParam;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 发票服务测试类
 *
 * <AUTHOR>
 */
@Transactional
public class InvoiceDAOTest extends InvoiceApplicationTests {

    @Resource
    private InvoiceDAO invoiceDAO;

    @Test
    public void selectByPrimaryKeyTest() {
        InvoiceDO invoiceDO = invoiceDAO.selectByPrimaryKey(218);
        Assert.assertTrue(invoiceDO != null);
    }

    @Test
    public void selectInvoiceDetailAllTest() {
        InvoiceParam param = new InvoiceParam();
        param.setBelongSubjectType(0);
        param.setBelongSubjectId(1);
        param.setSupplierNo("DC01003");
        param.setInvoiceNo("18802890993");
        List<InvoiceDetailDto> invoiceDetailDtoList = invoiceDAO.selectInvoiceDetailAll(param);
        Assert.assertTrue(CollectionUtils.isNotEmpty(invoiceDetailDtoList));
    }

    @Test
    public void selectInvoicePageTest() {
        InvoiceParam param = new InvoiceParam();
        param.setBelongSubjectType(0);
        param.setBelongSubjectId(1);
        param.setSupplierNo("DC01003");
        param.setInvoiceNo("18802890993");
        param.setOffset(0);
        param.setPageSize(10);
        List<InvoiceDto> invoiceDtoList = invoiceDAO.selectInvoicePage(param);
        Assert.assertTrue(CollectionUtils.isNotEmpty(invoiceDtoList));
    }

    @Test
    public void countTest() {
        InvoiceParam param = new InvoiceParam();
        param.setBelongSubjectType(0);
        param.setBelongSubjectId(1);
        param.setSupplierNo("DC01003");
        param.setInvoiceNo("18802890993");
        int result = invoiceDAO.count(param);
        Assert.assertTrue(result > 0);
    }


    @Test
    public void insertBatchTest() {
        List<InvoiceDO> list = new ArrayList<>();
        list.add(initInvoiceDO());
        list.add(initInvoiceDO());
        list.add(initInvoiceDO());
        int result = invoiceDAO.insertBatch(list);
        Assert.assertTrue(result > 0);
    }

    @Test
    public void insertSelectiveTest() {
        InvoiceDO invoiceDO = initInvoiceDO();
        int result = invoiceDAO.insertSelective(invoiceDO);
        Assert.assertTrue(result > 0);
    }

    @Test
    public void updateByPrimaryKeySelectiveTest() {
        InvoiceDO invoiceDO = invoiceDAO.selectByPrimaryKey(218);
        modifyInvoiceDO(invoiceDO);
        int result = invoiceDAO.updateByPrimaryKeySelective(invoiceDO);
        Assert.assertTrue(result > 0);
    }

    @Test
    public void updateInvoiceByInvoiceCodeAndNoTest() {
        InvoiceDO invoiceDO = invoiceDAO.selectByPrimaryKey(218);
        modifyInvoiceDO(invoiceDO);
        int result = invoiceDAO.updateInvoiceByInvoiceCodeAndNo(invoiceDO);
        Assert.assertTrue(result > 0);
    }

    private InvoiceDO initInvoiceDO() {
        InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setGmtCreate(new Date());
        invoiceDO.setGmtModified(new Date());
        invoiceDO.setIsDelete(0);
        invoiceDO.setBillNo("1111111111");
        invoiceDO.setCreator(11339);
        invoiceDO.setCreateTime(new Date());
        invoiceDO.setSource(1);
        invoiceDO.setInvoiceCode("aaaaa");
        invoiceDO.setSubjectType(0);
        invoiceDO.setSubjectName("湛卢测试");
        invoiceDO.setBelongSubjectId(333);
        invoiceDO.setBelongSubjectType(1);
        return invoiceDO;
    }

    private void modifyInvoiceDO(InvoiceDO invoiceDO) {
        invoiceDO.setGmtCreate(new Date());
        invoiceDO.setGmtModified(new Date());
        invoiceDO.setIsDelete(0);
        invoiceDO.setBillNo("1111111111");
        invoiceDO.setSource(1);
        invoiceDO.setSubjectType(0);
        invoiceDO.setSubjectName("湛卢测试");
        invoiceDO.setBelongSubjectId(333);
        invoiceDO.setBelongSubjectType(1);
    }
}