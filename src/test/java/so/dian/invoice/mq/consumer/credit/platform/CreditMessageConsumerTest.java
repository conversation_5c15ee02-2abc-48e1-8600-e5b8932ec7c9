package so.dian.invoice.mq.consumer.credit.platform;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import so.dian.fis.credit.dto.response.CreditMessageDTO;

import javax.annotation.Resource;

@SpringBootTest
@ActiveProfiles("dev")
public class CreditMessageConsumerTest {
    @Resource
    private CreditMessageConsumer creditMessageConsumer;

    @Test
    public void onMessage() {
        CreditMessageDTO messageDTO = JSON.parseObject(getRepayJSON122211(), CreditMessageDTO.class);
        creditMessageConsumer.onMessage(messageDTO);
    }

    @Test
    public void onMessage1() {
        CreditMessageDTO messageDTO = JSON.parseObject(getJSON22(), CreditMessageDTO.class);
        creditMessageConsumer.onMessage(messageDTO);
    }



    private static String getRepayJSON122211(){
        return "{\"creditCardId\":1152,\"applyId\":3345,\"extJson\":null,\"type\":1,\"detailDTOS\":[{\"recordId\":3214,\"recordDetailId\":4024,\"bizOrderNo\":\"2505285882001911980\",\"creditCardId\":1152,\"creditOrderId\":1629,\"amount\":10060,\"planAllPeriod\":3,\"planCurrentPeriod\":null}],\"refund\":false,\"repay\":true}";
    }

    private String getRefundJSON(){
        return "{\n" +
                "        \"applyId\": 3302,\n" +
                "        \"creditCardId\": 1196,\n" +
                "        \"detailDTOS\": [\n" +
                "            {\n" +
                "                \"amount\": 20167,\n" +
                "                \"applyId\": 3202,\n" +
                "                \"bizOrderNo\": \"2503286573901218072\",\n" +
                "                \"creditOrderId\": 1599,\n" +
                "                \"planAllPeriod\": 3,\n" +
                "                \"recordId\": 2541\n" +
                "            },\n" +
                "            {\n" +
                "                \"amount\": 66667,\n" +
                "                \"applyId\": 3202,\n" +
                "                \"bizOrderNo\": \"2504276712701800072\",\n" +
                "                \"creditOrderId\": 1601,\n" +
                "                \"planAllPeriod\": 3,\n" +
                "                \"recordId\": 2540\n" +
                "            }\n" +
                "        ],\n" +
                "        \"refundSuccess\": false,\n" +
                "        \"repaySuccess\": true,\n" +
                "        \"type\": 2\n" +
                "    }";
    }


    /**
     * 授信采购单: 2504296284001597072 授信还款：
     * @return
     */
    private String getRepayJSON111(){
        return "{\n" +
                "    \"creditCardId\": 1196,\n" +
                "    \"applyId\": 3210,\n" +
                "    \"extJson\": null,\n" +
                "    \"type\": 1,\n" +
                "    \"detailDTOS\": [\n" +
                "        {\n" +
                "            \"recordId\": 3098,\n" +
                "            \"recordDetailId\": 3912,\n" +
                "            \"bizOrderNo\": \"2504296284001597072\",\n" +
                "            \"creditCardId\": 1196,\n" +
                "            \"creditOrderId\": 1604,\n" +
                "            \"amount\": 14167,\n" +
                "            \"planAllPeriod\": 3,\n" +
                "            \"planCurrentPeriod\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"recordId\": 3098,\n" +
                "            \"recordDetailId\": 3913,\n" +
                "            \"bizOrderNo\": \"2504296284001597072\",\n" +
                "            \"creditCardId\": 1196,\n" +
                "            \"creditOrderId\": 1604,\n" +
                "            \"amount\": 14167,\n" +
                "            \"planAllPeriod\": 3,\n" +
                "            \"planCurrentPeriod\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"refundSuccess\": false,\n" +
                "    \"repaySuccess\": true\n" +
                "}";
    }

    /**
     * 授信采购单: 2504296284001597072 授信还款退款：
     * @return
     */
    private String getJSON22(){
        return "{\n" +
                "    \"applyId\": 3333,\n" +
                "    \"creditCardId\": 1169,\n" +
                "    \"detailDTOS\": [\n" +
                "        {\n" +
                "            \"recordId\": 3253,\n" +
                "            \"recordDetailId\": 4444,\n" +
                "            \"bizOrderNo\": \"000000000000000\",\n" +
                "            \"creditCardId\": 1169,\n" +
                "            \"creditOrderId\": 1646,\n" +
                "            \"amount\": 24476,\n" +
                "            \"planAllPeriod\": 3\n" +
                "        }\n" +
                "    ],\n" +
                "    \"refund\": false,\n" +
                "    \"repay\": true,\n" +
                "    \"type\": 1\n" +
                "}";
    }
}