package so.dian.invoice.mq.consumer.tanshan;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import so.dian.invoice.manager.message.snapshot.InvoiceNewsVoucherManager;
import so.dian.taishan.client.pojo.mq.TradeOrderChangeDTO;

import javax.annotation.Resource;

@SpringBootTest
@ActiveProfiles("dev")
public class TradeOrderChangeConsumerTest {
    @Resource
    private TradeOrderChangeConsumer tradeOrderChangeConsumer;
    @Resource
    private InvoiceNewsVoucherManager invoiceNewsVoucherManager;

    @Test
    public void onMessage() {
//	InvoiceNewsVoucherDTO invoiceNewsVoucherDTO = invoiceNewsVoucherManager.getById(15L);
//	String eventVoucher = invoiceNewsVoucherDTO.getEventVoucher();
	TradeOrderChangeDTO tradeOrderChangeDTO = JSON.parseObject(getJSON22(), TradeOrderChangeDTO.class);
	//业务逻辑处理
	tradeOrderChangeConsumer.onMessage(tradeOrderChangeDTO);
    }

    /**
     * 授信采购单 2504296284001597072
     * @return
     */
    private String getJSONCancel2(){
	String str = "{\"agentDiamondLevel\":3,\"bizType\":1,\"buyerId\":3016072,\"buyerName\":\"龙拳001133\",\"buyerType\":130,\"createTime\":1748592682000,\"discountAmount\":19828.00,\"logisticsFee\":0.00,\"orderDetailDTOList\":[{\"discountTotalAmount\":9933.00,\"goodsType\":1,\"id\":7264,\"quantity\":1,\"salePrice\":9999.00,\"saleTotalAmount\":9999.00,\"settlePrice\":66.00,\"settleTotalAmount\":66.00,\"skuId\":116,\"skuName\":\"16口箱机-特价\",\"spuCode\":\"GJ000003\"},{\"discountTotalAmount\":9895.00,\"goodsType\":1,\"id\":7265,\"quantity\":1,\"salePrice\":9999.00,\"saleTotalAmount\":9999.00,\"settlePrice\":104.00,\"settleTotalAmount\":104.00,\"skuId\":108,\"skuName\":\"8口盒子-特价\",\"spuCode\":\"HZ000001\"}],\"orderNo\":\"2505305828201939072\",\"orderPage\":2,\"orderSource\":1,\"orderStatus\":5,\"paidAmount\":170.00,\"payChannel\":1048049,\"payMode\":1,\"payOrderNo\":\"2865291511806959241\",\"payStatus\":2,\"payTime\":1748592749000,\"realPayAmount\":170.00,\"sellerId\":1,\"sellerName\":\"小电\",\"sellerType\":110,\"settleAmount\":170.00,\"totalAmount\":19998.00}" ;
	return str;
    }
    /**
     * 授信采购单 2504296284001597072
     * @return
     */
    private String getJSONCancel(){
	String str = " {\"bizType\":1,\"orderDetailDTOList\":[{\"quantity\":1,\"salePrice\":9999,\"discountTotalAmount\":9879,\"settlePrice\":120,\"settleTotalAmount\":120,\"goodsType\":1,\"skuName\":\"8口盒子-特价\",\"spuCode\":\"HZ000001\",\"id\":7255,\"saleTotalAmount\":9999,\"skuId\":108}],\"payTime\":1748510124000,\"settleAmount\":120,\"sellerName\":\"小电\",\"orderStatus\":5,\"discountAmount\":9879,\"buyerId\":3015980,\"sellerId\":1,\"orderPage\":2,\"realPayAmount\":120,\"orderSource\":1,\"orderNo\":\"2505296210201496980\",\"payOrderNo\":\"2863906046636019728\",\"agentDiamondLevel\":3,\"payMode\":1,\"buyerName\":\"张三20240403\",\"totalAmount\":9999,\"createTime\":1748510102000,\"buyerType\":120,\"payChannel\":48,\"logisticsFee\":0,\"sellerType\":110,\"payStatus\":2,\"paidAmount\":120}";
	return str;
    }

    /**
     * 授信采购单 2504296284001597072
     * @return
     */
    private String getJSON(){
	String str = "{\n" +
		"    \"agentDiamondLevel\": 3,\n" +
		"    \"bizType\": 1,\n" +
		"    \"buyerId\": 3012477,\n" +
		"    \"buyerName\": \"测试组企业有限公司\",\n" +
		"    \"buyerType\": 150,\n" +
		"    \"createTime\": 1749006837000,\n" +
		"    \"discountAmount\": 0.00,\n" +
		"    \"logisticsFee\": 0.00,\n" +
		"    \"orderDetailDTOList\": [\n" +
		"        {\n" +
		"            \"discountTotalAmount\": 0.00,\n" +
		"            \"goodsType\": 1,\n" +
		"            \"id\": 7281,\n" +
		"            \"quantity\": 1,\n" +
		"            \"salePrice\": 1250.00,\n" +
		"            \"saleTotalAmount\": 1250.00,\n" +
		"            \"settlePrice\": 1250.00,\n" +
		"            \"settleTotalAmount\": 1250.00,\n" +
		"            \"skuId\": 6,\n" +
		"            \"skuName\": \"M6小柜机(白色)-4宝 仅限年框商\",\n" +
		"            \"spuCode\": \"GJ000010\"\n" +
		"        },\n" +
		"        {\n" +
		"            \"discountTotalAmount\": 0.00,\n" +
		"            \"goodsType\": 1,\n" +
		"            \"id\": 7282,\n" +
		"            \"quantity\": 1,\n" +
		"            \"salePrice\": 1200.00,\n" +
		"            \"saleTotalAmount\": 1200.00,\n" +
		"            \"settlePrice\": 1200.00,\n" +
		"            \"settleTotalAmount\": 1200.00,\n" +
		"            \"skuId\": 288,\n" +
		"            \"skuName\": \"M6小柜机(绿色)-4宝 仅限年框商\",\n" +
		"            \"spuCode\": \"GJ000010\"\n" +
		"        }\n" +
		"    ],\n" +
		"    \"orderNo\": \"888804404370158848888\",\n" +
		"    \"orderPage\": 1,\n" +
		"    \"orderSource\": 1,\n" +
		"    \"orderStatus\": 6,\n" +
		"    \"paidAmount\": 2450.00,\n" +
		"    \"payChannel\": 50,\n" +
		"    \"payMode\": 1,\n" +
		"    \"payOrderNo\": \"2872239871486814230\",\n" +
		"    \"payStatus\": 2,\n" +
		"    \"payTime\": 1749006866000,\n" +
		"    \"realPayAmount\": 2450.00,\n" +
		"    \"sellerId\": 1,\n" +
		"    \"sellerName\": \"小电\",\n" +
		"    \"sellerType\": 110,\n" +
		"    \"settleAmount\": 2450.00,\n" +
		"    \"totalAmount\": 2450.00\n" +
		"}";

	return str;
    }

    /**
     * 授信采购单
     * @return
     */
    private String getJSON22(){
	String str = "{\n" +
		"    \"agentDiamondLevel\": 3,\n" +
		"    \"bizType\": 1,\n" +
		"    \"buyerId\": 3015980,\n" +
		"    \"buyerName\": \"张三20240403\",\n" +
		"    \"buyerType\": 120,\n" +
		"    \"createTime\": 1749175321000,\n" +
		"    \"discountAmount\": 19431.00,\n" +
		"    \"logisticsFee\": 0.00,\n" +
		"    \"orderDetailDTOList\": [\n" +
		"        {\n" +
		"            \"discountTotalAmount\": 9876.00,\n" +
		"            \"goodsType\": 1,\n" +
		"            \"id\": 7312,\n" +
		"            \"quantity\": 1,\n" +
		"            \"salePrice\": 9999.00,\n" +
		"            \"skuId\": 108,\n" +
		"            \"skuName\": \"8口盒子-特价\",\n" +
		"            \"spuCode\": \"HZ000001\"\n" +
		"        },\n" +
		"        {\n" +
		"            \"discountTotalAmount\": 9555.00,\n" +
		"            \"goodsType\": 1,\n" +
		"            \"id\": 7313,\n" +
		"            \"quantity\": 1,\n" +
		"            \"salePrice\": 9999.00,\n" +
		"            \"saleTotalAmount\": 9999.00,\n" +
		"            \"settlePrice\": 444.00,\n" +
		"            \"settleTotalAmount\": 444.00,\n" +
		"            \"skuId\": 116,\n" +
		"            \"skuName\": \"16口箱机-特价\",\n" +
		"            \"spuCode\": \"GJ000003\"\n" +
		"        }\n" +
		"    ],\n" +
		"    \"orderNo\": \"2222222222222222\",\n" +
		"    \"orderPage\": 2,\n" +
		"    \"orderSource\": 1,\n" +
		"    \"orderStatus\": 6,\n" +
		"    \"paidAmount\": 567.00,\n" +
		"    \"payChannel\": 1048049,\n" +
		"    \"payMode\": 1,\n" +
		"    \"payOrderNo\": \"2875066585781626896\",\n" +
		"    \"payStatus\": 2,\n" +
		"    \"payTime\": 1749177505000,\n" +
//		"    \"realPayAmount\": 567.00,\n" +
		"    \"sellerId\": 1,\n" +
		"    \"sellerName\": \"小电\",\n" +
		"    \"sellerType\": 110,\n" +
		"    \"settleAmount\": 567.00,\n" +
		"    \"totalAmount\": 19998.00\n" +
		"}";

	return str;
    }

}