package so.dian.invoice.mq.consumer.songshan;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import so.dian.invoice.manager.message.snapshot.InvoiceNewsVoucherManager;
import so.dian.songshan.client.pojo.dto.ChannelRepairChangeDTO;

import javax.annotation.Resource;

@SpringBootTest
@ActiveProfiles("dev")
public class ChannelRepairChangeConsumerTest {

    @Resource
    private ChannelRepairChangeConsumer channelRepairChangeConsumer;
    @Resource
    private InvoiceNewsVoucherManager invoiceNewsVoucherManager;

    @Test
    public void onMessage() {
//	InvoiceNewsVoucherDTO invoiceNewsVoucherDTO = invoiceNewsVoucherManager.getById(57L);
//	String eventVoucher = invoiceNewsVoucherDTO.getEventVoucher();
	ChannelRepairChangeDTO channelRepairChangeDTO1 = JSON.parseObject(getJSON(), ChannelRepairChangeDTO.class);
	//业务逻辑处理
	channelRepairChangeConsumer.onMessage(channelRepairChangeDTO1);
    }

    /**
     * 维修单
     * @return
     */
    private String getJSONCancel2(){
	String str = "{\"agentId\":19,\"agentIncomingWarehouseId\":476,\"agentReceiver\":\"邵文虎\",\"agentReceiverAddress\":\"安徽省利辛县工业园电商服务中心二楼213\",\"agentReceiverPhone\":\"18956756860\",\"applyType\":1,\"diamondLevel\":3,\"expressFee\":0,\"expressId\":1,\"expressName\":\"其他物流\",\"expressNo\":\"13131313\",\"id\":6440,\"incomingWarehouseId\":11,\"orderMark\":8192,\"outgoingWarehouseId\":476,\"payAmount\":16800,\"payNo\":\"2769254961974834728\",\"payTime\":1742868462000,\"repairDetailDTOList\":[{\"agentFactorNum\":0,\"applyNum\":2,\"id\":7072,\"receiveNum\":2,\"repairFeeFinal\":1800,\"repairFeeOrigin\":1800,\"repairId\":6440,\"sendNum\":2,\"spuCode\":\"CC000002\",\"xdFactorNum\":0},{\"agentFactorNum\":0,\"applyNum\":1,\"id\":7073,\"receiveNum\":1,\"repairFeeFinal\":0,\"repairFeeOrigin\":0,\"repairId\":6440,\"sendNum\":1,\"spuCode\":\"GJ000020\",\"xdFactorNum\":0},{\"agentFactorNum\":0,\"applyNum\":3,\"id\":7074,\"receiveNum\":3,\"repairFeeFinal\":15000,\"repairFeeOrigin\":15000,\"repairId\":6440,\"sendNum\":3,\"spuCode\":\"HZ000001\",\"xdFactorNum\":0},{\"agentFactorNum\":0,\"applyNum\":3,\"id\":7075,\"receiveNum\":3,\"repairFeeFinal\":0,\"repairFeeOrigin\":0,\"repairId\":6440,\"sendNum\":3,\"spuCode\":\"PB000001\",\"xdFactorNum\":0}],\"repairNo\":\"ADR25022517230863827\",\"status\":9,\"type\":1,\"xdReceiver\":\"遐想\",\"xdReceiverAddress\":\"浙江省杭州市下城区朝晖路168号元丰·钛合国际A座1508\",\"xdReceiverPhone\":\"13520432027\"}" ;
	return str;
    }

    /**
     * 维修单
     * @return
     */
    private String getJSON(){
	String str = "{\n" +
		"    \"agentId\": 54,\n" +
		"    \"agentIncomingWarehouseId\": 484,\n" +
		"    \"agentReceiver\": \"遐想\",\n" +
		"    \"agentReceiverAddress\": \"浙江省杭州市余杭区未来科技城海创园5幢703\",\n" +
		"    \"agentReceiverPhone\": \"13520432027\",\n" +
		"    \"applyType\": 1,\n" +
		"    \"expressFee\": 0,\n" +
		"    \"expressId\": 2,\n" +
		"    \"expressName\": \"宅急送\",\n" +
		"    \"expressNo\": \"123456\",\n" +
		"    \"id\": 3625,\n" +
		"    \"incomingWarehouseId\": 599571,\n" +
		"    \"orderMark\": 8192,\n" +
		"    \"outgoingWarehouseId\": 484,\n" +
		"    \"payAmount\": 15000,\n" +
		"    \"payNo\": \"2762472495322932895\",\n" +
		"    \"payTime\": 1742464195000,\n" +
		"    \"repairDetailDTOList\": [\n" +
		"        {\n" +
		"            \"agentFactorNum\": 0,\n" +
		"            \"applyNum\": 5,\n" +
		"            \"id\": 4267,\n" +
		"            \"receiveNum\": 4,\n" +
		"            \"repairFeeFinal\": 15000,\n" +
		"            \"repairFeeOrigin\": 15000,\n" +
		"            \"repairId\": 3625,\n" +
		"            \"sendNum\": 5,\n" +
		"            \"spuCode\": \"HZ000001\",\n" +
		"            \"xdFactorNum\": 0\n" +
		"        }\n" +
		"    ],\n" +
		"    \"repairNo\": \"ADR11111111111111111\",\n" +
		"    \"status\": 9,\n" +
		"    \"type\": 1,\n" +
		"    \"xdReceiver\": \"西府\",\n" +
		"    \"xdReceiverAddress\": \"未来科技城海创园5幢703\",\n" +
		"    \"xdReceiverPhone\": \"17610083068\"\n" +
		"}";
	return str;
    }
}