package so.dian.invoice.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class SessionUtilTest {
  @Test
  void testGenerateSessionId() {
    Integer userId = 123;
    String accessToken = "abc123";
    String expectedSessionId =
        "QkQ4MTIwMkJERjBFRjZDQzI2NUE0Q0RGQjI3NjM4OEYxOERGNjM5NTEyMw=="; // Replace with the expected session ID

    String sessionId = SessionUtil.generateSessionId(userId, accessToken);

    Assertions.assertEquals(expectedSessionId, sessionId);
  }

  @Test
  void testGenerateSessionKey() {
    Integer userId = 123;
    String accessToken = "abc123";
    String expectedSessionKey = "BD81202BDF0EF6CC265A4CDFB276388F18DF6395123"; // Replace with the expected session key

    String sessionKey = SessionUtil.generateSessionKey(userId, accessToken);

    Assertions.assertEquals(expectedSessionKey, sessionKey);
  }

  @Test
  void testGenerateSessionKey_NullUserId() {
    Integer userId = null;
    String accessToken = "abc123";

    String sessionKey = SessionUtil.generateSessionKey(userId, accessToken);

    Assertions.assertNull(sessionKey);
  }

  @Test
  void testGenerateSessionKey_BlankAccessToken() {
    Integer userId = 123;
    String accessToken = "";

    String sessionKey = SessionUtil.generateSessionKey(userId, accessToken);

    Assertions.assertNull(sessionKey);
  }

  @Test
  void testParseTokenFromSessionId() {
    String sessionId = "QkQ4MTIwMkJERjBFRjZDQzI2NUE0Q0RGQjI3NjM4OEYxOERGNjM5NTEyMw==";
    String expectedToken = "BD81202BDF0EF6CC265A4CDFB276388F18DF6395";

    String token = SessionUtil.parseTokenFromSessionId(sessionId);

    Assertions.assertEquals(expectedToken, token);
  }

  @Test
  void testParseUserIdFromSessionId() {
    String sessionId = "QkQ4MTIwMkJERjBFRjZDQzI2NUE0Q0RGQjI3NjM4OEYxOERGNjM5NTEyMw==";
    Integer expectedUserId = 123;

    Integer userId = SessionUtil.parseUserIdFromSessionId(sessionId);

    Assertions.assertEquals(expectedUserId, userId);
  }
}