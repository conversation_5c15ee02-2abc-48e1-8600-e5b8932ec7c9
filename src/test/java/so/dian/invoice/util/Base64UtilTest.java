package so.dian.invoice.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class Base64UtilTest {
    @Test
    void testEncodeByOSS() {
        String input = "Hello, World!";
        String expectedOutput = "SGVsbG8sIFdvcmxkIQ==";
        String actualOutput = Base64Util.encodeByOSS(input);
        assertEquals(expectedOutput, actualOutput);
    }
}