package so.dian.invoice.handle;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import so.dian.invoice.service.InvoiceValidateService;

@RunWith(MockitoJUnitRunner.class)
public class InvoiceOCRHandleTest {

    @Mock
    private InvoiceValidateService mockInvoiceValidateService;
    @Mock
    private RedissonClient mockRedisson;

    @InjectMocks private InvoiceOCRHandle invoiceOCRHandleUnderTest;

    @Test public void testOcrThreadTask() {
        // Setup

        // Run the test
        invoiceOCRHandleUnderTest.ocrThreadTask();

        // Verify the results
    }
}
