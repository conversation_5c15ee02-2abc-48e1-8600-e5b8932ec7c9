package so.dian.invoice.job;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("dev")
public class InvoiceManageRetryJobTest {

    @Resource
    private InvoiceManageRetryJob invoiceManageRetryJob;

    @Test
    public void execute() {
        invoiceManageRetryJob.execute("{\"maxRetryTime\":0}");
    }
}