package so.dian.invoice;

import com.alibaba.fastjson.JSON;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import so.dian.invoice.client.GlorityClient;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.pojo.dto.identify.InvoiceResultDTO;
import so.dian.invoice.pojo.param.GlorityParam;

@RunWith(SpringRunner.class)
@SpringBootTest
public class InvoiceApplicationTests {

    @Autowired
    private GlorityClient glorityClient;

    @Autowired
    private InvoiceManager invoiceManager;

    @Autowired
    private InvoiceDAO invoiceDAO;

    @Value("${glority.appKey}")
    private String appKey;

    @Value("${glority.appSecret}")
    private String appSecret;

    @Test
    public void contextLoads() {
        long timestamp = System.currentTimeMillis() / 1000;
        String token = new Md5Hash(appKey + "+" + timestamp + "+" + appSecret).toString();
        // InvoiceDO invoiceDO = invoiceDAO.selectByPrimaryKey(2590);
        GlorityParam param = GlorityParam.builder().app_key(appKey).token(token).code("032001800107")
                .number("12388048")
                .pretax_amount("0.0000")
                .date("2019-11-07")
                .type("10103")
                .timestamp(timestamp).build();
        // FormBody formBody = getFormBody(params.getInvoiceCode(), params.getInvoiceNo(), params.getCheckCode(),
        // params.getInvoiceDate(), params.getPretaxAmount(), params.getInvoiceTypeStr(), timestamp, token);
        // params.setInvoiceDate(DateUtil.stringReversalToString(params.getInvoiceDate()));
        // params.setInvoiceTypeStr(InvoiceTypeEnum.getBizTypeByType(params.getInvoiceType()));
        System.out.println("param:"+ JSON.toJSONString(param));
        InvoiceResultDTO invoiceResultDTO = glorityClient.invoiceValidation(param);
        System.out.println(invoiceResultDTO);
    }

}

