package so.dian.invoice.configuration.message;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import so.dian.invoice.enums.BizTypeEnum;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("dev")
public class DingtalkMessageManagerTest {

    @Resource
    public DingtalkMessageManager messageManager;
    @Test
    public void sendDingDingTextMessage() {
        String content = String.format("{%s}:{%s}的待开金额<0，请财务及时核对处理", BizTypeEnum.REPAIR_ORDER.desc(),"111111");
//        messageManager.sendDingDingTextMessage("应开发票异常通知", content);
    }
}