package so.dian.invoice.service;

import so.dian.invoice.pojo.dto.SupplierDTO;
import so.dian.jinyun.client.pojo.response.scm.PurchaseOrderDetailRsp;

import java.util.List;
import java.util.Set;

/**
 * @Author: chenan
 * @Date: 2020/9/21 11:03
 */
public interface SupplierService {

    /**
     * 查询供应商-批量
     *
     * @param supplierNoList
     * @return
     */
    List<SupplierDTO> listSupplierByNoList(List<String> supplierNoList);

    /**
     * 查询供应商
     *
     * @param supplierNo
     * @return
     */
    SupplierDTO getBySupplierNo(String supplierNo);

    /**
     * 查询供应商名字
     *
     * @param supplierNo
     * @return
     */
    String getSupplierNameBySupplierNo(String supplierNo);

    /**
     * 根据手机号查询供应商
     *
     * @param mobile
     * @return
     */
    List<SupplierDTO> getByMobile(String mobile);


    /**
     * 根据订单号查询订单是否存在
     *
     * @param purchaseNo
     * @return
     */
    PurchaseOrderDetailRsp getPurchaseOrderByPurchaseNo(String purchaseNo);


    Set<String> getSupplierNosByMobile(String mobile);

    String getSupplierNosById(Long supplierId);
}
