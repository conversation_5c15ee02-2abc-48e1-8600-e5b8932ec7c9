package so.dian.invoice.service;

import java.util.List;
import so.dian.center.common.entity.BizResult;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;

import java.util.Map;
import java.util.Set;
import so.dian.hr.api.entity.employee.CityDepartmentDTO;
import so.dian.invoice.pojo.request.CurrentUserReq;

public interface AgentEmployeeService {

    Map<Integer, String> getAgentEmployeeNickMap(Set<Integer> userIdSet);

    Map<Integer, Integer> getAgentEmployeeCityCodeMap(Set<Integer> userIdSet);

    String getEmployeeNickById(Integer id);

    AgentEmployeeDTO getEmployeeById(Integer id);

    AgentEmployeeDTO getByNickName(String nickName);

    BizResult<AgentDTO> getAgentByUserId(Long userId);

    AgentDTO getAgentDtoByUserId(Integer userId);

    Map<Integer, CityDepartmentDTO> listRegionNameByCityCodes(List<Integer> cityCodeList);

    List<Integer> listCityCodeByRegionName(List<String> regionNameList);

    List<String> selectRegionList();

    CurrentUserReq getRemoteUser(Integer userId);

    CurrentUserReq getRemoteUser(CurrentUserReq cookieUser);
}
