package so.dian.invoice.service;

import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.invoice.pojo.bo.InvoiceExpressesRelationBO;
import so.dian.invoice.pojo.dto.InvoiceExpressesMsgDto;
import so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO;
import so.dian.invoice.pojo.param.InvoiceExpressBatchAddParam;
import so.dian.invoice.pojo.query.InvoiceExpressQuery;
import so.dian.invoice.pojo.query.InvoiceExpressesRelationQuery;
import so.dian.invoice.pojo.vo.ExpressesVO;
import so.dian.invoice.pojo.vo.InvoiceExpressesVO;

import java.util.List;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 8:53 PM
 * @Description:
 */
public interface InvoiceExpressesRelationService {

	/**
	 * 发票流转状态列表
	 * @return
	 */
	BizResult<List<NameValueDTO>> listInvoiceExpressesRelationStatus();

	/**
	 *
	 * @param invoiceExpressQuery
	 * @return
	 */
	List<InvoiceExpressesVO> findInvoiceExpressesVO(InvoiceExpressQuery invoiceExpressQuery);

	/**
	 * 批量新增发票物流信息
	 * @param invoiceExpressBatchAddParam
	 */
	void batchUpsetInvoiceExpresses(InvoiceExpressBatchAddParam invoiceExpressBatchAddParam);

	/**
	 * 发票物流信息
	 * @param name
	 * @return
	 */
	List<ExpressesVO> findExpressesVO(String name);

	List<InvoiceExpressesMsgDto> findExpressesByInvoiceIds(List<Long> invoiceIds);

	int modifyInvoiceExpressesById(InvoiceExpressesRelationDO invoiceExpressesRelationDO);

	List<InvoiceExpressesRelationBO> findAllByQuery(InvoiceExpressesRelationQuery query);

	int countByQuery(InvoiceExpressesRelationQuery query);

    String getSerialNo();
}
