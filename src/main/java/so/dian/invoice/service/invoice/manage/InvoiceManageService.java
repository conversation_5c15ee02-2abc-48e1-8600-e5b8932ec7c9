package so.dian.invoice.service.invoice.manage;

import com.github.pagehelper.PageInfo;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.AgentVO;
import so.dian.invoice.pojo.vo.AuthenticationSubjectVO;

import java.util.List;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 10:44
 */
public interface InvoiceManageService {

    List<AuthenticationSubjectVO> getAuthenticationSubjectByAgentId(List<Long> manageIds, CurrentUserReq userReq);

    /**
     * 发票管理列表
     * @param param
     * @return
     */
    PageInfo<InvoiceManageDTO> page(InvoiceManageQueryParam param, CurrentUserReq userReq);

    List<AgentVO> getAgentInfo(CurrentUserReq userReq);

    List<InvoiceChangeRecordDTO> listExpectedInvoiceAmountChangeRecord(Long manageId);

    List<InvoiceManageDetailDTO> getInvoiceManageDetailById(Long manageId);

    void export(InvoiceManageQueryParam param, CurrentUserReq userReq);
}
