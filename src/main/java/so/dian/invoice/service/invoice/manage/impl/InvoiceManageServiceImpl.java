package so.dian.invoice.service.invoice.manage.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.customer.dto.request.AuthenticationSubjectDTO;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.converter.InvoiceManageConverter;
import so.dian.invoice.dao.InvoiceChangeRecordDAO;
import so.dian.invoice.enums.AgentTypeEnum;
import so.dian.invoice.facade.PassportFacade;
import so.dian.invoice.facade.SunReaverFacade;
import so.dian.invoice.facade.TiantaiFacade;
import so.dian.invoice.handle.ExcelHandler;
import so.dian.invoice.manager.AgentManager;
import so.dian.invoice.manager.invoice.manage.InvoiceManageManager;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.AgentVO;
import so.dian.invoice.pojo.vo.AuthenticationSubjectVO;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageDetailExcel;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageExcel;
import so.dian.invoice.service.invoice.manage.InvoiceManageService;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 10:44
 */
@Slf4j
@Service
public class InvoiceManageServiceImpl implements InvoiceManageService {

    @Resource
    private InvoiceManageManager invoiceManageManager;
    @Resource
    private InvoiceChangeRecordDAO invoiceChangeRecordDAO;
    @Resource
    private ExcelHandler excelHandler;
    @Resource
    private AgentManager agentManager;
    @Resource
    private SunReaverFacade sunReaverManager;
    @Resource
    private TiantaiFacade tiantaiManager;
    @Resource
    private PassportFacade passportFacade;

    @Override
    public List<AuthenticationSubjectVO> getAuthenticationSubjectByAgentId(List<Long> manageIds, CurrentUserReq userReq){
        if (CollectionUtils.isEmpty(manageIds)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"未选择开票业务单");
        }
        // 判断用户角色是否是 BD主管、代理商、合资公司老板
//        if (!UserRoleEnum.AGENT_SELLER_MANAGER.getRoleName().equals(userReq.getCurrentRole())
//                && !UserRoleEnum.AGENCY_BOSS.getRoleName().equals(userReq.getCurrentRole())
//                && !UserRoleEnum.JOINTAFFAIRSMANAGER.getRoleName().equals(userReq.getCurrentRole())) {
//            log.info("只有 BD主管、代理商、合资公司老板才允许开票");
//            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"只有 BD主管、代理商、合资公司老板才允许开票");
////            return new ArrayList<>();
//        }

        //判断开票主体是否相同
        List<InvoiceManageDTO> list = invoiceManageManager.listManageById(manageIds);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("根据业务单ID获取发票管理信息为空,manageIds:", JSON.toJSONString(manageIds));
            return new ArrayList<>();
        }
        Set<Long> set = list.stream().map(e -> e.getSubjectId()).collect(Collectors.toSet());
        if (set.size() > 1) {
            log.warn("申请开票勾选的业务单渠道商ID需要一致,manageIds:", JSON.toJSONString(manageIds));
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"申请开票勾选的业务单渠道商ID需要一致");
        }
        InvoiceManageDTO invoiceManage = list.get(0);
        Long subjectId = invoiceManage.getSubjectId();
        List<AuthenticationSubjectDTO>  authenticationSubjects = passportFacade.getAuthenticationSubjectByAgentId(subjectId);

        List<AuthenticationSubjectVO> voList = authenticationSubjects.stream().map(AuthenticationSubjectVO::fromDTO).collect(Collectors.toList());

        return voList;
    }
    @Override
    public PageInfo<InvoiceManageDTO> page(InvoiceManageQueryParam param, CurrentUserReq userReq){
        //场景            角色                              数据范围
        //小电财务	财务经理	                        可查看所有公司的应开数据，含一代、合资和二代
        //小电渠道经理	渠道经理、渠道总监	                仅可查看本人及下属员工负责的代理商的信息
        //小电运营	城市策划	                        仅可查看发布公司为本人及下属运营负责公司
        //渠道商老板	BD主管（且为老板身份）、代理商	仅可查看本人归属公司的应开数据
        //渠道商财务	合资公司财务经理
        // 查询发票管理
        // 分页
        // 校验参数中的主体id是否在相应的角色所负责的公司中
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> list = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !list.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(list);
            }
        }
        //空字符串处理
        if (StringUtils.isBlank(param.getBizNo())) {
            param.setBizNo(null);
        }
        Page<Object> page = PageMethod.startPage(param.getPageNo(), param.getPageSize());
        //查询发票管理
        List<InvoiceManageDTO> manages = invoiceManageManager.list(param);
        // 构建返回值
        PageInfo<InvoiceManageDTO> pageInfo = new PageInfo<>(manages);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 根据用户ID
     * 场景            角色                              数据范围
     * 小电财务	财务经理	                        可查看所有公司的应开数据，含一代、合资和二代
     * 小电渠道经理	渠道经理、渠道总监	        仅可查看本人及下属员工负责的代理商的信息
     * 小电运营	城市策划	                        仅可查看发布公司为本人及下属运营负责公司
     * 渠道商老板	BD主管（且为老板身份）、代理商	仅可查看本人归属公司的应开数据
     * 渠道商财务	合资公司财务经理                   仅可查看本人归属公司的应开数据
     *
     * @param userReq
     * @return
     */
    @Override
    public List<AgentVO> getAgentInfo(CurrentUserReq userReq){
        //财务经理 可查看所有公司的应开数据，含一代、合资和二代
        if(StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())){
	    List<AgentDTO> agentDTOS = agentManager.findAgentList();
            List<AgentVO> agentVOS = agentDTOS.stream().map(AgentVO::fromDTO).collect(Collectors.toList());
	    return agentVOS;
	}
        // 非财务经理角色查看到的公司范围
        List<Long> agentIds = invoiceManageManager.getAgentIds(userReq);
        if (CollectionUtils.isEmpty(agentIds)) {
            log.warn("远程获取代理商信息 | 公司不存在 | userId:{},role:{}", userReq.getUserId(), userReq.getCurrentRole());
            return new ArrayList<>();
        }
        List<AgentDTO> agentDTOS = agentManager.listByIds(agentIds);
        if (CollectionUtils.isEmpty(agentDTOS)) {
            log.warn("远程获取代理商信息 | 用户不存在 | agentIds:{}", agentIds);
            return new ArrayList<>();
        }
        List<AgentVO> agentVOS = agentDTOS.stream()
                .filter(e -> AgentTypeEnum.AGENT_TYPE.getId().equals(e.getType()) || AgentTypeEnum.JV_COMPANY_TYPE.getId().equals(e.getType()))
                .map(AgentVO::fromDTO).collect(Collectors.toList());
        return agentVOS;
    }

    @Override
    public List<InvoiceChangeRecordDTO> listExpectedInvoiceAmountChangeRecord(Long manageId){
        List<InvoiceChangeRecordDO> changeRecordDOS = invoiceChangeRecordDAO.listByManageId(manageId);
        List<InvoiceChangeRecordDTO> changeRecordDTOS = changeRecordDOS.stream().map(InvoiceChangeRecordDTO::fromDO).collect(Collectors.toList());
        // 查询业务单号
        List<Long> manageIds = changeRecordDTOS.stream().map(e -> e.getManageId()).collect(Collectors.toList());
        List<InvoiceManageDTO> manageDTOS = invoiceManageManager.listManageById(manageIds);
        Map<Long, InvoiceManageDTO> map = manageDTOS.stream().collect(Collectors.toMap(InvoiceManageDTO::getId, Function.identity(), (v1, v2) -> v1));
        for (InvoiceChangeRecordDTO changeRecordDTO : changeRecordDTOS) {
            InvoiceManageDTO manageDTO = map.get(changeRecordDTO.getManageId());
            if(Objects.nonNull(manageDTO)){
                changeRecordDTO.setBizNo(manageDTO.getBizNo());
                changeRecordDTO.setBizType(manageDTO.getBizType());
            }
        }
        return changeRecordDTOS;
    }

    @Override
    public List<InvoiceManageDetailDTO> getInvoiceManageDetailById(Long manageId){
        List<InvoiceManageDetailDTO> manageDetails = invoiceManageManager.getInvoiceManageDetailById(manageId);
        return manageDetails;
    }

    /**
     * 导出
     */
    @Override
    public void export(InvoiceManageQueryParam param, CurrentUserReq userReq) {
        if (StringUtils.isBlank(userReq.getEmail())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"操作人邮箱不存在，请联系运营或HR更新邮箱后重试");
        }
        // 权限校验
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> agentIds = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !agentIds.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(agentIds);
            }
        }

        List<InvoiceManageDTO> list = invoiceManageManager.list(param);
        if (CollectionUtils.isEmpty(list)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"没有要导出的数据");
        }
        Integer belongSubjectId = userReq.getBelongSubjectId();
        //填充产品名称
        List<String> spuCodes = list.stream().filter(e -> CollectionUtils.isNotEmpty(e.getSpuCodes())).map(e -> e.getSpuCodes()).flatMap(List::stream).collect(Collectors.toList());
        Map<String, String> map = tiantaiManager.getSpuName(spuCodes);
        list.forEach(e -> {
            e.fillProductName(map);
        });
        try {
            // 小电员工
            if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(belongSubjectId)) {
                //构建开票申请记录
                List<ExportInvoiceManageDetailExcel> excelVOList = InvoiceManageConverter.toDetailExcelBean(list);
                excelHandler.exportToEmail(excelVOList, ExportInvoiceManageDetailExcel.class, userReq);
            }else {
                //构建开票申请记录
                List<ExportInvoiceManageExcel> excelVOList = InvoiceManageConverter.toExcelBean(list);
                excelHandler.exportToEmail(excelVOList, ExportInvoiceManageExcel.class, userReq);
            }
        } catch (Exception e) {
            log.info("导出开票申请记录出错", e);
            throw BizException.create(BaseErrorCodeEnum.FALLBACK,"发送邮件失败");
        }
    }
}