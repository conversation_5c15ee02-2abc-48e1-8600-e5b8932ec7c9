package so.dian.invoice.service.invoice.manage;

import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDTO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceModifyParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestAuditParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.BatchAuditVO;
import so.dian.invoice.pojo.vo.invoice.manage.InvoiceDetailVO;
import so.dian.invoice.pojo.vo.invoice.manage.InvoiceRequestVO;

import java.util.List;


public interface InvoiceRequestService {

    int createInvoiceRequest(InvoiceRequestParam param, CurrentUserReq userReq);

    PageInfo<InvoiceRequestVO> page(InvoiceRequestQueryParam param, CurrentUserReq userReq);

    List<InvoiceRequestRecordDTO> listRequestRecordWithDetail(Long requestId);

    InvoiceRequestDTO getInvoiceRequestById(Long requestId);

    void export(InvoiceRequestQueryParam param, CurrentUserReq userReq);

    void downloadAuditResult(String key);

    BatchAuditVO batchAudit(MultipartFile file, int status, String financeFeedback);

    List<Long> whiteList();

    boolean audit(InvoiceRequestAuditParam param);

    InvoiceDetailVO getInvoiceDetail(Long requestId);

    boolean modifyInvoice(InvoiceModifyParam param);
}
