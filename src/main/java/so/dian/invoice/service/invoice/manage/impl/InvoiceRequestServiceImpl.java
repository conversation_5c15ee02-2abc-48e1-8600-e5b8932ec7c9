package so.dian.invoice.service.invoice.manage.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.EmailValidator;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import so.dian.astra.client.pojo.enums.MaskingDataTypeEnum;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.client.manager.MailManager;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.converter.InvoiceRequestConverter;
import so.dian.invoice.dao.InvoiceRequestDAO;
import so.dian.invoice.enums.InvoiceRequestStatusEnum;
import so.dian.invoice.enums.InvoiceTypeEnum;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.facade.PassportFacade;
import so.dian.invoice.facade.SunReaverFacade;
import so.dian.invoice.facade.TiantaiFacade;
import so.dian.invoice.facade.WutaiFacade;
import so.dian.invoice.handle.ExcelHandler;
import so.dian.invoice.manager.AstraManager;
import so.dian.invoice.manager.invoice.manage.InvoiceManageManager;
import so.dian.invoice.manager.invoice.manage.InvoiceRequestManage;
import so.dian.invoice.pojo.dto.MaskingDataDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceImportDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceImportListener;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDTO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.BatchAuditVO;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceRequestRecordDetailExcel;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceRequestRecordExcel;
import so.dian.invoice.pojo.vo.invoice.manage.InvoiceRequestVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.invoice.manage.InvoiceRequestService;
import so.dian.invoice.util.EasyExcelUtils;
import so.dian.invoice.util.OssUtil;
import so.dian.invoice.util.RequestUtils;
import so.dian.wutai.client.enums.LogBizTypeEnum;
import so.dian.wutai.client.enums.YesOrNoEnum;
import so.dian.wutai.client.pojo.dto.OperatorLogCreateRspDTO;
import so.dian.wutai.client.util.FileEncryptorUtil;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InvoiceRequestServiceImpl implements InvoiceRequestService {
    @Value("${temp.path}")
    private String tempPath;
    @Resource
    private InvoiceManageManager invoiceManageManager;
    @Resource
    private InvoiceRequestManage invoiceRequestManage;
    @Resource
    private InvoiceRequestDAO invoiceRequestDAO;
    @Resource
    private AgentEmployeeService agentEmployeeService;
    @Resource
    private RedissonClient redisson;
    @Resource
    private AstraManager astraManager;
    @Resource
    private TiantaiFacade tiantaiManager;
    @Resource
    private WutaiFacade wutaiManager;
    @Resource
    private ExcelHandler excelHandler;
    @Resource
    private MailManager mailManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private SunReaverFacade sunReaverManager;

    @Resource
    private PassportFacade passportFacade;

    @Value("${invoice.batch_audit_user_id}")
    private List<Long> auditUserIds;

    private final static String BATCH_AUDIT_KEY_PREFIX = "invoice_batch_audit_key_prefix";

    private final static List<String> CREATE_ROLES = Arrays.asList(UserRoleEnum.AGENT_SELLER_MANAGER.getRoleName(), UserRoleEnum.AGENCY_BOSS.getRoleName(), UserRoleEnum.JOINTAFFAIRSMANAGER.getRoleName());


    @Override
    public int createInvoiceRequest(InvoiceRequestParam param, CurrentUserReq userReq) {
        // 权限校验 BD主管（老板身份）/代理商/合资公司财务经理
        if (StringUtils.equals(UserRoleEnum.AGENT_SELLER_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            // 校验 渠道商老板 BD主管（且为老板身份）
            AgentEmployeeDTO agentEmployeeDTO = agentEmployeeService.getEmployeeById(userReq.getUserId());
            if (Objects.isNull(agentEmployeeDTO) || !agentEmployeeDTO.getIsLeader()) {
                log.info("权限校验失败,开票申请只有以下角色可以操作：{},当前用户角色为:{}", CREATE_ROLES, userReq.getCurrentRole());
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"只有BD主管（老板身份）/代理商/合资公司财务经理可以申请开票");
            }
        }else if(!CREATE_ROLES.contains(userReq.getCurrentRole())){
            log.info("权限校验失败,开票申请只有以下角色可以操作：{},当前用户角色为:{}", CREATE_ROLES, userReq.getCurrentRole());
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"只有BD主管（老板身份）/代理商/合资公司财务经理可以申请开票");
        }
        if (StringUtils.isBlank(param.getTitle())) {
            log.info("参数校验失败,开票申请标题不能为空");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"开票申请标题不能为空");
        }
        if (CollectionUtils.isEmpty(param.getInvoiceManageIds())) {
            log.info("参数校验失败,开票单据不能为空");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"开票单据不能为空");
        }
        // 邮箱格式验证
        if (StringUtils.isBlank(param.getEmail())) {
            log.info("参数校验失败,收件人邮箱不能为空");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"收件人邮箱不能为空");
        }
        if (!EmailValidator.getInstance().isValid(param.getEmail())) {
            log.info("参数校验失败,收件人邮箱格式错误");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"收件人邮箱格式错误");
        }
        if (InvoiceTypeEnum.VAT_SPECIAL_INVOICE.getCode() == param.getType() && StringUtils.isEmpty(param.getInvoiceNo())) {
            log.info("参数校验失败,增值税专用发票,税号不能为空");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"增值税专用发票,税号不能为空");
        }
        if (Objects.isNull(param.getAmount()) || param.getAmount() <= 0) {
            log.info("参数校验失败,开票金额不能为0");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"开票金额不能为0");
        }
        if (Objects.isNull(param.getType()) || InvoiceTypeEnum.UNKNOWN.equals(InvoiceTypeEnum.getByCode(param.getType()))) {
            log.info("参数校验失败,发票类型不对");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"发票类型不对");
        }
        // 加锁
        int result = invoiceRequestManage.safeCreateInvoiceRequest(param, userReq);
        return result;
    }

    @Override
    public PageInfo<InvoiceRequestVO> page(InvoiceRequestQueryParam param, CurrentUserReq userReq) {
        // 参数校验
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> list = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !list.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(list);
            }
        }
        //空字符串处理
        if (StringUtils.isBlank(param.getBizNo())) {
            param.setBizNo(null);
        }
        // 分页查询开票申请
        Page<Object> page = PageMethod.startPage(param.getPageNo().intValue(), param.getPageSize());
        PageMethod.orderBy("a.gmt_update desc,a.gmt_create desc");
        List<InvoiceRequestDTO> list = invoiceRequestDAO.listByParam(param);
        if (CollectionUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        // 查询申请人信息
        Set<Integer> applicantIds = list.stream().filter(e -> Objects.nonNull(e.getApplicantId())).map(e -> e.getApplicantId().intValue()).collect(Collectors.toSet());
        Map<Integer, String> employeeNickMap = agentEmployeeService.getAgentEmployeeNickMap(applicantIds);

        list.forEach(e -> {
            e.setApplicantName(employeeNickMap.get(e.getApplicantId().intValue()));
        });
        List<InvoiceRequestVO> voList =  list.stream().map(InvoiceRequestDTO::toVO).collect(Collectors.toList());
        Map<Long, String> map = list.stream().filter(e -> StringUtils.isNotBlank(e.getInvoiceNo())).collect(Collectors.toMap(InvoiceRequestDTO::getId, InvoiceRequestDTO::getInvoiceNo));
        //脱敏数据
        Map<String, MaskingDataDTO> maskingData = this.maskingData(Long.valueOf(userReq.getUserId()),list);
        voList.forEach(e -> {
            //只有开票申请状态为完成时返回开票完成时间
            if (!InvoiceRequestStatusEnum.COMPLETED_INVOICE.getCode().equals(e.getStatus())) {
                e.setInvoiceCompletedTime(null);
            }
            String invoiceNo = map.get(e.getId());
            e.setInvoiceNo(maskingData.get(invoiceNo));
        });
        // 构建返回值
        PageInfo<InvoiceRequestVO> pageInfo = new PageInfo<>(voList);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    @Override
    public List<InvoiceRequestRecordDTO> listRequestRecordWithDetail(Long requestId){
        List<InvoiceRequestRecordDTO> recordDTOS = invoiceRequestManage.listRequestRecordWithDetail(requestId);
        //填充产品名称
        List<String> spuCodes = recordDTOS.stream().map(e -> e.getSpuCodes()).flatMap(List::stream).collect(Collectors.toList());
        Map<String, String> map = tiantaiManager.getSpuName(spuCodes);
        recordDTOS.forEach(e -> {
            e.fillProductName(map);
        });

        return recordDTOS;
    }

    @Override
    public InvoiceRequestDTO getInvoiceRequestById(Long requestId){
        InvoiceRequestDTO requestDTO = invoiceRequestManage.getInvoiceRequestById(requestId);
        return requestDTO;
    }

    @Override
    public void export(InvoiceRequestQueryParam param, CurrentUserReq userReq ) {
        if (StringUtils.isBlank(userReq.getEmail())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"操作人邮箱不存在，请联系运营或HR更新邮箱后重试");
        }
        // 权限校验 根据角色校验
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> list = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !list.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(list);
            }
        }
        //查询开票申请单, 小电可以查询所有, 非小电只能查询自己的
        List<InvoiceRequestDTO> requestDTOS = invoiceRequestDAO.listByParam(param);
        if (CollectionUtils.isEmpty(requestDTOS)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"没有要导出的数据");
        }
        //构建开票申请记录
        Set<Integer> applicantIds = new HashSet<>();
        List<InvoiceRequestDTO> list = new ArrayList<>();
        for(InvoiceRequestDTO requestDTO : requestDTOS){
            //查询开票申请记录
            List<InvoiceRequestRecordDTO> recordDTOS = invoiceRequestManage.listRequestRecordWithDetail(requestDTO.getId());
            requestDTO.setRequestRecords(recordDTOS);

            applicantIds.add(requestDTO.getApplicantId().intValue());
            list.add(requestDTO);
        }
        //填充申请人名称
        Map<Integer, String> employeeNickMap = agentEmployeeService.getAgentEmployeeNickMap(applicantIds);
        list.forEach(e -> {
            e.setApplicantName(employeeNickMap.get(e.getApplicantId().intValue()));
        });
        //填充产品名称
        List<String> spuCodes = list.stream().map(e -> e.getSpuCodes()).flatMap(List::stream).collect(Collectors.toList());
        Map<String, String> map = tiantaiManager.getSpuName(spuCodes);
        list.forEach(e -> {
            //只有开票申请状态为完成时返回开票完成时间
            if (!InvoiceRequestStatusEnum.COMPLETED_INVOICE.getCode().equals(e.getStatus())) {
                e.setInvoiceCompletedTime(null);
            }
            e.fillProductName(map);
        });

        String subject = "开票申请单-" + userReq.getUserName() + "-" + System.currentTimeMillis();
        String tmpExcelPath = tempPath + subject + ".xlsx";
        String tmpExcelPathOut = tempPath + subject + "out" + ".xlsx";

        String body = null;
        File tmpExcel = new File(tmpExcelPath);
        File tmpExcelOut = new File(tmpExcelPathOut);
        try {
            // 小电员工
            if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(userReq.getBelongSubjectId())) {
                List<ExportInvoiceRequestRecordDetailExcel> excelBeans = InvoiceRequestConverter.toRecordDetailExcelBean(list);
                body = String.format(InvoiceConstants.EXPORT_RESULT_TEMPLATE, excelBeans.size());
                excelHandler.exportToFile(excelBeans, ExportInvoiceRequestRecordDetailExcel.class, tmpExcelPath);
            }else {
                List<ExportInvoiceRequestRecordExcel> excelBeans = InvoiceRequestConverter.toRecordExcelBean(list);
                body = String.format(InvoiceConstants.EXPORT_RESULT_TEMPLATE, excelBeans.size());
                excelHandler.exportToFile(excelBeans, ExportInvoiceRequestRecordExcel.class, tmpExcelPath);
            }
        } catch (Exception e) {
            log.info("导出开票申请记录出错", e);
        }
        //发送邮件
        try {
            mailManager.sendEmail(userReq.getEmail(), subject, body, tmpExcel);
            FileEncryptorUtil.encrypt(tmpExcel,tmpExcelOut,InvoiceConstants.PASSWORD);
            String ossUrl = OssUtil.uploadFile(tmpExcelOut, OssUtil.getMimeType(InvoiceConstants.EXCEL_XLSX));
            wutaiManager.operatorLogCreate(buildOperatorLogCreateRspDTO(param,userReq,ossUrl));
        } catch (Exception e) {
            log.error("------ send mail error, to = {},e={}", userReq.getEmail(), e);
            throw BizException.create(BaseErrorCodeEnum.FALLBACK,"导出文件出现异常");
        }finally {
            // 删除临时文件
            if (tmpExcel.exists()) {
                tmpExcel.delete();
            }
            if (tmpExcelOut.exists()) {
                tmpExcelOut.delete();
            }
        }
        log.info("导出开票申请单成功。user:{},条数:{}", userReq, list.size());
    }

    private OperatorLogCreateRspDTO buildOperatorLogCreateRspDTO(InvoiceRequestQueryParam param, CurrentUserReq user, String ossUrl){
        OperatorLogCreateRspDTO operatorLogCreateRspDTO = new OperatorLogCreateRspDTO();
        operatorLogCreateRspDTO.setBizSystem("invoice");
        operatorLogCreateRspDTO.setBizType(LogBizTypeEnum.EXPORT.code());
        operatorLogCreateRspDTO.setBizName("开票申请列表");
        operatorLogCreateRspDTO.setOperatorId(Long.valueOf(user.getUserId()));
        operatorLogCreateRspDTO.setOperatorRole(user.getCurrentRole());
        operatorLogCreateRspDTO.setOperatorTime(new Date());
        operatorLogCreateRspDTO.setOperatorDetail(JSONObject.toJSONString(param));
        operatorLogCreateRspDTO.setFileUrl(ossUrl);
        operatorLogCreateRspDTO.setDesensitize(YesOrNoEnum.YSE.code());
        return operatorLogCreateRspDTO;
    }

    /**
     * 下载批量审核结果
     *
     */
    @Override
    public void downloadAuditResult(String key) {

        RBucket<String> keyObject = redisson.getBucket(key);
        if (Objects.isNull(keyObject)) {
            return ;
        }
        List<InvoiceImportDTO> invoiceImportDTOS = JSON.parseObject(keyObject.get(),  new TypeReference<List<InvoiceImportDTO>>() {});
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        try {
            EasyExcelUtils.exportExcel(response, invoiceImportDTOS,InvoiceImportDTO.class,"批量审核失败明细");
        } catch (Exception e) {
            log.info("导出开票申请记录出错", e);
        }
    }


    @Override
    public BatchAuditVO batchAudit(MultipartFile file, int status, String financeFeedback) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();

        InvoiceRequestStatusEnum statusEnum =InvoiceRequestStatusEnum.getByCode(status);
        if (statusEnum == null) {
            log.info("批量审核失败, 非法状态, status={}", status);
            throw BizException.create(InvoiceCodeEnum.BATCH_AUDIT_ERROR, "批量审核失败, 非法状态");
        }
        // 白名单控制 只开放给西窗
        if (CollectionUtils.isEmpty(auditUserIds) || !auditUserIds.contains(userReq.getUserId().longValue())) {
            log.info("批量审核失败, 非白名单用户, userId={},auditUserIds:{}", userReq.getUserId(),auditUserIds);
            throw BizException.create(InvoiceCodeEnum.BATCH_AUDIT_ERROR);
        }

        InvoiceImportListener importListener = new InvoiceImportListener(this, status, financeFeedback);
        try {
            EasyExcel.read(file.getInputStream(), InvoiceImportDTO.class, importListener).sheet().doRead();
        } catch (Exception e) {
            log.info("导入开票申请记录出错", e);
        }
        List<InvoiceImportDTO> failedData = importListener.getFailedData();

        String value = JSON.toJSONString(failedData);

        String key = BATCH_AUDIT_KEY_PREFIX + "_" + userReq.getUserId() + "_" + System.currentTimeMillis();
        // 缓存结果
        RBucket<String> keyObject = redissonClient.getBucket(key);
        keyObject.set(value);

        BatchAuditVO batchAuditVO = new BatchAuditVO();
        batchAuditVO.setKey(key);
        batchAuditVO.setTotal(importListener.getAllDataSize());
        batchAuditVO.setSuccessCount(failedData.size());
        batchAuditVO.setFailCount(failedData.size());
        batchAuditVO.setSuccessCount(importListener.getAllDataSize() - failedData.size());

        return batchAuditVO;
    }


    public int batchAudit(List<InvoiceImportDTO> importDTOS, int status, String reason) {
        List<Long> ids = importDTOS.stream().map(e -> e.getRequestId()).collect(Collectors.toList());
        int result = invoiceRequestManage.batchApprove(ids, status, reason);
        return result;
    }

    private Map<String, MaskingDataDTO> maskingData(Long userId,List<InvoiceRequestDTO> list){
        List<Pair<String, String>> pairs = list.stream().filter(e -> StringUtils.isNotEmpty(e.getInvoiceNo())).map(e -> Pair.of(MaskingDataTypeEnum.社会信用证代码.getCode(),e.getInvoiceNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pairs)) {
            return new HashMap<>();
        }
        // 脱敏
        Map<String, MaskingDataDTO> maskingDataDTOMap = astraManager.encrypts(userId, pairs);
        return maskingDataDTOMap;
    }

    @Override
    public List<Long> whiteList() {
        // 白名单控制 只开放给西窗
        if (CollectionUtils.isEmpty(auditUserIds)) {
            return new ArrayList<>();
        }
        return auditUserIds;
    }
}
