package so.dian.invoice.service;


import so.dian.himalaya.common.entity.PageData;
import so.dian.invoice.pojo.param.*;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailPageVO;
import so.dian.invoice.pojo.vo.SupplierInvoicePageVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 供应商发票主表
 *
 * <AUTHOR>
 * @date 2021-03-17 14:03:28
 */
public interface SupplierInvoiceService {

    PageData<SupplierInvoicePageVO> findPageByReq(SupplierInvoicePageParam param, Set<String> supplierNos);

    void deleteInvoice(CurrentUserReq userReq, SupplierInvoiceDeleteParam param);

    void deleteAttachment(CurrentUserReq userReq, SupplierInvoiceAttachmentDeleteParam param);

    void addInvoicePicture(CurrentUserReq userReq, SupplierInvoiceAttachmentAddParam param);

    List<SupplierInvoiceDetailPageVO> findDetailListByReq(SupplierInvoiceDetailPageParam param, Set<String> supplierNos);

    Long excelExport(CurrentUserReq userReq, SupplierInvoicePageParam param, Set<String> supplierNos);

    void excelTemplate(HttpServletResponse response);

    String excelImport(CurrentUserReq userReq, List<SupplierInvoiceImportParam> paramList, Set<String> supplierNos);

    void transferInvoice(CurrentUserReq userReq, Integer supplierInvoiceId);
}
