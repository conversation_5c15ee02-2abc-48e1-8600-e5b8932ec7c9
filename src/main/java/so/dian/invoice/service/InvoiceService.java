package so.dian.invoice.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Multimap;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Workbook;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.dto.*;
import so.dian.invoice.pojo.entity.*;
import so.dian.invoice.pojo.param.*;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;
import so.dian.invoice.pojo.vo.InvoiceVO;
import so.dian.invoice.util.PageData;
import so.dian.invoice.util.ServiceResult;

public interface InvoiceService {

    void downLoadInvoiceDetail(InvoiceParam param, HttpServletRequest request, HttpServletResponse response);

    void addWithDeductInvoice(InvoiceDto dto);

    Set<String> addInvoiceBatch(Integer userId, InvoiceBatchParam dto, List<String> noList);

    InvoiceDO getInvoiceById(Integer id);

    InvoiceDO getInvoiceByInvoiceCodeAndNo(String invoiceCode, String invoiceNo);

    List<InvoiceDO> getInvoiceByInvoiceNoList(List<String> invoiceNoList);

    int insertInvoiceList(List<InvoiceDO> invoiceList);

    int updateInvoice(InvoiceDO invoice);

    int batchUpdateInvoiceProcessStatus(InvoiceProcessStatusEnum invoiceProcessStatusEnum,List<Long> invoiceIds);

    Boolean deleteInvoiceByInvoiceCodeAndNo(InvoiceObsoleteParam param);

    List<InvoiceDO> getInvoiceList(InvoiceParam param);

    List<InvoiceDto> getInvoicePage(InvoiceParam param);

    int count(InvoiceParam param);

    String uploadFile(String filePathName, String file);

    void downLoadFile(HttpServletRequest request, HttpServletResponse response, String fileType,
                      String filePathName, String downFileName);

    List<InvoiceDetailDO> getInvoiceDetailByInvoiceCodeAndNo(String invoiceCode, String invoiceNo);

    void saveInvoiceDetail(String invoiceCode, String invoiceNo, List<InvoiceDetailDto> invoiceDetailDtoList);

    void saveInvoiceDetail(String invoiceCode, String invoiceNo, List<InvoiceDetailDto> invoiceDetailDtoList,
                           String oldInvoiceCode, String oldInvoiceNo);

    int insertInvoiceDetailBatch(List<InvoiceDetailDO> invoiceDetailDOList);

    int updateInvoiceDetailList(List<InvoiceDetailDO> invoiceDetailDOList);

    JSONObject parse(List<List<String>> excelData, List<InvoiceDO> invoiceDOList,
                     List<InvoiceDetailDO> invoiceDetailDOList, Integer userId,
                     Multimap<String, Integer> invoiceNoMultimap);

    JSONObject parseErrorMsg(String filePathName, Workbook workbook, List<InvoiceDO> invoiceDOList,
                             JSONObject res, Multimap<String, Integer> invoiceNoMultimap);

    void deleteInvoiceFile(String filePathName);

    void writeInvoiceFile(String filePathName, Workbook workbook);

    /**
     * 自动新增发票(批量)
     * @param params
     */
    String autoAddBatch(InvoiceValidateParams params);

    ServiceResult<String> autoInsertInvoice(InvoiceValidateParams params, InvoiceIdentifyRecordDO identifyRecordDO);
    /**
     * 自动新增发票(单张)
     * @param params
     * @return
     */
    ServiceResult<String> autoAddBatchSingle(InvoiceValidateParams params);

    /**
     * 发票主体对应关系
     * @param param
     * @return
     */
    ServiceResult<Boolean> getSubjectRelation(InvoiceSubjectRelationParam param);

    /**
     * 获取发票主体列表
     */
    List<BuyerDTO> getBuyerList(BuyerParam buyerParam);

    /**
     * 获取发票归属信息
     *
     * @param userId
     * @return
     */
    AgentDTO getInvoiceBelongInfo(Integer userId);

    /**
     * 根据关键字前置模糊匹配查询提现主体列表
     *
     * @param keyword
     * @return
     */
    List<String> queryWithdrawSubjectNameList(String keyword);

    /**
     * 校验发票归属人
     *
     * @param invoiceDO
     * @param userId
     * @return
     */
    void checkInvoiceBelong(InvoiceDO invoiceDO, Integer userId);

    /**
     * 发票详情列表查询
     *
     * @param param
     * @return
     */
    List<InvoiceInfoVO> getInvoiceInfoList(InvoiceParam param);

    /**
     * 发票复核
     * @param param
     * @return
     */
    BizResult<Boolean> invoiceReview(InvoiceReviewParam param);

    /**
     * 发票列表导出
     * @param param
     * @return
     */
    BizResult exportInvoiceList(InvoiceExportParam param);

    /**
     * 通过销售方名称模糊搜索
     * @param param
     * @return
     */
    BizResult<List<String>> listSubjectName(InvoiceSubjectInfoParam param);

    /**
     * 小二端分页获取发票列表
     * @param param
     * @param loginUserId
     * @return
     */
    BizResult<PageData<InvoiceVO>> pageInvoiceList(InvoiceFilterTimeParam param, CurrentUserReq currentUserReq);

    /**
     * 新增单张发票
     * @param param
     * @param recordDO
     * @param comparedResult
     * @return
     */
    BizResult<Boolean> addSingleInvoice(AddSingleInvoiceParam param, InvoiceIdentifyRecordDO recordDO, Boolean comparedResult);

    /**
     * 根据条件查询未核销完的发票列表
     */
    List<InvoiceInfoVO> findByInvoiceDeductQueryParam(InvoiceDeductQueryParam param, Long agentId);

    /**
     * 发票主体订正
     */
    BizResult<Integer> trimInvoice(Long invoiceId, Integer bizType,Integer lastId);

    void invoiceValidate(InvoiceBO validateInvoiceBO, InvoiceValidateStatusDO statusDOForInsert);

    /**
     * 发票验真历史数据处理
     */
    void processValidateData(Date startTime, Date endTime);


    List<Long> listInvoiceIdsByExpressNo(String expressNo);

    /**
     * 填充业务编号
     */
    void fillBusinessNo(InvoiceDto dto);

    /**
     * 获取发票核销的编号
     */
    List<String> getBusinessNoListByInvoiceCodeAndNo(String invoiceCode, String invoiceNo, List<String> businessNoList, Integer operateType);

    /**
     * 获取业务类型列表
     * @param roleEnum
     * @param userId
     * @param subjectTypeParam
     * @return
     */
    List<KeyValueDto> getSubjectTypeList(UserRoleEnum roleEnum, Integer userId, SubjectTypeParam subjectTypeParam);

    InvoiceDO selectInvoiceByInvoiceCodeAndNo(String invoiceCode, String invoiceNo);

    void saveInvoiceAndDetail(InvoiceDO invoiceDO, List<InvoiceDetailDO> invoiceDetailDOList);
}
