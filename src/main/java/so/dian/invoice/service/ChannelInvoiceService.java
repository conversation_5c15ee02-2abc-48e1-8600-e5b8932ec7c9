package so.dian.invoice.service;

import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.invoice.client.manager.AreaManager;
import so.dian.invoice.converter.ChannelInvoiceConvert;
import so.dian.invoice.dao.ChannelInvoiceDao;
import so.dian.invoice.pojo.dtos.ChannelInvoiceDTO;
import so.dian.invoice.pojo.entity.ChannelInvoiceDO;
import so.dian.invoice.pojo.param.AddChannelInvoiceParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.ChannelInvoiceVO;
import so.dian.virgo.client.dto.AreaInfoDTO;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2022/10/28 14:17
 * @description:
 */
@Service
public class ChannelInvoiceService {

    @Autowired
    private ChannelInvoiceDao channelInvoiceDao;

    @Autowired
    private AreaManager areaManager;

    private final ChannelInvoiceConvert convert = new ChannelInvoiceConvert();

    public void add(AddChannelInvoiceParam param, CurrentUserReq userReq) {

        if (Objects.isNull(param.getId())) {
            ChannelInvoiceDO channelInvoiceDO = channelInvoiceDao.selectBySettleSubjectId(userReq.getBelongSubjectId().longValue());
            if (!Objects.isNull(channelInvoiceDO)) {
                param.setId(channelInvoiceDO.getId());
            }
        }

        ChannelInvoiceDO invoiceDO = convert.from(param);
        invoiceDO.setSettleSubjectId(userReq.getBelongSubjectId().longValue());
        invoiceDO.setSettleSubjectType(userReq.getBelongSubjectType());
        invoiceDO.setCreator(userReq.getUserId().longValue());
        if (Objects.isNull(invoiceDO.getId())) {
            invoiceDO.init();
            channelInvoiceDao.insertSelective(invoiceDO);
        }else {
            invoiceDO.setGmtUpdate(new Date().getTime());
            channelInvoiceDao.updateByPrimaryKeySelective(invoiceDO);
        }
    }

    public ChannelInvoiceVO find(@NonNull Long settleSubjectId) {
        ChannelInvoiceDO channelInvoiceDO = channelInvoiceDao.selectBySettleSubjectId(settleSubjectId);
        return Optional.ofNullable(channelInvoiceDO).map(convert::from).orElse(null);
    }

    public ChannelInvoiceDTO get(@NonNull Long settleSubjectId) {
        ChannelInvoiceDO channelInvoiceDO = channelInvoiceDao.selectBySettleSubjectId(settleSubjectId);
        if (Objects.isNull(channelInvoiceDO)) {
            return null;
        }

        AreaInfoDTO province = areaManager.get(channelInvoiceDO.getProvinceCode());
        AreaInfoDTO city = areaManager.get(channelInvoiceDO.getCityCode());
        AreaInfoDTO area = areaManager.get(channelInvoiceDO.getAreaCode());

        ChannelInvoiceDTO invoiceDTO = new ChannelInvoiceDTO();
        invoiceDTO.setTitle(channelInvoiceDO.getTitle());
        invoiceDTO.setTaxNo(channelInvoiceDO.getTaxNo());
        invoiceDTO.setAddressDetail(channelInvoiceDO.getAddressDetail());
        invoiceDTO.setProvinceName(Optional.ofNullable(province).map(AreaInfoDTO::getName).orElse(null));
        invoiceDTO.setCityName(Optional.ofNullable(city).map(AreaInfoDTO::getName).orElse(null));
        invoiceDTO.setAreaName(Optional.ofNullable(area).map(AreaInfoDTO::getName).orElse(null));
        invoiceDTO.setReceiver(channelInvoiceDO.getReceiver());
        invoiceDTO.setReceiverPhone(channelInvoiceDO.getReceiverPhone());
        return invoiceDTO;
    }
}
