package so.dian.invoice.service;

import org.springframework.stereotype.Service;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.invoice.manager.AgentManager;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.BelongSubjectVO;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2022/10/28 16:17
 * @description:
 */
@Service
public class CommonService {

    @Resource
    private AgentManager agentManager;

    public BelongSubjectVO get(CurrentUserReq userReq) {
        AgentDTO agentDTO = agentManager.findById(userReq.getBelongSubjectId());

        BelongSubjectVO subjectVO = new BelongSubjectVO();
        subjectVO.setId(userReq.getBelongSubjectId().longValue());
        subjectVO.setType(userReq.getBelongSubjectType());
        subjectVO.setSubType(Optional.ofNullable(agentDTO).map(AgentDTO::getSubType).orElse(null));
        return subjectVO;
    }
}
