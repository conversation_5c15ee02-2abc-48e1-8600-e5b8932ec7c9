package so.dian.invoice.service.message.handle;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import so.dian.fis.credit.dto.response.CreditMessageDTO;
import so.dian.fis.credit.dto.response.RepaySuccessMessageDetailDTO;
import so.dian.invoice.enums.BizTypeEnum;
import so.dian.invoice.manager.invoice.manage.InvoiceChangeRecordManager;
import so.dian.invoice.manager.invoice.manage.InvoiceManageManager;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecord;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper.AbstractChangeWrapper;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper.RepaySuccessChangeWrapper;
import so.dian.invoice.service.message.AbstractMessageHandle;
import so.dian.invoice.service.message.context.MessageContext;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-20 10:45
 */
@Slf4j
@Component
public class GrantRepaymentSuccessHandle extends AbstractMessageHandle {

    @Resource
    private InvoiceChangeRecordManager invoiceChangeRecordManager;
    @Resource
    private InvoiceManageManager invoiceManageManager;

    @Override
    public Class getCode() {
        return CreditMessageDTO.class;
    }

    @Override
    protected boolean needHandle(MessageContext message) {
        boolean b = this.getCode().isAssignableFrom(message.getMessage().getClass());
        boolean result = b;
        if (b) {
            CreditMessageDTO creditMessageDTO = (CreditMessageDTO) message.getMessage();
            result = b && creditMessageDTO.isRepay();
        }
        return result;
    }

    @Override
    public void doHandle(MessageContext message) {
        CreditMessageDTO creditMessageDTO = (CreditMessageDTO) message.getMessage();

        log.info("开始处理设备采购单还款成功消息,newsId:{}, applyId:{}, outBizType:{}", message.getNewsID(), creditMessageDTO.getApplyId(), message.getOutBizType());
        repaySuccessHandle(creditMessageDTO);
        log.info("处理设备采购单还款成功消息成功,newsId:{}, applyId:{}, outBizType:{}", message.getNewsID(), creditMessageDTO.getApplyId(), message.getOutBizType());

    }


    private void repaySuccessHandle(CreditMessageDTO creditMessageDTO){
//        if (CollectionUtils.isEmpty(creditMessageDTO.getDetailDTOS())) {
//            log.warn("还款明细不存在，applyId:{}, bizType:{}", creditMessageDTO.getApplyId(), BizTypeEnum.PURCHASE_ORDER.getCode());
//            return;
//        }
//
//        CreditMessageDTO.Type type = CreditMessageDTO.Type.getByCode(creditMessageDTO.getType());
//        if (Objects.isNull(type) || CreditMessageDTO.Type.UNKNOWN.equals(type)) {
//            log.warn("CreditMessageDTO.Type is null,type:{}", creditMessageDTO.getType());
//            return;
//        }
        for(Object object : creditMessageDTO.getDetailDTOS()){
            String jsonStr = JSON.toJSONString(object);
            RepaySuccessMessageDetailDTO messageDetailDTO = JSON.parseObject(jsonStr,RepaySuccessMessageDetailDTO.class);
            log.info("处理设备采购单还款成功明细消息,recordDetailId:{}", messageDetailDTO.getRecordDetailId());
            if (Objects.isNull(messageDetailDTO)) {
                log.warn("消息明细 messageDetailDTO is null, applyId:{}", creditMessageDTO.getApplyId());
                continue;
            }
            InvoiceManageDTO invoiceManageDTO = invoiceManageManager.getByBizNoAndBizType(messageDetailDTO.getBizOrderNo(), BizTypeEnum.PURCHASE_ORDER.getCode());
            if (Objects.isNull(invoiceManageDTO)) {
                log.warn("设备采购单不存在，bizNo:{}, bizType:{}", messageDetailDTO.getBizOrderNo(), BizTypeEnum.PURCHASE_ORDER.getCode());
                continue;
            }
            AbstractChangeWrapper<RepaySuccessMessageDetailDTO> changeWrapper = new RepaySuccessChangeWrapper(messageDetailDTO, invoiceManageDTO)
                    .calSpuChangeAmount();
            InvoiceChangeRecord changeRecordAgg = changeWrapper.getInvoiceChangeRecord();

            InvoiceChangeRecordDTO invoiceChangeRecordDTO = invoiceChangeRecordManager.getByBizNoAndBizType(String.valueOf(changeRecordAgg.getOutBizId()), changeRecordAgg.getOutBizType());
            if (Objects.nonNull(invoiceChangeRecordDTO)) {
                log.warn("该变更记录已处理，changeRecord_outBizId:{}, changeRecord_outBizType:{}", invoiceChangeRecordDTO.getOutBizId(), invoiceChangeRecordDTO.getOutBizType());
                continue;
            }

            InvoiceChangeRecordDTO changeRecordDTO = changeRecordAgg.toDTO();
            InvoiceManageDTO manageDTO = changeWrapper.buildInvoiceManageDTO();
            // 计算本次还款金额，按产品比例分摊到每个产品上的金额。
            // 比例计算公式：还款金额*（该产品类型的业务单金额/业务单总金额）
            //生成变更记录
            batchUpdate(manageDTO,changeRecordDTO);
        }
    }

    /**
     * 对于授信还款、还款退款消息，可能会出现一个申请中有多个授信还款计划明细id，其中有部分成功，部分失败，
     * 授信还款校验，由于变更记录是以授信还款计划明细为维度，而授信消息体中的是以申请ID为维度，一个申请ID可以对应多个授信还款计划明细id，
     * 需要处理这种情况，所以这个方法需要重写，直接返回true，在repaySuccessHandle有校验逻辑。
     * @param message
     * @return
     */
    protected boolean validate(MessageContext message){
        log.info("开始授信还款业务处理前的校验,newsId:{}, outBizId:{},outBizType:{}", message.getNewsID(), message.getOutBizId(), message.getOutBizType());
        CreditMessageDTO creditMessageDTO = (CreditMessageDTO) message.getMessage();
        if (CollectionUtils.isEmpty(creditMessageDTO.getDetailDTOS())) {
            log.warn("还款明细不存在，applyId:{}, bizType:{}", creditMessageDTO.getApplyId(), BizTypeEnum.PURCHASE_ORDER.getCode());
            return false;
        }
        CreditMessageDTO.Type type = CreditMessageDTO.Type.getByCode(creditMessageDTO.getType());
        if (Objects.isNull(type) || CreditMessageDTO.Type.UNKNOWN.equals(type)) {
            log.warn("CreditMessageDTO.Type is null,type:{}", creditMessageDTO.getType());
            return false;
        }
        return true;
    }
}
