package so.dian.invoice.service.message.handle;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.invoice.dao.InvoiceManageDAO;
import so.dian.invoice.enums.OutBizTypeEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecord;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper.AbstractChangeWrapper;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper.ChannelRepairChangeWrapper;
import so.dian.invoice.pojo.entity.InvoiceManageDO;
import so.dian.invoice.service.message.AbstractMessageHandle;
import so.dian.invoice.service.message.context.MessageContext;
import so.dian.songshan.client.pojo.dto.ChannelRepairChangeDTO;
import so.dian.songshan.client.pojo.enums.ChannelRepairStatusEnum;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @program: invoice
 * @description: 维修单mq消息处理
 * @author: yuechuan
 * @create: 2025-03-19 16:15
 */
@Slf4j
@Component
public class ChannelRepairChangeHandle extends AbstractMessageHandle {

    @Resource
    private InvoiceManageDAO invoiceManageDAO;

    @Override
    public Class getCode() {
	return ChannelRepairChangeDTO.class;
    }
    @Override
    protected boolean needHandle(MessageContext message) {
	return this.getCode().isAssignableFrom(message.getMessage().getClass());
    }

    /**
     * 处理授维修订单，
     * 1.维修单支付完成，且金额大于0，则更新业务单的应开金额，维修单如果在质保期只维修单金额为0；
     * 2.维修单支付完成后，无退款业务。
     * 基于以上两点，这里只考虑新增，
     * 并更新业务单的待开金额记录的变更记录，
     * 维修单需要判断是否还有待开金额，如果有待开金额小于0，则发送告警短信给业务员
     *
     * @param message 维修单消息对象
     */
    @Override
    public void doHandle(MessageContext message) {
	ChannelRepairChangeDTO channelRepairChangeDTO = (ChannelRepairChangeDTO)message.getMessage();
	log.info("收到维修单支付完成消息,newsId:{}, bizNo:{},status:{},payAmount:{}", message.getNewsID(), channelRepairChangeDTO.getRepairNo(), channelRepairChangeDTO.getStatus(), channelRepairChangeDTO.getPayAmount());
	handleRepairMessage(channelRepairChangeDTO);
	log.info("处理维修单支付完成消息成功,newsId:{}, bizNo:{}, bizType:{}", message.getNewsID(), channelRepairChangeDTO.getRepairNo(), OutBizTypeEnum.REPAIR_ORDER_COMPLETE.getCode());

    }

    private void handleRepairMessage(ChannelRepairChangeDTO channelRepairChangeDTO) {

	AbstractChangeWrapper<ChannelRepairChangeDTO> changeWrapper = new ChannelRepairChangeWrapper(channelRepairChangeDTO).calSpuChangeAmount();
	InvoiceChangeRecord changeRecordAgg = changeWrapper.getInvoiceChangeRecord();
	InvoiceChangeRecordDTO changeRecordDTO = changeRecordAgg.toDTO();
	InvoiceManageDTO manageDTO = changeWrapper.buildInvoiceManageDTO();

	manageDTO.setSubjectType(getAgentType(manageDTO.getSubjectId()));

	batchSave(manageDTO, changeRecordDTO);
    }

    /**
     * 业务处理前的校验
     * @param message
     */
    protected boolean validate(MessageContext message){
	log.info("开始维修单业务处理前的校验,newsId:{}, outBizId:{},outBizType:{}", message.getNewsID(), message.getOutBizId(), message.getOutBizType());
	//通过bizNo和bizType查询变更记录，如果存在则说明此条消息已处理
	InvoiceChangeRecordDTO invoiceChangeRecordDTO = invoiceChangeRecordManager.getByBizNoAndBizType(message.getOutBizId(), message.getOutBizType());
	if (Objects.nonNull(invoiceChangeRecordDTO)) {
	    log.warn("该变更记录已处理，outBizId:{}, outBizType:{}", invoiceChangeRecordDTO.getOutBizId(), invoiceChangeRecordDTO.getOutBizType());
	    return false;
	}
	ChannelRepairChangeDTO channelRepairChangeDTO = (ChannelRepairChangeDTO)message.getMessage();
	log.info("收到维修单支付完成消息,newsId:{}, bizNo:{},status:{},payAmount:{}", message.getNewsID(), channelRepairChangeDTO.getRepairNo(), channelRepairChangeDTO.getStatus(), channelRepairChangeDTO.getPayAmount());
	InvoiceManageDO invoiceManageDO = invoiceManageDAO.getByBizNoAndBizType(channelRepairChangeDTO.getRepairNo(), OutBizTypeEnum.REPAIR_ORDER_COMPLETE.getCode());
	if(Objects.nonNull(invoiceManageDO)){
	    log.warn("系统存在此笔维修单，不进行后续处理,newsId:{},  bizNo:{}, bizType:{}",  message.getNewsID(), channelRepairChangeDTO.getRepairNo(), OutBizTypeEnum.REPAIR_ORDER_COMPLETE.getCode());
	    return false;
	}
	// 维修单已完结，且金额大于0
	if (!ChannelRepairStatusEnum.COMPETED.getCode().equals(channelRepairChangeDTO.getStatus())
		|| Objects.isNull(channelRepairChangeDTO.getPayAmount()) || channelRepairChangeDTO.getPayAmount() <= 0) {
	    log.warn("维修未完结或维修金额为0，不处理，newsId:{}, bizNo:{},status:{},payAmount:{}", message.getNewsID(), channelRepairChangeDTO.getRepairNo(), channelRepairChangeDTO.getStatus(), channelRepairChangeDTO.getPayAmount());
	    return false;
	}
	// 现在业务类型，只处理合资 或 代理商的数据, AgentTypeEnum
	// AGENT_TYPE(0, "代理商"),JV_COMPANY_TYPE(5, "合资公司");
//	Integer agentType = this.getAgentType(channelRepairChangeDTO.getAgentId().longValue());
//	if (Objects.isNull(agentType) || !BIZ_TYPES.contains(agentType)) {
//	    log.warn("该业务类型不处理，newsId:{},bizNo:{},agentId:{},agentType:{}", message.getNewsID(),channelRepairChangeDTO.getRepairNo(), channelRepairChangeDTO.getAgentId(), agentType);
//	    // todo 告警
//	    return false;
//	}
	return true;
    }
}
