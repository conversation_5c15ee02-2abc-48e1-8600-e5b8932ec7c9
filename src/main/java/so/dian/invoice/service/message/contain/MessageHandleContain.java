package so.dian.invoice.service.message.contain;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.manager.message.snapshot.InvoiceNewsVoucherManager;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;
import so.dian.invoice.service.message.MessageHandle;
import so.dian.invoice.service.message.context.MessageContext;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-19 18:59
 */
@Slf4j
@Component
public class MessageHandleContain implements ApplicationContextAware {

    private List<MessageHandle> handles;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private InvoiceNewsVoucherManager invoiceNewsVoucherManager;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
	this.applicationContext = applicationContext;
    }

    @PostConstruct
    private void init() {
	handles = this.applicationContext.getBeansOfType(MessageHandle.class)
		.values()
		.stream()
		.collect(Collectors.toList());
    }

    public void execute(MessageContext context){
	if(CollectionUtils.isEmpty(handles)){
	    log.info("未找到消息处理器");
	    return;
	}
	//查询消息快照
	InvoiceNewsVoucherDTO voucherDTO = invoiceNewsVoucherManager.getById(context.getNewsID());
	try {
	    voucherDTO.process();
	    if (Objects.isNull(voucherDTO)) {
		log.warn("消息快照为空.newsId:{}", context.getNewsID());
		throw BizException.create(InvoiceCodeEnum.EVENT_EXECUTE_ERROR,"消息快照不存在：newsId："+context.getNewsID());
	    }
	    if (Objects.isNull(voucherDTO.getOutBizId())) {
		log.warn("消息快照OutBizId为空, newsId:{}", context.getNewsID());
		throw BizException.create(InvoiceCodeEnum.EVENT_EXECUTE_ERROR,"消息快照外部业务ID为空：newsId："+context.getNewsID());
	    }
	    if (Objects.isNull(voucherDTO.getOutBizType())) {
		log.warn("消息快照OutBizType为空, newsId:{}", context.getNewsID());
		throw BizException.create(InvoiceCodeEnum.EVENT_EXECUTE_ERROR,"消息快照外部业务类型为空：newsId："+context.getNewsID());
	    }
	    if (voucherDTO.isSuccess()) {
		log.warn("消息快照已被处理, newsId:{}", context.getNewsID());
	    	return;
	    }
	    //处理器处理
	    handles.forEach(e -> {
		e.handle(context);
	    });
	    voucherDTO.success();
	}catch (Exception e){
	    voucherDTO.fail();
	    log.warn("消息处理失败",e);
//	    throw BizException.create(InvoiceCodeEnum.EVENT_EXECUTE_ERROR,"消息处理失败");
	}finally {
	    if (Objects.nonNull(voucherDTO)) {
		invoiceNewsVoucherManager.update(voucherDTO);
	    }
	}

    }
}
