package so.dian.invoice.service.message;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.configuration.message.DingtalkMessageManager;
import so.dian.invoice.enums.AgentTypeEnum;
import so.dian.invoice.enums.CacheEnum;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.manager.AgentManager;
import so.dian.invoice.manager.invoice.manage.InvoiceChangeRecordManager;
import so.dian.invoice.manager.invoice.manage.InvoiceManageManager;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.service.message.context.MessageContext;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-19 16:14
 */
@Slf4j
public abstract class AbstractMessageHandle implements MessageHandle{

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    protected InvoiceManageManager invoiceManageManager;

    @Resource
    protected InvoiceChangeRecordManager invoiceChangeRecordManager;

    @Resource
    protected DingtalkMessageManager messageManager;

    @Resource
    protected AgentManager agentManager;

    protected final static List<Integer> BIZ_TYPES = Lists.newArrayList(AgentTypeEnum.AGENT_TYPE.getId(), AgentTypeEnum.JV_COMPANY_TYPE.getId());


    public void handle(MessageContext message) {
	if (this.needHandle(message)) {
	    CacheEnum lockEnum = CacheEnum.INVOICE_MANAGE_LOCK;
	    String lockKey = lockEnum + ":" + message.getOutBizId() + ":" + message.getOutBizType();
	    RLock lock = redissonClient.getLock(lockKey);
	    try {
		if (!lock.tryLock(lockEnum.getExpire(), lockEnum.getUnit())) {
		    return;
		}
		//校验是通过
		boolean b = this.validate(message);
		if (!b) {
		    return;
		}
		//执行业务逻辑
		this.doHandle(message);
	    } catch (Exception e) {
		log.error("news voucher execute error.", e);
		throw BizException.create(InvoiceCodeEnum.EVENT_EXECUTE_ERROR, e.getMessage());
	    } finally {
		if (lock.isLocked() && lock.isHeldByCurrentThread()) {
		    lock.unlock();
		}
	    }
	}
    }

    protected abstract boolean needHandle(MessageContext message);

    public abstract void doHandle(MessageContext message);

    /**
     * 业务处理前的校验
     * @param message
     */
    protected abstract boolean validate(MessageContext message);


    protected Integer getAgentType(Long agentId){
	// 获取主体信息
	AgentDTO agentDO = agentManager.findById(agentId.intValue());
	if (agentDO == null) {
	    log.info("获取代理商信息失败，agentId:{}", agentId);
	    return null;
	}
	return agentDO.getType();
    }

    @Transactional
    public void batchSave(InvoiceManageDTO invoiceManageDTO, InvoiceChangeRecordDTO invoiceChangeRecordDTO){

	Map<String, Long> productsCodeIds = new HashMap<>();
	invoiceManageManager.save(invoiceManageDTO);
	invoiceChangeRecordDTO.setManageId(invoiceManageDTO.getId());
	invoiceManageDTO.getManageDetailDTOS().forEach(manageDetailDTO -> {
	    productsCodeIds.put(manageDetailDTO.getProductCode(), manageDetailDTO.getId());
	});
	invoiceChangeRecordDTO.getChangeRecordDetailDTOList().forEach(changeRecordDetailDTO -> {
	    changeRecordDetailDTO.setManageId(invoiceManageDTO.getId());
	    changeRecordDetailDTO.setManageDetailId(productsCodeIds.get(changeRecordDetailDTO.getProductCode()));
	});
	invoiceChangeRecordManager.save(invoiceChangeRecordDTO);
    }


    @Transactional
    public void batchUpdate(InvoiceManageDTO invoiceManageDTO, InvoiceChangeRecordDTO invoiceChangeRecordDTO){

	Map<String, Long> productsCodeIds = new HashMap<>();
	invoiceManageManager.update(invoiceManageDTO);
	invoiceChangeRecordDTO.setManageId(invoiceManageDTO.getId());
	invoiceManageDTO.getManageDetailDTOS().forEach(manageDetailDTO -> {
	    productsCodeIds.put(manageDetailDTO.getProductCode(), manageDetailDTO.getId());
	});
	invoiceChangeRecordDTO.getChangeRecordDetailDTOList().forEach(changeRecordDetailDTO -> {
	    changeRecordDetailDTO.setManageId(invoiceManageDTO.getId());
	    changeRecordDetailDTO.setManageDetailId(productsCodeIds.get(changeRecordDetailDTO.getProductCode()));
	});
	invoiceChangeRecordManager.save(invoiceChangeRecordDTO);
    }
}
