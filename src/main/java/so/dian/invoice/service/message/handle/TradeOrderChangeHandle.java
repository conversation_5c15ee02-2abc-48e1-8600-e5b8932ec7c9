package so.dian.invoice.service.message.handle;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.enums.BizTypeEnum;
import so.dian.invoice.enums.ChangeTypeEnum;
import so.dian.invoice.enums.OutBizTypeEnum;
import so.dian.invoice.enums.PayChannel;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.facade.TradeV5Facade;
import so.dian.invoice.manager.invoice.manage.InvoiceManageManager;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceManage;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecord;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper.AbstractChangeWrapper;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper.TradeOrderChangeWrapper;
import so.dian.invoice.service.message.AbstractMessageHandle;
import so.dian.invoice.service.message.context.MessageContext;
import so.dian.newyork.center.client.v5.resp.TradeOrderChannelRelationV5Resp;
import so.dian.newyork.center.client.v5.resp.TradeOrderQueryV5Resp;
import so.dian.taishan.client.pojo.mq.TradeOrderChangeDTO;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @program: invoice
 * @description: 维修单mq消息处理
 * @author: yuechuan
 * @create: 2025-03-19 16:15
 */
@Slf4j
@Component
public class TradeOrderChangeHandle extends AbstractMessageHandle {

    @Resource
    private InvoiceManageManager invoiceManageManager;
    @Resource
    private TradeV5Facade tradeV5Facade;

    @Override
    public Class getCode() {
	return TradeOrderChangeDTO.class;
    }

    @Override
    protected boolean needHandle(MessageContext message) {
	return this.getCode().isAssignableFrom(message.getMessage().getClass());
    }

    @Override
    public void doHandle(MessageContext message) {
	TradeOrderChangeDTO tradeOrderChangeDTO = (TradeOrderChangeDTO)message.getMessage();
	String changeType = this.getChangeType(tradeOrderChangeDTO.getOrderStatus());
	if (StringUtils.isBlank(changeType)) {
	    log.warn("交易单状态不在业务处理场景，newsId:{}, bizNo:{},status:{}", message.getNewsID(),tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getOrderStatus());
	    return;
	}
	log.info("开始处理交易单消息，newsId:{}, bizNo:{},status:{}", message.getNewsID(),tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getOrderStatus());

	if (ChangeTypeEnum.DECREASE.getCode() == changeType) {
	    handleCancelTradeOrderMessage(tradeOrderChangeDTO);
	}else {
	    handlePurchaseTradeOrderMessage(tradeOrderChangeDTO);
	}
	log.info("处理交易单消息成功，newsId:{}, bizNo:{},orderStatus:{}", message.getNewsID(),tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getOrderStatus());
    }

    /**
     * 处理采购订单
     * @param tradeOrderChangeDTO 采购单消息对象
     */
    private void handlePurchaseTradeOrderMessage(TradeOrderChangeDTO tradeOrderChangeDTO ) {
	if (!tradeOrderChangeDTO.getOrderStatus().equals(InvoiceConstants.WAIT_APPROVAL)) {
	    log.warn("交易单非支付完成，不处理，bizNo:{},status:{},payAmount:{}", tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getOrderStatus());
	    return;
	}

	InvoiceManageDTO invoiceManageDTO = invoiceManageManager.getByBizNoAndBizType(tradeOrderChangeDTO.getOrderNo(), BizTypeEnum.PURCHASE_ORDER.getCode());
	if (Objects.nonNull(invoiceManageDTO)) {
	    log.warn("设备采购单已存在，bizNo:{}, bizType:{}", tradeOrderChangeDTO.getOrderNo(), BizTypeEnum.PURCHASE_ORDER.getCode());
	    return;
	}
	//是授信订单
	boolean b = isGrantPay(tradeOrderChangeDTO.getPayChannel());
	Long changeAmount = null;
	if (b) {
	    Long downPayment = getDownPayment(tradeOrderChangeDTO.getPayOrderNo(), tradeOrderChangeDTO.getBuyerId());
	    changeAmount = downPayment;
	}

	AbstractChangeWrapper<TradeOrderChangeDTO> changeWrapper = new TradeOrderChangeWrapper(tradeOrderChangeDTO)
		    .changeType(this.getChangeType(tradeOrderChangeDTO.getOrderStatus()))
		    .outBizType(this.getOutBizType(tradeOrderChangeDTO.getOrderStatus()))
		    .changeAmount(changeAmount)
		    .calSpuChangeAmount();


	InvoiceChangeRecord changeRecordAgg = changeWrapper.getInvoiceChangeRecord();
	InvoiceChangeRecordDTO changeRecordDTO = changeRecordAgg.toDTO();
	InvoiceManageDTO manageDTO = changeWrapper.buildInvoiceManageDTO();
	manageDTO.setSubjectType(getAgentType(manageDTO.getSubjectId()));
	batchSave(manageDTO, changeRecordDTO);
	// 待开票金额小于0
//	if (manageDTO.existPendingInvoiceAmountLessThenZero()) {
//	    //{应开明细的业务类型}:{应开明细的业务单号}的待开金额<0，请财务及时核对处理
//	    String content = String.format("{%s}:{%s}的待开金额<0，请财务及时核对处理", BizTypeEnum.PURCHASE_ORDER.desc(),manageDTO.getBizNo());
//	    messageManager.sendDingDingTextMessage("应开发票异常通知", content);
//	}
    }

    /**
     * 处理撤销交易单
     * @param tradeOrderChangeDTO 交易单消息对象
     */
    private void handleCancelTradeOrderMessage(TradeOrderChangeDTO tradeOrderChangeDTO ) {
	if (!tradeOrderChangeDTO.getOrderStatus().equals(InvoiceConstants.CLOSED) ) {
	    log.info("交易单非退款完成状态，不处理，bizNo:{},orderStatus:{}", tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getOrderStatus());
	    return;
	}

	InvoiceManageDTO invoiceManageDTO = invoiceManageManager.getByBizNoAndBizType(tradeOrderChangeDTO.getOrderNo(), BizTypeEnum.PURCHASE_ORDER.getCode());
	if (Objects.isNull(invoiceManageDTO)) {
	    log.info("设备采购单不存在，bizNo:{}, bizType:{}", tradeOrderChangeDTO.getOrderNo(), BizTypeEnum.PURCHASE_ORDER.getCode());
	    return;
	}
	//是授信订单
	boolean b = isGrantPay(tradeOrderChangeDTO.getPayChannel());
	log.info("是否授信订单：result:{},payChannel:{}", b, tradeOrderChangeDTO.getPayChannel());
	Long changeAmount = null;
	if (b) {
	    Long downPayment = getDownPayment(tradeOrderChangeDTO.getPayOrderNo(), tradeOrderChangeDTO.getBuyerId());
	    changeAmount = downPayment;
	}

	AbstractChangeWrapper<TradeOrderChangeDTO> changeWrapper = new TradeOrderChangeWrapper(tradeOrderChangeDTO)
			.originManage(invoiceManageDTO)
			.changeType(this.getChangeType(tradeOrderChangeDTO.getOrderStatus()))
			.outBizType(this.getOutBizType(tradeOrderChangeDTO.getOrderStatus()))
			.changeAmount(changeAmount)
			.calSpuChangeAmount();

	InvoiceChangeRecord changeRecordAgg = changeWrapper.getInvoiceChangeRecord();
	InvoiceChangeRecordDTO changeRecordDTO = changeRecordAgg.toDTO();
	InvoiceManageDTO manageDTO = changeWrapper.buildInvoiceManageDTO();
	manageDTO.setSubjectType(getAgentType(manageDTO.getSubjectId()));
	batchUpdate(manageDTO, changeRecordDTO);
	// 当待开票金额小于0，发送钉钉通知
	// 计算待开票金额
	InvoiceManage invoiceManage = invoiceManageManager.getInvoiceManageAggById(manageDTO.getId());
	InvoiceManageDTO dto = invoiceManage.getInvoiceManageDTO();
	if (dto.existPendingInvoiceAmountLessThenZero()) {
	    log.info("待开票金额小于0，发送钉钉通知,manageID:{}", manageDTO.getId());
	    //{应开明细的业务类型}:{应开明细的业务单号}的待开金额<0，请财务及时核对处理
	    String content = String.format("{%s}:{%s}的待开金额<0，请财务及时核对处理", BizTypeEnum.REPAIR_ORDER.desc(),invoiceManageDTO.getBizNo());
	    messageManager.sendDingDingTextMessage("应开发票异常通知", content);
	}
    }

    /**
     * 业务处理前的校验
     * @param message
     */
    protected boolean validate(MessageContext message){
	log.info("开始交易单业务处理前的校验,newsId:{}, outBizId:{},outBizType:{}", message.getNewsID(), message.getOutBizId(), message.getOutBizType());
	TradeOrderChangeDTO tradeOrderChangeDTO = (TradeOrderChangeDTO)message.getMessage();
	//通过bizNo和bizType查询变更记录，如果存在则说明此条消息已处理
	InvoiceChangeRecordDTO invoiceChangeRecordDTO = invoiceChangeRecordManager.getByBizNoAndBizType(message.getOutBizId(), message.getOutBizType());
	if (Objects.nonNull(invoiceChangeRecordDTO)) {
	    log.warn("该变更记录已处理，outBizId:{}, outBizType:{}", invoiceChangeRecordDTO.getOutBizId(), invoiceChangeRecordDTO.getOutBizType());
	    return false;
	}
	String changeType = this.getChangeType(tradeOrderChangeDTO.getOrderStatus());
	if (StringUtils.isBlank(changeType)) {
	    log.warn("交易单状态不正确，newsId:{},bizNo:{},orderStatus:{}", message.getNewsID(),tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getOrderStatus());
	    return false;
	}
	BigDecimal totalAmount = tradeOrderChangeDTO.getRealPayAmount();
	if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
	    log.warn("交易单金额为0，不做业务处理，newsId:{},bizNo:{},OrderStatus:{}", message.getNewsID(),tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getOrderStatus());
	    return false;
	}
	// 现在业务类型，只处理合资 或 代理商的数据, AgentTypeEnum
	// AGENT_TYPE(0, "代理商"),JV_COMPANY_TYPE(5, "合资公司");
//	Integer agentType = this.getAgentType(tradeOrderChangeDTO.getBuyerId());
//	if (Objects.isNull(agentType) || !BIZ_TYPES.contains(agentType)) {
//	    log.warn("该业务类型不处理，newsId:{},bizNo:{},buyerId:{},buyerAgentType:{}", message.getNewsID(),tradeOrderChangeDTO.getOrderNo(), tradeOrderChangeDTO.getBuyerId(), agentType);
//	    // todo 告警
//	    return false;
//	}
	return true;
    }

    /**
     *  1.交易订单支付成功后发消息 - TradeOrderChangeDTO(状态=待审核)
     * 	2.交易订单撤销订单退款后发消息 - TradeOrderChangeDTO(状态=交易关闭)
     * 	3.交易订单审核拒绝退款后发消息 - TradeOrderChangeDTO(状态=交易关闭)
     * @param orderStatus
     * @return
     */
    private String getChangeType(Integer orderStatus){
	if (orderStatus == InvoiceConstants.WAIT_APPROVAL){
	    return ChangeTypeEnum.INCREASE.getCode();
	}else if (orderStatus == InvoiceConstants.CLOSED) {
	    return ChangeTypeEnum.DECREASE.getCode();
	}
	return null;
    }
    private Integer getOutBizType(Integer orderStatus){
	if (orderStatus == InvoiceConstants.WAIT_APPROVAL){
	    return OutBizTypeEnum.PURCHASE_PAYMENT.getCode();
	}else if (orderStatus == InvoiceConstants.CLOSED) {
	    return OutBizTypeEnum.PURCHASE_CANCEL.getCode();
	}
	return null;
    }

    /**
     * 查询首付金额
     */
    private Long getDownPayment(String payOrderNo, Long buyerId){
	TradeOrderQueryV5Resp resp = tradeV5Facade.getTradeV5Service(Long.valueOf(payOrderNo), String.valueOf(buyerId));
	if (resp == null) {
	    throw BizException.create(InvoiceCodeEnum.TRADE_ORDER_NOT_EXIST_ERROR);
	}
	List<TradeOrderChannelRelationV5Resp> channelRelationList = resp.getChannelRelationList();
	Long payTotalAmount = resp.getPayAmount();
	Long grantPay = 0L;
	for(TradeOrderChannelRelationV5Resp v5Resp : channelRelationList){
	    // 授信支付详情中，49 或 50 的只存在其中一种，并且是一条
	    if(v5Resp.getPayType().equals(49) || v5Resp.getPayType().equals(50)){
		grantPay = v5Resp.getPayAmount();
	    }
	}
	//首付金额
	Long downPayment = payTotalAmount - grantPay;
	return downPayment;
    }

    /**
     * 是否授信支付
     */
    private boolean isGrantPay(Integer payChannel) {
	PayChannel payChannelEnum = PayChannel.getByCode(payChannel);
	if (payChannelEnum == null) {
	    return false;
	}
	return true;
    }
}
