package so.dian.invoice.service;

import java.util.List;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.pojo.bo.UserBO;
import so.dian.invoice.pojo.dto.InvoiceDeductionDTO;
import so.dian.invoice.pojo.dto.InvoiceRollBackResultDTO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.param.ApplyInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceDeductBatchParam;
import so.dian.invoice.pojo.param.InvoiceDeductOperationParam;
import so.dian.invoice.pojo.param.InvoiceManualDeductParam;
import so.dian.invoice.pojo.param.InvoiceRecoverBatchParam;
import so.dian.invoice.pojo.param.InvoiceRecoverOperationParam;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;

public interface InvoiceDeductionService {

    List<InvoiceDeductionDO> getInvoiceDeductionList(InvoiceDeductOperationParam param);

    /**
     * 核销发票
     */
    BizResult<InvoiceDeductionDTO> deductInvoice(InvoiceDeductOperationParam param);

    /**
     * 批量核销发票
     */
    BizResult<List<InvoiceDeductionDTO>> batchDeductInvoice(InvoiceDeductBatchParam params);

    /**
     * 回滚发票
     */
    BizResult<InvoiceDeductionDTO> recoverInvoice(InvoiceRecoverOperationParam param);

    /**
     * 批量回滚发票
     */
    BizResult<List<InvoiceDeductionDTO>> batchRecoverInvoice(InvoiceRecoverBatchParam param);

    /**
     * 发票手动核销
     */
    void invoiceManualDeduct(InvoiceManualDeductParam param);

    /**
     * 付款单关联发票详情列表查询
     */
    List<InvoiceInfoVO> getApplyInvoiceList(ApplyInvoiceParam param);

    /**
     * 根据业务单号查询已核销的发票列表
     */
    BizResult<List<InvoiceDeductionDTO>> getDeductInvoiceList(String businessNo, Integer businessType);

    /**
     * 获取业务关联的发票
     */
    List<InvoiceDeductionDO> getInvoiceByBusinessRelation(String invoiceNo, String invoiceCode);

    List<InvoiceRollBackResultDTO> rollback(UserBO userBO, List<Long> invoiceDeductIds);

    /**
     * 回滚单次核销记录
     *
     * @param userBO
     * @param invoiceDeductionDO
     */
    void rollbackSingle(UserBO userBO,InvoiceDeductionDO invoiceDeductionDO);
}
