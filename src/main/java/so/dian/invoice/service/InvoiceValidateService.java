package so.dian.invoice.service;

import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.util.List;

import org.apache.commons.collections4.map.SingletonMap;
import org.apache.commons.lang3.tuple.Pair;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.enums.InvoiceValidateTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.dto.CheckInvoiceBuyerDTO;
import so.dian.invoice.pojo.dto.OCRQueueDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceIdentifyRecordDTO;
import so.dian.invoice.pojo.dto.identify.ValidationDTO;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.param.InvoiceUploadParams;
import so.dian.invoice.pojo.param.InvoiceValidateDetailParams;
import so.dian.invoice.pojo.param.InvoiceValidateParams;
import so.dian.invoice.util.ServiceResult;

public interface InvoiceValidateService {

    ServiceResult<List<Pair<String,String>>> batchUpload(Integer loginUserId, InvoiceUploadParams params);

    /**
     * 批量上传发票图片
     */
    ServiceResult<String> batchUpload(Integer loginUserId, String clientFileName, byte[] bytes, String contentType,
          int length);

    /**
     * 获取发票识别信息（商家端上传发票）
     */
    BizResult<InvoiceIdentifyRecordDTO> getInvoiceIdentifyDetail(String imageUrl);

    /**
     * 发票识别记录列表
     */
    ServiceResult<JSONObject> list(InvoiceValidateParams params);

    /**
     * 删除发票识别记录
     */
    ServiceResult<String> delete(InvoiceValidateParams params);

    /**
     * 批量删除发票识别记录
     */
    ServiceResult<String> batchDelete(InvoiceValidateParams params);

    /**
     * 生成批次号
     */
    ServiceResult<String> getBatchNo();

    /**
     * 批量发票验证
     */
    ServiceResult<String> batchValidate(InvoiceValidateParams params);

    /**
     * 编辑发票记录
     */
    ServiceResult<String> update(InvoiceValidateDetailParams params);

    /**
     * 单条发票验证
     */
    ServiceResult<String> validate(InvoiceValidateDetailParams params);

    /**
     * 发票验证
     *
     * @throws IOException
     */
    ValidationDTO handleInvoiceValidate(InvoiceValidateDetailParams params) throws IOException;

    InvoiceValidateStatusDO requireGlority(InvoiceBO validateInvoiceBO, Integer count, InvoiceValidateTypeEnum typeEnum);

    /**
     * OCR识别
     * @param ocrQueueDTO
     * @return
     */
    boolean handleOCR(OCRQueueDTO ocrQueueDTO);

    /**
     * 购买方税号校验，小电集团的只能填写小电集团的，非小电集团的购买方税号只能填写非小电集团的
     */
    boolean checkInvoiceBuyer(CheckInvoiceBuyerDTO checkInvoiceBuyerDTO);
}
