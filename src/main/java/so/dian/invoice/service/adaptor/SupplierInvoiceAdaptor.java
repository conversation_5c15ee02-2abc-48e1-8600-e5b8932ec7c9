package so.dian.invoice.service.adaptor;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.entity.PageData;
import so.dian.invoice.manager.SupplierInvoiceManager;
import so.dian.invoice.pojo.entity.SupplierInvoiceDO;
import so.dian.invoice.pojo.param.SupplierInvoiceAttachmentAddParam;
import so.dian.invoice.pojo.param.SupplierInvoiceAttachmentDeleteParam;
import so.dian.invoice.pojo.param.SupplierInvoiceDeleteParam;
import so.dian.invoice.pojo.param.SupplierInvoiceDetailPageParam;
import so.dian.invoice.pojo.param.SupplierInvoiceImportParam;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailPageVO;
import so.dian.invoice.pojo.vo.SupplierInvoicePageVO;
import so.dian.invoice.service.SupplierInvoiceService;
import so.dian.invoice.service.SupplierService;
import so.dian.invoice.util.Assert;
import so.dian.kunlun.common.enums.EmployeeRoleEnum;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @author: miaoshuai
 * @create: 2024/01/18 15:33
 * @description:
 */
@Slf4j
@Component
public class SupplierInvoiceAdaptor {

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private SupplierInvoiceManager supplierInvoiceManager;

    @Autowired
    private SupplierInvoiceService supplierInvoiceService;

    public PageData<SupplierInvoicePageVO> findPageByReq(CurrentUserReq userReq, SupplierInvoicePageParam param) {
        // 如果采购单操作人，没有关联的供应商，不让他看到数据
        Set<String> supplierNos = null;
        if (Objects.equals(EmployeeRoleEnum.PO_OPERATOR.getDesc(), userReq.getCurrentRole())) {
            supplierNos = supplierService.getSupplierNosByMobile(userReq.getMobile());
            if (CollectionUtils.isEmpty(supplierNos)) {
                return PageData.create(Lists.newArrayList(), 0L, (long) param.getPageNo(), param.getPageSize());
            }
        }

        return supplierInvoiceService.findPageByReq(param, supplierNos);
    }

    /**
     * 发票删除
     * 权限：【财务经理/采购主管】操作所有
     * 【采购单操作人】操作关联供应商的数据
     *
     * @param param   附件信息
     * @param userReq 当前登录用户信息
     */
    public void deleteInvoice(CurrentUserReq userReq, SupplierInvoiceDeleteParam param) {
        log.info("供应商发票删除,userReq:{},param:{}", userReq, param);
        Integer supplierInvoiceId = param.getSupplierInvoiceId();
        Assert.isTrue(operatorHasPermission(userReq, supplierInvoiceId), "无权限");

        supplierInvoiceService.deleteInvoice(userReq, param);
    }

    /**
     * 附件删除
     * 权限：【财务经理/采购主管】操作所有
     * 【采购单操作人】操作关联供应商的数据
     *
     * @param param   附件信息
     * @param userReq 当前登录用户信息
     */
    public void deleteAttachment(CurrentUserReq userReq, SupplierInvoiceAttachmentDeleteParam param) {
        log.info("供应商发票附件删除,userReq:{},param:{}", userReq, param);

        Integer supplierInvoiceId = param.getSupplierInvoiceId();
        Assert.isTrue(operatorHasPermission(userReq, supplierInvoiceId), "无权限");
        supplierInvoiceService.deleteAttachment(userReq, param);
    }

    public void addInvoicePicture(CurrentUserReq userReq, SupplierInvoiceAttachmentAddParam param) {
        log.info("供应商发票图片添加,userReq:{},param:{}", userReq, param);

        Integer supplierInvoiceId = param.getSupplierInvoiceId();
        Assert.isTrue(operatorHasPermission(userReq, supplierInvoiceId), "无权限");
        supplierInvoiceService.addInvoicePicture(userReq, param);
    }

    public List<SupplierInvoiceDetailPageVO> findDetailListByReq(CurrentUserReq userReq, SupplierInvoiceDetailPageParam param) {
        // 如果采购单操作人，没有关联的供应商，不让他看到数据
        Set<String> supplierNos = null;
        if (Objects.equals(EmployeeRoleEnum.PO_OPERATOR.getDesc(), userReq.getCurrentRole())) {
            supplierNos = supplierService.getSupplierNosByMobile(userReq.getMobile());
            if (CollectionUtils.isEmpty(supplierNos)) {
                return Lists.newArrayList();
            }
        }

        return supplierInvoiceService.findDetailListByReq(param, supplierNos);
    }

    public Long excelExport(CurrentUserReq userReq, SupplierInvoicePageParam param) {
        // 如果采购单操作人，没有关联的供应商，不让他看到数据
        Set<String> supplierNos = null;
        if (Objects.equals(EmployeeRoleEnum.PO_OPERATOR.getDesc(), userReq.getCurrentRole())) {
            supplierNos = supplierService.getSupplierNosByMobile(userReq.getMobile());
            if (CollectionUtils.isEmpty(supplierNos)) {
                return 0L;
            }
        }

        return supplierInvoiceService.excelExport(userReq, param, supplierNos);
    }

    public void excelTemplate(HttpServletResponse response) {
        supplierInvoiceService.excelTemplate(response);
    }

    public void transferInvoice(CurrentUserReq userReq, Integer supplierInvoiceId) {
        supplierInvoiceService.transferInvoice(userReq, supplierInvoiceId);
    }

    public String excelImport(CurrentUserReq userReq, List<SupplierInvoiceImportParam> paramList) {
        Set<String> supplierNos = supplierService.getSupplierNosByMobile(userReq.getMobile());
        return supplierInvoiceService.excelImport(userReq, paramList, supplierNos);
    }

    /**
     * 【采购单操作人】是否有权限操作这个供应商的数据
     * @return true有权限；false没权限
     */
    private boolean operatorHasPermission(CurrentUserReq userReq, Integer supplierInvoiceId) {
        if (Objects.equals(EmployeeRoleEnum.PO_OPERATOR.getDesc(), userReq.getCurrentRole())) {
            Set<String> supplierNos = supplierService.getSupplierNosByMobile(userReq.getMobile());
            if (CollectionUtils.isEmpty(supplierNos)) {
                return false;
            }
            SupplierInvoiceDO supplierInvoiceDO = supplierInvoiceManager.findById(supplierInvoiceId);
            if (!supplierNos.contains(supplierInvoiceDO.getSupplierNo())) {
                return false;
            }
        }
        return true;
    }
}
