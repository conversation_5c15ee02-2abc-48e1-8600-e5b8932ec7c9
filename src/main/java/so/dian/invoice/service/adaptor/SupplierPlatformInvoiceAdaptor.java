package so.dian.invoice.service.adaptor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.commons.eden.exception.BizException;
import so.dian.himalaya.common.entity.PageData;
import so.dian.invoice.pojo.param.SupplierInvoiceImportParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceAttachmentAddParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceAttachmentDeleteParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceDeleteParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceDetailParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceImportParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.request.SupplierPlatformUser;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailPageVO;
import so.dian.invoice.pojo.vo.SupplierInvoicePageVO;
import so.dian.invoice.pojo.vo.supplier.SupplierPlatformInvoiceDetailPageVO;
import so.dian.invoice.pojo.vo.supplier.SupplierPlatformInvoicePageVO;
import so.dian.invoice.service.SupplierInvoiceService;
import so.dian.invoice.service.SupplierService;
import so.dian.invoice.util.RequestUtils;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static so.dian.invoice.enums.error.SupplierInvoiceErrorCode.COOKIE_USER_NOT_EXIST;
import static so.dian.invoice.enums.error.SupplierInvoiceErrorCode.USER_BELONG_SUPPLIER_NOT_EXIST;
import static so.dian.invoice.enums.error.SupplierInvoiceErrorCode.USER_EMAIL_NOT_EXIST;

/**
 * @author: miaoshuai
 * @create: 2024/01/18 15:29
 * @description:
 */
@Component
public class SupplierPlatformInvoiceAdaptor {

    @Autowired
    private SupplierInvoiceService supplierInvoiceService;

    @Autowired
    private SupplierService supplierService;

    public PageData<SupplierPlatformInvoicePageVO> supplierInvoicePage(@Validated @RequestBody SupplierPlatformInvoiceParam param) {
        SupplierPlatformUser platformUser = getUser();

        PageData<SupplierInvoicePageVO> pageByReq = supplierInvoiceService.findPageByReq(param, Collections.singleton(platformUser.getSupplerNo()));
        List<SupplierInvoicePageVO> invoiceVOList = pageByReq.getList();
        if (CollUtil.isNotEmpty(invoiceVOList)) {
            List<SupplierPlatformInvoicePageVO> platformInvoiceVOS = invoiceVOList.stream().map(invoiceVO -> BeanUtil.copyProperties(invoiceVO, SupplierPlatformInvoicePageVO.class)).collect(Collectors.toList());
            return PageData.create(platformInvoiceVOS, pageByReq.getTotalCount(), pageByReq.getPageNo(), pageByReq.getPageSize());
        }
        return PageData.create(Collections.emptyList(), pageByReq.getTotalCount(), pageByReq.getPageNo(), pageByReq.getPageSize());
    }

    public List<SupplierPlatformInvoiceDetailPageVO> supplierInvoiceDetailList(@Validated @RequestBody SupplierPlatformInvoiceDetailParam param) {
        SupplierPlatformUser platformUser = getUser();
        List<SupplierInvoiceDetailPageVO> detailList = supplierInvoiceService.findDetailListByReq(param, Collections.singleton(platformUser.getSupplerNo()));
        return detailList.stream().map(detail -> BeanUtil.copyProperties(detail, SupplierPlatformInvoiceDetailPageVO.class)).collect(Collectors.toList());
    }

    public Long detailExcelExport(@Validated @RequestBody SupplierPlatformInvoiceParam param) {
        SupplierPlatformUser platformUser = getUser();
        if (StrUtil.isBlank(platformUser.getUserReq().getEmail()))
            throw BizException.create(USER_EMAIL_NOT_EXIST);

        return supplierInvoiceService.excelExport(platformUser.getUserReq(), param, Collections.singleton(platformUser.getSupplerNo()));
    }

    public void deleteInvoice(@Validated @RequestBody SupplierPlatformInvoiceDeleteParam param) {
        SupplierPlatformUser platformUser = getUser();
        supplierInvoiceService.deleteInvoice(platformUser.getUserReq(), param);
    }

    public void deleteAttachment(@Valid @RequestBody SupplierPlatformInvoiceAttachmentDeleteParam param) {
        SupplierPlatformUser platformUser = getUser();
        supplierInvoiceService.deleteAttachment(platformUser.getUserReq(), param);
    }

    public void addAttachment(@Validated @RequestBody SupplierPlatformInvoiceAttachmentAddParam param) {
        SupplierPlatformUser platformUser = getUser();
        supplierInvoiceService.addInvoicePicture(platformUser.getUserReq(), param);
    }

    public void excelTemplate(HttpServletResponse response) {
        supplierInvoiceService.excelTemplate(response);
    }

    public String excelImport(@RequestBody List<SupplierPlatformInvoiceImportParam> paramList) {
        SupplierPlatformUser platformUser = getUser();

        if (CollUtil.isEmpty(paramList))
            return "没有导入的内容";

        if (StrUtil.isBlank(platformUser.getUserReq().getEmail()))
            throw BizException.create(USER_EMAIL_NOT_EXIST);

        List<SupplierInvoiceImportParam> params = paramList.stream().map(param -> BeanUtil.copyProperties(param, SupplierInvoiceImportParam.class)).collect(Collectors.toList());
        return supplierInvoiceService.excelImport(platformUser.getUserReq(), params, Collections.singleton(platformUser.getSupplerNo()));
    }


    private SupplierPlatformUser getUser() {
        String user64 = RequestUtils.getRequest().getHeader("user64");
        String userJson = Base64.decodeStr(user64);
        JSONObject userObj = JSON.parseObject(userJson);
        if (Objects.isNull(userObj))
            throw BizException.create(COOKIE_USER_NOT_EXIST);

        Long deptId = userObj.getLong("deptId");
        if (Objects.isNull(deptId))
            throw BizException.create(COOKIE_USER_NOT_EXIST);

        String supplierNo = supplierService.getSupplierNosById(deptId);
        if (StrUtil.isBlank(supplierNo))
            throw BizException.create(USER_BELONG_SUPPLIER_NOT_EXIST);

        CurrentUserReq userReq = new CurrentUserReq();
        userReq.setUserId(userObj.getInteger("userId"));
        userReq.setEmail(userObj.getString("email"));
        userReq.setUserName(userObj.getString("userName"));

        return SupplierPlatformUser.builder()
                .supplerNo(supplierNo).userReq(userReq).build();
    }
}
