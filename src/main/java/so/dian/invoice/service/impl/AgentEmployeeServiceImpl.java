package so.dian.invoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.center.common.entity.BizResult;
import so.dian.center.common.util.CollectionUtil;
import so.dian.center.common.util.StringUtil;
import so.dian.commons.eden.exception.BizException;
import so.dian.hr.api.entity.common.CityAreaDTO;
import so.dian.hr.api.entity.common.HrCommonDTO;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.hr.api.entity.employee.CityDepartmentDTO;
import so.dian.hr.api.entity.employee.DMDepartmentDTO;
import so.dian.invoice.client.HrClient;
import so.dian.invoice.enums.BelongSubjectTypeEnum;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.manager.AgentManager;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.service.AgentEmployeeService;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static so.dian.invoice.constant.InvoiceConstants.DIAN_INVOICE_BELONG_ID;

@Service
@Slf4j
public class AgentEmployeeServiceImpl implements AgentEmployeeService {

    @Resource
    private HrClient hrClient;
    @Resource
    private AgentManager agentManager;

    @Override
    public Map<Integer, String> getAgentEmployeeNickMap(Set<Integer> userIdSet) {
        if (CollectionUtil.isEmpty(userIdSet)) {
            return Maps.newHashMap();
        }
        Map<Integer, String> res = new HashMap<>(userIdSet.size());
        HrCommonDTO commonDTO = new HrCommonDTO();
        commonDTO.setIds(new ArrayList<>(userIdSet));
        BizResult<List<AgentEmployeeDTO>> userRes = hrClient.getByIds(commonDTO);
        if (userRes == null || !userRes.isSuccess() || userRes.getData() == null) {
            return res;
        }

        List<AgentEmployeeDTO> userList = userRes.getData();
        for (AgentEmployeeDTO user : userList) {
            res.put(user.getId(), user.getNickNameOrName());
        }

        return res;
    }

    /**
     * 根据userId列表查询得到 userId和城市code对应map
     *
     * @param userIdSet
     * @return
     */
    @Override
    public Map<Integer, Integer> getAgentEmployeeCityCodeMap(Set<Integer> userIdSet) {
        if (CollectionUtil.isEmpty(userIdSet)) {
            return Maps.newHashMap();
        }
        Map<Integer, Integer> res = new HashMap<>(userIdSet.size());
        HrCommonDTO commonDTO = new HrCommonDTO();
        commonDTO.setIds(new ArrayList<>(userIdSet));
        BizResult<List<AgentEmployeeDTO>> userRes = hrClient.getByIds(commonDTO);
        if (userRes == null || !userRes.isSuccess() || userRes.getData() == null) {
            return res;
        }

        List<AgentEmployeeDTO> userList = userRes.getData();
        for (AgentEmployeeDTO user : userList) {
            res.put(user.getId(), user.getCityCode());
        }

        return res;
    }


    @Override
    public String getEmployeeNickById(Integer id) {

        if (id == null) {
            return null;
        }

        BizResult<AgentEmployeeDTO> employeeRes = hrClient.getById(id);
        if (employeeRes == null || !employeeRes.isSuccess() || employeeRes.getData() == null) {
            return null;
        }

        return employeeRes.getData().getNickNameOrName();
    }

    @Override
    public AgentEmployeeDTO getEmployeeById(Integer id) {

        if (id == null) {
            return null;
        }

        BizResult<AgentEmployeeDTO> employeeRes = hrClient.getById(id);

        log.info("------ HR 根据id获取员工信息。id = {}, result = {}", id, JSON.toJSONString(employeeRes));

        if (employeeRes == null || !employeeRes.isSuccess() || employeeRes.getData() == null) {
            return null;
        }

        return employeeRes.getData();
    }

    @Override
    public AgentEmployeeDTO getByNickName(String nickName) {
        if (!StringUtil.isNotBlank(nickName)) {
            return null;
        }
        BizResult<AgentEmployeeDTO> employeeRes = hrClient.getByNickName(nickName);

        log.info("------ HR 根据花名获取员工信息。nickName = {}, result = {}", nickName, JSON.toJSONString(employeeRes));

        if (employeeRes == null || !employeeRes.isSuccess() || employeeRes.getData() == null) {
            return null;
        }

        return employeeRes.getData();
    }

    @Override
    public BizResult<AgentDTO> getAgentByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return null;
        }
        BizResult<AgentDTO> bizResult = hrClient.getAgentByUserId(userId);

        log.info("------ HR 根据userId获取员工的agent信息。userId = {}, result = {}", userId, JSON.toJSONString(bizResult));

        return bizResult;
    }

    @Override
    public AgentDTO getAgentDtoByUserId(Integer userId) {
        if (userId == null) {
            return null;
        }

        BizResult<AgentDTO> agentResult = getAgentByUserId(Long.valueOf(userId));

        if (agentResult == null || agentResult.getData() == null) {
            return null;
        }

        return agentResult.getData();
    }

    /**
     * 通过cityCode获取大区信息
     */
    @Override
    public Map<Integer, CityDepartmentDTO> listRegionNameByCityCodes(List<Integer> cityCodeList) {

        so.dian.center.common.entity.BizResult<List<CityDepartmentDTO>> bizResult =
                hrClient.batchGetDMNameByCityCodes(cityCodeList);
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            log.error("通过城市code获取大区信息失败 | fallback | cityCodeList:{}", cityCodeList.toString());
            return Maps.newHashMap();
        }

        List<CityDepartmentDTO> departmentDTOList = bizResult.getData();
        if (CollectionUtils.isEmpty(departmentDTOList)) {
            log.warn("通过城市code获取大区信息失败 | departmentDTOList:{}", departmentDTOList.toString());
            return Maps.newHashMap();
        }

        return departmentDTOList.stream().collect(Collectors.toMap(CityDepartmentDTO::getCityCode, Function.identity()));
    }

    /**
     * 通过大区名称查询城市code列表
     */
    @Override
    public List<Integer> listCityCodeByRegionName(List<String> regionNameList) {

        List<Integer> cityCodeList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regionNameList)) {
            log.error("大区名称列表为空 | fallback | regionNameList:{}", regionNameList.toString());
            return cityCodeList;
        }
        so.dian.center.common.entity.BizResult<List<DMDepartmentDTO>> bizResult =
                hrClient.getDMDepWithCitysForSelect();
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            log.error("通过大区名称获取城市code列表失败 | fallback");
            return cityCodeList;
        }

        List<DMDepartmentDTO> departmentDTOList = bizResult.getData();
        if (CollectionUtils.isEmpty(departmentDTOList)) {
            log.warn("通过大区名称获取城市code列表为空 | departmentDTOList:{}", departmentDTOList.toString());
            return cityCodeList;
        }

        List<DMDepartmentDTO> regionCityList = departmentDTOList.stream().filter(item -> (regionNameList
                .contains(item.getDepartmentName()))).collect(Collectors.toList());

        regionCityList.forEach(item -> {
            List<Integer> itemCityCodeList = item.getCitys().stream().map(CityAreaDTO::getCode).collect(Collectors.toList());
            cityCodeList.addAll(itemCityCodeList);
        });

        return cityCodeList;
    }

    /**
     * 查询大区下拉选
     */
    @Override
    public List<String> selectRegionList() {

        so.dian.center.common.entity.BizResult<List<DMDepartmentDTO>> bizResult =
                hrClient.getDMDepWithCitysForSelect();
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            log.error("获取大区下拉选失败 | fallback");
            return Lists.newArrayList();
        }

        List<DMDepartmentDTO> departmentDTOList = bizResult.getData();
        if (CollectionUtils.isEmpty(departmentDTOList)) {
            log.warn("获取大区下拉选为空 | departmentDTOList:{}", departmentDTOList.toString());
            return Lists.newArrayList();
        }

        return departmentDTOList.stream().map(DMDepartmentDTO::getDepartmentName).collect(Collectors.toList());
    }

    @Override
    public CurrentUserReq getRemoteUser(Integer userId) {
        return getRemoteUser(userId,"");
    }

    @Override
    public CurrentUserReq getRemoteUser(CurrentUserReq cookieInfo) {
        return getRemoteUser(cookieInfo.getUserId(),cookieInfo.getCurrentRole());
    }

    public CurrentUserReq getRemoteUser(Integer userId,String currentRole) {
        if (Objects.isNull(userId)) {
            throw BizException.create(InvoiceCommentErrorCodeEnum.USER_NOT_EXIST);
        }
        AgentEmployeeDTO employeeDTO = getEmployeeById(userId);
        if (Objects.isNull(employeeDTO)) {
            throw BizException.create(InvoiceCommentErrorCodeEnum.USER_NOT_EXIST);
        }
        CurrentUserReq currentUserReq = new CurrentUserReq();
        currentUserReq.setUserId(userId);
        currentUserReq.setCurrentRole(currentRole);
        currentUserReq.setNickName(employeeDTO.getNickNameOrName());

        currentUserReq.setUserName(employeeDTO.getNickNameOrName());
        currentUserReq.setEmail(employeeDTO.getEmail());
        currentUserReq.setRoleId(employeeDTO.getRole());
        currentUserReq.setMobile(employeeDTO.getMobile());

        //结算方
        Integer agentId = employeeDTO.getAgentId();
        if (Objects.isNull(agentId)) {
            throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL,"用户agentId不存在");
        }
        currentUserReq.setBelongSubjectId(agentId);
        if (DIAN_INVOICE_BELONG_ID.contains(agentId)) {
            currentUserReq.setBelongSubjectType(BelongSubjectTypeEnum.XIAODIAN.getCode());
        } else {
            so.dian.agent.api.dto.AgentDTO agentDTO = agentManager.findById(agentId);
            if (Objects.isNull(agentDTO)) {
                log.error("远程获取代理商信息异常 currentUserReq:{}", currentUserReq);
                throw BizException.create(InvoiceCommentErrorCodeEnum.USER_NOT_EXIST);
            }
            currentUserReq.setBelongSubjectType(agentDTO.getType());
        }
        return currentUserReq;
    }

}
