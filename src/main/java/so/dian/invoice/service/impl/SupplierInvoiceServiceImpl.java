package so.dian.invoice.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.invoice.converter.SupplierInvoiceConverter;
import so.dian.invoice.converter.SupplierInvoiceDetailConverter;
import so.dian.invoice.enums.BelongSubjectTypeEnum;
import so.dian.invoice.enums.BuyerTaxIdEnum;
import so.dian.invoice.enums.SupplierAttachmentTypeEnum;
import so.dian.invoice.enums.SupplierInvoiceOperateEnum;
import so.dian.invoice.enums.SupplierInvoiceStatusEnum;
import so.dian.invoice.enums.SupplierInvoiceTypeEnum;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.handle.ExcelHandler;
import so.dian.invoice.manager.SupplierInvoiceAttachmentManager;
import so.dian.invoice.manager.SupplierInvoiceDetailBillNoManager;
import so.dian.invoice.manager.SupplierInvoiceDetailManager;
import so.dian.invoice.manager.SupplierInvoiceManager;
import so.dian.invoice.manager.SupplierInvoiceOperateLogManager;
import so.dian.invoice.pojo.dto.BillNoDTO;
import so.dian.invoice.pojo.dto.SupplierInvoiceImportErrorDTO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceAttachmentDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.param.SupplierInvoiceAttachmentAddParam;
import so.dian.invoice.pojo.param.SupplierInvoiceAttachmentDeleteParam;
import so.dian.invoice.pojo.param.SupplierInvoiceDeleteParam;
import so.dian.invoice.pojo.param.SupplierInvoiceDetailPageParam;
import so.dian.invoice.pojo.param.SupplierInvoiceImportParam;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailPageVO;
import so.dian.invoice.pojo.vo.SupplierInvoicePageVO;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.service.SupplierInvoiceService;
import so.dian.invoice.service.SupplierService;
import so.dian.invoice.util.Assert;
import so.dian.invoice.util.LocalDateUtil;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.invoice.volidator.InvoiceValidator;
import so.dian.jinyun.client.pojo.response.scm.PurchaseOrderDetailRsp;
import so.dian.kunlun.common.enums.EmployeeRoleEnum;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 供应商发票主表
 *
 * <AUTHOR>
 * @date 2021-03-17 14:03:28
 */
@Service
@Slf4j(topic = "biz")
public class SupplierInvoiceServiceImpl implements SupplierInvoiceService {

//    public Set<String> permissionRoleSet = Sets.newHashSet(EmployeeRoleEnum.AFFAIRS_MANAGER.getDesc(),
//            EmployeeRoleEnum.PO_OPERATOR.getDesc(), EmployeeRoleEnum.PURCHASE_MANAGER.getDesc());

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private SupplierInvoiceManager supplierInvoiceManager;

    @Autowired
    private SupplierInvoiceAttachmentManager attachmentManager;

    @Autowired
    private SupplierInvoiceDetailManager detailManager;

    @Autowired
    private SupplierInvoiceDetailBillNoManager detailBillNoManager;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private ExcelHandler excelHandler;

    @Autowired
    private SupplierInvoiceOperateLogManager operateLogManager;


    @Override
    public PageData<SupplierInvoicePageVO> findPageByReq(SupplierInvoicePageParam param, Set<String> supplierNos) {

        List<BillNoDTO> billNoList = SupplierInvoiceDetailConverter.toBillNoList(param);
        Long count = supplierInvoiceManager.countByReq(param, billNoList, supplierNos);
        if (count == 0) {
            return PageData.create(Lists.newArrayList(), 0L, (long) param.getPageNo(), param.getPageSize());
        }

        // limit offset,pageSize 是由 PageRequest 算出来的
        PageRequest pageRequest = PageRequest.of(param.getPageNo(), param.getPageSize());
        // 分页查询
        List<SupplierInvoiceDO> supplierInvoiceDOList = supplierInvoiceManager.findPageByReq(param, billNoList, supplierNos, pageRequest);

        Map<Integer, SupplierInvoiceAttachmentDO> invoiceIdMap = new HashMap<>();
        for (SupplierInvoiceDO supplierInvoiceDO : supplierInvoiceDOList) {
            SupplierInvoiceAttachmentDO invoicePicture = attachmentManager.findInvoicePicture(supplierInvoiceDO.getId());
            if (Objects.nonNull(invoicePicture)) {
                invoiceIdMap.put(supplierInvoiceDO.getId(), invoicePicture);
            }
        }

        List<SupplierInvoicePageVO> supplierInvoicePageRspList = SupplierInvoiceConverter.toVOList(supplierInvoiceDOList, invoiceIdMap);
        return PageData.create(supplierInvoicePageRspList, count, (long) param.getPageNo(), param.getPageSize());
    }

    /**
     * 发票删除
     * 权限：【财务经理/采购主管】操作所有
     * 【采购单操作人】操作关联供应商的数据
     *
     * @param param   附件信息
     * @param userReq 当前登录用户信息
     */
    @Override
    public void deleteInvoice(CurrentUserReq userReq, SupplierInvoiceDeleteParam param) {
        log.info("供应商发票删除,userReq:{},param:{}", userReq, param);
        Integer supplierInvoiceId = param.getSupplierInvoiceId();

        SupplierInvoiceDO supplierInvoiceDO = supplierInvoiceManager.findByIdAndStatus(supplierInvoiceId, SupplierInvoiceStatusEnum.DEAL_WITH);
        Assert.isNull(supplierInvoiceDO, "供应商发票已转发票台账,不允许操作");

        supplierInvoiceManager.cascadeDeleteById(supplierInvoiceId);

        String content = JSON.toJSONString(param);
        operateLogManager.insert(SupplierInvoiceOperateEnum.删除发票, supplierInvoiceId, content, userReq);
    }

    /**
     * 附件删除
     * 权限：【财务经理/采购主管】操作所有
     * 【采购单操作人】操作关联供应商的数据
     *
     * @param param   附件信息
     * @param userReq 当前登录用户信息
     */
    @Override
    public void deleteAttachment(CurrentUserReq userReq, SupplierInvoiceAttachmentDeleteParam param) {
        log.info("供应商发票附件删除,userReq:{},param:{}", userReq, param);

        Integer attachmentId = param.getAttachmentId();
        Integer supplierInvoiceId = param.getSupplierInvoiceId();

        SupplierInvoiceDO supplierInvoiceDO = supplierInvoiceManager.findByIdAndStatus(supplierInvoiceId, SupplierInvoiceStatusEnum.DEAL_WITH);
        Assert.isNull(supplierInvoiceDO, "供应商发票已转发票台账,不允许操作");

        attachmentManager.deleteById(attachmentId);

        String content = JSON.toJSONString(param);
        operateLogManager.insert(SupplierInvoiceOperateEnum.删除附件, supplierInvoiceId, content, userReq);
    }

    @Override
    public void addInvoicePicture(CurrentUserReq userReq, SupplierInvoiceAttachmentAddParam param) {
        log.info("供应商发票图片添加,userReq:{},param:{}", userReq, param);

        Integer supplierInvoiceId = param.getSupplierInvoiceId();

        Assert.isTrue(operatorHasPermission(userReq, supplierInvoiceId), "无权限");
        attachmentManager.insert(supplierInvoiceId, SupplierAttachmentTypeEnum.INVOICE_PIC, param.getUrl());

        String content = JSON.toJSONString(param);
        operateLogManager.insert(SupplierInvoiceOperateEnum.上传附件, supplierInvoiceId, content, userReq);
    }

    /**
     * 【采购单操作人】是否有权限操作这个供应商的数据
     *
     * @param userReq
     * @param supplierInvoiceId
     * @return true有权限；false没权限
     */
    public boolean operatorHasPermission(CurrentUserReq userReq, Integer supplierInvoiceId) {
//        if (!permissionRoleSet.contains(userReq.getCurrentRole())) {
//            return false;
//        }
        if (Objects.equals(EmployeeRoleEnum.PO_OPERATOR.getDesc(), userReq.getCurrentRole())) {
            Set<String> supplierNos = supplierService.getSupplierNosByMobile(userReq.getMobile());
            if (CollectionUtils.isEmpty(supplierNos)) {
                return false;
            }
            SupplierInvoiceDO supplierInvoiceDO = supplierInvoiceManager.findById(supplierInvoiceId);
            if (!supplierNos.contains(supplierInvoiceDO.getSupplierNo())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<SupplierInvoiceDetailPageVO> findDetailListByReq(SupplierInvoiceDetailPageParam param, Set<String> supplierNos) {

        List<SupplierInvoiceDetailDO> supplierInvoiceDetailBOList = detailManager.findListByReq(param, supplierNos);
        if (CollectionUtils.isEmpty(supplierInvoiceDetailBOList)) {
            return Lists.newArrayList();
        }
        // k:SupplierInvoiceDetailId————v:Map<Integer,String>
        // k:bill_type————v:bill_no
        Map<Integer, Map<Integer, String>> detailIdBillNoListMap = detailBillNoManager.findDetailIdBillTypeMap(supplierInvoiceDetailBOList);

        return SupplierInvoiceDetailConverter.toPageRspList(supplierInvoiceDetailBOList, detailIdBillNoListMap);
    }

    @Override
    public Long excelExport(CurrentUserReq userReq, SupplierInvoicePageParam param, Set<String> supplierNos) {

        log.info("供应商发票导出,userReq:{},param:{}", userReq, param);

        List<BillNoDTO> billNoDTOList = SupplierInvoiceDetailConverter.toBillNoList(param);
        Long count = detailManager.countByReq(param, billNoDTOList, supplierNos);
        if (count == 0L) {
            log.info("供应商发票明细表 | 根据搜索条件未查询到记录 | request:{}", param);
            throw BizException.create(InvoiceErrorCodeEnum.TO_NO_RECORD);
        }
        if (count > 50000L) {
            log.info("供应商发票明细表 | 根据搜索条件查询到记录超过50000条 | request:{}", param);
            throw BizException.create(InvoiceErrorCodeEnum.TO_TOO_MUCH);
        }
        if (StringUtils.isBlank(userReq.getEmail())) {
            log.info("供应商发票明细表 | 操作人邮箱为空 | request:{}", param);
            throw BizException.create(InvoiceCommentErrorCodeEnum.USER_EMAIL_NOT_EXIST);
        }
        excelHandler.asyncExportSupplierInvoiceDetail(param, supplierNos, userReq);
        return count;
    }

    @Override
    public void excelTemplate(HttpServletResponse response) {
        excelHandler.supplierInvoiceExcelTemplate(response);
    }

    @Override
    public void transferInvoice(CurrentUserReq userReq, Integer supplierInvoiceId) {
        log.info("供应商发票转发票台账,userReq:{},supplierInvoiceId:{}", userReq, supplierInvoiceId);
        Assert.notNull(supplierInvoiceId, "供应商发票主体id不能为空");

        SupplierInvoiceDO supplierInvoiceDO = supplierInvoiceManager.findById(supplierInvoiceId);
        Assert.notNull(supplierInvoiceDO, "供应商发票主体不存在");
        // 已转发票台账 状态不能操作
        if (Objects.equals(SupplierInvoiceStatusEnum.DEAL_WITH.getCode(), supplierInvoiceDO.getStatus())) {
            throw BizException.create(InvoiceErrorCodeEnum.NOT_OPERATE_DEAL_WITH);
        }

        // 转发票台帐，全电发票如果有发票代码和校验报错
        if (InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(supplierInvoiceDO.getInvoiceType())) && (StringUtils.isNotBlank(supplierInvoiceDO.getInvoiceCode()) || StringUtils.isNotBlank(supplierInvoiceDO.getCheckCode()))) {
            throw BizException.create(InvoiceErrorCodeEnum.NULL_INVOICE_CODE);
        }
        // 发票台账存在，不能操作
        InvoiceDO existInvoice = invoiceService.selectInvoiceByInvoiceCodeAndNo(supplierInvoiceDO.getInvoiceCode(), supplierInvoiceDO.getInvoiceNo());
        if (Objects.nonNull(existInvoice)) {
            throw BizException.create(InvoiceErrorCodeEnum.EXIST_INVOICE);
        }
        //发票日期校验
        String checkBuyerResult = InvoiceBuyerValidator.validInvoiceBuyer(BelongSubjectTypeEnum.XIAODIAN.getCode(), supplierInvoiceDO.getBuyer(),
                LocalDateUtil.localDateToDate(supplierInvoiceDO.getInvoiceDate()), true);
        Assert.notHasText(checkBuyerResult, checkBuyerResult);

        List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList = detailManager.findListBySupplierInvoiceId(supplierInvoiceDO.getId());
        Assert.notNull(supplierInvoiceDetailDOList, "供应商发票主体下的详情不存在");

        SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO = attachmentManager.findInvoicePicture(supplierInvoiceId);
        Assert.notNull(supplierInvoiceAttachmentDO, "供应商发票图片不存在");

        AgentDTO agentDTO = invoiceService.getInvoiceBelongInfo(userReq.getUserId());
        if (Objects.isNull(agentDTO)) {
            log.error("当前登录者的渠道信息获取失败,userReq:{}", userReq);
            throw BizException.create(InvoiceCommentErrorCodeEnum.USER_LOGIN_IS_INVALID_MAST_REGISTER);
        }

        try {
            InvoiceDO invoiceDO = SupplierInvoiceConverter.toInvoice(userReq, supplierInvoiceDO, supplierInvoiceAttachmentDO, agentDTO);
            List<InvoiceDetailDO> invoiceDetailDOList = SupplierInvoiceDetailConverter.toInvoiceDetailList(supplierInvoiceDetailDOList);

            supplierInvoiceManager.saveInvoiceAndDetail(invoiceDO, invoiceDetailDOList, supplierInvoiceId, userReq);
        } catch (Exception e) {
            log.error("转发票台账失败", e);
        }
    }

    @Override
    public String excelImport(CurrentUserReq userReq, List<SupplierInvoiceImportParam> paramList, Set<String> supplierNos) {
        log.info("供应商发票导入——,userReq:{},paramList:{}", userReq, paramList);
        Assert.notEmpty(paramList, "不能导入空模板");

        // 权限校验
//        if (!Objects.equals(EmployeeRoleEnum.PO_OPERATOR.getDesc(), userReq.getCurrentRole())) {
//            throw BizException.create(InvoiceErrorCodeEnum.NO_PERMISSION_PO_OPERATOR);
//        }

        // 错误明细集合
        Set<SupplierInvoiceImportErrorDTO> errorSet = new HashSet<>();
        // excel 转换后的 detail
        List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList = new ArrayList<>();

        this.checkExcelImport(paramList, userReq, errorSet, supplierInvoiceDetailDOList, supplierNos);

        // 同一张发票的主体
        Map<String, SupplierInvoiceDO> ukInvoiceMap = new HashMap<>();
        // 同一张发票的明细列表
        Map<String, List<SupplierInvoiceDetailDO>> ukInvoiceDetailListMap = new HashMap<>();

        // 分组明细，并汇总主体
        for (SupplierInvoiceDetailDO invoiceDetailDO : supplierInvoiceDetailDOList) {
            String uk = invoiceDetailDO.getInvoiceNo() + "-" + invoiceDetailDO.getInvoiceCode();
            // 明细分组
            List<SupplierInvoiceDetailDO> invoiceDetailDOList = ukInvoiceDetailListMap.get(uk);
            if (CollectionUtils.isEmpty(invoiceDetailDOList)) {
                invoiceDetailDOList = new ArrayList<>();
                ukInvoiceDetailListMap.put(uk, invoiceDetailDOList);
            }
            invoiceDetailDOList.add(invoiceDetailDO);

            // 追加供应商发票主体
            SupplierInvoiceDO supplierInvoiceDO = ukInvoiceMap.get(uk);
            if (Objects.isNull(supplierInvoiceDO)) {
                supplierInvoiceDO = new SupplierInvoiceDO();
                ukInvoiceMap.put(uk, supplierInvoiceDO);
            }
            SupplierInvoiceConverter.appendSupplierInvoice(supplierInvoiceDO, invoiceDetailDO, userReq.getUserId());
        }

        Iterator<Map.Entry<String, SupplierInvoiceDO>> iterator = ukInvoiceMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, SupplierInvoiceDO> entry = iterator.next();
            SupplierInvoiceDO supplierInvoiceDO = entry.getValue();

            SupplierInvoiceImportErrorDTO errorDTO = new SupplierInvoiceImportErrorDTO();
            errorDTO.setInvoiceNo(supplierInvoiceDO.getInvoiceNo());
            errorDTO.setInvoiceCode(supplierInvoiceDO.getInvoiceCode() == null ? "" : supplierInvoiceDO.getInvoiceCode());

            // 在遍历时无法感知的错误集合，在这里剔除
            if (errorSet.contains(errorDTO)) {
                iterator.remove();
                continue;
            }

            BigDecimal rawPrice = supplierInvoiceDO.getRawPrice();
            BigDecimal tax = supplierInvoiceDO.getTax();
            BigDecimal price = supplierInvoiceDO.getPrice();

            // 汇总金额校验  金额校验：税前金额+税额=价税合计（明细和汇总均需校验）
            if (rawPrice.add(tax).compareTo(price) != 0) {
                errorDTO.setTip("【金额汇总校验】税前金额+税额不等于价税合计");
                errorSet.add(errorDTO);
                iterator.remove();
            }
        }

        // 保存到数据库
        for (Map.Entry<String, SupplierInvoiceDO> entry : ukInvoiceMap.entrySet()) {
            try {
                supplierInvoiceManager.save(entry, ukInvoiceDetailListMap, userReq);
            } catch (Exception e) {
                SupplierInvoiceImportErrorDTO tip = new SupplierInvoiceImportErrorDTO();
                tip.setInvoiceNo(entry.getValue().getInvoiceNo());
                tip.setInvoiceCode(entry.getValue().getInvoiceCode());
                tip.setTip("供应商发票——保存到数据库失败");
                errorSet.add(tip);
                log.warn("供应商发票导入——保存到数据库失败", e);
            }
        }
        log.info("excel 错误的数据:{}", errorSet);
        excelHandler.asyncExportToEmail(errorSet, SupplierInvoiceImportErrorDTO.class, userReq);

        String resultStr = String.format("导入总条数:%s ,", paramList.size());
        if (CollectionUtils.isEmpty(errorSet)) {
            resultStr += "全部导入成功";
        } else {
            resultStr += "部分导入失败,失败原因请查看邮件";
        }
        return resultStr;
    }

    /**
     * Excel 数据校验
     *
     * @param paramList
     * @param userReq
     * @param errorSet
     * @param supplierInvoiceDetailDOList
     */
    private void checkExcelImport(List<SupplierInvoiceImportParam> paramList, CurrentUserReq userReq, Set<SupplierInvoiceImportErrorDTO> errorSet, List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList, Set<String> supplierNos) {
        // k:发票代码和发票号码,v:发票类型、发票日期、开票方名称、开票方税号、校验码、购买方名、购买方税号
        Map<String, String> ukCheckMap = new HashMap<>();
        // 获取当前采购单操作员可以操作的供应商
//        Set<String> supplierNos = supplierService.getSupplierNosByMobile(userReq.getMobile());

        for (SupplierInvoiceImportParam param : paramList) {
            SupplierInvoiceImportErrorDTO errorDTO = new SupplierInvoiceImportErrorDTO();
            errorDTO.setInvoiceNo(param.getInvoiceNo());
            errorDTO.setInvoiceCode(param.getInvoiceCode() == null ? "" : param.getInvoiceCode());

            String uk = param.getInvoiceNo() + "-" + param.getInvoiceCode();

            // 跳过错误的明细
            if (errorSet.contains(errorDTO)) {
                continue;
            } else {
                errorSet.add(errorDTO);
            }
            // 参数 trim
            param.trim();

            if (StringUtils.isBlank(param.getInvoiceTypeStr())) {
                errorDTO.setTip("【发票类型】不能为空");
                continue;
            }
            SupplierInvoiceTypeEnum invoiceTypeEnum = LocalEnumUtils.findByDesc(SupplierInvoiceTypeEnum.class, param.getInvoiceTypeStr().trim());
            if (Objects.isNull(invoiceTypeEnum)) {
                errorDTO.setTip("【发票类型】不存在");
                continue;
            }
            if (InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(LocalEnumUtils.findByDesc(SupplierInvoiceTypeEnum.class, param.getInvoiceTypeStr()).getCode())) && StringUtils.isNotBlank(param.getInvoiceCode())) {
                errorDTO.setTip("【发票代码】全电发票，发票代码必须为空");
                continue;
            }
            if (InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(LocalEnumUtils.findByDesc(SupplierInvoiceTypeEnum.class, param.getInvoiceTypeStr()).getCode())) && StringUtils.isNotBlank(param.getCheckCode())) {
                errorDTO.setTip("【发票校验码】全电发票，校验码必须为空");
                continue;
            }
            if (!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(LocalEnumUtils.findByDesc(SupplierInvoiceTypeEnum.class, param.getInvoiceTypeStr()).getCode())) && StringUtils.isBlank(param.getInvoiceCode())) {
                errorDTO.setTip("【发票代码】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getInvoiceNo())) {
                errorDTO.setTip("【发票号码】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getInvoiceDateStr())) {
                errorDTO.setTip("【发票日期】不能为空");
                continue;
            }
            LocalDate invoiceDate = null;
            try {
                invoiceDate = LocalDateUtil.parseToDate(param.getInvoiceDateStr(), LocalDateUtil.yyyy_slash_MM_slash_dd);
            } catch (Exception e) {
                log.warn("供应商发票导入日期按yyyy/MM/dd解析错误警告,invoiceDateStr:{}", param.getInvoiceDateStr(), e);
            }
            if (Objects.isNull(invoiceDate)) {
                try {
                    invoiceDate = LocalDateUtil.parseToDate(param.getInvoiceDateStr(), LocalDateUtil.yyyy_MM_dd);
                } catch (Exception e) {
                    log.warn("供应商发票导入日期按yyyy_MM_dd解析错误警告,invoiceDateStr:{}", param.getInvoiceDateStr(), e);
                }
            }
            if (Objects.isNull(invoiceDate)) {
                errorDTO.setTip(String.format("【发票日期】格式错误-【%s】", param.getInvoiceDateStr() ));
                continue;
            }

            if (StringUtils.isBlank(param.getSellerName())) {
                errorDTO.setTip("【开票方名称】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getSellerTaxId())) {
                errorDTO.setTip("【开票方税号】不能为空");
                continue;
            }
            // 发票类型为【增值税专用发票】时校验码可以为空，发票类型是全电发票校验码可以为空
            if (!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(Integer.valueOf(LocalEnumUtils.findByDesc(SupplierInvoiceTypeEnum.class, param.getInvoiceTypeStr()).getCode()))) &&
                    StringUtils.isBlank(param.getCheckCode()) && (!invoiceTypeEnum.equals(SupplierInvoiceTypeEnum.VAT))) {
                errorDTO.setTip("【校验码】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getBuyer())) {
                errorDTO.setTip("【购买方名称】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getBuyerTaxId())) {
                errorDTO.setTip("【购买方税号】不能为空");
                continue;
            }
            // 购买方名称和购买方税号必须是已有的我司税号范围且匹配的
            boolean buyerTaxIdFlag = BuyerTaxIdEnum.checkInvoiceBuyerAndTaxId(param.getBuyer(), param.getBuyerTaxId());
            if (!buyerTaxIdFlag) {
                errorDTO.setTip(String.format("【购买方名称-购买方税号】不匹配我司税号-【%s-%s】", param.getBuyer(), param.getBuyerTaxId()));
                continue;
            }

            if (StringUtils.isBlank(param.getMaterialName())) {
                errorDTO.setTip("【物料名称】不能为空");
                continue;
            }
            if (Objects.isNull(param.getQuantity())) {
                errorDTO.setTip("【数量】不能为空");
                continue;
            }
            int quantity;
            try {
                quantity = Integer.parseInt(param.getQuantity());
            } catch (Exception e) {
                errorDTO.setTip("【数量】必须数字");
                continue;
            }
            if (quantity <= 0) {
                errorDTO.setTip("【数量】必须大于0");
                continue;
            }

            if (StringUtils.isBlank(param.getUnitPriceStr())) {
                errorDTO.setTip("【不含税单价】不能为空");
                continue;
            }

            if (StringUtils.isBlank(param.getRawPriceStr())) {
                errorDTO.setTip("【不含税金额】不能为空");
                continue;
            }

            if (StringUtils.isBlank(param.getTaxRateStr())) {
                errorDTO.setTip("【税率】不能为空");
                continue;
            }
            String taxRateStr = param.getTaxRateStr().replace("%", "");
            BigDecimal taxRate;
            try {
                taxRate = new BigDecimal(taxRateStr);
            } catch (Exception e) {
                log.warn("【税率】格式错误，必须为数字,taxRateStr:{}", taxRateStr, e);
                errorDTO.setTip(String.format("【税率】格式错误，必须为数字-【%s】",param.getTaxRateStr()));
                continue;
            }
            if (taxRate.compareTo(new BigDecimal("20")) > 0) {
                errorDTO.setTip("【税率】不能超过20%");
                continue;
            }
            if (taxRate.compareTo(new BigDecimal("0")) < 0) {
                errorDTO.setTip("【税率】不能低于0%");
                continue;
            }

            if (StringUtils.isBlank(param.getTaxStr())) {
                errorDTO.setTip("【税额】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getPriceStr())) {
                errorDTO.setTip("【价税合计】不能为空");
                continue;
            }

            if (StringUtils.isBlank(param.getPurchaseBatchBillNo())) {
                errorDTO.setTip("【采购批次】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getPurchaseOrderBillNo())) {
                errorDTO.setTip("【采购单号】不能为空");
                continue;
            }
            if (StringUtils.isBlank(param.getVerifyBillNo())) {
                errorDTO.setTip("【对账单号】不能为空");
                continue;
            }

            BigDecimal rawPrice, tax, price;
            try {
                rawPrice = new BigDecimal(param.getRawPriceStr())
                        .setScale(4, RoundingMode.DOWN);
            } catch (Exception e) {
                log.warn("【税前金额】格式错误，必须为数字,taxRateStr:{}", param.getRawPriceStr(), e);
                errorDTO.setTip(String.format("【税前金额】格式错误，必须为数字-【%s】", param.getRawPriceStr()));
                continue;
            }
            try {
                tax = new BigDecimal(param.getTaxStr())
                        .setScale(4, RoundingMode.DOWN);
            } catch (Exception e) {
                log.warn("【税额】格式错误，必须为数字,tax:{}", param.getTaxStr(), e);
                errorDTO.setTip(String.format("【税额】格式错误，必须为数字-【%s】", param.getTaxStr()));
                continue;
            }
            try {
                price = new BigDecimal(param.getPriceStr())
                        .setScale(4, RoundingMode.DOWN);
            } catch (Exception e) {
                log.warn("【价税合计】格式错误，必须为数字,price:{}", param.getPriceStr(), e);
                errorDTO.setTip(String.format("【价税合计】格式错误，必须为数字-【%s】", param.getPriceStr()));
                continue;
            }
            // 金额校验：税前金额+税额=价税合计（明细和汇总均需校验）
            if (rawPrice.add(tax).compareTo(price) != 0) {
                errorDTO.setTip("【金额明细校验】税前金额+税额不等于价税合计");
                continue;
            }
            // 发票代码和发票号码同一项中，发票类型、发票日期、开票方名称、开票方税号、校验码、购买方名、购买方税号要一致
            String unionStr = ukCheckMap.get(uk);
            String nowUnionStr = param.getInvoiceTypeStr() + "-" + param.getInvoiceDateStr() + "-" + param.getSellerName() + "-" +
                    param.getSellerTaxId() + "-" + param.getBuyer() + "-" + param.getBuyerTaxId();
            if (StringUtils.isBlank(unionStr)) {
                ukCheckMap.put(uk, nowUnionStr);
            } else {
                if (!Objects.equals(unionStr, nowUnionStr)) {
                    errorDTO.setTip("同一张发票中，发票类型、发票日期、开票方名称、开票方税号、校验码、购买方名、购买方税号并不一致");
                    continue;
                }
            }

            // 如果发票代码和发票号码在系统存在且未删除，对应发票不可导入
            String invoiceCode = Objects.isNull(param.getInvoiceCode()) ? StrUtil.EMPTY : param.getInvoiceCode();
            SupplierInvoiceDO existInvoiceDO = supplierInvoiceManager.findSupplierNoCode(param.getInvoiceNo(), invoiceCode);
            if (Objects.nonNull(existInvoiceDO)) {
                errorDTO.setTip("发票代码和发票号码在系统存在且未删除，请删除后再导入");
                continue;
            }

            // 采购订单号：必须在供应链系统存在，且供应链系统存的供应商标识和当前登陆人的供应商标识一致
            PurchaseOrderDetailRsp purchaseOrderDetailRsp = supplierService.getPurchaseOrderByPurchaseNo(param.getPurchaseOrderBillNo());
            if (Objects.isNull(purchaseOrderDetailRsp)) {
                log.warn("供应商发票导入——【采购单号】在供应链系统不存在对应采购订单,采购订单号:{}", param.getPurchaseOrderBillNo());
                errorDTO.setTip(String.format("【采购单号】在供应链系统不存在对应采购订单,采购订单号:【%s】",param.getPurchaseOrderBillNo()));
                continue;
            }

            if (!supplierNos.contains(purchaseOrderDetailRsp.getSupplierNo())) {
                errorDTO.setTip("【操作供应商数据权限校验】采购单操作员无权操作该供应商数据");
                continue;
            }
            // 移除无错误的
            errorSet.remove(errorDTO);

            // 组装明细
            SupplierInvoiceDetailDO invoiceDetailDO = SupplierInvoiceDetailConverter.toDO(param, purchaseOrderDetailRsp);
            supplierInvoiceDetailDOList.add(invoiceDetailDO);
        }
    }


}
