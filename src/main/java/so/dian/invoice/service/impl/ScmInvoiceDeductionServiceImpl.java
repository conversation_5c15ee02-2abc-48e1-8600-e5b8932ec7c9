package so.dian.invoice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.commons.eden.util.LocalMapUtils;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.converter.InvoiceDeductionConverter;
import so.dian.invoice.converter.InvoiceOperateLogConverter;
import so.dian.invoice.converter.ScmInvoiceDeductionConverter;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.enums.BuyerTaxIdEnum;
import so.dian.invoice.enums.InvoiceStatusEnum;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.enums.SupplierInvoiceBillNoEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.manager.InvoiceDeductionManager;
import so.dian.invoice.manager.InvoiceDetailManager;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.manager.InvoiceOperateLogManager;
import so.dian.invoice.manager.SupplierInvoiceDetailManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.bo.InvoiceQueryBO;
import so.dian.invoice.pojo.dto.ScmInvoiceDeductionDTO;
import so.dian.invoice.pojo.dto.ScmInvoiceInfoDTO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.param.OperatorParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductBatchParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductQueryParam;
import so.dian.invoice.pojo.param.ScmInvoiceRecoverBatchParam;
import so.dian.invoice.pojo.param.ScmInvoiceRecoverParam;
import so.dian.invoice.service.ScmInvoiceDeductionService;
import so.dian.invoice.util.StringUtil;

@Slf4j
@Service
public class ScmInvoiceDeductionServiceImpl implements ScmInvoiceDeductionService {

    @Autowired
    private InvoiceManager invoiceManager;
    @Autowired
    private InvoiceDetailManager invoiceDetailManager;
    @Autowired
    private SupplierInvoiceDetailManager supplierInvoiceDetailManager;
    @Autowired
    private InvoiceDeductionManager invoiceDeductionManager;
    @Autowired
    private InvoiceOperateLogManager invoiceOperateLogManager;

    @Override
    public List<ScmInvoiceInfoDTO> queryByInvoiceId(List<Long> invoiceIds) {
        //查询发票
        List<InvoiceBO> list = invoiceManager.listByIds(invoiceIds);
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        //查询发票明细的供应链发票ID
        List<Integer> supplierInvoiceDetailIdList = invoiceDetailManager.findSupplierInvoiceDetailId(list);
        //查询供应链发票
        List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOS
                = supplierInvoiceDetailManager.findListById(supplierInvoiceDetailIdList);

        return ScmInvoiceDeductionConverter.buildInvoiceBOList(list, supplierInvoiceDetailDOS);
    }

    @Override
    public List<ScmInvoiceInfoDTO> queryInvoiceList(ScmInvoiceDeductQueryParam param) {

        if (StringUtils.isNotBlank(param.getVerifyBillNo())) {
            //获取对账单号的发票
            List<SupplierInvoiceDetailDO> detailDOList =
                    supplierInvoiceDetailManager.findByBillTypeAndBillNo(SupplierInvoiceBillNoEnum.VERIFY,
                            param.getVerifyBillNo());
            if (CollectionUtil.isEmpty(detailDOList)) {
                return Lists.newArrayList();
            }
            List<Map<String, String>> invoiceMapList = Lists.newArrayList();
            Set<String> invoiceCodeSet = Sets.newHashSet();
            Set<String> invoiceNoSet = Sets.newHashSet();
            Set<String> invoiceUKSet = Sets.newHashSet();
            for (SupplierInvoiceDetailDO obj : detailDOList) {
                String invoiceCode = obj.getInvoiceCode();
                String invoiceNo = obj.getInvoiceNo();
                Map<String, String> map = Maps.newHashMap();
                map.put("invoiceCode", invoiceCode == null ? "" : invoiceCode);
                map.put("invoiceNo", invoiceNo);
                invoiceMapList.add(map);
                invoiceCodeSet.add(invoiceCode == null ? "" : invoiceCode);
                invoiceNoSet.add(invoiceNo);
                invoiceUKSet.add(ScmInvoiceDeductionConverter.getUk(invoiceNo, invoiceCode));
            }
            if (StringUtils.isBlank(param.getInvoiceCode())) {
                param.setInvoiceCode("");
            }
            //查询的发票是否在对账单发票中
            if (CollectionUtil.isNotEmpty(invoiceMapList)) {
                param.setInvoiceMapList(invoiceMapList);
                if (StringUtils.isNotBlank(param.getInvoiceCode())
                        && StringUtils.isNotBlank(param.getInvoiceNo())) {
                    String key = ScmInvoiceDeductionConverter.getUk(param.getInvoiceNo(), param.getInvoiceCode());
                    if (!invoiceUKSet.contains(key)) {
                        return Lists.newArrayList();
                    }
                } else if (StringUtils.isNotBlank(param.getInvoiceNo())) {
                    if (!invoiceNoSet.contains(param.getInvoiceNo())) {
                        return Lists.newArrayList();
                    }
                } else if (StringUtils.isNotBlank(param.getInvoiceCode())) {
                    if (!invoiceCodeSet.contains(param.getInvoiceCode())) {
                        return Lists.newArrayList();
                    }
                }
            }
        }
        String sellerName = param.getSellerName();
        if (StringUtils.isNotBlank(sellerName) && sellerName.contains("杭州满格")) {
            param.setSellerNames(Arrays.asList("杭州满格网络科技有限公司", "杭州满格智能设备有限公司"));
            param.setSellerName(null);
        }
        String buyerName = param.getBuyerName();
        if (StringUtils.isNotBlank(buyerName) && buyerName.contains("杭州满格")) {
            param.setBuyerNames(Arrays.asList("杭州满格网络科技有限公司", "杭州满格智能设备有限公司"));
            param.setBuyerName(null);
        }
        List<InvoiceBO> list = invoiceManager.queryInvoiceList(param);
        //查询发票明细的供应链发票ID
        List<Integer> supplierInvoiceDetailIdList = invoiceDetailManager.findSupplierInvoiceDetailId(list);
        //查询供应链发票
        List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOS
                = supplierInvoiceDetailManager.findListById(supplierInvoiceDetailIdList);

        return ScmInvoiceDeductionConverter.buildInvoiceBOList(list, supplierInvoiceDetailDOS);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public List<ScmInvoiceDeductionDTO> batchDeductInvoice(ScmInvoiceDeductBatchParam params) {
        if (Objects.isNull(params) || CollectionUtil.isEmpty(params.getList())) {
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "发票不能为空");
        }
        List<ScmInvoiceDeductParam> list = params.getList();

        List<Long> invoiceIdList = list.stream().map(ScmInvoiceDeductParam::getInvoiceId).collect(Collectors.toList());
        List<InvoiceBO> invoiceBOList = invoiceManager.listByIds(invoiceIdList);
        if (CollectionUtil.isEmpty(invoiceBOList)) {
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "发票不存在，请重新选择发票核销");
        }
        Map<Integer, InvoiceBO> InvoiceBOMap = invoiceBOList.stream()
                .collect(Collectors.toMap(InvoiceBO::getId, obj -> obj));
        OperatorParam operatorParam = new OperatorParam();
        operatorParam.setOperatorId(params.getOperatorId());
        operatorParam.setOperatorName(params.getOperatorName());
        operatorParam.setRemark(params.getRemark());

        List<ScmInvoiceDeductionDTO> reList = Lists.newArrayList();
        for (ScmInvoiceDeductParam scmInvoiceDeductParam : list) {
            InvoiceBO invoiceBO = InvoiceBOMap.get(scmInvoiceDeductParam.getInvoiceId().intValue());
            ScmInvoiceDeductionDTO invoiceDeductionDTO = deductInvoice(scmInvoiceDeductParam, operatorParam, invoiceBO);
            reList.add(invoiceDeductionDTO);
        }

        return reList;
    }

    private ScmInvoiceDeductionDTO deductInvoice(ScmInvoiceDeductParam param, OperatorParam operatorParam,
            InvoiceBO invoiceBO) {
        if (Objects.isNull(invoiceBO)) {
            log.error("批量核销发票,发票不存在！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST, "发票不存在，请重新选择发票核销");
        }
        String invoiceCode = param.getInvoiceCode();
        String invoiceNo = param.getInvoiceNo();
        String businessNo = param.getBusinessNo();
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.findByType(param.getBusinessType());

        if (!Objects.equals(invoiceNo, invoiceBO.getInvoiceNo())
                || !Objects.equals(invoiceCode, invoiceBO.getInvoiceCode())) {
            log.error("批量核销发票,发票不存在！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST,
                    "发票不存在，请重新选择发票核销！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        if (!InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(invoiceBO.getBelongSubjectId())) {
            log.error("批量核销发票,只有小电主体的发票才能参与核销！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "只有小电主体的发票才能参与核销！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        //匹配业务类型
        if (!Objects.equals(param.getBusinessType(), invoiceBO.getSubjectType())) {
            log.error("批量核销发票,发票业务类型不一致！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_SUBJECT_TYPE_ERROR,
                    "发票业务类型不一致！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }

        String taxId = BuyerTaxIdEnum.getTaxIdByBuyer(invoiceBO.getBuyer());
        if (StringUtils.isBlank(taxId)) {
            log.error("批量核销发票，发票抬头不正确.{}", invoiceBO);
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_BUYER_INVALID,
                    "发票抬头不正确！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        //购买方
        if (!Objects.equals(invoiceBO.getBuyer(), param.getBuyerName())) {
            log.error("批量核销发票,发票购买方和签约主体不一致！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票购买方和签约主体不一致！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        //开票方
        if (StringUtils.isBlank(param.getSellerName())) {
            log.error("批量核销发票,发票主体名称不能为空！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票主体名称不能为空！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        String subjectName = StringUtil.trimFull2Half(invoiceBO.getSubjectName());
        if (!Objects.equals(subjectName, StringUtil.trimFull2Half(param.getSellerName()))) {
            log.error("批量核销发票,发票主体名称不匹配！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票主体名称不匹配！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        //匹配金额
        BigDecimal remainAmount = invoiceBO.getPrice().subtract(invoiceBO.getUsedAmount());
        if (param.getAmount().compareTo(remainAmount) > 0) {
            log.error("批量核销发票,发票余额不足！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票余额不足！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        //检查是否已核销过
        List<InvoiceDeductionDO> invoiceDeductionDOList =
                invoiceDeductionManager.selectInvoiceDeductionList(invoiceCode, invoiceNo, businessNo, businessTypeEnum,
                        OperateTypeEnum.DEDUCT);
        if (CollectionUtil.isNotEmpty(invoiceDeductionDOList)) {
            log.error("批量核销发票,请勿重复核销同一张发票！param: {}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "请勿重复核销同一张发票！发票代码:" + invoiceCode + "，发票号码：" + invoiceNo);
        }
        //核销发票
        BigDecimal totalAmount = invoiceBO.getUsedAmount().add(param.getAmount()).setScale(2, RoundingMode.HALF_DOWN);
        InvoiceStatusEnum invoiceStatusEnum = InvoiceStatusEnum.PART;
        if (invoiceBO.getPrice().compareTo(totalAmount) == 0) {
            invoiceStatusEnum = InvoiceStatusEnum.ALL;
        }
        invoiceManager.deductInvoice(invoiceBO.getId(), invoiceStatusEnum.getType(), invoiceBO.getUsedAmount(),
                totalAmount);

        //核销记录
        InvoiceDeductionDO invoiceDeductionDO = InvoiceDeductionConverter.deductInvoice(param, operatorParam);
        invoiceDeductionManager.insertSelective(invoiceDeductionDO);
        //核销日志
        InvoiceOperateLogBO invoiceOperateLogBO =
                InvoiceOperateLogConverter.deductInvoiceLog(invoiceBO.getId(), invoiceBO.getUsedAmount(),
                        invoiceDeductionDO);
        invoiceOperateLogManager.insert(invoiceOperateLogBO);

        //
        invoiceDeductionDO = invoiceDeductionManager.selectByPrimaryKey(invoiceDeductionDO.getId());

        return ScmInvoiceDeductionConverter.buildByInvoiceDeductionDO(invoiceBO.getId(), invoiceDeductionDO);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public List<Long> batchRecoverInvoice(ScmInvoiceRecoverBatchParam params) {
        if (Objects.isNull(params) || CollectionUtil.isEmpty(params.getList())) {
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "发票不能为空");
        }
        List<ScmInvoiceRecoverParam> list = params.getList();

        List<Long> deductionIdList = list.stream().map(ScmInvoiceRecoverParam::getDeductionId)
                .collect(Collectors.toList());

        List<InvoiceDeductionDO> deductionDOList = invoiceDeductionManager.findByIdIn(deductionIdList);
        if (CollectionUtil.isEmpty(deductionDOList)) {
            log.error("发票回滚，发票核销记录不存在。params: {}", list);
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_DEDUCTION_NOT_EXIST);
        }

        List<InvoiceQueryBO> queryBOList = LocalListUtils.transferList(list,
                invoiceRecoverParam -> InvoiceQueryBO.builder()
                        .invoiceCode(invoiceRecoverParam.getInvoiceCode() == null ? ""
                                : invoiceRecoverParam.getInvoiceCode())
                        .invoiceNo(invoiceRecoverParam.getInvoiceNo())
                        .build()
        );
        List<InvoiceBO> invoiceBOList = invoiceManager.findByInvoiceNoAndInvoiceCode(queryBOList);
        if (CollectionUtils.isEmpty(invoiceBOList)) {
            log.error("发票回滚，发票不存在。params: {}", list);
            throw BizException.create(InvoiceErrorCodeEnum.RECOVER_ERROR, "发票不存在");
        }

        Map<Integer, InvoiceDeductionDO> deductionDOMap =
                deductionDOList.stream().collect(Collectors.toMap(InvoiceDeductionDO::getId, obj -> obj));
        Map<String, InvoiceBO> invoiceBOMap = LocalMapUtils.listAsHashMap(invoiceBOList,
                obj -> obj.getInvoiceCode() + StrUtil.UNDERLINE + obj.getInvoiceNo());
        OperatorParam operatorParam = new OperatorParam();
        operatorParam.setOperatorId(params.getOperatorId());
        operatorParam.setOperatorName(params.getOperatorName());
        operatorParam.setRemark(params.getRemark());

        List<Long> reList = Lists.newArrayList();
        for (ScmInvoiceRecoverParam scmInvoiceRecoverParam : list) {
            InvoiceDeductionDO invoiceDeductionDO = deductionDOMap.get(
                    scmInvoiceRecoverParam.getDeductionId().intValue());
            InvoiceBO invoiceBO = invoiceBOMap.get(
                    scmInvoiceRecoverParam.getInvoiceCode() + StrUtil.UNDERLINE
                            + scmInvoiceRecoverParam.getInvoiceNo());
            recoverInvoice(scmInvoiceRecoverParam, operatorParam, invoiceDeductionDO, invoiceBO);
            reList.add(scmInvoiceRecoverParam.getDeductionId());
        }
        return reList;
    }

    public void recoverInvoice(ScmInvoiceRecoverParam param, OperatorParam operatorParam,
            InvoiceDeductionDO invoiceDeductionDO, InvoiceBO invoiceBO) {
        if (Objects.isNull(invoiceBO)) {
            log.error("发票回滚，发票不存在。{}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票不存在！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        if (Objects.isNull(invoiceDeductionDO)) {
            log.error("发票回滚，发票核销记录不存在。{}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票核销记录不存在！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }

        if (!Objects.equals(invoiceDeductionDO.getBusinessNo(), param.getBusinessNo())) {
            log.error("发票回滚，业务单号不匹配。{}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "业务单号不匹配！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        if (Objects.equals(invoiceDeductionDO.getOperateType(), OperateTypeEnum.RECOVER.getType())) {
            log.error("发票回滚，核销记录已回滚。{}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "核销记录已回滚！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        if (invoiceDeductionDO.getAmount().compareTo(param.getAmount()) != 0) {
            log.error("发票回滚，核销金额不一致。{}", param);
            throw BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "核销金额不一致！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }

        //回滚发票
        InvoiceStatusEnum invoiceStatusEnum = InvoiceStatusEnum.PART;
        if (invoiceBO.getUsedAmount().compareTo(param.getAmount()) == 0) {
            invoiceStatusEnum = InvoiceStatusEnum.WAIT;
        }
        invoiceManager.recoverInvoice(invoiceBO.getId(), invoiceStatusEnum.getType(), param.getAmount());
        //更新核销明细
        invoiceDeductionManager.recoverInvoice(invoiceDeductionDO.getId());
        //添加回滚日志
        InvoiceOperateLogBO invoiceOperateLogBO =
                InvoiceOperateLogConverter.recoverInvoiceLog(invoiceBO.getId(), invoiceBO.getUsedAmount(),
                        invoiceDeductionDO.getAmount(), operatorParam);
        invoiceOperateLogManager.insert(invoiceOperateLogBO);
    }

    @Override
    public List<ScmInvoiceDeductionDTO> getDeductInvoiceList(List<Long> deductionIds) {
        List<InvoiceDeductionDO> list = invoiceDeductionManager.findByIdIn(deductionIds);
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<InvoiceQueryBO> invoiceQueryBOList = list.stream().map(obj -> {
            InvoiceQueryBO invoiceQueryBO = new InvoiceQueryBO();
            invoiceQueryBO.setInvoiceNo(obj.getInvoiceNo());
            invoiceQueryBO.setInvoiceCode(obj.getInvoiceCode() == null ? "" : obj.getInvoiceCode());
            return invoiceQueryBO;
        }).collect(Collectors.toList());
        List<InvoiceBO> invoiceBOList = invoiceManager.findByInvoiceNoAndInvoiceCode(invoiceQueryBOList);
        return ScmInvoiceDeductionConverter.buildByInvoiceDeductionDOList(list, invoiceBOList);
    }
}
