package so.dian.invoice.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.invoice.converter.InvoiceExpressesRelationConverter;
import so.dian.invoice.converter.InvoiceExpressesVOConverter;
import so.dian.invoice.dao.ExpressesDAO;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceExpressesRelationDAO;
import so.dian.invoice.pojo.bo.InvoiceExpressesRelationBO;
import so.dian.invoice.pojo.dto.InvoiceExpressesDto;
import so.dian.invoice.pojo.dto.InvoiceExpressesMsgDto;
import so.dian.invoice.pojo.dto.InvoiceExpressesUpdateDto;
import so.dian.invoice.pojo.entity.ExpressesDO;
import so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;
import so.dian.invoice.pojo.param.InvoiceExpressBatchAddParam;
import so.dian.invoice.pojo.query.InvoiceExpressQuery;
import so.dian.invoice.pojo.query.InvoiceExpressesRelationQuery;
import so.dian.invoice.service.InvoiceExpressesRelationService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.util.LocalDateUtil;
import so.dian.invoice.util.ServiceResult;
import so.dian.invoice.util.StringUtil;
import so.dian.invoice.pojo.vo.ExpressesVO;
import so.dian.invoice.pojo.vo.InvoiceExpressesVO;
import so.dian.lvy.pojo.enums.DeletedEnum;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 8:53 PM
 * @Description:
 */
@Service
@Slf4j
public class InvoiceExpressesRelationServiceImpl implements InvoiceExpressesRelationService {

    @Autowired
    private InvoiceDAO invoiceDAO;
    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private InvoiceExpressesRelationDAO invoiceExpressesRelationDAO;
    @Autowired
    private ExpressesDAO expressesDAO;
    @Autowired
    private RedisTemplate redisTemplate;

    final static List<NameValueDTO> invoiceProcessStatusEnumList
            = LocalEnumUtils.getEnumCodeAndDesc(InvoiceProcessStatusEnum.class);

    @Override
    public BizResult<List<NameValueDTO>> listInvoiceExpressesRelationStatus() {
        return BizResult.create(invoiceProcessStatusEnumList);
    }


    @Override
    public List<InvoiceExpressesVO> findInvoiceExpressesVO(InvoiceExpressQuery invoiceExpressQuery) {

        Assert.notNull(invoiceExpressQuery, "查询条件不能为空");
        Assert.notNull(invoiceExpressQuery.getEndCreateTime(), "创建结束时间查询条件不能为空");
        Assert.notNull(invoiceExpressQuery.getStartCreateTime(), "创建开始时间查询条件不能为空");

        LocalDate endCreateTime = LocalDateUtil.parseToDate(invoiceExpressQuery.getEndCreateTime());
        LocalDate startCreateTime = LocalDateUtil.parseToDate(invoiceExpressQuery.getStartCreateTime());
        Long diffDaysTemp = endCreateTime.toEpochDay() - startCreateTime.toEpochDay();
        Assert.isTrue(diffDaysTemp.compareTo(31L) <= 0, "查询条件创建时间差不能大于31");
        LocalDateTime localDateTime=LocalDateTime.of(endCreateTime, LocalTime.MAX);
        //前端页面数据给的日期到天，比如查询2019-09-01 的数据要小于2019-09-02之前的数据
        invoiceExpressQuery.setEndCreateTime(LocalDateUtil.localDateToStr(endCreateTime.plusDays(1)));
        if(StringUtil.isNotBlank(invoiceExpressQuery.getEndGmtCreate())){
            LocalDate endGmtCreate = LocalDateUtil.parseToDate(invoiceExpressQuery.getEndGmtCreate());
            invoiceExpressQuery.setEndGmtCreate(LocalDateUtil.localDateToStr(endGmtCreate.plusDays(1)));
        }

        invoiceExpressQuery.setDeleted(DeletedEnum.NOT_DELETED.getCode());
        List<InvoiceExpressesDto> invoiceExpressesDtoLists = invoiceDAO.selectInvoiceExpressesDto(invoiceExpressQuery);
        if (CollectionUtils.isEmpty(invoiceExpressesDtoLists)) {
            return Collections.emptyList();
        }

        List<InvoiceExpressesVO> invoiceExpressesVOResults = new ArrayList<>(invoiceExpressesDtoLists.size());
        for (InvoiceExpressesDto invoiceExpressesDto : invoiceExpressesDtoLists) {
            InvoiceExpressesVO invoiceExpressesVO = InvoiceExpressesVOConverter.fromInvoiceExpressesDTO(invoiceExpressesDto);
            if (Objects.nonNull(invoiceExpressesVO)) {
                invoiceExpressesVOResults.add(invoiceExpressesVO);
            }
        }

        return invoiceExpressesVOResults;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpsetInvoiceExpresses(InvoiceExpressBatchAddParam invoiceExpressBatchAddParam) {
        if(CollectionUtils.isEmpty(invoiceExpressBatchAddParam.getIds())){
            return;
        }

        //新增发票数据
        List<InvoiceExpressesRelationDO> batchAdds = new LinkedList<>();
        //更新发票数据
        List<Long> batchUpdateIds = new LinkedList<>();

        //key：发票id，value发票db查询对象
        Map<Long,InvoiceExpressesRelationDO> existInvoiceMap= getExistInvoiceIds(invoiceExpressBatchAddParam);

        for (Long invoiceID : invoiceExpressBatchAddParam.getIds()) {

            InvoiceExpressesRelationDO invoiceExpressesRelationDb=existInvoiceMap.get(invoiceID);
            //比较invoiceID 避免hash 碰撞导致问题
            if (Objects.nonNull(invoiceExpressesRelationDb)&&Objects.equals(invoiceExpressesRelationDb.getInvoiceId(),invoiceID)) {
                batchUpdateIds.add(invoiceExpressesRelationDb.getId());
            } else {
                InvoiceExpressesRelationDO invoiceExpressesRelationUpdate = new InvoiceExpressesRelationDO();
                invoiceExpressesRelationUpdate.setDeleted(DeletedEnum.NOT_DELETED.getCode());
                invoiceExpressesRelationUpdate.setExpressId(invoiceExpressBatchAddParam.getExpressId());
                invoiceExpressesRelationUpdate.setExpressName(invoiceExpressBatchAddParam.getExpressName());
                invoiceExpressesRelationUpdate.setExpressTrackingNo(invoiceExpressBatchAddParam.getExpressTrackingNo());
                invoiceExpressesRelationUpdate.setInvoiceId(invoiceID);

                invoiceExpressesRelationUpdate.setSerialNo(invoiceExpressBatchAddParam.getSerialNo());
                invoiceExpressesRelationUpdate.setOperatorId(invoiceExpressBatchAddParam.getOperatorId());
                invoiceExpressesRelationUpdate.setOperatorName(invoiceExpressBatchAddParam.getOperatorName());
                invoiceExpressesRelationUpdate.setExpressImg(invoiceExpressBatchAddParam.getExpressImg());

                batchAdds.add(invoiceExpressesRelationUpdate);
            }
        }
        invoiceService.batchUpdateInvoiceProcessStatus(InvoiceProcessStatusEnum.SENDING,invoiceExpressBatchAddParam.getIds());
        //批量修改
        batchUpdateInvoiceData(invoiceExpressBatchAddParam, batchUpdateIds);
        //批量新增
        if(!CollectionUtils.isEmpty(batchAdds)) {
            invoiceExpressesRelationDAO.insertBatch(batchAdds);
        }
    }

    private void batchUpdateInvoiceData(InvoiceExpressBatchAddParam invoiceExpressBatchAddParam, List<Long> batchUpdateIds) {
        if (!CollectionUtils.isEmpty(batchUpdateIds)) {
            InvoiceExpressesUpdateDto invoiceExpressesUpdateDto = new InvoiceExpressesUpdateDto();
            invoiceExpressesUpdateDto.setIds(batchUpdateIds);
            invoiceExpressesUpdateDto.setDeleted(DeletedEnum.NOT_DELETED.getCode());
            invoiceExpressesUpdateDto.setExpressId(invoiceExpressBatchAddParam.getExpressId());
            invoiceExpressesUpdateDto.setExpressName(invoiceExpressBatchAddParam.getExpressName());
            invoiceExpressesUpdateDto.setExpressTrackingNo(invoiceExpressBatchAddParam.getExpressTrackingNo());
            invoiceExpressesUpdateDto.setProcessStatus(InvoiceProcessStatusEnum.SENDING.getCode());

            invoiceExpressesUpdateDto.setSerialNo(invoiceExpressBatchAddParam.getSerialNo());
            invoiceExpressesUpdateDto.setOperatorId(invoiceExpressBatchAddParam.getOperatorId());
            invoiceExpressesUpdateDto.setOperatorName(invoiceExpressBatchAddParam.getOperatorName());
            invoiceExpressesUpdateDto.setExpressImg(invoiceExpressBatchAddParam.getExpressImg());
            invoiceExpressesRelationDAO.updateBatchInvoiceExpressesRelationById(invoiceExpressesUpdateDto);
        }
    }

    private  Map<Long,InvoiceExpressesRelationDO> getExistInvoiceIds(InvoiceExpressBatchAddParam invoiceExpressBatchAddParam) {
        List<InvoiceExpressesRelationDO> invoiceExpressesRelationDOS= invoiceExpressesRelationDAO.findSimpleInvoiceExpressesRelations(DeletedEnum.NOT_DELETED.getCode(),invoiceExpressBatchAddParam.getIds());

        Map<Long,InvoiceExpressesRelationDO> existInvoiceIds=new HashMap<>(invoiceExpressesRelationDOS.size());
        for (InvoiceExpressesRelationDO invoiceExpressesRelationDO:invoiceExpressesRelationDOS){
            existInvoiceIds.put(invoiceExpressesRelationDO.getInvoiceId(),invoiceExpressesRelationDO);
        }
        return existInvoiceIds;
    }

    @Override
    public List<ExpressesVO> findExpressesVO(String name) {
        if (StringUtils.isEmpty(name)) {
            return Collections.emptyList();
        }

        List<ExpressesVO> expressesVOList = new LinkedList<>();

        List<ExpressesDO> expressesDOS = expressesDAO.findExpresses(name, DeletedEnum.NOT_DELETED.getCode());

        if (!CollectionUtils.isEmpty(expressesDOS)) {
            for (ExpressesDO expressesDO : expressesDOS) {
                expressesVOList.add(new ExpressesVO(expressesDO.getId(), expressesDO.getExpressName()));
            }
        }
        return expressesVOList;
    }

    @Override
    public List<InvoiceExpressesMsgDto> findExpressesByInvoiceIds(List<Long> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.emptyList();
        }

        List<InvoiceExpressesRelationDO> expressesRelationDOList = invoiceExpressesRelationDAO.findInvoiceExpressesRelations(DeletedEnum.NOT_DELETED.getCode(), invoiceIds);

        if (CollectionUtils.isEmpty(expressesRelationDOList)) {
            return Collections.emptyList();
        }

        List<InvoiceExpressesMsgDto> invoiceExpressesMsgDtos = new LinkedList<>();

        for (InvoiceExpressesRelationDO invoiceExpressesRelationDO : expressesRelationDOList) {

            InvoiceExpressesMsgDto invoiceExpressesMsgDto = new InvoiceExpressesMsgDto();
            invoiceExpressesMsgDto.setExpressName(invoiceExpressesRelationDO.getExpressName());
            invoiceExpressesMsgDto.setExpressTrackingNo(invoiceExpressesRelationDO.getExpressTrackingNo());
            invoiceExpressesMsgDto.setInvoiceId(invoiceExpressesRelationDO.getInvoiceId());
            invoiceExpressesMsgDtos.add(invoiceExpressesMsgDto);
        }
        return invoiceExpressesMsgDtos;
    }

    @Override
    @Transactional()
    public int modifyInvoiceExpressesById(InvoiceExpressesRelationDO invoiceExpressesRelationDO) {
        if (Objects.isNull(invoiceExpressesRelationDO) || Objects.isNull(invoiceExpressesRelationDO.getId())) {
            return 0;
        }
        return invoiceExpressesRelationDAO.updateInvoiceExpressesRelationById(invoiceExpressesRelationDO);
    }

    @Override
    public List<InvoiceExpressesRelationBO> findAllByQuery(InvoiceExpressesRelationQuery query) {
        List<InvoiceExpressesRelationDO> doList = invoiceExpressesRelationDAO.findAllByQuery(query);
        List<InvoiceExpressesRelationBO> boList = InvoiceExpressesRelationConverter.convertDO2BOFromList(doList);
        return boList;
    }

    @Override
    public int countByQuery(InvoiceExpressesRelationQuery query) {
        int count = invoiceExpressesRelationDAO.countByQuery(query);
        return count;
    }

    @Override
    public String getSerialNo() {
        ServiceResult<String> result = new ServiceResult();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Date date = new Date();
            String formatDate = sdf.format(date);
            String key = "invoice_express_no" + formatDate;
            Long incr = getIncr(key, getCurrent2TodayEndMillisTime());
            if (incr == 0) {
                incr = getIncr(key, getCurrent2TodayEndMillisTime());//从0001开始
            }
            DecimalFormat df = new DecimalFormat("000000");//六位序列号
            String s = formatDate +"11"+ df.format(incr);
            return s;
        } catch (Exception e) {
            log.error("生成发票批次号异常", e);
            return null;
        }
    }
    //现在到今天结束的毫秒数
    private Long getCurrent2TodayEndMillisTime() {
        Calendar todayEnd = Calendar.getInstance();
        // Calendar.HOUR 12小时制
        // HOUR_OF_DAY 24小时制
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTimeInMillis() - System.currentTimeMillis();
    }
    private Long getIncr(String key, long liveTime) {
        RedisAtomicLong entityIdCounter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        Long increment = entityIdCounter.getAndIncrement();

        if ((null == increment || increment.longValue() == 0) && liveTime > 0) {//初始设置过期时间
            entityIdCounter.expire(liveTime, TimeUnit.MILLISECONDS);//单位毫秒
        }
        return increment;
    }


}
