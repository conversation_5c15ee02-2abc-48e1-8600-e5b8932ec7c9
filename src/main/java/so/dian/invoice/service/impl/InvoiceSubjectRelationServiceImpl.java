package so.dian.invoice.service.impl;

import static so.dian.invoice.constant.InvoiceSubjectRelationConstants.MERCHANT_INVOICING_SUBJECT_OF_THE_MERCHANT_ALREADY_EXISTS;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.client.LeoClient;
import so.dian.invoice.converter.SubjectRelationConverter;
import so.dian.invoice.enums.InvoiceSubjectRelationEnum.BizTypeEnum;
import so.dian.invoice.enums.InvoiceSubjectRelationStatusEnum;
import so.dian.invoice.enums.error.InvoiceSubjectRelationErrorCodeEnum;
import so.dian.invoice.manager.InvoiceSubjectRelationManager;
import so.dian.invoice.pojo.bo.InvoiceSubjectRelationBO;
import so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO;
import so.dian.invoice.pojo.param.AddInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvalidInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvoiceSubjectRelationPageParam;
import so.dian.invoice.pojo.query.InvoiceSubjectRelationQuery;
import so.dian.invoice.pojo.vo.InvoiceSubjectRelationPageVO;
import so.dian.invoice.util.PageData;
import so.dian.invoice.util.StringUtil;
import so.dian.invoice.volidator.InvoiceSubjectRelationValidator;

/**
 * InvoiceSubjectRelationServiceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class InvoiceSubjectRelationServiceImpl {

    @Resource
    private InvoiceSubjectRelationManager invoiceSubjectRelationManager;

    @Resource
    private LeoClient leoClient;

    public BizResult<PageData<InvoiceSubjectRelationPageVO>> listInvoiceSubjectRelation(
          InvoiceSubjectRelationPageParam param) {
        InvoiceSubjectRelationValidator.checkInvoiceSubjectRelationPageParam(param);

        InvoiceSubjectRelationQuery query = SubjectRelationConverter.buildParam2Query(param);
        Long totalCount = invoiceSubjectRelationManager.countInvoiceSubjectRelation(query);
        if (totalCount <= 0L) {
            return BizResult.create(
                  PageData.create(Lists.newArrayList(), totalCount, param.getPageNo(), param.getPageSize()));
        }

        List<InvoiceSubjectRelationBO> invoiceSubjectRelationBOList =
              invoiceSubjectRelationManager.selectInvoiceSubjectRelationList(query);
        if (CollectionUtils.isEmpty(invoiceSubjectRelationBOList)) {
            return BizResult.create(
                  PageData.create(Lists.newArrayList(), totalCount, param.getPageNo(), param.getPageSize()));
        }
        Map<Integer, AgentEmployeeDTO> employeeInfoMap =
              invoiceSubjectRelationManager.getAgentEmployeeDTO(invoiceSubjectRelationBOList);
        List<InvoiceSubjectRelationPageVO> voList =
              SubjectRelationConverter.convertBO2VO(invoiceSubjectRelationBOList, employeeInfoMap);

        return BizResult.create(PageData.create(voList, totalCount, param.getPageNo(), param.getPageSize()));
    }

    public BizResult<List<NameValueDTO>> listInvoiceSubjectRelationStatus() {
        List<NameValueDTO> invoiceSubjectRelationStatusEnumList
              = LocalEnumUtils.getEnumCodeAndDesc(InvoiceSubjectRelationStatusEnum.class);
        return BizResult.create(invoiceSubjectRelationStatusEnumList);
    }

    public BizResult addInvoiceSubjectRelation(AddInvoiceSubjectRelationParam param) {
        InvoiceSubjectRelationValidator.checkAddInvoiceSubjectRelationParam(param);

        //商户校验
        BizResult<String> result = leoClient.getMerchantNameByIdToInvoice(Long.valueOf(param.getMerchantId()));
        if (!result.isSuccess()) {
            throw BizException.create(InvoiceSubjectRelationErrorCodeEnum.MERCHANT_NOT_EXISTED);
        }
        if (StringUtils.isBlank(result.getData())) {
            throw BizException.create(InvoiceSubjectRelationErrorCodeEnum.MERCHANT_SUBJECT_NAME_NOT_NULL,
                  result.getMsg());
        }

        //通过商户ID + 商户名称 查找，因为商户名称可能会改
        List<InvoiceSubjectRelationBO> invoiceSubjectRelationBOList = invoiceSubjectRelationManager
              .listByMerchantIdAndRelationSubjectName(param.getMerchantId(), param.getSignSubjectName());

        //判断是否有已存在的开票主体
        if (CollectionUtils.isNotEmpty(invoiceSubjectRelationBOList)) {
            List<String> existSubjectName =
                  LocalListUtils.transferList(invoiceSubjectRelationBOList, InvoiceSubjectRelationBO::getSubjectName);
            List<String> repeatSubjectName = Lists.newArrayList();
            param.getSubjectNameList().forEach(subjectName -> {
                if (existSubjectName.contains(subjectName)) {
                    repeatSubjectName.add(subjectName);
                }
            });
            if (CollectionUtils.isNotEmpty(repeatSubjectName)) {
                throw BizException.create(InvoiceSubjectRelationErrorCodeEnum
                            .MERCHANT_INVOICING_SUBJECT_OF_THE_MERCHANT_ALREADY_EXISTS,
                      repeatSubjectName + ":" + MERCHANT_INVOICING_SUBJECT_OF_THE_MERCHANT_ALREADY_EXISTS);
            }
        }

        invoiceSubjectRelationManager.insert(param);
        return BizResult.create(true);
    }

    public BizResult invalidInvoiceSubjectRelation(InvalidInvoiceSubjectRelationParam param) {
        InvoiceSubjectRelationValidator.checkInvalidInvoiceSubjectRelationParam(param);

        InvoiceSubjectRelationBO invoiceSubjectRelationBO =
              invoiceSubjectRelationManager.getInvoiceSubjectRelationById(param.getId());
        if (Objects.isNull(invoiceSubjectRelationBO)) {
            throw BizException.create(InvoiceSubjectRelationErrorCodeEnum.INVOICE_SUBJECT_RECORD_DOSE_NOT_EXIST);
        }
        if (Objects.equals(invoiceSubjectRelationBO.getStatusEnum(), InvoiceSubjectRelationStatusEnum.INVALID)) {
            throw BizException.create(InvoiceSubjectRelationErrorCodeEnum.INVOICE_SUBJECT_RECORD_ALREADY_INVALID);
        }

        boolean updateResult = invoiceSubjectRelationManager.updateStatusById(param.getId(),
              param.getUserId(), InvoiceSubjectRelationStatusEnum.INVALID, invoiceSubjectRelationBO.getStatusEnum());
        if (!updateResult) {
            throw BizException.create(InvoiceSubjectRelationErrorCodeEnum.INSERT_MERCHANT_SUBJECT_FAILED);
        }

        return BizResult.create(true);
    }

    /**
     * 处理特殊字符
     */
    public BizResult<Integer> trimRelation(Long bizId, Integer bizType, Integer lastId) {
        if ((Objects.nonNull(bizId) && Objects.isNull(bizType))
              || (Objects.nonNull(bizType) && Objects.isNull(bizId))) {
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }
        int count = 0;
        if (Objects.nonNull(bizId) && Objects.nonNull(bizType)) {
            List<InvoiceSubjectRelationDO> list = invoiceSubjectRelationManager.findByBizIdAndBizType(bizId, bizType);
            if (CollectionUtils.isEmpty(list)) {
                return BizResult.error(InvoiceSubjectRelationErrorCodeEnum.INVOICE_SUBJECT_RECORD_DOSE_NOT_EXIST);
            }
            for (InvoiceSubjectRelationDO relationDO : list) {
                log.info("批量修正发票关联主体. relationDO:{}", relationDO);
                relationDO.setSubjectName(StringUtil.trimFull2Half(relationDO.getSubjectName()));
                relationDO.setRelationSubjectName(StringUtil.trimFull2Half(relationDO.getRelationSubjectName()));
                int i = invoiceSubjectRelationManager.trimRelation(relationDO);
                count = count + i;
            }
        } else {
            while (true) {
                List<InvoiceSubjectRelationDO> list = invoiceSubjectRelationManager.batchQuery(lastId.longValue());
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                for (InvoiceSubjectRelationDO relationDO : list) {
                    log.info("批量修正发票关联主体. relationDO:{}", relationDO);
                    lastId = relationDO.getId();
                    relationDO.setSubjectName(StringUtil.trimFull2Half(relationDO.getSubjectName()));
                    relationDO.setRelationSubjectName(StringUtil.trimFull2Half(relationDO.getRelationSubjectName()));
                    int i = invoiceSubjectRelationManager.trimRelation(relationDO);
                    count = count + i;
                }
                if (list.size() < 1000) {
                    break;
                }
            }
        }

        return BizResult.create(count);
    }

    public List<String> listSubjectNameByRelationSubjectName(String relationSubjectName, BizTypeEnum bizType) {
        List<InvoiceSubjectRelationBO> invoiceSubjectRelationBOList = invoiceSubjectRelationManager
                .listByRelationSubjectNameAndBizType(relationSubjectName, bizType);
        return LocalListUtils.transferList(invoiceSubjectRelationBOList, InvoiceSubjectRelationBO::getSubjectName);
    }
}
