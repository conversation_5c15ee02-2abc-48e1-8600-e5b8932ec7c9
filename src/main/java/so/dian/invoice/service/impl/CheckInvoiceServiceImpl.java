package so.dian.invoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.center.common.util.CollectionUtil;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.hr.api.entity.employee.CityDepartmentDTO;
import so.dian.invoice.constant.CheckInvoiceConstants;
import so.dian.invoice.converter.*;
import so.dian.invoice.dao.InvoiceExpressesRelationDAO;
import so.dian.invoice.enums.CheckInvoiceStatusEnum;
import so.dian.invoice.enums.ConclusionStatusEnum;
import so.dian.invoice.enums.InCheckPoolStatusEnum;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.manager.*;
import so.dian.invoice.pojo.bo.CheckInvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.dto.CheckInvoiceDTO;
import so.dian.invoice.pojo.entity.*;
import so.dian.invoice.pojo.param.*;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;
import so.dian.invoice.pojo.vo.*;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.InvoiceDeductionService;
import so.dian.invoice.util.PageData;
import so.dian.invoice.util.biz.AssertUtils;
import so.dian.invoice.volidator.CheckInvoiceValidator;
import static so.dian.invoice.constant.CheckInvoiceConstants.*;
import static so.dian.invoice.enums.error.CheckInvoiceErrorCodeEnum.*;
import static so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum.*;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:46 PM
 * @Description:
 */
@Slf4j
@Service
public class CheckInvoiceServiceImpl {

	@Resource
	private CheckInvoiceManager checkInvoiceManager;

	@Resource
	private InvoiceManager invoiceManager;

	@Resource
	private InvoiceExpressesRelationDAO invoiceExpressesRelationDAO;

	@Resource
	private AgentEmployeeService agentEmployeeService;

	@Resource
	private CheckInvoiceConclusionManager checkInvoiceConclusionManager;

	@Resource
	private InvoiceDeductionService invoiceDeductionService;

	@Resource
	private CheckInvoiceEmployeeManager checkInvoiceEmployeeManager;

	@Resource
	private InvoiceConfigManager invoiceConfigManager;

	@Resource
	private InvoiceOperateLogManager invoiceOperateLogManager;

	public BizResult<PageData<CheckInvoiceVO>> listInvoiceCheck(InvoiceCheckPageParam param) {
		CheckInvoiceValidator.checkInvoiceCheckPageParam(param);
		boolean hasAuthority = Objects.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), param.getRole());
		if (!hasAuthority) {
			return BizResult.create(PageData
					.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
		}
		if (Objects.nonNull(param.getCreator())) {
			AgentEmployeeDTO employeeDTO = agentEmployeeService.getByNickName(param.getCreator());
			if (Objects.isNull(employeeDTO)) {
				return BizResult.create(PageData
						.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
			}
			param.setCreatorId(employeeDTO.getId().longValue());
		}
		if (Objects.nonNull(param.getChecker())) {
			AgentEmployeeDTO employeeDTO = agentEmployeeService.getByNickName(param.getChecker());
			if (Objects.isNull(employeeDTO)) {
				return BizResult.create(PageData
						.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
			}
			param.setCheckerId(employeeDTO.getId().longValue());
		}
		BizResult<PageData<CheckInvoiceVO>> fillResult = fillCityCodes(param);
		if(!Objects.isNull(fillResult)){
			return fillResult;
		}
		PageInfo<CheckInvoiceDTO> invoiceDTOPageInfo = checkInvoiceManager.selectCheckInvoiceForPage(param);
		List<CheckInvoiceBO> checkInvoiceBOList  =Lists.newArrayList();
		if(!CollectionUtil.isEmpty(invoiceDTOPageInfo.getList())){
			checkInvoiceBOList = CheckInvoiceConverter.convertDTO2BO(invoiceDTOPageInfo.getList());
		}
		if (CollectionUtils.isEmpty(checkInvoiceBOList)) {
			return BizResult.create(PageData
					.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
		}
		/**
		 * 查询质检结论
		 */
		List<CheckInvoiceConclusionDO> conclusionDOList = checkInvoiceConclusionManager.listConclusion();
		List<CheckInvoiceVO> checkInvoiceVOList = getCheckInvoiceVOs(checkInvoiceBOList,conclusionDOList);
		//质检结论对应发票统计
		List<CheckInvoiceBO> invoiceBOS = checkInvoiceManager.listCheckInvoice(param);

		List<CheckInvoiceCountVO> countVOList
				= CheckInvoiceConverter.convertCountVO(invoiceBOS, conclusionDOList);
		HashMap<String, Object> extraMap = Maps.newHashMap();
		extraMap.put("checkInvoiceCount", countVOList);
		return BizResult.create(PageData
				.create(checkInvoiceVOList, invoiceDTOPageInfo.getTotal(), param.getPageNo(), param.getPageSize(), extraMap));
	}

	private List<CheckInvoiceVO> getCheckInvoiceVOs(List<CheckInvoiceBO> checkInvoiceBOList,
			List<CheckInvoiceConclusionDO> conclusionDOList){
		/**
		 * 查询发票台账
		 */
		List<Long> invoiceIdList = checkInvoiceBOList.stream().map(CheckInvoiceBO::getInvoiceId).collect(Collectors.toList());
		List<Integer> cityCodes = checkInvoiceBOList.stream().map(CheckInvoiceBO::getCityCode).collect(Collectors.toList());
		List<InvoiceBO> invoiceBOList = invoiceManager.listByIds(invoiceIdList);
		/**
		 * 查询已寄出发票的物流信息，这里录入质检池的发票都是已寄出状态，都会关联物流单号
		 */
		List<InvoiceExpressesRelationDO> relationDOList =
				invoiceExpressesRelationDAO.findInvoiceExpressesRelations(
						InvoiceIdentifyRecordEnum.DeletedEnum.UN_DELETED.getCode(), invoiceIdList);
		/**
		 * 查询员工信息
		 */
		Set<Integer> employeeIdSet = getEmployeeIdSet(checkInvoiceBOList);
		Map<Integer, String> agentEmployeeNickMap = agentEmployeeService.getAgentEmployeeNickMap(employeeIdSet);

		/**
		 * 查询所有负责大区的财务经理的花名
		 */
		List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOS = checkInvoiceEmployeeManager.selectAll();
		Set<Integer> userIds = checkInvoiceEmployeeDOS.stream().map(CheckInvoiceEmployeeDO -> CheckInvoiceEmployeeDO.getUserId().intValue()).collect(Collectors.toSet());
		Map<Integer, String> financialManagerNickMap = agentEmployeeService.getAgentEmployeeNickMap(userIds);
		//得到大区和花名对应map
		Map<String, String> regionNickMap = Maps.newHashMap();
		checkInvoiceEmployeeDOS.forEach(obj ->{
				regionNickMap.put(obj.getRegion(),financialManagerNickMap.get(obj.getUserId().intValue()));
		});

		/**
		 * 查询关联业务单号
		 */
		List<InvoiceDeductionDO> deductionDOList = getInvoiceByBusinessRelation(checkInvoiceBOList);
		/**
		 * 查询大区信息
		 */
		Map<Integer, CityDepartmentDTO> regionCityNameMap = agentEmployeeService.listRegionNameByCityCodes(cityCodes);

		return CheckInvoiceConverter.convertVO(checkInvoiceBOList, invoiceBOList, relationDOList, agentEmployeeNickMap,
				conclusionDOList, deductionDOList, regionCityNameMap, regionNickMap);
	}


	/**
	 * 填充该请求所有符合的城市行政
	 */
	public BizResult<PageData<CheckInvoiceVO>> fillCityCodes(InvoiceCheckPageParam param){
		/**
		 * config CHECK_INVOICE_AFFAIRS_ADMIN配置的用户能查看所有大区
		 * 其他的财务经理只能看到自己负责的大区
		 */
		List<Long> affairsAdmins = invoiceConfigManager.getUserIdsByKey(CHECK_INVOICE_AFFAIRS_ADMIN);
		List<String> regions = Lists.newArrayList();
		List<Integer> cityCodeList = Lists.newArrayList();
		Boolean isAffairsAdmin = false;
		if(!CollectionUtil.isEmpty(affairsAdmins) && affairsAdmins.contains(param.getEmployeeId())){
			isAffairsAdmin = true;
		}
		if (!isAffairsAdmin) {
			List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList
					= checkInvoiceEmployeeManager.getByUserId(param.getEmployeeId());
			if (CollectionUtils.isEmpty(checkInvoiceEmployeeDOList)) {
				return BizResult.create(PageData
						.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
			}
			regions = checkInvoiceEmployeeDOList.stream().map(CheckInvoiceEmployeeDO::getRegion).collect(Collectors.toList());
			List<Integer> cityCodes = listCityCode(regions);
			if (CollectionUtils.isEmpty(cityCodes)) {
				return BizResult.create(PageData
						.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
			}
			cityCodeList.addAll(cityCodes);
		}
		if (StringUtils.isNotBlank(param.getRegion())) {
			if(!isAffairsAdmin){
				CheckInvoiceEmployeeDO invoiceEmployeeDO
						= checkInvoiceEmployeeManager.getByUserIdAndRegion(param.getEmployeeId(),param.getRegion());
				if (Objects.isNull(invoiceEmployeeDO)) {
					return BizResult.create(PageData
							.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
				}
			}
			regions = Lists.newArrayList(param.getRegion());
			List<Integer> cityCodes = listCityCode(regions);
			if (CollectionUtils.isEmpty(cityCodes)) {
				return BizResult.create(PageData
						.create(Lists.newArrayList(), 0L, param.getPageNo(), param.getPageSize()));
			}
			cityCodeList.clear();
			cityCodeList.addAll(cityCodes);
		}

		param.setCityCodes(cityCodeList);
		return null;
	}

	private List<Integer> listCityCode(List<String> regions) {
		List<Integer> cityCodeList = agentEmployeeService.listCityCodeByRegionName(regions);
		if (CollectionUtils.isEmpty(cityCodeList)) {
			return Lists.newArrayList();
		}
		return cityCodeList;
	}

	public BizResult<List<String>> getRegions(Long userId){
		List<String> list = new LinkedList<>();
		List<Long> affairsAdmins = invoiceConfigManager.getUserIdsByKey(CHECK_INVOICE_AFFAIRS_ADMIN);
		/**
		 * 有的财务经理可以查询所有的大区列表(config表中配置key:check_invoice_affairs_admin)
		 * 否则只能看自己负责的大区列表
		 */
		if (!CollectionUtil.isEmpty(affairsAdmins) && affairsAdmins.contains(userId)) {
			list = agentEmployeeService.selectRegionList();
		}else{
			List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList = checkInvoiceEmployeeManager.getByUserId(userId);
			if (CollectionUtils.isNotEmpty(checkInvoiceEmployeeDOList)) {
				List<String> finalList = new LinkedList<>();
				checkInvoiceEmployeeDOList.forEach(obj -> finalList.add(obj.getRegion()));
				list = finalList;
			}

		}
		return BizResult.create(list);
	}

	private List<InvoiceDeductionDO> getInvoiceByBusinessRelation(List<CheckInvoiceBO> checkInvoiceBOList) {
		List<InvoiceDeductionDO> deductionDOList = Lists.newArrayList();
		checkInvoiceBOList.forEach(checkInvoiceBO -> {
			List<InvoiceDeductionDO> invoiceDeductionDOList =
					invoiceDeductionService.getInvoiceByBusinessRelation(checkInvoiceBO.getInvoiceNo(),
							checkInvoiceBO.getInvoiceCode());
			deductionDOList.addAll(invoiceDeductionDOList);
		});
		return deductionDOList;
	}

	private Set<Integer> getEmployeeIdSet(List<CheckInvoiceBO> checkInvoiceBOList) {
		Set<Integer> allEmployeeIdSet = Sets.newHashSet();
		Set<Long> operatorIdSet
				= checkInvoiceBOList.stream().map(CheckInvoiceBO::getOperator).collect(Collectors.toSet());
		Set<Long> checkerSet = checkInvoiceBOList.stream().map(CheckInvoiceBO::getChecker).collect(Collectors.toSet());
		operatorIdSet.forEach(operatorId->{
			allEmployeeIdSet.add(operatorId.intValue());
		});
		if (CollectionUtils.isNotEmpty(checkerSet)) {
			checkerSet.forEach(checker->{
				allEmployeeIdSet.add(checker.intValue());
			});
		}
		return allEmployeeIdSet;
	}

	@Transactional(rollbackFor = Exception.class)
	public BizResult<Boolean> checkInvoice(CheckInvoiceParam param) {
		CheckInvoiceValidator.checkCheckInvoiceParam(param);
		boolean hasAuthority = Objects.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), param.getRole());
		AssertUtils.notTrueWithBizExp(hasAuthority, USER_NO_PERMISSION_OPERATE);
		//质检表更新
		CheckInvoiceDO checkInvoiceDO = checkInvoiceManager.getById(param.getId());
		AssertUtils.notEmptyWithBizExp(checkInvoiceDO, INVOICE_CHECK_INFO_NOT_EXIST);
		//判断是否已经质检
		boolean hasCheck = Objects.equals(checkInvoiceDO.getStatus(), CheckInvoiceStatusEnum.NOT_CHECK.getCode());
		AssertUtils.notTrueWithBizExp(hasCheck, INVOICE_IS_ALREADY_CHECK);
		CheckInvoiceDO updateCheckInvoiceDO = CheckInvoiceConverter.buildUpdateParam2DO(param, checkInvoiceDO.getStatus());
		Boolean result = checkInvoiceManager.update(updateCheckInvoiceDO);
		AssertUtils.notTrueWithBizExp(result, CHECK_INVOICE_UPDATE_FAILED);
		//质检结论表更新
		CheckInvoiceConclusionDO conclusionDO = checkInvoiceConclusionManager.getById(param.getCheckResult());
		AssertUtils.notEmptyWithBizExp(conclusionDO, CONCLUSION_IS_NOT_EXIST);
		//落日志
		String nickName = agentEmployeeService.getEmployeeNickById(param.getEmployeeId().intValue());
		invoiceOperateLogManager.insert(InvoiceOperateLogConverter
				.checkInvoice(param, nickName, checkInvoiceDO.getInvoiceId()));
		boolean isUsed = Objects.equals(conclusionDO.getIsUsed(), ConclusionStatusEnum.ALREADY_USED.getCode());
		if (isUsed) {
			return BizResult.create(true);
		}
		Boolean updateResult = checkInvoiceConclusionManager.updateIsUsedById(param.getCheckResult(),
				ConclusionStatusEnum.ALREADY_USED.getCode(), System.currentTimeMillis(), conclusionDO.getIsUsed());
		AssertUtils.notTrueWithBizExp(updateResult, CONCLUSION_UPDATE_FAILED);
		return BizResult.create(true);
	}

	public BizResult<InvoiceCheckDetailsVO> invoiceCheckDetails(InvoiceCheckDetailsParam param) {
		CheckInvoiceValidator.checkInvoiceCheckDetailsParam(param);
		boolean hasAuthority = Objects.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), param.getRole());
		if (!hasAuthority) {
			return null;
		}
		//查询发票质检信息
		CheckInvoiceDO checkInvoiceDO = checkInvoiceManager.getById(param.getId());
		AssertUtils.notEmptyWithBizExp(checkInvoiceDO, INVOICE_CHECK_INFO_NOT_EXIST);
		//查询质检结论
		CheckInvoiceConclusionDO conclusionDO = checkInvoiceConclusionManager.getById(checkInvoiceDO.getConclusionId());
		AssertUtils.notEmptyWithBizExp(conclusionDO, CONCLUSION_IS_NOT_EXIST);
		//查询员工信息
		AgentEmployeeDTO employeeDTO = agentEmployeeService.getEmployeeById(checkInvoiceDO.getChecker().intValue());
		AssertUtils.notEmptyWithBizExp(employeeDTO, USER_NOT_EXIST);

		InvoiceCheckDetailsVO invoiceCheckDetailsVO =
				CheckInvoiceConverter.buildDetailsVO(checkInvoiceDO, conclusionDO.getCode(), employeeDTO.getNickName());
		return BizResult.create(invoiceCheckDetailsVO);
	}

	@Transactional(rollbackFor = Exception.class)
	public BizResult<Boolean> invoiceCheckRateConfig(InvoiceCheckRateParam param) {
		CheckInvoiceValidator.checkInvoiceCheckRateParam(param);
		checkUserRole(param.getEmployeeId());
		InvoiceConfigDO invoiceConfigDO = invoiceConfigManager.getByKey(CheckInvoiceConstants.CHECK_INVOICE_CHECK_RATE);
		if (Objects.isNull(invoiceConfigDO)) {
			InvoiceConfigDO buildDO = InvoiceConfigConverter.buildDO(CheckInvoiceConstants.CHECK_INVOICE_CHECK_RATE,
					param.getCheckRate().toString());
			invoiceConfigManager.insert(buildDO);
		} else {
			Boolean updateResult = invoiceConfigManager.update(InvoiceConfigDO.of(invoiceConfigDO, param.getCheckRate().toString()));
			AssertUtils.notTrueWithBizExp(updateResult, CHECK_RATE_UPDATE_FAILED);
		}
		//落日志
		String nickName = agentEmployeeService.getEmployeeNickById(param.getEmployeeId().intValue());
		invoiceOperateLogManager.insert(InvoiceOperateLogConverter
				.invoiceCheckRateConfigLog(param, nickName, invoiceConfigDO.getValue()));
		return BizResult.create(true);
	}

	public BizResult<Integer> getCheckRate(BaseEmpRoleReq req){
		boolean hasAuthority = Objects.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), req.getRole());
		if (!hasAuthority) {
			log.warn("用户无权限,role:{}",req.getRole());
			return BizResult.create(null);
		}
		InvoiceConfigDO invoiceConfigDO = invoiceConfigManager.getByKey(CheckInvoiceConstants.CHECK_INVOICE_CHECK_RATE);
		if (Objects.isNull(invoiceConfigDO)) {
			return BizResult.create(null);
		}
		return BizResult.create(Integer.valueOf(invoiceConfigDO.getValue()));
	}

	@Transactional(rollbackFor = Exception.class)
	public BizResult<Boolean> invoiceCheckerConfig(InvoiceCheckerParam param) {
		CheckInvoiceValidator.checkInvoiceCheckerParam(param);
		checkUserRole(param.getEmployeeId());
		//员工校验
		AgentEmployeeDTO agentEmployeeDTO = agentEmployeeService.getEmployeeById(param.getChecker().intValue());
		AssertUtils.notEmptyWithBizExp(agentEmployeeDTO, USER_NOT_EXIST);
		//员工权限校验
		List<Integer> userRoles = agentEmployeeDTO.getUserRoles();
		boolean hasRole = userRoles.contains(UserRoleEnum.AFFAIRS_MANAGER.getSessionValue());
		AssertUtils.notTrueWithBizExp(hasRole, USER_IS_NOT_AFFAIRS_MANAGER_CANNOT_ADD);
		//通过员工ID查询配置信息如果有配置则不能新增
		List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList
				= checkInvoiceEmployeeManager.getByUserId(param.getChecker());
		AssertUtils.collectionIsNotEmptyWithBizExp(checkInvoiceEmployeeDOList, USER_ALREADY_CONFIG);
		//一个大区只能配置一个人
		regionOnlyCheck(param.getRegionList());
		//新增员工大区配置
		Boolean result =
				checkInvoiceEmployeeManager.batchInsert(CheckInvoiceEmployeeConverter.convertParam2DO(param));
		AssertUtils.notTrueWithBizExp(result, ADD_USER_REGION_RELATION_CONFIG_FAILED);
		//落日志
		String nickName = agentEmployeeService.getEmployeeNickById(param.getEmployeeId().intValue());
		invoiceOperateLogManager.insert(InvoiceOperateLogConverter.invoiceCheckerConfigLog(param, nickName));
		return BizResult.create(true);
	}

	private void regionOnlyCheck(List<String> regions) {
		List<CheckInvoiceEmployeeDO> all = checkInvoiceEmployeeManager.selectAll();
		List<String> regionList = all.stream().map(CheckInvoiceEmployeeDO::getRegion).collect(Collectors.toList());
		List<String> oldRegionList = regionList.stream().filter(regions::contains).collect(Collectors.toList());
		AssertUtils.collectionIsNotEmptyWithBizExp(oldRegionList, REGION_ALREADY_CONFIG,JSON.toJSONString(oldRegionList));
	}

	@Transactional(rollbackFor = Exception.class)
	public BizResult<Boolean> modifyInvoiceCheckerRegionConfig(InvoiceCheckerParam param) {
		CheckInvoiceValidator.checkInvoiceCheckerParam(param);
		checkUserRole(param.getEmployeeId());
		//只能修改大区，人员有变动直接删除
		List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList
				= checkInvoiceEmployeeManager.getByUserId(param.getChecker());
		AssertUtils.collectionIsEmptyWithBizExp(checkInvoiceEmployeeDOList, USER_REGION_CONFIG_NOT_EXIST);

		List<String> newRegionList = param.getRegionList();
		List<String> oldRegionList =
				checkInvoiceEmployeeDOList.stream().map(CheckInvoiceEmployeeDO::getRegion).collect(Collectors.toList());
		List<String> newInsertRegionList = newRegionList.stream()
				.filter(newRegion -> !oldRegionList.contains(newRegion)).collect(Collectors.toList());
		//校验大区唯一
		regionOnlyCheck(newInsertRegionList);
		List<String> needDeletedRegionList = oldRegionList.stream()
				.filter(oldRegion -> !newRegionList.contains(oldRegion)).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(newInsertRegionList) && CollectionUtils.isEmpty(needDeletedRegionList)) {
			return BizResult.create(true);
		}
		if (CollectionUtils.isNotEmpty(newInsertRegionList)) {
			checkInvoiceEmployeeManager.batchInsert(CheckInvoiceEmployeeConverter.convert2DO(newInsertRegionList,
					param.getChecker()));
		}
		if (CollectionUtils.isNotEmpty(needDeletedRegionList)) {
			List<Long> idList = checkInvoiceEmployeeDOList.stream()
					.filter(item -> needDeletedRegionList.contains(item.getRegion())).map(CheckInvoiceEmployeeDO::getId)
					.collect(Collectors.toList());
			Boolean result = checkInvoiceEmployeeManager.batchLogicDeleted(idList);
			AssertUtils.notTrueWithBizExp(result, BATCH_DELETED_USER_REGION_CONFIG_FAILED);
		}
		//落日志
		String nickName = agentEmployeeService.getEmployeeNickById(param.getEmployeeId().intValue());
		InvoiceOperateLogBO invoiceOperateLogBO =
				InvoiceOperateLogConverter.modifyInvoiceCheckerRegionConfigLog(param, nickName, oldRegionList);
		invoiceOperateLogManager.insert(invoiceOperateLogBO);
		return BizResult.create(true);
	}

	@Transactional(rollbackFor = Exception.class)
	public BizResult<Boolean> deletedInvoiceCheckerRegionConfig(DeletedInvoiceCheckerParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
		checkUserRole(param.getEmployeeId());
		List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList
				= checkInvoiceEmployeeManager.getByUserId(param.getChecker());
		AssertUtils.collectionIsEmptyWithBizExp(checkInvoiceEmployeeDOList, USER_REGION_CONFIG_NOT_EXIST);
		List<Long> idList =
				checkInvoiceEmployeeDOList.stream().map(CheckInvoiceEmployeeDO::getId).collect(Collectors.toList());
		Boolean result = checkInvoiceEmployeeManager.batchLogicDeleted(idList);
		AssertUtils.notTrueWithBizExp(result, BATCH_DELETED_USER_REGION_CONFIG_FAILED);
		//落日志
		String nickName = agentEmployeeService.getEmployeeNickById(param.getEmployeeId().intValue());
		InvoiceOperateLogBO invoiceOperateLogBO =
				InvoiceOperateLogConverter.deletedInvoiceCheckerRegionConfigLog(param, nickName);
		invoiceOperateLogManager.insert(invoiceOperateLogBO);
		return BizResult.create(true);
	}

	@Transactional(rollbackFor = Exception.class)
	public BizResult<Boolean> invoiceCheckConclusionConfig(InvoiceCheckConclusionParam param) {
		CheckInvoiceValidator.checkInvoiceCheckConclusionParam(param);
		checkUserRole(param.getEmployeeId());
		log.info("结论长度不能大于10,code:{}", param.getCode().length());
		if (param.getCode().length() > CONCLUSION_LENGTH) {
			throw BizException.create(CONCLUSION_MUST_NOT_EXCEED_10);
		}
		CheckInvoiceConclusionDO invoiceConclusionDO = checkInvoiceConclusionManager.getByCode(param.getCode());
		AssertUtils.isNotNullWithBizExp(invoiceConclusionDO, CHECK_CONCLUSION_IS_ALREADY_EXIST);
		//如果ID为空，则为新增
		if (Objects.isNull(param.getId())) {
			checkInvoiceConclusionManager.insert(CheckInvoiceConclusionConverter.buildParam2DO(param));
		} else {
			CheckInvoiceConclusionDO conclusionDO = checkInvoiceConclusionManager.getById(param.getId());
			AssertUtils.notEmptyWithBizExp(conclusionDO, CONCLUSION_IS_NOT_EXIST);
			boolean isNoUsed = Objects.equals(conclusionDO.getIsUsed(), ConclusionStatusEnum.NOT_USED.getCode());
			AssertUtils.notTrueWithBizExp(isNoUsed, CONCLUSION_IS_USED_CAN_NOT_MODIFY);
			Boolean updateResult =
					checkInvoiceConclusionManager.update(param.getId(), param.getCode(), System.currentTimeMillis(),
							conclusionDO.getGmtUpdate());
			AssertUtils.notTrueWithBizExp(updateResult, CONCLUSION_UPDATE_FAILED);
		}
		//落日志
		String nickName = agentEmployeeService.getEmployeeNickById(param.getEmployeeId().intValue());
		InvoiceOperateLogBO invoiceOperateLogBO =
				InvoiceOperateLogConverter.invoiceCheckConclusionConfigLog(param, nickName);
		invoiceOperateLogManager.insert(invoiceOperateLogBO);
		return BizResult.create(true);
	}

	@Transactional(rollbackFor = Exception.class)
	public BizResult<Boolean> deletedInvoiceCheckConclusion(InvoiceCheckConclusionParam param) {
		if (Objects.isNull(param) || Objects.isNull(param.getId())) {
			throw BizException.create(PARAM_RESOLVER_FAIL);
		}
		checkUserRole(param.getEmployeeId());
		CheckInvoiceConclusionDO conclusionDO = checkInvoiceConclusionManager.getById(param.getId());
		AssertUtils.notEmptyWithBizExp(conclusionDO, CONCLUSION_IS_NOT_EXIST);
		boolean isNoUsed = Objects.equals(conclusionDO.getIsUsed(), ConclusionStatusEnum.NOT_USED.getCode());
		AssertUtils.notTrueWithBizExp(isNoUsed, CONCLUSION_IS_USED_CAN_NOT_MODIFY);
		Boolean deleted = checkInvoiceConclusionManager.logicDeleted(conclusionDO.getId());
		AssertUtils.notTrueWithBizExp(deleted, DELETED_CHECK_CONCLUSION_FAILED);
		//落日志
		String nickName = agentEmployeeService.getEmployeeNickById(param.getEmployeeId().intValue());
		InvoiceOperateLogBO invoiceOperateLogBO =
				InvoiceOperateLogConverter.deletedInvoiceCheckConclusionLog(conclusionDO, nickName,param.getEmployeeId());
		invoiceOperateLogManager.insert(invoiceOperateLogBO);
		return BizResult.create(true);
	}

	public BizResult<List<CheckInvoiceConclusionVO>> listCheckInvoiceConclusion(BaseEmpRoleReq req) {
		boolean hasAuthority = Objects.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), req.getRole());
		if (!hasAuthority) {
			log.warn("用户无权限,role:{}",req.getRole());
			return BizResult.create(Lists.newArrayList());
		}
		List<CheckInvoiceConclusionDO> conclusionDOList = checkInvoiceConclusionManager.listConclusion();
		if (CollectionUtils.isEmpty(conclusionDOList)) {
			return BizResult.create(Lists.newArrayList());
		}
		return BizResult.create(CheckInvoiceConclusionConverter.convertDO2VO(conclusionDOList));
	}

	public BizResult<List<CheckerDetailsVO>> getInvoiceCheckConfigDetails(BaseEmpRoleReq req) {
		boolean hasAuthority = Objects.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), req.getRole());
		if (!hasAuthority) {
			return null;
		}
		//质检人员
		List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList = checkInvoiceEmployeeManager.selectAll();
		//获取质检员工名称
		Set<Integer> userIdSet =
				checkInvoiceEmployeeDOList.stream().map(item->item.getUserId().intValue()).collect(Collectors.toSet());
		Map<Integer, String> employeeNickMap = agentEmployeeService.getAgentEmployeeNickMap(userIdSet);

		List<CheckerDetailsVO> voList =
				CheckInvoiceConverter.convertCheckerDO2VO(checkInvoiceEmployeeDOList, employeeNickMap);
		return BizResult.create(voList);
	}

	public BizResult<Boolean> initConfig(ConfigParam param) {
		Boolean insertResult = invoiceConfigManager.insert(InvoiceConfigConverter.buildParam2DO(param));
		AssertUtils.notTrueWithBizExp(insertResult, ADD_CHECK_CONFIG_FAILED);
		return BizResult.create(true);
	}

	public BizResult<Boolean> exportCheckInvoiceList(InvoiceCheckPageParam param) {
		boolean hasAuthority = Objects.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), param.getRole());
		if (!hasAuthority) {
			return BizResult.create(true);
		}
		if (Objects.nonNull(param.getCreator())) {
			AgentEmployeeDTO employeeDTO = agentEmployeeService.getByNickName(param.getCreator());
			if (Objects.isNull(employeeDTO)) {
				return BizResult.create(true);
			}
			param.setCreatorId(employeeDTO.getId().longValue());
		}
		if (Objects.nonNull(param.getChecker())) {
			AgentEmployeeDTO employeeDTO = agentEmployeeService.getByNickName(param.getChecker());
			if (Objects.isNull(employeeDTO)) {
				return BizResult.create(true);
			}
			param.setCheckerId(employeeDTO.getId().longValue());
		}
		BizResult<PageData<CheckInvoiceVO>> fillResult = fillCityCodes(param);
		if(!Objects.isNull(fillResult)){
			return BizResult.create(true);
		}
		Long totalCount = checkInvoiceManager.count(param);
		if (totalCount <= 0) {
			return BizResult.create(true);
		}
		if (totalCount > CHECK_INVOICE_EXPORT_LENGTH_LIMIT) {
			throw BizException.create(EXPORT_DATA_MUST_NOT_MORE_THAN_10000);
		}
		List<CheckInvoiceBO> checkInvoiceBOList = checkInvoiceManager.listCheckInvoiceDto(param);
		if (CollectionUtils.isEmpty(checkInvoiceBOList)) {
			return BizResult.create(true);
		}

		//查询质检结论
		List<CheckInvoiceConclusionDO> conclusionDOList = checkInvoiceConclusionManager.listConclusion();

		List<CheckInvoiceVO> checkInvoiceVOList = getCheckInvoiceVOs(checkInvoiceBOList,conclusionDOList);
		List<CheckInvoiceExportVO> invoiceExportVOList = CheckInvoiceConverter.convert(checkInvoiceVOList);
		AgentEmployeeDTO employeeDTO = agentEmployeeService.getEmployeeById(param.getEmployeeId().intValue());
		AssertUtils.notEmptyWithBizExp(employeeDTO, USER_NOT_EXIST);
		if (StringUtils.isBlank(employeeDTO.getEmail())) {
			throw BizException.create(USER_EMAIL_NOT_EXIST);
		}
		checkInvoiceManager.exportCheckInvoiceList(invoiceExportVOList, employeeDTO.getEmail(),param.getStatus());
		return BizResult.create(true);
	}



	/**
	 * 用户操作权限校验
	 * @param userId 当前登陆人ID
	 */
	private void checkUserRole(Long userId) {
		List<Long> userIdList = invoiceConfigManager.getUserIdsByKey(CheckInvoiceConstants.CHECK_INVOICE_ADMIN);
		AssertUtils.collectionIsEmptyWithBizExp(userIdList, USER_NO_PERMISSION_OPERATE);
		boolean hasAuthority = userIdList.contains(userId);
		AssertUtils.notTrueWithBizExp(hasAuthority,USER_NO_PERMISSION_OPERATE);
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean backTrackCheckInvoice(Date endTime) {
		if (Objects.isNull(endTime)) {
			throw BizException.create(CHECK_INVOICE_TIME_MUST_NUT_BE_NULL);
		}
		List<InvoiceBO> invoiceBOList = invoiceManager.listInvoiceByEndTime(endTime);
		if (CollectionUtils.isEmpty(invoiceBOList)) {
			log.warn("没有需要质检的发票", JSONObject.toJSONString(invoiceBOList));
			return true;
		}
		List<Integer> invoiceIdList = invoiceBOList.stream().map(InvoiceBO::getId).collect(Collectors.toList());
		List<Long> invoiceId2LongList = invoiceIdList.stream().map(Long::valueOf).collect(Collectors.toList());
		List<InvoiceExpressesRelationDO> relationDOList =
				invoiceExpressesRelationDAO.findInvoiceExpressesRelations(
						InvoiceIdentifyRecordEnum.DeletedEnum.UN_DELETED.getCode(), invoiceId2LongList);
		//获取抽取比例
		InvoiceConfigDO invoiceConfigDO = invoiceConfigManager.getByKey(CheckInvoiceConstants.CHECK_INVOICE_CHECK_RATE);
		//获取发票关联物流单号
		List<CheckInvoiceDO> checkInvoiceDOList =
				CheckInvoiceConverter.convertCheckInvoiceDO(invoiceBOList, invoiceConfigDO, relationDOList);
		log.info("录入质检池的发票,checkInvoiceDOList:{}", JSON.toJSON(checkInvoiceDOList));
		Boolean result = checkInvoiceManager.batchInsert(checkInvoiceDOList);
		AssertUtils.notTrueWithBizExp(result, BATCH_ADD_CHECK_INVOICE_FAILED);
		//发票台账更新为已录入质检池
		List<Long> checkInvoiceList
				= checkInvoiceDOList.stream().map(CheckInvoiceDO::getInvoiceId).collect(Collectors.toList());
		Boolean updateResult
				= invoiceManager.batchUpdateInCheckPool(checkInvoiceList, InCheckPoolStatusEnum.ENTERED.getCode(),
				InCheckPoolStatusEnum.NOT_ENTERED.getCode());
		AssertUtils.notTrueWithBizExp(updateResult, BATCH_UPDATE_INVOICE_IN_CHECK_POOL_FAILED);
		return true;
	}

}

