package so.dian.invoice.service.impl;

import com.meidalife.common.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import so.dian.invoice.enums.LeoExcs;
import so.dian.invoice.service.ImageService;
import so.dian.invoice.util.ImageUtils;
import so.dian.invoice.util.QCloudHelper;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;

@Service
public class ImageServiceImpl implements ImageService {
    private static Logger logger = LoggerFactory.getLogger(ImageServiceImpl.class);
    @Override
    public String uploadImageQCloud(String rootName, String originalFilename, byte[] imageBytes, String contentType, long size)
            throws IOException {
        ByteArrayInputStream inputStream  = new ByteArrayInputStream(imageBytes);
        BufferedImage image = ImageIO.read(inputStream);
        Integer width = image.getWidth();
        Integer height = image.getHeight();
        String saveFileName =
                ImageUtils.getSaveFileName(rootName, originalFilename, contentType, Long.toHexString(size), width, height);
        String cosPath = "/"+saveFileName;
        byte[] markBytes = ImageUtils.markFontXDKJ(imageBytes);
        try {
            QCloudHelper.uploadFile(cosPath,imageBytes);
            QCloudHelper.uploadFile(cosPath+"_mark.jpg",markBytes);
        } catch (Exception e) {
            logger.error("[uploadImageQCloud Failed]", e);
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }finally {
            imageBytes = null;
            markBytes = null;
            inputStream = null;
        }

        return saveFileName;
    }

    @Override
    public String uploadFileQCloud(String rootName, String originalFilename, byte[] fileBytes, String contentType, long size) throws IOException {
        String saveFileName =
                ImageUtils.getSaveFileName(rootName, originalFilename, contentType, Long.toHexString(size), 0, 0);
        String cosPath = "/" + saveFileName;
        try {
            QCloudHelper.uploadFile(cosPath, fileBytes);
        } catch (Exception e) {
            logger.error("[uploadImageQCloud Failed]", e);
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }

        return saveFileName;
    }
}
