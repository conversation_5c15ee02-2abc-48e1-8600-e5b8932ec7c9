package so.dian.invoice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.manager.InvoiceBizRelationManager;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.service.InvoiceBizRelationService;

@Slf4j(topic = "biz")
@Service
public class InvoiceBizRelationServiceImpl implements InvoiceBizRelationService {
    @Resource
    private InvoiceDAO invoiceDAO;
    @Resource
    private InvoiceDeductionDAO invoiceDeductionDAO;
    @Resource
    private InvoiceBizRelationManager invoiceBizRelationManager;
    @Resource
    private Executor invoiceBizRelationExecutor;

    @Override
    public void disposalByAllInvoice(Integer minId) {
        if (minId == null) {
            minId = 0;
        }
        while (true) {
            List<InvoiceDO> list = invoiceDAO.findByIdGreaterThan(minId);
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            minId = CollectionUtil.getLast(list).getId();
            invoiceBizRelationExecutor.execute(() -> {
                for (InvoiceDO invoiceDO : list) {
                    int count = invoiceBizRelationManager.disposal(invoiceDO.getInvoiceCode(), invoiceDO.getInvoiceNo());
                    log.info("整理发票核销记录,invoice:{},{},共{}条", invoiceDO.getInvoiceCode(), invoiceDO.getInvoiceNo(), count);
                }
            });
            if (list.size() < 100) {
                break;
            }
        }
    }

    @Override
    public int disposalByDeduction() {
        Date yesterday = DateUtil.yesterday();
        List<InvoiceDeductionDO> list = invoiceDeductionDAO.findByCreateTimeAfter(DateUtil.beginOfDay(yesterday));
        if (CollectionUtil.isEmpty(list)) {
            return 0;
        }
        for (InvoiceDeductionDO invoiceDeductionDO : list) {
            invoiceBizRelationExecutor.execute(() -> {
                int count = invoiceBizRelationManager.disposal(invoiceDeductionDO.getInvoiceCode(),
                      invoiceDeductionDO.getInvoiceNo());
                log.info("整理发票核销记录,invoice:{},{},共{}条", invoiceDeductionDO.getInvoiceCode(),
                      invoiceDeductionDO.getInvoiceNo(), count);
            });
        }
        return list.size();
    }
}