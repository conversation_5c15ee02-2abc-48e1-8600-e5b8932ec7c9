package so.dian.invoice.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import com.meidalife.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;
import so.dian.center.common.util.CollectionUtil;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalObjectUtils;
import so.dian.customer.dto.CustomerAccountDTO;
import so.dian.customer.dto.request.AuthenticationSubjectDTO;
import so.dian.customer.enums.AccountReferTypeEnum;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.client.LvyClient;
import so.dian.invoice.client.PassportClient;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.converter.InvoiceConverter;
import so.dian.invoice.converter.InvoiceDetailConverter;
import so.dian.invoice.converter.InvoiceOperateLogConverter;
import so.dian.invoice.dao.*;
import so.dian.invoice.enums.*;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.facade.CustomerFacade;
import so.dian.invoice.facade.PassportFacade;
import so.dian.invoice.internal.CommonInternalService;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.manager.InvoiceOperateLogManager;
import so.dian.invoice.manager.InvoiceValidateStatusManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.dto.*;
import so.dian.invoice.pojo.dto.identify.DetailAndExtraDTO;
import so.dian.invoice.pojo.dto.identify.DetailItemsDTO;
import so.dian.invoice.pojo.entity.*;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.param.*;
import so.dian.invoice.pojo.query.InvoiceFilterTimeQuery;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;
import so.dian.invoice.pojo.vo.InvoiceVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.service.InvoiceValidateService;
import so.dian.invoice.util.*;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.invoice.volidator.InvoiceValidator;
import so.dian.lvy.pojo.query.WithdrawListQuery;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static so.dian.invoice.constant.InvoiceConstants.DIAN_INVOICE_BELONG_ID;
import static so.dian.invoice.constant.InvoiceConstants.INVOICE_EXPORT_COUNT;

@Service
public class InvoiceServiceImpl implements InvoiceService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceServiceImpl.class);

    @Resource
    private InvoiceDAO invoiceDAO;

    @Resource
    private InvoiceDetailDAO invoiceDetailDAO;

    @Resource
    private InvoiceExpressesRelationDAO invoiceExpressesRelationDAO;

    @Resource
    private InvoiceDeductionDAO invoiceDeductionDAO;

    @Resource
    private LvyClient lvyClient;

    @Resource
    private AgentEmployeeService agentEmployeeService;

    @Resource
    private InvoiceIdentifyRecordDAO recordDAO;

    @Resource
    private ApplicationContext context;

    @Resource
    private InvoiceServiceImpl proxySelf;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private InvoiceSubjectRelationDAO invoiceSubjectRelationDAO;

    @Resource
    private CommonInternalService commonInternalService;

    @Resource
    private InvoiceManager invoiceManager;

    @Resource
    private InvoiceOperateLogManager invoiceOperateLogManager;

    @Resource
    private InvoiceValidateStatusManager invoiceValidateStatusManager;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private PassportFacade passportFacade;
    @Autowired
    private InvoiceValidateService invoiceValidateService;
    private static String[] headers =
            {"序号", "发票日期", "发票号码", "开票内容", "规格", "数量", "单价（不含税）", "不含税金额", "税率（%）", "税额", "价税合计", "开票公司", "备注", "发票类型"};

    /**
     * 新增已核销的发票
     */
    @Override
    @Transactional
    public void addWithDeductInvoice(InvoiceDto dto) {

        if (dto.getType() != null && !InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(dto.getType()))
                && (StringUtil.isBlank(dto.getInvoiceCode()) || StringUtil.isBlank(dto.getInvoiceNo()))) {
            LeoExcs.SERVER_INTERNAL_ERROR.setMessage("信息不完整");
            throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
        }else{
            if(StringUtil.isBlank(dto.getInvoiceNo())){
                LeoExcs.SERVER_INTERNAL_ERROR.setMessage("信息不完整");
                throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
            }
        }

        InvoiceDO oldInvoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(dto.getInvoiceCode(), dto.getInvoiceNo());
        if (oldInvoiceDO != null) {
            LeoExcs.SERVER_INTERNAL_ERROR.setMessage("发票已经存在");
            throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
        }

        List<InvoiceDO> invoiceDOList = Lists.newArrayList();
        List<InvoiceDetailDO> detailDOList = Lists.newArrayList();
        createInvoiceDO("", dto.getCreator(), dto, invoiceDOList, detailDOList);
        InvoiceDO invoiceDO = invoiceDOList.iterator().next();
        invoiceDO.setStatus(InvoiceStatusEnum.ALL.getType());

        insertInvoiceList(invoiceDOList);
        insertInvoiceDetailBatch(detailDOList);

        InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        BeanUtils.copyProperties(dto, invoiceDeductionDO);
        invoiceDeductionDO.setAmount(dto.getPrice());
        invoiceDeductionDO.setBusinessType(dto.getSubjectType());
        invoiceDeductionDO.setOperateType(OperateTypeEnum.DEDUCT.getType());

        invoiceDeductionDAO.insertSelective(invoiceDeductionDO);
    }

    @Override
    @Transactional
    public Set<String> addInvoiceBatch(Integer userId, InvoiceBatchParam param, List<String> noList) {

        if (param == null || CollectionUtil.isEmpty(param.getInvoiceDtoList())) {
            return Sets.newHashSet();
        }

        List<InvoiceDto> invoiceDtoList = param.getInvoiceDtoList();
        List<InvoiceDO> invoiceDOList = Lists.newArrayList();
        List<InvoiceDetailDO> detailDOList = Lists.newArrayList();

        Set<String> codeAndNoSet =
                validAndCreateInvoice(param.getBatchNo(), userId, invoiceDtoList, noList, invoiceDOList, detailDOList,param.getCurrentUserReq());

        insertInvoiceList(invoiceDOList);
        insertInvoiceDetailBatch(detailDOList);

        //新增手动录入操作日志
        List<InvoiceOperateLogBO> logBOList =
                InvoiceOperateLogConverter.buildBatchInsertLog(invoiceDOList);
        invoiceOperateLogManager.insertBatch(logBOList);
        return codeAndNoSet;
    }

    @Override
    public InvoiceDO getInvoiceById(Integer id) {
        return invoiceDAO.selectByPrimaryKey(id);
    }

    @Override
    public InvoiceDO getInvoiceByInvoiceCodeAndNo(String invoiceCode, String invoiceNo) {

        return invoiceDAO.selectInvoiceByInvoiceCodeAndNo(invoiceCode, invoiceNo);
    }

    @Override
    public List<InvoiceDO> getInvoiceByInvoiceNoList(List<String> invoiceNoList) {

        if (invoiceNoList == null || invoiceNoList.size() == 0) {

            return new ArrayList<>();
        }

        return invoiceDAO.selectInvoiceByInvoiceNoList(invoiceNoList);
    }


    @Override
    public int  insertInvoiceList(List<InvoiceDO> invoiceList) {
        if (invoiceList != null && invoiceList.size() > 0) {
            return invoiceDAO.insertBatch(invoiceList);
        }
        return 0;
    }

    @Override
    public int updateInvoice(InvoiceDO invoice) {

        return invoiceDAO.updateByPrimaryKeySelective(invoice);
    }

    @Override
    @Transactional
    public Boolean deleteInvoiceByInvoiceCodeAndNo(InvoiceObsoleteParam param) {
        Date nowDate = new Date();
        // 删除发票
        InvoiceDO invoice = new InvoiceDO();
        invoice.setInvoiceNo(param.getInvoiceNo());
        invoice.setInvoiceCode(param.getInvoiceCode() == null ? "" : param.getInvoiceCode());
        invoice.setGmtModified(nowDate);
        invoice.setIsDelete(1);
        // 删除发票详情
        InvoiceDetailDO invoiceDetail = new InvoiceDetailDO();
        invoiceDetail.setInvoiceNo(param.getInvoiceNo());
        invoiceDetail.setInvoiceCode(param.getInvoiceCode() == null ? "" : param.getInvoiceCode());
        invoiceDetail.setGmtModified(nowDate);
        invoiceDetail.setIsDelete(1);
        invoiceDAO.updateInvoiceByInvoiceCodeAndNo(invoice);
        invoiceDetailDAO.updateInvoiceDetailByInvoiceCodeAndNo(invoiceDetail);
        //删除发票日志
        invoiceOperateLogManager.insert(InvoiceOperateLogConverter.obsoleteLog(param));
        return true;
    }

    @Override
    public List<InvoiceDO> getInvoiceList(InvoiceParam param) {
        return invoiceDAO.selectInvoiceList(param);
    }

    @Override
    public List<InvoiceDto> getInvoicePage(InvoiceParam param) {
        return invoiceDAO.selectInvoicePage(param);
    }

    @Override
    public int count(InvoiceParam param) {

        return invoiceDAO.count(param);
    }

    @Override
    public List<InvoiceDetailDO> getInvoiceDetailByInvoiceCodeAndNo(String invoiceCode, String invoiceNo) {

        return invoiceDetailDAO.selectInvoiceDetailList(invoiceCode, invoiceNo);
    }

    @Override
    public void saveInvoiceDetail(String invoiceCode, String invoiceNo, List<InvoiceDetailDto> invoiceDetailDtoList) {

        List<InvoiceDetailDO> addList = new ArrayList<>();
        Map<Integer, InvoiceDetailDO> newDetailMap = new HashMap<>();

        if (invoiceDetailDtoList != null && invoiceDetailDtoList.size() > 0) {

            for (InvoiceDetailDto invoiceDetailDto : invoiceDetailDtoList) {

                InvoiceDetailDO invoiceDetail = new InvoiceDetailDO();
                BeanUtils.copyProperties(invoiceDetailDto, invoiceDetail);
                invoiceDetail.setInvoiceNo(invoiceNo);
                invoiceDetail.setInvoiceCode(invoiceCode);

                if (invoiceDetail.getId() == null) {
                    addList.add(invoiceDetail);
                } else {
                    newDetailMap.put(invoiceDetail.getId(), invoiceDetail);
                }
            }
        }

        List<InvoiceDetailDO> updateList = new ArrayList<>();
        List<InvoiceDetailDO> oldDetailList = getInvoiceDetailByInvoiceCodeAndNo(invoiceCode, invoiceNo);

        if (oldDetailList != null && oldDetailList.size() > 0) {
            for (InvoiceDetailDO oldDetail : oldDetailList) {
                InvoiceDetailDO newDetail = newDetailMap.get(oldDetail.getId());
                if (newDetail == null) {
                    oldDetail.setIsDelete(1);
                    updateList.add(oldDetail);
                } else if (!isInvoiceDetailEqual(oldDetail, newDetail)) {
                    updateList.add(newDetail);
                }
            }
        }

        insertInvoiceDetailBatch(addList);
        updateInvoiceDetailList(updateList);
    }

    @Override
    public void saveInvoiceDetail(String invoiceCode, String invoiceNo, List<InvoiceDetailDto> invoiceDetailDtoList, String oldInvoiceCode, String oldInvoiceNo) {
        List<InvoiceDetailDO> addList = new ArrayList<>();
        Map<Integer, InvoiceDetailDO> newDetailMap = new HashMap<>();

        if (invoiceDetailDtoList != null && invoiceDetailDtoList.size() > 0) {

            for (InvoiceDetailDto invoiceDetailDto : invoiceDetailDtoList) {

                InvoiceDetailDO invoiceDetail = new InvoiceDetailDO();
                BeanUtils.copyProperties(invoiceDetailDto, invoiceDetail);
                invoiceDetail.setInvoiceNo(invoiceNo);
                invoiceDetail.setInvoiceCode(invoiceCode == null ? "" : invoiceCode);

                if (invoiceDetail.getId() == null) {
                    addList.add(invoiceDetail);
                } else {
                    newDetailMap.put(invoiceDetail.getId(), invoiceDetail);
                }
            }
        }

        List<InvoiceDetailDO> updateList = new ArrayList<>();
        List<InvoiceDetailDO> oldDetailList = getInvoiceDetailByInvoiceCodeAndNo(oldInvoiceCode, oldInvoiceNo);

        if (oldDetailList != null && oldDetailList.size() > 0) {
            for (InvoiceDetailDO oldDetail : oldDetailList) {
                InvoiceDetailDO newDetail = newDetailMap.get(oldDetail.getId());
                if (newDetail == null) {
                    oldDetail.setIsDelete(1);
                    updateList.add(oldDetail);
                } else {
                    updateList.add(newDetail);
                }
            }
        }

        insertInvoiceDetailBatch(addList);
        updateInvoiceDetailList(updateList);
    }

    @Override
    public int insertInvoiceDetailBatch(List<InvoiceDetailDO> invoiceDetailDOList) {

        if (invoiceDetailDOList != null && invoiceDetailDOList.size() > 0) {
            return invoiceDetailDAO.insertBatch(invoiceDetailDOList);
        }

        return 0;
    }

    @Override
    public int updateInvoiceDetailList(List<InvoiceDetailDO> invoiceDetailDOList) {

        int num = 0;
        if (invoiceDetailDOList != null && invoiceDetailDOList.size() > 0) {
            for (InvoiceDetailDO invoiceDetail : invoiceDetailDOList) {
                num += invoiceDetailDAO.updateByPrimaryKeySelective(invoiceDetail);
            }
        }

        return num;
    }

    @Override
    public void downLoadInvoiceDetail(InvoiceParam param, HttpServletRequest request, HttpServletResponse response) {

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("发票");
        int rowNum = 0;
        HSSFRow row = sheet.createRow(rowNum++);
        for (int i = 0; i < headers.length; i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellValue(headers[i]);
        }

        List<InvoiceDetailDto> list = invoiceDAO.selectInvoiceDetailAll(param);
        if (list != null && list.size() > 0) {
            for (InvoiceDetailDto detail : list) {
                int cellIndex = 0;
                row = sheet.createRow(rowNum++);
                HSSFCell cell = row.createCell(cellIndex++);
                cell.setCellValue(rowNum - 1); //
                //"序号","发票日期","发票号码","开票内容","规格",
                //"数量","单价（不含税）","不含税金额","税率（%）",
                // "税额","价税合计","开票公司","备注","发票类型"
                cell = row.createCell(cellIndex++);
                cell.setCellValue(DateUtil.dateToString(detail.getGmtCreate(), "yyyyMMdd"));
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getInvoiceNo());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getMaterialName());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getMaterialSpec());

                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getQuantity());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getUnitPrice().doubleValue());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getRawPrice().doubleValue());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getTaxRate().intValue());

                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getTaxPrice().doubleValue());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getPrice().doubleValue());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getSupplierName());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(detail.getMemo());
                cell = row.createCell(cellIndex++);
                cell.setCellValue(InvoiceTypeEnum.getField(detail.getType()));
            }
        }

        String fileName = "发票";
        fileName += ".xls";
        transportData(request, response, fileName, workbook);
    }

    private void transportData(HttpServletRequest request, HttpServletResponse response, String fileName,
                               HSSFWorkbook workbook) {

        ServletOutputStream outputStream = null;
        try {
            response.setContentType("application/vnd.ms-excel;");
            String agent = request.getHeader("USER-AGENT");
            if (agent != null && agent.toLowerCase().indexOf("firefox") > 0) {
                fileName = "=?UTF-8?B?" + (new String(Base64Utils.encodeToString(fileName.getBytes("UTF-8")))) + "?=";
            } else {
                fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            }
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            outputStream = response.getOutputStream();
            workbook.write(outputStream);
        } catch (IOException e) {
            logger.error("------ 供应商发票导出，流操作出错。", e);
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }

                if (workbook != null) {
                    workbook.close();
                }
            } catch (IOException e) {
                logger.error("------ 供应商发票导出，流关闭出错。", e);
            }
        }
    }

    @Override
    public JSONObject parse(List<List<String>> excelData, List<InvoiceDO> invoiceDOList,
                            List<InvoiceDetailDO> invoiceDetailDOList, Integer userId,
                            Multimap<String, Integer> invoiceNoMultimap) {

        JSONObject res = new JSONObject();
        if (excelData == null || excelData.size() == 0) {
            res.put("err", "没有发票数据");
            res.put("row", 1);
            res.put("column", 0);
            return res;
        }

        res.put("column", headers.length);
        int row = excelData.size();
        List<String> firstRowData = excelData.iterator().next();
        Map<Integer, Integer> headMap = new HashMap<>();
        for (int index = 0; index < headers.length; index++) {
            int i = 0;
            for (String head : firstRowData) {
                if (headers[index].equals(head)) {
                    headMap.put(i, index);
                    break;
                }

                i++;
            }

            if (i == firstRowData.size()) {
                res.put("err", "发票数据不完整");
                res.put("row", 1);
                return res;
            }
        }

        Map<String, InvoiceDO> invoiceDOMap = new HashMap<>();
        Set<String> uniqueMaterial = new HashSet<>();
        for (int index = 2; index < row; index++) {
            List<String> subList = excelData.get(index);
            if (subList.size() != firstRowData.size()) {
                res.put("err", "发票数据不完整");
                res.put("row", index);
                return res;
            }

            InvoiceDO invoiceDO = new InvoiceDO();
            invoiceDO.setCreator(userId);
            invoiceDO.setStatus(1);
            invoiceDO.setInvoiceCode("");
            invoiceDO.setSource(InvoiceSourceEnum.BATCH.getType());
            invoiceDO.setUsedAmount(new BigDecimal(0));

            // 获取当前登录者的渠道信息
            AgentDTO agentDTO = getInvoiceBelongInfo(userId);
            if (agentDTO == null) {
                logger.error("当前登录者的渠道信息获取失败。userId：" + userId);
                throw new BizException(LeoExcs.EMPLOYEE_NOT_EXIST);
            }
            // 填充发票归属信息
            invoiceDO.setBelongSubjectType(agentDTO.getType());
            invoiceDO.setBelongSubjectId(agentDTO.getAgentId().intValue());

            InvoiceDetailDO invoiceDetail = new InvoiceDetailDO();
            invoiceDetail.setUnit("");
            invoiceDetail.setInvoiceCode("");
            int column = subList.size();
            Map<String, String> nameToNoMap = new HashMap<>();
            for (int j = 0; j < column; j++) {
                initInvoice(headMap.get(j), subList.get(j), invoiceDO, invoiceDetail, nameToNoMap, res);
                if (res.containsKey("err")) {
                    break;
                }
            }

            if (res.containsKey("err")) {
                res.put("row", index);
                break;
            }

            String invoiceMaterial = invoiceDetail.getInvoiceNo() + "_" + invoiceDetail.getMaterialName();
            boolean notUnique = uniqueMaterial.contains(invoiceMaterial);
            if (notUnique) {
                res.put("err", "同一张的发票开票内容重复");
                res.put("row", index);
                break;
            }
            uniqueMaterial.add(invoiceMaterial);

            invoiceDetailDOList.add(invoiceDetail);
            InvoiceDO oldInvoice = invoiceDOMap.get(invoiceDO.getInvoiceNo());
            if (oldInvoice == null) {
                invoiceDOMap.put(invoiceDO.getInvoiceNo(), invoiceDO);
            } else {
                oldInvoice.setRawPrice(oldInvoice.getRawPrice().add(invoiceDO.getRawPrice()));
                oldInvoice.setPrice(oldInvoice.getPrice().add(invoiceDO.getPrice()));
            }

            invoiceNoMultimap.put(invoiceDO.getInvoiceNo(), index);
        }

        invoiceDOList.addAll(invoiceDOMap.values());

        return res;
    }

    @Override
    public JSONObject parseErrorMsg(String filePathName, Workbook workbook, List<InvoiceDO> invoiceDOList,
                                    JSONObject res, Multimap<String, Integer> invoiceNoMultimap) {

        JSONObject resJson = new JSONObject();
        List<String> invoiceNoList = new ArrayList<>();
        for (InvoiceDO invoiceDO : invoiceDOList) {
            invoiceNoList.add(invoiceDO.getInvoiceNo());
        }

        List<InvoiceDO> oldInvoiceDOList = getInvoiceByInvoiceNoList(invoiceNoList);

        List<Integer> rowList = new ArrayList<>();
        if (oldInvoiceDOList != null && oldInvoiceDOList.size() > 0) {
            for (InvoiceDO invoiceDO : oldInvoiceDOList) {
                rowList.addAll(invoiceNoMultimap.get(invoiceDO.getInvoiceNo()));
            }

            res.put("err", "发票号重复");
        }

        if (res.containsKey("err")) {

            int lastColumn = res.getInteger("column");
            Row titleRow = workbook.getSheetAt(0).getRow(0);
            if (titleRow == null) {
                titleRow = workbook.getSheetAt(0).createRow(0);
            }
            Cell errCell = titleRow.getCell(lastColumn);
            if (errCell == null) {
                errCell = titleRow.createCell(lastColumn);
            }
            errCell.setCellStyle(ExcelParser.getCellStyle(workbook));
            errCell.setCellValue("错误原因");

            if (rowList.size() == 0) {
                rowList.add(res.getInteger("row"));
            }

            for (Integer rowIndex : rowList) {
                Row row = workbook.getSheetAt(0).getRow(rowIndex);
                if (row == null) {
                    row = workbook.getSheetAt(0).createRow(rowIndex);
                }

                Cell cell = row.getCell(lastColumn);
                if (cell == null) {
                    cell = row.createCell(lastColumn);
                }

                cell.setCellStyle(ExcelParser.getCellStyle(workbook));
                cell.setCellValue(res.getString("err"));
            }

            try {
                writeInvoiceFile(filePathName, workbook);
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

            String url = uploadFile(filePathName, "xls");

            deleteInvoiceFile(filePathName);
            resJson.put("url", url);
            resJson.put("errMsg", "解析excel异常");
        }

        return resJson;
    }

    @Override
    public String uploadFile(String filePathName, String fileType) {

        File file = new File(filePathName);
        FileInputStream fis = null;

        try {
            fis = new FileInputStream(filePathName);
            long size = file.length();
            String saveFileName = ImageUtils.getSaveFileName(file.getName(), fileType, Long.toHexString(size));
            OSSHelper.uploadFile(saveFileName, fis, size, fileType);

            return ImageUtils.IMAGE_DOMAIN_WITH_PROTOCOL + saveFileName;
        } catch (FileNotFoundException e) {
            logger.error("uploadFile, filePathName {} - {}", filePathName, e);
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }

                file.delete();
            } catch (IOException e) {
                logger.error("uploadFile, filePathName {} - {}", filePathName, e);
            }
        }

        return null;
    }

    @Override
    public void downLoadFile(HttpServletRequest request, HttpServletResponse response, String fileType,
                             String filePathName, String downFileName) {

        if (org.apache.commons.lang3.StringUtils.isBlank(downFileName)) {
            return;
        }

        String fileName = downFileName;

        if (fileType.equals("xls")) {
            response.setContentType("application/vnd.ms-excel;");
        } else {
            response.setContentType("application/pdf;");
        }

        try {
            String agent = request.getHeader("USER-AGENT");
            if (agent != null && agent.toLowerCase().indexOf("firefox") > 0) {
                fileName = "=?UTF-8?B?" + (new String(Base64Utils.encodeToString(fileName.getBytes("UTF-8")))) + "?=";
            } else {
                fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            }

            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        } catch (IOException e) {

        }

        byte[] b = new byte[1024];
        int len;

        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        InputStream stream = null;
//        FileInputStream fis = null;
        ServletOutputStream sos = null;
        try {
            //获取所有匹配的文件
            org.springframework.core.io.Resource resource = resolver.getResource(filePathName);
            //获得文件流，因为在jar文件中，不能直接通过文件资源路径拿到文件，但是可以在jar包中拿到文件流
            stream = resource.getInputStream();
//            fis = new FileInputStream(filePathName);
            sos = response.getOutputStream();
            while ((len = stream.read(b)) != -1) {
                sos.write(b, 0, len);
            }
            sos.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (stream != null) {
                    stream.close();
                }
                if (sos != null) {
                    sos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void deleteInvoiceFile(String filePathName) {

        File file = new File(filePathName);
        if (file.exists()) {
            file.delete();
        }
    }

    @Override
    public void writeInvoiceFile(String filePathName, Workbook workbook) {

        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(filePathName);
            workbook.write(fos);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void initInvoice(int headIndex, String data, InvoiceDO invoiceDO, InvoiceDetailDO invoiceDetail,
                             Map<String, String> nameToNoMap, JSONObject res) {
        //                                            5                                                    10
        //"序号","发票日期","发票号码","开票内容","规格","数量","单价（不含税）","不含税金额","税率（%）","税额","价税合计","开票公司","备注","发票类型"
        if (data != null) {
            data = data.trim();
        }

        switch (headIndex) {

            case 1:
                Date createTime = DateUtil.stringToDate(data, "yyyyMMdd");
                if (data != null && data.length() != 8) {
                    createTime = null;
                }
                if (createTime == null) {
                    res.put("err", "发票日期为空或格式不正确（例：20180101）");
                }
                if (createTime != null) {
                    String dateTemp = DateUtil.dateToString(createTime, "yyyyMMdd");
                    if (!dateTemp.equals(data)) {
                        res.put("err", "发票日期为空或格式不正确（例：20180101）");
                    }
                }
                invoiceDO.setGmtCreate(createTime);
                break;
            case 2:
                if (StringUtil.isBlank(data)) {
                    res.put("err", "发票号码为空");
                }
                invoiceDO.setInvoiceNo(data);
                invoiceDetail.setInvoiceNo(data);
                break;
            case 3:
                if (StringUtil.isBlank(data)) {
                    res.put("err", "开票内容为空");
                }
                invoiceDetail.setMaterialName(data);
                break;
            case 4:
                invoiceDetail.setMaterialSpec(data);
                break;
            case 5:
                BigDecimal quantityDec = parseBigDecimal(data);
                if (quantityDec == null) {
                    res.put("err", "数量为空或数据有误");
                }

                Integer quantity = quantityDec == null ? null : quantityDec.intValue();
                invoiceDetail.setQuantity(quantity);
                break;
            case 6:
                BigDecimal dec = parseBigDecimal(data);
                if (dec == null) {
                    res.put("err", "单价（不含税）为空或数据有误");
                }

                invoiceDetail.setUnitPrice(dec);
                break;
            case 7:
                BigDecimal dec1 = parseBigDecimal(data);
                if (dec1 == null) {
                    res.put("err", "不含税金额为空或数据有误");
                }

                invoiceDO.setRawPrice(dec1);
                invoiceDetail.setRawPrice(dec1);
                break;
            case 8:
                BigDecimal dec2 = parseBigDecimal(data);
                if (dec2 == null) {
                    res.put("err", "税率（%）为空或数据有误");
                }

                // 赋值第一个
                if (null == invoiceDO.getTaxRate()) {
                    invoiceDO.setTaxRate(dec2);
                }
                invoiceDetail.setTaxRate(dec2);
                break;
            case 9:
                BigDecimal dec3 = parseBigDecimal(data);
                if (dec3 == null) {
                    res.put("err", "税额为空或数据有误");
                }

                invoiceDetail.setTaxPrice(dec3);
                break;
            case 10:
                BigDecimal dec4 = parseBigDecimal(data);
                if (dec4 == null) {
                    res.put("err", "价税合计为空或数据有误");
                }

                invoiceDO.setPrice(dec4);
                invoiceDetail.setPrice(dec4);
                break;
            case 11:
                String supplierNo = nameToNoMap.get(data);
                if (supplierNo == null) {
                    res.put("err", "开票公司为空或有误");
                }
                break;
            case 12:
                invoiceDO.setMemo(data);
                break;
            case 13:
                invoiceDO.setType(InvoiceTypeEnum.getTypeByField(data));
                if (invoiceDO.getType() == null) {
                    res.put("err", "发票类型为空或有误");
                }
                break;
            default:
                break;
        }
    }

    private BigDecimal parseBigDecimal(String data) {

        try {
            BigDecimal dec = new BigDecimal(data.trim());
            return dec;
        } catch (Exception e) {
        }

        return null;
    }

    private boolean isInvoiceDetailEqual(InvoiceDetailDO oldDetail, InvoiceDetailDO newDetail) {

        return oldDetail.getMaterialName().equals(newDetail.getMaterialName())
                && oldDetail.getMaterialSpec().equals(newDetail.getMaterialSpec())
                && oldDetail.getQuantity().equals(newDetail.getQuantity())
                && oldDetail.getRawPrice().compareTo(newDetail.getRawPrice()) == 0
                && oldDetail.getTaxPrice().compareTo(newDetail.getTaxPrice()) == 0
                && oldDetail.getUnit().equals(newDetail.getUnit())
                && oldDetail.getTaxRate().equals(newDetail.getTaxRate())
                && oldDetail.getUnitPrice().compareTo(newDetail.getUnitPrice()) == 0;
    }

    private Set<String> validAndCreateInvoice(String batchNo, Integer userId, List<InvoiceDto> invoiceDtoList,
                                              List<String> noList, List<InvoiceDO> invoiceDOList, List<InvoiceDetailDO> detailDOList,CurrentUserReq currentUserReq) {

        Set<String> codeAndNoSet = Sets.newHashSet();

        for (InvoiceDto dto : invoiceDtoList) {

            //发票日期校验
//            String companySubject = dto.getCompanySubject();
//            CompanySubjectEnum enumByName = CompanySubjectEnum.getEnumByName(companySubject);
//            if (Objects.nonNull(enumByName)) {
//                String checkBuyerResult = InvoiceBuyerValidator.validInvoiceBuyer(enumByName.getDesc(),
//                        dto.getGmtCreate(), true);
//                if (!StringUtils.isEmpty(checkBuyerResult)) {
//                    LeoExcs.PARAM_RESOLVER_FAIL.setMessage(checkBuyerResult);
//                    throw new BizException(LeoExcs.PARAM_RESOLVER_FAIL);
//                }
//            }
            if(dto.getGmtCreate() == null || dto.getType() == null || StringUtil.isBlank(dto.getInvoiceNo())){
                LeoExcs.SERVER_INTERNAL_ERROR.setMessage("信息不完整");
                throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
            }
            // 非空校验
            if (!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(dto.getType()))
                    && StringUtil.isBlank(dto.getInvoiceCode())) {
                LeoExcs.SERVER_INTERNAL_ERROR.setMessage("信息不完整");
                throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
            }
            boolean checkResult = invoiceValidateService.checkInvoiceBuyer(CheckInvoiceBuyerDTO.builder()
                    .belongSubjectType(currentUserReq.getBelongSubjectType())
                    .belongSubjectId(Long.valueOf(currentUserReq.getBelongSubjectId()))
                    .buyer(dto.getCompanySubject())
                    .buyerTaxId(dto.getBuyerTaxId()).build());
            if (!checkResult) {
                logger.error("无权录入该购买方发票 buyerTaxId:{}",dto.getBuyerTaxId());
                throw so.dian.commons.eden.exception.BizException.create(
                        InvoiceErrorCodeEnum.NO_PERMISSION_BUYER);
            }
            // 增值税专用发票，税前金额不能为空
            if (InvoiceTypeEnum.VAT.getType() == dto.getType()) {
                if (dto.getRawPrice() == null) {
                    logger.error("发票验真信息参数税前金额信息为空:{},", dto);
                    throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
                }
            } else {
                // "增值税专用发票"以外的发票，校验码不能为空
                if (!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(dto.getType())) && StringUtil.isBlank(dto.getCheckCode())) {
                    logger.error("发票验真信息参数校验码信息为空:{},", dto);
                    throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
                }
            }
            createInvoiceDO(batchNo, userId, dto, invoiceDOList, detailDOList);

            codeAndNoSet.add((dto.getInvoiceCode() == null ? "" : dto.getInvoiceCode()) + "_" + dto.getInvoiceNo());
            noList.add(dto.getInvoiceNo());
        }


        if (codeAndNoSet.size() < invoiceDtoList.size()) {
            LeoExcs.SERVER_INTERNAL_ERROR.setMessage("提交的发票有重复");
            throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
        }

        Set<String> resSet = Sets.newHashSet();
        resSet.addAll(codeAndNoSet);
        List<String> codeAndNoList = invoiceDAO.selectCodeAndNoByInvoiceNoList(noList);
        if (!CollectionUtil.isEmpty(codeAndNoList)) {
            codeAndNoSet.retainAll(codeAndNoList);
            if (codeAndNoSet.size() > 0) {
                LeoExcs.SERVER_INTERNAL_ERROR.setMessage("发票记录已存在");
                throw new BizException(LeoExcs.SERVER_INTERNAL_ERROR);
            }
        }

        return resSet;
    }

    private void createInvoiceDO(String batchNo, Integer userId, InvoiceDto dto,
                                 List<InvoiceDO> invoiceDOList, List<InvoiceDetailDO> detailDOList) {

        BigDecimal zero = new BigDecimal(0);

        InvoiceDO invoice = new InvoiceDO();
        BeanUtils.copyProperties(dto, invoice);
        invoice.setCreator(userId);
        invoice.setStatus(InvoiceStatusEnum.WAIT.getType());
        invoice.setSource(InvoiceSourceEnum.MANUAL.getType());
        invoice.setUsedAmount(zero);
        invoice.setSupplierNo("");
        invoice.setTax(Objects.isNull(dto.getTaxPrice()) ? zero : dto.getTaxPrice());
        invoice.setBillNo("");
        invoice.setReceiveTime(new Date());
        invoice.setBatchNo(batchNo);
//        Boolean isReal = false;
        InvoiceValidateDetailParams params = new InvoiceValidateDetailParams();
        params.setInvoiceCode(invoice.getInvoiceCode() == null ? "" : invoice.getInvoiceCode());
        params.setInvoiceNo(invoice.getInvoiceNo());
        params.setInvoiceDate(DateUtil.dateToYMD(invoice.getGmtCreate()));
        params.setInvoiceType(invoice.getType());
        if (InvoiceTypeEnum.VAT.getType() == params.getInvoiceType()) {
            params.setPretaxAmount(invoice.getRawPrice().toString());
        } else {
            params.setCheckCode(invoice.getCheckCode() == null ? "" : invoice.getCheckCode());
        }
        invoice.setBuyerTaxId(dto.getBuyerTaxId());
        invoice.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.STAY.getCode());
        invoice.setCheckCode(dto.getCheckCode() == null ? "" : dto.getCheckCode());
        invoice.setRawPrice(dto.getRawPrice());

        // 获取当前登录者的渠道信息
        AgentDTO agentDTO = getInvoiceBelongInfo(userId);
        if (agentDTO == null) {
            logger.error("当前登录者的渠道信息获取失败。userId：" + userId);
            throw new BizException(LeoExcs.EMPLOYEE_NOT_EXIST);
        }
        // 填充发票归属信息
        invoice.setBelongSubjectType(agentDTO.getType());
        invoice.setBelongSubjectId(agentDTO.getAgentId().intValue());

        if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(invoice.getBelongSubjectId())) {
//            CompanySubjectEnum companySubjectEnum = CompanySubjectEnum.getEnumByName(dto.getCompanySubject());
//            invoice.setBuyer(companySubjectEnum.getDesc());
            invoice.setBuyer(dto.getCompanySubject());
            String employeeNick = agentEmployeeService.getEmployeeNickById(userId);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(employeeNick)) {
                invoice.setCreateName(employeeNick);
            }
        } else {
            invoice.setCreateName(agentDTO.getAgentName() == null ? "" : agentDTO.getAgentName());
            invoice.setBuyer(dto.getCompanySubject());
        }


        //发票详情组装
        List<InvoiceDetailDO> invoiceDetailDOList =
                InvoiceDetailConverter.convertDTO2DO(dto.getInvoiceDetailDtoList(), zero,
                        dto.getInvoiceCode(), dto.getInvoiceNo());

        if (!CollectionUtils.isEmpty(invoiceDetailDOList)) {
            invoice.setTaxRate(invoiceDetailDOList.get(0).getTaxRate());
        }
        invoiceDOList.add(invoice);
        detailDOList.addAll(invoiceDetailDOList);
    }

    /**
     * 自动新增发票(批量)
     */
    @Override
    public String autoAddBatch(InvoiceValidateParams params) {
        CurrentUserReq currentUserReq = params.getCurrentUserReq();
        //更新状态为关联中
        recordDAO.updateIsRelatedByIds(params.getIds(), InvoiceIdentifyRecordEnum.IsRelatedEnum.DOING.getCode());
        int failCount = 0;
        StringBuilder resultStringBuilder = new StringBuilder("");
        RLock lock = redissonClient.getLock("autoAddBatch:" + params.getLoginUserId());
        try {
            if (lock.tryLock(1000, 15000, TimeUnit.MILLISECONDS)) {
                List<InvoiceIdentifyRecordDO> list = recordDAO.getListByIds(params.getIds());
                for (InvoiceIdentifyRecordDO identifyRecordDO : list) {
                    Integer id = identifyRecordDO.getId();
                    if (identifyRecordDO.getInvoiceType() != null && !InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(identifyRecordDO.getInvoiceType())) && (StringUtil.isBlank(identifyRecordDO.getInvoiceCode()) || StringUtil.isBlank(
                            identifyRecordDO.getInvoiceNo()))) {
                        identifyRecordDO.setReason("信息不完善");
                        identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
                        recordDAO.updateByPrimaryKeySelective(identifyRecordDO);
                        resultStringBuilder.append("id:").append(id).append("信息不完善;\n");
                        failCount++;
                        continue;
                    }
                    if(identifyRecordDO.getInvoiceType() != null && InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(identifyRecordDO.getInvoiceType()))
                            && StringUtil.isBlank(identifyRecordDO.getInvoiceNo())){
                        identifyRecordDO.setReason("信息不完善");
                        identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
                        recordDAO.updateByPrimaryKeySelective(identifyRecordDO);
                        resultStringBuilder.append("id:").append(id).append("信息不完善;\n");
                        failCount++;
                        continue;
                    }
                    //去重
                    InvoiceDO invoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(identifyRecordDO.getInvoiceCode(),
                            identifyRecordDO.getInvoiceNo());
                    if (invoiceDO != null) {
                        identifyRecordDO.setReason("发票已经存在");
                        identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
                        recordDAO.updateByPrimaryKeySelective(identifyRecordDO);
                        resultStringBuilder.append("id:").append(id).append("发票已经存在;\n");
                        failCount++;
                        continue;
                    }
                    // 购买方税号校验，小电集团的只能填写小电集团的，非小电集团的购买方税号只能填写非小电集团的
                    boolean checkResult = invoiceValidateService.checkInvoiceBuyer(CheckInvoiceBuyerDTO.builder()
                            .belongSubjectType(currentUserReq.getBelongSubjectType())
                            .belongSubjectId(Long.valueOf(currentUserReq.getBelongSubjectId()))
                            .buyer(identifyRecordDO.getBuyer())
                            .buyerTaxId(identifyRecordDO.getBuyerTaxId()).build());
                    if (!checkResult) {
                        identifyRecordDO.setReason(InvoiceErrorCodeEnum.NO_PERMISSION_BUYER.getDesc());
                        identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
                        recordDAO.updateByPrimaryKeySelective(identifyRecordDO);
                        resultStringBuilder.append("id:").append(id).append(InvoiceErrorCodeEnum.NO_PERMISSION_BUYER.getDesc()).append(";\n");
                        failCount++;
                        continue;
                    }
                    // 我司的发票有此功能，判断发票购买方 2020-03-25 北京伊电园->杭州伊电园
                    if (Objects.isNull(DateUtil.stringToDate(identifyRecordDO.getInvoiceDate()))) {
                        identifyRecordDO.setReason("发票日期校验有误");
                        identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
                        recordDAO.updateByPrimaryKeySelective(identifyRecordDO);
                        resultStringBuilder.append("id:").append(id).append("发票日期校验有误;\n");
                        failCount++;
                        continue;
                    }

                    // OCR识别目前只对小电开放，若对代理商开放，则此逻辑需要修改
                    // 跟小电抬头以及税号做校验，校验失败时，记录错误原因，但不影响转入发票台账
                    if(!InvoiceBuyerValidator.checkInvoiceBuyerAndTaxId(currentUserReq.getBelongSubjectType(),identifyRecordDO.getBuyer(), identifyRecordDO.getBuyerTaxId())) {
                        // 抬头或税号信息校验失败
                        identifyRecordDO.setReason("抬头或税号信息校验失败");
                        identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
                        recordDAO.updateByPrimaryKeySelective(identifyRecordDO);
                        resultStringBuilder.append("id:").append(id).append("抬头或税号信息校验失败;\n");
                        failCount++;
                    }
                    proxySelf.autoInsertInvoice(params, identifyRecordDO);
                }
            }
        } catch (Exception e) {
            logger.error("批量自动新增发票异常，用户：{}", params.getLoginUserId(), e);
        } finally {
            lock.unlock();
        }
        if (failCount <= 0) {
            return "批量新增发票成功";
        }
        return "失败条数：" + failCount + "\n" + resultStringBuilder.toString();
    }

    @Override
    public ServiceResult<String> autoInsertInvoice(InvoiceValidateParams params,
                                                   InvoiceIdentifyRecordDO identifyRecordDO) {
        ServiceResult<String> result = new ServiceResult<>();
        try {
            //插入发票信息
            BigDecimal zero = new BigDecimal(0);
            InvoiceDO invoice = new InvoiceDO();
            invoice.setSubjectType(params.getSubjectType());
            invoice.setInvoiceNo(identifyRecordDO.getInvoiceNo());
            invoice.setInvoiceCode(identifyRecordDO.getInvoiceCode() == null ? "" : identifyRecordDO.getInvoiceCode());
            invoice.setSubjectName(StringUtil.trimFull2Half(identifyRecordDO.getSeller()));
            invoice.setGmtCreate(DateUtil.stringToDate(identifyRecordDO.getInvoiceDate()));
            invoice.setPrice(DecimalUtil.transBigDecimal(identifyRecordDO.getTotal(), 4));
            //失败的话把失败原因透出
            if (InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode().equals(identifyRecordDO.getIsReal())) {
                invoice.setMemo(identifyRecordDO.getReason());
            } else {
                invoice.setMemo("自动录入");
            }
            invoice.setType(identifyRecordDO.getInvoiceType());
            invoice.setCreator(params.getLoginUserId());
            invoice.setCreateName(params.getLoginUserName() == null ? "" : params.getLoginUserName());
            invoice.setStatus(InvoiceStatusEnum.WAIT.getType());
            invoice.setSource(InvoiceSourceEnum.AUTO.getType());
            invoice.setUsedAmount(zero);
            invoice.setSupplierNo("");
            invoice.setTax(DecimalUtil.transBigDecimal(identifyRecordDO.getTax(), 4));
            invoice.setRawPrice(DecimalUtil.transBigDecimal(identifyRecordDO.getPretaxAmount(), 4));
            invoice.setBillNo("");
            invoice.setReceiveTime(new Date());
            invoice.setKind(identifyRecordDO.getKind());
            invoice.setCheckCode(identifyRecordDO.getCheckCode() == null ? "" : identifyRecordDO.getCheckCode());
            invoice.setTax(DecimalUtil.transBigDecimal(identifyRecordDO.getTax(), 4));
            invoice.setSeller(StringUtil.trimFull2Half(identifyRecordDO.getSeller()));
            invoice.setSellerTaxId(identifyRecordDO.getSellerTaxId());
            invoice.setBuyerTaxId(identifyRecordDO.getBuyerTaxId());
            invoice.setUrl(identifyRecordDO.getUrl());
            invoice.setIsReal(identifyRecordDO.getIsReal());
            invoice.setBuyer(StringUtil.trimFull2Half(identifyRecordDO.getBuyer()));
            invoice.setBatchNo(params.getBatchNo());

            // 获取userId
            Integer userId = params.getLoginUserId();
            // 获取当前登录者的渠道信息
            AgentDTO agentDTO = getInvoiceBelongInfo(userId);
            if (agentDTO == null) {
                logger.error("当前登录者的渠道信息获取失败。userId：" + userId);
                return result.fail("批量自动新增发票异常");
            }
            invoice.setBelongSubjectId(agentDTO.getAgentId().intValue());
            invoice.setBelongSubjectType(agentDTO.getType());

            // 设置发票列表的税率
            assembleInvoiceTaxRate(identifyRecordDO, invoice);

            //插入发票详情
            // 发票识别的第三方服务接口发生了变化，identifyRecordDO.getDetails()取到的已不是"[{...}]"格式，因此这里做兼容处理
            // 改成取发票识别结果
            List<InvoiceDetailDO> invoiceDetailDOList = assembleInvoiceDetail(identifyRecordDO);

            //更新发票关联状态
            identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.YES.getCode());

            //新增发票操作日志
            InvoiceOperateLogBO logBO = InvoiceOperateLogConverter.buildOCRInsertParam2BO(identifyRecordDO, invoice);

            // 写表
          proxySelf.autoInsertInvoiceTransactional(identifyRecordDO, invoice, invoiceDetailDOList, logBO);
        } catch (Exception e) {

            identifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
            recordDAO.updateByPrimaryKeySelective(identifyRecordDO);
            logger.error("批量自动新增发票异常 params:{},identifyRecordDO:{}", params,identifyRecordDO, e);
            return result.fail("批量自动新增发票异常");
        }
        return result.success("新增发票成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public void autoInsertInvoiceTransactional(InvoiceIdentifyRecordDO identifyRecordDO, InvoiceDO invoice,
                                               List<InvoiceDetailDO> invoiceDetailDOList, InvoiceOperateLogBO logBO) {

        invoiceDAO.insertSelective(invoice);

        if (CollectionUtils.isNotEmpty(invoiceDetailDOList)) {
            invoiceDetailDAO.insertBatch(invoiceDetailDOList);
        }

        recordDAO.updateByPrimaryKeySelective(identifyRecordDO);

        invoiceOperateLogManager.insert(logBO);
    }

    private void assembleInvoiceTaxRate(InvoiceIdentifyRecordDO identifyRecordDO, InvoiceDO invoice) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(identifyRecordDO.getResult())
                && identifyRecordDO.getResult().startsWith("{")) {
            DetailAndExtraDTO detailAndExtraDTO = JSON.parseObject(identifyRecordDO.getResult(), DetailAndExtraDTO.class);
            if (null != detailAndExtraDTO
                    && null != detailAndExtraDTO.getDetails()
                    && CollectionUtils.isNotEmpty(detailAndExtraDTO.getDetails().getItems())) {
                String taxRate = StringUtil.isBlank(detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate())
                        ? "" : detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate().replace("%", "");
                invoice.setTaxRate(DecimalUtil.transBigDecimal(taxRate, 4));
            }
        }
    }

    private List<InvoiceDetailDO> assembleInvoiceDetail(InvoiceIdentifyRecordDO identifyRecordDO) {
        List<InvoiceDetailDO> invoiceDetailDOList = new ArrayList<>();

        if (org.apache.commons.lang3.StringUtils.isNotBlank(identifyRecordDO.getResult())
                && identifyRecordDO.getResult().startsWith("{")) {

            DetailAndExtraDTO detailAndExtraDTO = JSON.parseObject(identifyRecordDO.getResult(), DetailAndExtraDTO.class);

            if (null != detailAndExtraDTO
                    && null != detailAndExtraDTO.getDetails()
                    && CollectionUtils.isNotEmpty(detailAndExtraDTO.getDetails().getItems())) {

                for (DetailItemsDTO itemsDTO : detailAndExtraDTO.getDetails().getItems()) {
                    InvoiceDetailDO invoiceDetail = new InvoiceDetailDO();
                    invoiceDetail.setUnit(itemsDTO.getUnit());
                    invoiceDetail.setUnitPrice(DecimalUtil.transBigDecimal(itemsDTO.getPrice(), 4));
                    String tax_rate =
                            StringUtil.isBlank(itemsDTO.getTax_rate()) ? "" : itemsDTO.getTax_rate().replace("%", "");
                    invoiceDetail.setTaxRate(DecimalUtil.transBigDecimal(tax_rate, 4));
                    invoiceDetail.setRawPrice(DecimalUtil.transBigDecimal(itemsDTO.getTotal(), 4));
                    invoiceDetail.setTaxPrice(DecimalUtil.transBigDecimal(itemsDTO.getTax(), 4));
                    invoiceDetail.setPrice(invoiceDetail.getRawPrice().add(invoiceDetail.getTaxPrice()));
                    invoiceDetail.setQuantity(DecimalUtil.transIntegerPositive(itemsDTO.getQuantity(),1));
                    invoiceDetail.setMaterialName(itemsDTO.getName());
                    invoiceDetail.setMaterialSpec(itemsDTO.getSpecification());
                    invoiceDetail.setInvoiceCode(identifyRecordDO.getInvoiceCode() == null ? "" : identifyRecordDO.getInvoiceCode());
                    invoiceDetail.setInvoiceNo(identifyRecordDO.getInvoiceNo());
                    invoiceDetail.setSupplierInvoiceDetailId(null);
                    invoiceDetailDOList.add(invoiceDetail);
                }

            }
        } else {
            //当前第三方接口不能通过items获取相信信息，现在通过ocr识别录入只存名称
            invoiceDetailDOList = InvoiceConverter.convertRecord2DOList(identifyRecordDO);
        }
        return invoiceDetailDOList;
    }

    /**
     * 自动新增发票(单张)
     */
    @Override
    public ServiceResult<String> autoAddBatchSingle(InvoiceValidateParams params) {
        ServiceResult<String> result = new ServiceResult<>();
        try {
            InvoiceIdentifyRecordDO identifyRecordDO = recordDAO.selectByPrimaryKey(params.getId());
            if (identifyRecordDO == null) {
                return result.fail("发票识别记录不存在");
            }
            // 我司的发票有此功能，判断发票购买方 2020-03-25 北京伊电园->杭州伊电园
            if (Objects.isNull(DateUtil.stringToDate(identifyRecordDO.getInvoiceDate()))) {
                return result.fail("发票日期校验有误");
            }
            CurrentUserReq currentUserReq = params.getCurrentUserReq();
            // 购买方税号校验，小电集团的只能填写小电集团的，非小电集团的购买方税号只能填写非小电集团的
            boolean checkResult = invoiceValidateService.checkInvoiceBuyer(CheckInvoiceBuyerDTO.builder()
                    .belongSubjectType(currentUserReq.getBelongSubjectType())
                    .belongSubjectId(Long.valueOf(currentUserReq.getBelongSubjectId()))
                    .buyer(identifyRecordDO.getBuyer())
                    .buyerTaxId(identifyRecordDO.getBuyerTaxId()).build());
            if (!checkResult) {
                logger.error("购买方税号校验失败，购买方税号：{}", identifyRecordDO.getBuyerTaxId());
                return result.fail(InvoiceErrorCodeEnum.NO_PERMISSION_BUYER.getDesc());
            }
            //去重
            InvoiceDO invoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(identifyRecordDO.getInvoiceCode(),
                    identifyRecordDO.getInvoiceNo());
            if (invoiceDO != null) {
                return result.fail("发票记录已经存在");
            }
            ServiceResult<String> serviceResult = proxySelf.autoInsertInvoice(params, identifyRecordDO);
            return serviceResult;
        } catch (Exception e) {
            logger.error("批量自动新增发票异常，发票记录：{}" , params.getId(), e);
            return result.fail("批量自动新增发票异常");
        }
    }

    @Override
    public ServiceResult<Boolean> getSubjectRelation(InvoiceSubjectRelationParam param) {
        ServiceResult<Boolean> result = new ServiceResult<>();
        try {
            if (param == null || param.getBizId() == null || param.getBizType() == null) {
                return result.fail("参数为空");
            }
            param.setApplySubjectName(StringUtil.trimFull2Half(param.getApplySubjectName()));
            param.setInvoiceSubjectName(StringUtil.trimFull2Half(param.getInvoiceSubjectName()));
            InvoiceSubjectRelationDO invoiceSubjectRelationDO =
                    invoiceSubjectRelationDAO.selectByBizIdAndBizTypeAndSubjectName(param.getBizId(), param.getBizType(),
                            param.getInvoiceSubjectName());
            if (invoiceSubjectRelationDO == null) {
                return result.success(false);
            }
            if (param.getApplySubjectName().equals(invoiceSubjectRelationDO.getRelationSubjectName())) {
                return result.success(true);
            }
            return result.success(false);
        } catch (Exception e) {
            logger.error("获取发票主体对应信息异常，param：{}", param, e);
            return result.fail("获取发票主体对应信息异常");
        }
    }

    @Override
    public List<BuyerDTO> getBuyerList(BuyerParam buyerParam) {
        if (Objects.isNull(buyerParam) || Objects.isNull(buyerParam.getBelongSubjectType())
                || Objects.isNull(buyerParam.getBelongSubjectId())) {
            throw new BizException(LeoExcs.AGENT_NOT_EXISTS);

        }
        logger.info("获取购买方列表，buyerParam：{}", buyerParam);
        // 类型判断
        if (BelongSubjectTypeEnum.isXD(buyerParam.getBelongSubjectType())) {
            // 获取小电集团发票购买方列表
            return getXiaoDianBuyerList();
        }
        // 获取非小电集团发票购买方列表
        return getOtherBuyerList(buyerParam.getBelongSubjectId());
    }

    private List<BuyerDTO> getXiaoDianBuyerList(){
        logger.info("小电集团发票购买方列表获取");
        List<BuyerDTO> buyerDTOS = new ArrayList<>();
        for (BuyerTaxIdEnum value : BuyerTaxIdEnum.values()) {
            BuyerDTO buyerDTO = new BuyerDTO();
            buyerDTO.setName(value.getBuyer());
            buyerDTO.setTaxId(value.getTaxId());
            buyerDTOS.add(buyerDTO);
        }
        return buyerDTOS;
    }

    /***
     * 其他非小电购买方(发票主体)
     */
    private List<BuyerDTO> getOtherBuyerList(Long belongSubjectId) {
        logger.info("getOtherBuyerList get belongSubjectId:{}",belongSubjectId);
        List<BuyerDTO> buyerDTOS = new ArrayList<>();
        CustomerAccountDTO customerAccountDTO = customerFacade.getByReferId(AccountReferTypeEnum.JOINT_VENTURE.getCode(),belongSubjectId);
        if (Objects.isNull(customerAccountDTO)) {
            return buyerDTOS;
        }
        // 对象转化
        return authenticationSubjectDTOList(passportFacade.queryByAccountId(customerAccountDTO.getId()));
    }

    /**
     * 类型转化
     */
    private List<BuyerDTO> authenticationSubjectDTOList(List<AuthenticationSubjectDTO> subjectDTOList) {
        if (CollectionUtils.isEmpty(subjectDTOList)) {
            return new ArrayList<>();
        }
        return subjectDTOList.stream().map(k -> {
            BuyerDTO buyerDTO = new BuyerDTO();
            buyerDTO.setName(k.getName());
            buyerDTO.setTaxId(k.getCertificateCode());
            return buyerDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public AgentDTO getInvoiceBelongInfo(Integer userId) {
        if (userId == null) {
            return null;
        }
        // 获取userId对应的渠道信息
        so.dian.center.common.entity.BizResult<AgentDTO> agentResult =
                agentEmployeeService.getAgentByUserId(Long.parseLong(userId.toString()));
        if (agentResult == null || agentResult.getData() == null) {
            return null;
        }
        // 设置渠道信息
        AgentDTO agentDTO = agentResult.getData();
        return agentDTO;
    }

    @Override
    public List<String> queryWithdrawSubjectNameList(String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return new ArrayList<>();
        }
        WithdrawListQuery query = new WithdrawListQuery();
        query.setSubjectNameKeyword(keyword);
        BizResult<List<String>> result = lvyClient.queryWithdrawSubjectNameList(query);
        if (!result.isSuccess()) {
            logger.error("获取发票主体对应信息异常，keyword：{}", keyword);
            return new ArrayList<>();
        }
        return result.getData();
    }

    @Override
    public void checkInvoiceBelong(InvoiceDO invoiceDO, Integer userId) {
        // 获取当前登录者的渠道信息
        AgentDTO agentDTO = getInvoiceBelongInfo(userId);
        if (agentDTO == null) {
            throw new BizException(LeoExcs.AGENT_NOT_EXISTS);
        }
        // 发票同属于小电时，验证通过
        if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(invoiceDO.getBelongSubjectId())) {
            return;
        }
        // 发票不属于小电，但所属代理商ID相同，验证通过
        if (agentDTO.getAgentId().compareTo(invoiceDO.getBelongSubjectId().longValue()) == 0) {
            return;
        }
        // 验证失败
        throw new BizException(LeoExcs.INVOICE_PERMISSION_ERROR);
    }

    @Override
    public List<InvoiceInfoVO> getInvoiceInfoList(InvoiceParam param) {
        List<InvoiceInfoVO> invoiceInfoList = new ArrayList<>();

        // 查询发票列表
        List<InvoiceDO> invoiceList = getInvoiceList(param);
        if (CollectionUtils.isEmpty(invoiceList)) {
            return invoiceInfoList;
        }

        // 构造返回对象
        InvoiceInfoVO invoiceInfo;
        for (InvoiceDO invoice : invoiceList) {
            // 初始化结果对象
            invoiceInfo = new InvoiceInfoVO();
            // 设置对象属性
            invoiceInfo.setInvoiceId(invoice.getId());
            invoiceInfo.setInvoiceSellerName(invoice.getSubjectName());
            invoiceInfo.setBusinessTypeCode(invoice.getSubjectType());
            invoiceInfo.setBusinessTypeName(BusinessTypeEnum.getField(invoice.getSubjectType()));
            invoiceInfo.setInvoiceCode(invoice.getInvoiceCode() == null ? "" : invoice.getInvoiceCode());
            invoiceInfo.setInvoiceNo(invoice.getInvoiceNo());
            invoiceInfo.setInvoiceDate(DateUtil.dateToYMD(invoice.getReceiveTime()));
            invoiceInfo.setTotalAmount(invoice.getPrice());
            invoiceInfo.setUnUseAmount(invoice.getPrice().subtract(invoice.getUsedAmount()));
            invoiceInfo.setInvoiceStatus(invoice.getStatus());
            invoiceInfo.setInvoiceStatusName(InvoiceStatusEnum.getField(invoice.getStatus()));
            invoiceInfo.setBatchNo(invoice.getBatchNo());
            // 对象加入列表
            invoiceInfoList.add(invoiceInfo);
        }

        return invoiceInfoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Boolean> invoiceReview(InvoiceReviewParam param) {
        logger.info("发票复核请求参数,param:{}", param);
        InvoiceValidator.checkInvoiceReviewParam(param);

        //查询当前登录人
        AgentEmployeeDTO agentEmployeeDTO = agentEmployeeService.getEmployeeById(param.getUserId());
        if (agentEmployeeDTO == null) {
            logger.error("当前登录用户异常,请稍后再试。userId：{}", param.getUserId());
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR);
        }

        InvoiceBO invoiceBO = invoiceManager.getInvoiceById(param.getId());
        if (Objects.isNull(invoiceBO)) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST);
        }
        if (Objects.equals(InvoiceProcessStatusEnum.ALREADY_ENTRY, invoiceBO.getInvoiceProcessStatusEnum())) {
            throw so.dian.commons.eden.exception.BizException
                    .create(InvoiceErrorCodeEnum.INVOICE_NOT_SEND_OUT_DO_NOT_REVIEW);
        }

        if (InvoiceProcessStatusEnum.REVIEW_STATUS.contains(invoiceBO.getInvoiceProcessStatusEnum().getCode())) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_ALREADY_REVIEW);
        }

        Boolean result = invoiceManager.invoiceReview(param.getId(), param.getUserId(), param.getSuggestion(),
                LocalEnumUtils.findByCodeWithoutDefault(InvoiceProcessStatusEnum.class, param.getStatus()),
                invoiceBO.getInvoiceProcessStatusEnum());
        if (!result) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_REVIEW_FAILED);
        }

        //操作日志
        invoiceOperateLogManager.insert(InvoiceOperateLogConverter.invoiceReviewLog(param, agentEmployeeDTO));

        return BizResult.create(true);
    }

    @Override
    public BizResult exportInvoiceList(InvoiceExportParam param) {
        InvoiceValidator.checkInvoiceExportParam(param);

        AgentEmployeeDTO employeeDTO = agentEmployeeService.getEmployeeById(param.getUserId());
        if (Objects.isNull(employeeDTO)) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceCommentErrorCodeEnum.USER_NOT_EXIST);
        }
        if (!DIAN_INVOICE_BELONG_ID.contains(employeeDTO.getAgentId()) && !employeeDTO.getAgentId().equals(param.getBelongSubjectId())) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceCommentErrorCodeEnum.USER_UNAUTHORIZED);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(employeeDTO.getEmail())) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceCommentErrorCodeEnum.USER_EMAIL_NOT_EXIST);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getExpressNo())) {
            List<Long> invoiceIdList = invoiceExpressesRelationDAO.listInvoiceIdsByExpressNo(param.getExpressNo());
            if (CollectionUtils.isNotEmpty(invoiceIdList)) {
                param.setInvoiceIdList(invoiceIdList);
            } else {
                return BizResult.create(null);
            }
        }
        Long totalCount = invoiceManager.countInvoiceExportList(param);

        if (totalCount > INVOICE_EXPORT_COUNT) {
            throw so.dian.commons.eden.exception.BizException.create(
                    InvoiceErrorCodeEnum.INVOICE_EXPORT_DATA_TO_BIG_PLEASE_FILTER_AND_TRY_AGAIN);
        }

        invoiceManager.asyncExportInvoiceExcel(param, totalCount, employeeDTO.getEmail());
        return BizResult.create(true);
    }


    @Override
    public BizResult<List<String>> listSubjectName(InvoiceSubjectInfoParam param) {
        if (LocalObjectUtils.allNull(param.getSubjectName(), param.getLoginUserId())) {
            throw so.dian.commons.eden.exception.BizException.create(
                    InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
        }

        AgentEmployeeDTO employeeDTO = agentEmployeeService.getEmployeeById(param.getLoginUserId());
        if (Objects.isNull(employeeDTO)) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceCommentErrorCodeEnum.USER_NOT_EXIST);
        }

        List<String> subjectNameList
                = invoiceManager.listSubjectNameListByParam(param.getSubjectName(), employeeDTO.getAgentId());
        return BizResult.create(subjectNameList);
    }

    @Override
    public BizResult<PageData<InvoiceVO>> pageInvoiceList(InvoiceFilterTimeParam param, CurrentUserReq currentUserReq) {
        InvoiceValidator.checkInvoiceCreateTimeParam(param);

        InvoiceFilterTimeQuery query = InvoiceConverter.buildParam2Query(param, currentUserReq);
        Long totalCount = invoiceDAO.countInvoice(query);
        if (totalCount <= 0L) {
            return BizResult.create(PageData.create(Lists.newArrayList(),
                    totalCount, param.getPageNo(), param.getPageSize()));
        }

        List<InvoiceBO> invoiceBOList = invoiceManager.listInvoice(query);
        if (CollectionUtils.isEmpty(invoiceBOList)) {
            return BizResult.create(PageData.create(Lists.newArrayList(),
                    totalCount, param.getPageNo(), param.getPageSize()));
        }

        return BizResult.create(PageData.create(InvoiceConverter.convertBO2VO(invoiceBOList),
                totalCount, param.getPageNo(), param.getPageSize()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Boolean> addSingleInvoice(AddSingleInvoiceParam param, InvoiceIdentifyRecordDO recordDO,
                                               Boolean comparedResult) {
        CurrentUserReq currentUserReq = param.getCurrentUserReq();
        // 购买方税号校验，小电集团的只能填写小电集团的，非小电集团的购买方税号只能填写非小电集团的
        boolean checkResult = invoiceValidateService.checkInvoiceBuyer(CheckInvoiceBuyerDTO.builder()
                .belongSubjectType(currentUserReq.getBelongSubjectType())
                .belongSubjectId(Long.valueOf(currentUserReq.getBelongSubjectId()))
                .buyer(param.getBuyer())
                .buyerTaxId(param.getBuyerTaxId()).build());
        if (!checkResult) {
            logger.error("addSingleInvoice checkInvoiceBuyer error, param:{}", param);
            throw so.dian.commons.eden.exception.BizException.create(
                    InvoiceErrorCodeEnum.NO_PERMISSION_BUYER);
        }

        //发票日期校验
       /* String checkBuyerResult =
                InvoiceBuyerValidator.validInvoiceBuyer(currentUserReq.getBelongSubjectType(), param.getBuyer(), param.getGmtCreate(), true);
        if (!StringUtils.isEmpty(checkBuyerResult)) {
            // 暂时这样写死
            if(checkBuyerResult.equals(InvoiceBuyerValidator.errorMsg)){
                checkBuyerResult = "购买方只能为‘杭州友电科技有限公司‘，’杭州伊电园网络科技有限公司‘，’北京伊电园网络科技有限公司‘，’杭州小电科技股份有限公司‘";
            }
            LeoExcs.PARAM_RESOLVER_FAIL.setMessage(checkBuyerResult);
            throw new BizException(LeoExcs.PARAM_RESOLVER_FAIL);
        }*/

        //发票重复校验
        InvoiceDO invoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(param.getInvoiceCode(), param.getInvoiceNo());
        if (Objects.nonNull(invoiceDO)) {
            throw so.dian.commons.eden.exception.BizException.create(
                    InvoiceErrorCodeEnum.MULTIPLE_UPLOADS_ARE_NOT_ALLOWED_ON_THE_SAME_INVOICE);
        }

        //新增发票and发票详情
        AgentDTO agentDTO = getInvoiceBelongInfo(param.getLoginUserId());
        if (agentDTO == null) {
            logger.error("当前登录者的渠道信息获取失败,userId：" + param.getLoginUserId());
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.ADD_INVOICE_ERROR);
        }
        String employeeNick = null;
        Integer cityCode = null;
        AgentEmployeeDTO agentEmployeeDTO = agentEmployeeService.getEmployeeById(param.getLoginUserId());
        if (Objects.nonNull(agentEmployeeDTO)) {
            employeeNick = agentEmployeeDTO.getNickName();
            cityCode = agentEmployeeDTO.getCityCode();
        }

        InvoiceDO invoice = InvoiceConverter.buildAddParam2DO(param, recordDO, agentDTO.getAgentId().intValue(),
                agentDTO.getType(), employeeNick, comparedResult);
        invoice.setCityCode(cityCode);
        invoiceDAO.insertSelective(invoice);

        List<InvoiceDetailDO> invoiceDetailDOList = assembleInvoiceDetail(recordDO);
        if (CollectionUtils.isNotEmpty(invoiceDetailDOList)) {
            invoiceDetailDAO.insertBatch(invoiceDetailDOList);
        }
        //更新发票关联状态
        recordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.YES.getCode());
        recordDAO.updateByPrimaryKeySelective(recordDO);
        //新增发票操作日志
        InvoiceOperateLogBO logBO = InvoiceOperateLogConverter.buildInvoiceInsertParam2BO(recordDO, invoice);
        invoiceOperateLogManager.insert(logBO);
        return BizResult.create(true);
    }

    @Override
    public int batchUpdateInvoiceProcessStatus(InvoiceProcessStatusEnum invoiceProcessStatusEnum,
                                               List<Long> invoiceIds) {
        if (Objects.isNull(invoiceProcessStatusEnum) || org.springframework.util.CollectionUtils.isEmpty(invoiceIds)) {
            return 0;
        }
        return invoiceDAO.batchUpdateInvoiceProcessStatus(invoiceIds, invoiceProcessStatusEnum.getCode());
    }

    @Override
    public List<InvoiceInfoVO> findByInvoiceDeductQueryParam(InvoiceDeductQueryParam param, Long agentId) {
        if (Objects.isNull(param)) {
            return Lists.newArrayList();
        }
        List<InvoiceDO> list = invoiceDAO.findByInvoiceDeductQueryParam(param);
        return InvoiceConverter.convertDO2VOList(list, agentId);
    }

    @Override
    public BizResult<Integer> trimInvoice(Long invoiceId, Integer bizType, Integer lastId) {
        int count = 0;
        if (Objects.nonNull(invoiceId)) {
            InvoiceDO invoiceDO = invoiceDAO.selectByPrimaryKey(invoiceId.intValue());
            if (Objects.isNull(invoiceDO)) {
                return BizResult.error(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST);
            }
            logger.info("批量修正发票主体. invoiceDO:{}", invoiceDO);
            invoiceDO.setBuyer(StringUtil.trimFull2Half(invoiceDO.getBuyer()));
            invoiceDO.setSubjectName(StringUtil.trimFull2Half(invoiceDO.getSubjectName()));
            invoiceDO.setSeller(StringUtil.trimFull2Half(invoiceDO.getSeller()));
            count = invoiceDAO.trimInvoice(invoiceDO);
        } else {
            if (Objects.isNull(bizType)) {
                return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
            }
            while (true) {
                List<InvoiceDO> list = invoiceDAO.findBySubjectType(bizType, lastId.longValue());
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                for (InvoiceDO invoiceDO : list) {
                    logger.info("批量修正发票主体. invoiceDO:{}", invoiceDO);
                    lastId = invoiceDO.getId();
                    invoiceDO.setBuyer(StringUtil.trimFull2Half(invoiceDO.getBuyer()));
                    invoiceDO.setSubjectName(StringUtil.trimFull2Half(invoiceDO.getSubjectName()));
                    invoiceDO.setSeller(StringUtil.trimFull2Half(invoiceDO.getSeller()));
                    int i = invoiceDAO.trimInvoice(invoiceDO);
                    count = count + i;
                }
                if (list.size() < 1000) {
                    break;
                }
            }
        }

        return BizResult.create(count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceValidate(InvoiceBO validateInvoiceBO, InvoiceValidateStatusDO statusDOForInsert) {
        invoiceManager.updateValidationById(validateInvoiceBO.getId(), validateInvoiceBO.getIsReal(), validateInvoiceBO.getValidateCode());
        invoiceValidateStatusManager.insert(statusDOForInsert);
    }

    @Override
    public void processValidateData(Date startTime, Date endTime) {
        logger.info("历史数据处理：startTime：{}，endTime：{}", startTime, endTime);
        // 只针对 11.01 开始的
        // 已验真发票，设置为10000
        List<InvoiceDO> invoiceDoListsSuc = invoiceDAO.selectValidateInvoice(startTime, endTime, InvoiceIdentifyRecordEnum.IsRealEnum.SUC);
        if (CollectionUtils.isNotEmpty(invoiceDoListsSuc)) {
            logger.info("已验真发票数量：{}", invoiceDoListsSuc.size());
            for (InvoiceDO invoiceDO : invoiceDoListsSuc) {
                invoiceDO.setValidateCode(InvoiceIdentifyRecordEnum.ValidationCodeEnum.SUCCESS.getCode());
                invoiceDAO.updateValidationById(invoiceDO.getId(), invoiceDO.getIsReal(), invoiceDO.getValidateCode());
                logger.info("===验真成功发票，初始化成功，invoiceId:{}, isReal:{}, invoiceNo:{}, invoiceCode:{}, date:{}", invoiceDO.getId(),
                        invoiceDO.getIsReal(), invoiceDO.getInvoiceNo(), invoiceDO.getInvoiceCode(),
                        cn.hutool.core.date.DateUtil.format(invoiceDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        // 验真失败发票，取最新的记录，然后设置给发票
        List<InvoiceDO> invoiceDoListsFail = invoiceDAO.selectValidateInvoice(startTime, endTime, InvoiceIdentifyRecordEnum.IsRealEnum.FAIL);
        if (CollectionUtils.isNotEmpty(invoiceDoListsFail)) {
            logger.info("验真失败发票数量：{}", invoiceDoListsFail.size());
            // 获取最新记录
            List<InvoiceValidateStatusDO> statusDOS = invoiceValidateStatusManager.getAllValidateInvoice();
            Map<Long, InvoiceValidateStatusDO> statusDOMap =
                    statusDOS.stream().collect(Collectors.toMap(InvoiceValidateStatusDO::getInvoiceId,
                            statusDO -> statusDO));
            int count = 0;
            for (InvoiceDO invoiceDO : invoiceDoListsFail) {
                Long invoiceId = Convert.toLong(invoiceDO.getId());
                InvoiceValidateStatusDO statusDO = statusDOMap.get(invoiceId);
                if (statusDO == null) {
                    logger.info("---验真失败发票，初始化没有数据，invoiceId:{}, isReal:{}, invoiceNo:{}, invoiceCode:{}, date:{}", invoiceDO.getId(),
                            invoiceDO.getIsReal(), invoiceDO.getInvoiceNo(), invoiceDO.getInvoiceCode(),
                            cn.hutool.core.date.DateUtil.format(invoiceDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    continue;
                }
                invoiceDO.setValidateCode(statusDO.getValidateCode());
                invoiceDAO.updateValidationById(invoiceDO.getId(), invoiceDO.getIsReal(), invoiceDO.getValidateCode());
                logger.info(">>>验真失败发票，初始化成功，invoiceId:{}, isReal:{}, invoiceNo:{}, invoiceCode:{}, date:{}", invoiceDO.getId(),
                        invoiceDO.getIsReal(), invoiceDO.getInvoiceNo(), invoiceDO.getInvoiceCode(),
                        cn.hutool.core.date.DateUtil.format(invoiceDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                count++;
            }
            logger.info("验真失败成功修改发票数量：{}", count);
        }

    }

    @Override
    public List<Long> listInvoiceIdsByExpressNo(String expressNo) {
        List<Long> invoiceIds = invoiceExpressesRelationDAO.listInvoiceIdsByExpressNo(expressNo);
        return invoiceIds;
    }


    @Override
    public void fillBusinessNo(InvoiceDto dto) {

        if (dto.getSubjectType().equals(SubjectTypeEnum.SCM.getType())) {
            return;
        }

        List<String> businessNoList =
                getBusinessNoListByInvoiceCodeAndNo(dto.getInvoiceCode(), dto.getInvoiceNo(), null, 2);
        List<String> billNoList =
                getBusinessNoListByInvoiceCodeAndNo(dto.getInvoiceCode(), dto.getInvoiceNo(), businessNoList, 1);

        StringBuilder sb = new StringBuilder();
        if (billNoList != null && billNoList.size() > 0) {
            for (int i = 0; i < billNoList.size(); i++) {
                sb.append(billNoList.get(i));
                if (i < billNoList.size() - 1) {
                    sb.append(",");
                }
            }
        }

        dto.setBillNo(sb.toString());
    }

    @Override
    public List<String> getBusinessNoListByInvoiceCodeAndNo(String invoiceCode, String invoiceNo,
                                                            List<String> businessNoList, Integer operateType) {

        return invoiceDeductionDAO.selectByInvoiceCodeAndNo(invoiceCode, invoiceNo, businessNoList, operateType);
    }

    @Override
    public List<KeyValueDto> getSubjectTypeList(UserRoleEnum roleEnum, Integer userId, SubjectTypeParam subjectTypeParam) {
        List<KeyValueDto> list = new ArrayList<>();

        // 获取当前登录者的主体信息
        AgentDTO agentDO = agentEmployeeService.getAgentDtoByUserId(userId);
        if (agentDO == null) {
            return list;
        }

        // 代理商（非小电）、运营型服务商，只显示 商家分成
        if ((AgentTypeEnum.AGENT_TYPE.getId().equals(agentDO.getType())
                && !Integer.valueOf(1).equals(agentDO.getAgentId().intValue()))
                || AgentTypeEnum.OP_AGENT_TYPE.getId().equals(agentDO.getType())) {
            KeyValueDto dto = new KeyValueDto();
            dto.setKey(SubjectTypeEnum.DEVIDE.getType());
            dto.setValue(SubjectTypeEnum.DEVIDE.getField());
            list.add(dto);
            return list;
        }

        if (UserRoleEnum.PURCHASE_MANAGER.equals(roleEnum)
                || UserRoleEnum.PO_OPERATOR.equals(roleEnum)
                || UserRoleEnum.SUPPLIER_MANAGER.equals(roleEnum)
                || UserRoleEnum.FACTORY_WAREHOUSE_MANAGER.equals(roleEnum)) {
            KeyValueDto dto = new KeyValueDto();
            dto.setKey(SubjectTypeEnum.SCM.getType());
            dto.setValue(SubjectTypeEnum.SCM.getField());
            list.add(dto);
            return list;
        }

        for (SubjectTypeEnum subjectType : SubjectTypeEnum.values()) {

            if (subjectTypeParam != null && subjectTypeParam.getScmFilter() != null && subjectTypeParam.getScmFilter()
                    && SubjectTypeEnum.SCM.equals(subjectType)) {
                continue;
            }
            /**
             * @since 20200409-liangfang-addInvoiceBizType
             * <AUTHOR>
             * 只有财务经理才能看到设备采购发票业务类型
             */
            if (subjectType.getType() == SubjectTypeEnum.EQUIPMENT_PROCUREMENT.getType() || subjectType.getType() == SubjectTypeEnum.FEE.getType()) {
                if (UserRoleEnum.AFFAIRS_MANAGER.equals(roleEnum) || UserRoleEnum.JOINTAFFAIRSMANAGER.equals(roleEnum)) {
                    KeyValueDto dto = new KeyValueDto();
                    dto.setKey(subjectType.getType());
                    dto.setValue(subjectType.getField());
                    list.add(dto);
                }

            } else {
                KeyValueDto dto = new KeyValueDto();
                dto.setKey(subjectType.getType());
                dto.setValue(subjectType.getField());
                list.add(dto);
            }

        }

        return list;
    }

    @Override
    public InvoiceDO selectInvoiceByInvoiceCodeAndNo(String invoiceCode, String invoiceNo) {
        if(LocalObjectUtils.anyNull(invoiceCode,invoiceNo)){
            return null;
        }
        return invoiceDAO.selectInvoiceByInvoiceCodeAndNo(invoiceCode,invoiceNo);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void saveInvoiceAndDetail(InvoiceDO invoiceDO, List<InvoiceDetailDO> invoiceDetailDOList) {
        if(Objects.isNull(invoiceDO)|| CollectionUtils.isEmpty(invoiceDetailDOList)){
            return;
        }
        Assert.assertEquals(invoiceDAO.insertSelective(invoiceDO),1,"发票主体保存失败");
        Assert.assertEquals(insertInvoiceDetailBatch(invoiceDetailDOList),invoiceDetailDOList.size(),"发票明细保存失败");
    }


}
