package so.dian.invoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.client.SupplierClient;
import so.dian.invoice.pojo.dto.SupplierDTO;
import so.dian.invoice.service.SupplierService;
import so.dian.jinyun.client.pojo.request.scm.SupplierBaseListReq;
import so.dian.jinyun.client.pojo.response.scm.PurchaseOrderDetailRsp;
import so.dian.jinyun.client.pojo.response.scm.SCMSupplierRsp;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 供应商相关接口
 *
 * @Author: chenan
 * @Date: 2020/9/21 16:54
 */
@Service
@Slf4j
public class SupplierServiceImpl implements SupplierService {

    @Resource
    private SupplierClient supplierClient;

    @Override
    public List<SupplierDTO> listSupplierByNoList(List<String> supplierNoList) {
        SupplierBaseListReq supplierBaseListReq = new SupplierBaseListReq();
        supplierBaseListReq.setSupplierNoFilterList(supplierNoList);
        BizResult<List<SCMSupplierRsp>> supplierListBiz = supplierClient.getSupplierList(supplierBaseListReq);

        if (!supplierListBiz.isSuccess() || CollectionUtils.isEmpty(supplierListBiz.getData())) {
            return null;
        }
        log.info("------ 供应商批量查询，supplierNoList = {}, resultSize = {}",
                JSON.toJSONString(supplierBaseListReq), supplierListBiz.getData().size());

        List<SupplierDTO> supplierDTOList = parseDTOList(supplierListBiz.getData());

        return supplierDTOList;
    }


    private List<SupplierDTO> parseDTOList(List<SCMSupplierRsp> scmSupplierRsps) {
        List<SupplierDTO> supplierDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(scmSupplierRsps)) {
            return supplierDTOList;
        }

        for (SCMSupplierRsp supplierRsp : scmSupplierRsps) {
            SupplierDTO supplierDTO = new SupplierDTO();
            BeanUtils.copyProperties(supplierRsp, supplierDTO);
            List<String> addressList = JSON.parseArray(supplierRsp.getAddressList(), String.class);
            supplierDTO.setAddressList(addressList);
            supplierDTOList.add(supplierDTO);
        }

        return supplierDTOList;
    }


    private SupplierDTO parseDTO(SCMSupplierRsp supplierRsp) {
        if (null == supplierRsp) {
            return null;
        }

        SupplierDTO supplierDTO = new SupplierDTO();
        BeanUtils.copyProperties(supplierRsp, supplierDTO);
        List<String> addressList = JSON.parseArray(supplierRsp.getAddressList(), String.class);
        supplierDTO.setAddressList(addressList);

        return supplierDTO;
    }

    @Override
    public SupplierDTO getBySupplierNo(String supplierNo) {
        BizResult<SCMSupplierRsp> supplierRspBizResult = supplierClient.getSupplierBySupplierNo(supplierNo);
        log.info("------ 供应商查询，supplierNo = {}, result = {}", supplierNo, JSON.toJSONString(supplierRspBizResult));
        if (!supplierRspBizResult.isSuccess() || null == supplierRspBizResult.getData()) {
            return null;
        }

        return parseDTO(supplierRspBizResult.getData());
    }

    @Override
    public String getSupplierNameBySupplierNo(String supplierNo) {
        SupplierDTO supplierDTO = getBySupplierNo(supplierNo);
        if (null == supplierDTO) {
            return null;
        }
        return supplierDTO.getName();
    }

    @Override
    public List<SupplierDTO> getByMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return null;
        }

        BizResult<List<SCMSupplierRsp>> supplierListBiz = supplierClient.getSupplierByMobile(mobile);

        log.info("------ 供应商查询，mobile = {}, result = {}", mobile, JSON.toJSONString(supplierListBiz));

        if (!supplierListBiz.isSuccess() || CollectionUtils.isEmpty(supplierListBiz.getData())) {
            return null;
        }
        List<SupplierDTO> supplierDTOList = parseDTOList(supplierListBiz.getData());
        return supplierDTOList;
    }

    @Override
    public PurchaseOrderDetailRsp getPurchaseOrderByPurchaseNo(String purchaseNo) {
        if (StringUtils.isBlank(purchaseNo)) {
            return null;
        }
        BizResult<PurchaseOrderDetailRsp> bizResult = null;
        try {
            bizResult = supplierClient.getPurchaseOrderByPurchaseNo(purchaseNo);
            if (!bizResult.isSuccess() || Objects.isNull(bizResult.getData())) {
                return null;
            }else {
                return bizResult.getData();
            }
        } catch (Exception e) {
            log.info("供应链系统getPurchaseOrderByPurchaseNo调用失败",e);
        }
        return null;
    }

    /**
     * 一个采购单操作人只有一个供应商
     * @param mobile 采购单操作人的电话
     * @return
     */
    @Override
    public Set<String> getSupplierNosByMobile(String mobile) {
        List<SupplierDTO> supplierDTOList = getByMobile(mobile);
        if (CollectionUtils.isEmpty(supplierDTOList)) {
            return new HashSet<>();
        }
        Set<String> supplierNos = supplierDTOList.stream().map(SupplierDTO::getSupplierNo).collect(Collectors.toSet());
        return supplierNos;
    }

    @Override
    public String getSupplierNosById(Long supplierId) {
        if (Objects.isNull(supplierId))
            return null;
        BizResult<SCMSupplierRsp> bizResult = supplierClient.getByPlatformSupplierId(supplierId);
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            return null;
        }
        SCMSupplierRsp supplierRsp = bizResult.getData();
        return Optional.ofNullable(supplierRsp).map(SCMSupplierRsp::getSupplierNo).orElse(null);
    }

}
