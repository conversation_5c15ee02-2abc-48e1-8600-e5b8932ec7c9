package so.dian.invoice.service.impl;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import so.dian.invoice.service.AsyncMessageService;
import so.dian.invoice.service.message.contain.MessageHandleContain;
import so.dian.invoice.service.message.context.MessageContext;

import javax.annotation.Resource;

@Service
public class AsyncMessageServiceImpl implements AsyncMessageService {

    @Resource
    private MessageHandleContain messageHandleContain;

    @Async("mqMessageTaskExecutor")
    @Override
    public void handleMessageAsync(MessageContext context) {
        // 业务逻辑处理
        messageHandleContain.execute(context);
    }
}
