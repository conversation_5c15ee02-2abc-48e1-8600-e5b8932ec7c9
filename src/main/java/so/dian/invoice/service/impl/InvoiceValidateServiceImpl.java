package so.dian.invoice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.map.SingletonMap;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.invoice.client.GlorityClient;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceIdentifyRecordDAO;
import so.dian.invoice.enums.*;
import so.dian.invoice.pojo.dto.BuyerDTO;
import so.dian.invoice.pojo.dto.CheckInvoiceBuyerDTO;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.manager.DingTalkManager;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.dto.OCRQueueDTO;
import so.dian.invoice.pojo.dto.identify.*;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.param.*;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.ImageService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.service.InvoiceValidateService;
import so.dian.invoice.util.*;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.invoice.volidator.InvoiceValidator;

@Slf4j
@Service
public class InvoiceValidateServiceImpl implements InvoiceValidateService {

    @Resource
    private InvoiceIdentifyRecordDAO recordDAO;
    @Resource
    private ImageService imageService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private InvoiceDAO invoiceDAO;
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private InvoiceManager invoiceManager;
    @Resource
    private GlorityClient glorityClient;
    @Resource
    private Validator validator;
    @Value("${glority.appKey}")
    private String appKey;
    @Value("${glority.appSecret}")
    private String appSecret;
    private final Integer retryCount = 3;
    // private static final String appKey = "5c7ce1b9"; //这里输入提供的app_key
    // private static final String appSecret = "3f0734c737bc255bdda7b1c5a681bef4"; //这里输入提供的app_secret
    private static final String host = "http://fapiao.glority.cn/v1/item/get_item_info";
    private static final String host_validate = "http://fapiao.glority.cn/v1/item/get_item_info_with_validation";
    private static final String host_validate_single = "http://fapiao.glority.cn/v1/item/fapiao_validation";

    @Resource
    private RedissonClient redisson;
    @Resource
    private DingTalkManager dingTalkManager;

    private RRateLimiter invoiceIdentifyRateLimiter;

    // 线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10); // 根据需求调整线程数


    @Override
    public ServiceResult<List<Pair<String,String>>> batchUpload(Integer loginUserId, InvoiceUploadParams params) {
        List<Pair<String,String>> list = new ArrayList<>();
        try {
            List<Future<Pair<String,String>>> futures = new ArrayList<>();
            for (String imageUrl : params.getImageUrls()) {
                String objectName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1); // 从URL中提取文件名
                log.info("开始批量下载和上传发票：{}", imageUrl);
                Callable<Pair<String,String>> task = () -> downloadAndUpload(loginUserId, imageUrl, objectName);
                Future<Pair<String,String>> future = executorService.submit(task);
                futures.add(future);
            }

            // 获取所有任务的结果
            for (Future<Pair<String,String>> future : futures) {
                try {
                    Pair<String,String> result = future.get();
                    list.add(result);
                    log.info("发票上传结果：{}", JSON.toJSONString(result));
                } catch (InterruptedException | ExecutionException e) {
                    log.error("获取发票上传结果异常", e);
                    return ServiceResult.failResult("发票上传失败");
                }
            }
        }
        finally {
//             关闭线程池
//           executorService.shutdown();
        }
        return ServiceResult.successResult(list);
    }


    private Pair<String,String> downloadAndUpload(Integer loginUserId, String imageUrl, String objectName) {
        Pair<String,String> result = MutablePair.of(imageUrl,null);
        try {
            String fileName = objectName.toLowerCase();
            String contentType;
            if (fileName.endsWith("gif")) {
                contentType = "gif";
            } else if (fileName.endsWith("pdf")) {
                contentType = "pdf";
            } else {
                contentType = "jpg";
            }
            // 打开URL连接并获取输入流
            byte[] bytes = ImageUtils.downloadImageToByte(imageUrl);
            ServiceResult<String> serviceResult = this.batchUpload(loginUserId, objectName, bytes, contentType, bytes.length);
            log.info("发票上传成功，url:{}", JSON.toJSONString(serviceResult));
            result.setValue(JSON.toJSONString(serviceResult));
        } catch (Exception e) {
            log.error("发票上传异常，url:{}", imageUrl, e);
            result.setValue("发票上传失败");
        }
        return result;
    }

    /**
     * 批量上传发票图片
     */
    @Override
    public ServiceResult<String> batchUpload(Integer loginUserId, String clientFileName, byte[] bytes,
                                             String contentType, int length) {
        ServiceResult<String> serviceResult = new ServiceResult<>();
        String imageUrl = null;
        try {
            if ("pdf".equals(contentType)) {
                imageUrl = ImageUtils.IMAGE_DOMAIN_WITH_PROTOCOL_QCLOUD +
                        imageService.uploadFileQCloud(ImageService.LHC, clientFileName, bytes, contentType, length);
            } else {
                //上传发票
                imageUrl = ImageUtils.IMAGE_DOMAIN_WITH_PROTOCOL_QCLOUD + imageService
                        .uploadImageQCloud(ImageService.LHC, clientFileName, bytes, contentType, length);
            }
            // url放入redis队列，定时异步处理
            OCRQueueDTO ocrQueueDTO = new OCRQueueDTO();
            ocrQueueDTO.setImageUrl(imageUrl);
            ocrQueueDTO.setLoginUserId(loginUserId);
            ocrQueueDTO.setTimes(0);
            RQueue<OCRQueueDTO> queue = redisson.getQueue(InvoiceConstants.INVOICE_OCR_QUEUE);
            queue.add(ocrQueueDTO);
            log.info("发票上传成功，url:{}", imageUrl);
        } catch (Exception e) {
            log.error("发票上传异常，url:{}", imageUrl, e);
            return serviceResult.fail("发票上传失败");
        }
        return serviceResult.success("上传完成，ocr识别中，请刷新页面查看");
    }

    @Autowired
    private AgentEmployeeService agentEmployeeService;

    /**
     * 异步OCR识别发票信息
     */
    @Override
    public boolean handleOCR(OCRQueueDTO ocrQueueDTO) {

        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(ocrQueueDTO.getLoginUserId());

        //获取发票识别信息
        try {
            InvoiceResultDTO result = getInvoiceInfoAndValidate(ocrQueueDTO.getImageUrl(), false);
            StringBuilder s = new StringBuilder();

            // OCR识别失败
            if (InvoiceIdentifyRecordEnum.InvoiceResultEnum.FAIL.getCode().equals(result.getResult())) {
                s.append("发票识别失败，url:").append(ocrQueueDTO.getImageUrl()).append(";");
                log.warn("发票识别失败，url:{}", ocrQueueDTO.getImageUrl());
                insertFailRecord(result.getMessage(), ocrQueueDTO.getLoginUserId(), ocrQueueDTO.getImageUrl());
                dingTalkManager.sendWarnMsgAsync("OCR识别失败", JSON.toJSONString(ocrQueueDTO) + "|" +
                        result.getError() + result.getMessage());
                return false;
            }

            // OCR识别成功
            // 插入识别记录
            InvoiceDataDTO data = result.getResponse().getData();

            List<DetailAndExtraDTO> identifyResults = data.getIdentify_results();
            for (DetailAndExtraDTO detailAndExtraDTO : identifyResults) {
                if (InvoiceConstants.typeList.contains(detailAndExtraDTO.getType())) {
                    DetailsDTO detailsDTO = detailAndExtraDTO.getDetails();
                    InvoiceIdentifyRecordDO recordDO = new InvoiceIdentifyRecordDO();
                    recordDO.setInvoiceNo(detailsDTO.getNumber());
                    recordDO.setInvoiceCode(detailsDTO.getCode() == null ? "" : detailsDTO.getCode());
                    recordDO.setInvoiceType(InvoiceTypeEnum.getTypeByBizType(detailAndExtraDTO.getType()));
                    recordDO.setInvoiceDate(DateUtil.stringToString(detailsDTO.getDate()));
                    recordDO.setCheckCode(detailsDTO.getCheck_code() == null ? "" : detailsDTO.getCheck_code());
                    recordDO.setPretaxAmount(detailsDTO.getPretax_amount());
                    recordDO.setTotal(detailsDTO.getTotal());
                    recordDO.setTax(detailsDTO.getTax());
                    recordDO.setSeller(detailsDTO.getSeller());
                    recordDO.setSellerTaxId(detailsDTO.getSeller_tax_id());
                    recordDO.setBuyer(detailsDTO.getBuyer());
                    String buyerTaxId = BuyerTaxIdEnum.getTaxIdByBuyer(detailsDTO.getBuyer());
                    if(StringUtils.isBlank(buyerTaxId)){
                        buyerTaxId=detailsDTO.getBuyer_tax_id();
                    }
                    recordDO.setBuyerTaxId(buyerTaxId);
                    recordDO.setKind(detailsDTO.getKind());
                    recordDO.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.STAY.getCode());
                    recordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.NO.getCode());
                    recordDO.setUrl(ocrQueueDTO.getImageUrl());
                    recordDO.setDeleted(InvoiceIdentifyRecordEnum.DeletedEnum.UN_DELETED.getCode());
                    recordDO.setCreator(ocrQueueDTO.getLoginUserId());
                    recordDO.setUpdater(ocrQueueDTO.getLoginUserId());
                    recordDO.setDetails(detailsDTO.getItem_names());
                    recordDO.setResult(JSON.toJSONString(detailAndExtraDTO));
                    recordDO.setIsIdentify(InvoiceIdentifyRecordEnum.IsIdentifyEnum.SUC.getCode());
                    // 跟小电抬头以及税号做校验，校验失败时，记录错误原因

                    if (!InvoiceBuyerValidator.checkInvoiceBuyerAndTaxId(currentUserReq.getBelongSubjectType(),detailsDTO.getBuyer(), detailsDTO.getBuyer_tax_id())) {
                        // 抬头或税号信息校验失败
                        recordDO.setReason("抬头或税号信息校验失败");
                    }
                    recordDAO.insertSelective(recordDO);
                    continue;
                }
                String identifyTypeDesc = InvoiceIdentifyRecordEnum.InvoiceIdentifyTypeEnum
                        .getDescByCode(detailAndExtraDTO.getType());
                log.warn("发票类型：{},当前不支持 url:{}", detailAndExtraDTO.getType(), ocrQueueDTO.getImageUrl());
                String type = StringUtil.isBlank(identifyTypeDesc) ? detailAndExtraDTO.getType() : identifyTypeDesc;
                s.append("发票类型：").append(type).append("当前不支持;");
                insertFailRecord(s.toString(), ocrQueueDTO.getLoginUserId(), ocrQueueDTO.getImageUrl());
            }
        } catch (Exception e) {
            log.error("发票OCR识别异常。loginUserId = {}, imageUrl = {}",
                    ocrQueueDTO.getLoginUserId(), ocrQueueDTO.getImageUrl(), e);
            return false;
        }
        return true;
    }

    @Override
    public boolean checkInvoiceBuyer(CheckInvoiceBuyerDTO checkInvoiceBuyerDTO) {
        log.info("checkInvoiceBuyer get checkInvoiceBuyerDTO:{}", JSON.toJSON(checkInvoiceBuyerDTO));
        if (Objects.isNull(checkInvoiceBuyerDTO) || StringUtils.isBlank(checkInvoiceBuyerDTO.getBuyer())
                || Objects.isNull(checkInvoiceBuyerDTO.getBelongSubjectId()) || Objects.isNull(checkInvoiceBuyerDTO.getBelongSubjectType())) {
            log.error("checkInvoiceBuyerDTO is failed  checkInvoiceBuyerDTO:{}", checkInvoiceBuyerDTO);
            return false;
        }

        // 获取购买方列表
        List<BuyerDTO> buyerDTOS = invoiceService.getBuyerList(BuyerParam.builder()
                .belongSubjectType(checkInvoiceBuyerDTO.getBelongSubjectType())
                .belongSubjectId(checkInvoiceBuyerDTO.getBelongSubjectId()).build());
        if (CollectionUtils.isEmpty(buyerDTOS)) {
            log.error(" buyerDTOS is null  get  checkInvoiceBuyerDTO:{}", JSON.toJSON(checkInvoiceBuyerDTO));
            return false;
        }
        // 获取税号列表
        List<String> buyers = buyerDTOS.stream().map(BuyerDTO::getName).collect(Collectors.toList());
        // 判断税号是否包含
        return buyers.contains(checkInvoiceBuyerDTO.getBuyer());
    }

    private void insertFailRecord(String message, Integer loginUserId, String imageUrl) {
        InvoiceIdentifyRecordDO recordDO = new InvoiceIdentifyRecordDO();
        recordDO.setIsIdentify(InvoiceIdentifyRecordEnum.IsIdentifyEnum.FAIL.getCode());
        recordDO.setReason(message);
        recordDO.setCreator(loginUserId);
        recordDO.setUpdater(loginUserId);
        recordDO.setDeleted(InvoiceIdentifyRecordEnum.DeletedEnum.UN_DELETED.getCode());
        recordDO.setUrl(imageUrl);
        recordDO.setInvoiceType(InvoiceTypeEnum.OTHER.getType());
        recordDAO.insertSelective(recordDO);
    }

    /**
     * 获取发票识别信息（商家端上传发票）
     */
    @Override
    public BizResult<InvoiceIdentifyRecordDTO> getInvoiceIdentifyDetail(String imageUrl) {
        try {
            //获取发票识别信息
            InvoiceResultDTO result = getInvoiceInfoAndValidate(imageUrl, false);
            if (Objects.equals(InvoiceIdentifyRecordEnum.InvoiceResultEnum.FAIL.getCode(), result.getResult())) {
                log.error("发票识别失败，url:{}", imageUrl);
                throw BizException.create(InvoiceErrorCodeEnum.INVOICE_CANNOT_BE_IDENTIFIED_PLEASE_ENTER_BY_HAND);
            }

            InvoiceDataDTO data = result.getResponse().getData();
            List<DetailAndExtraDTO> identifyResults = data.getIdentify_results();
            DetailAndExtraDTO detailAndExtraDTO = identifyResults.get(0);
            if (InvoiceConstants.typeList.contains(detailAndExtraDTO.getType())) {
                DetailsDTO detailsDTO = detailAndExtraDTO.getDetails();
                InvoiceIdentifyRecordDTO recordDO = new InvoiceIdentifyRecordDTO();
                recordDO.setInvoiceNo(detailsDTO.getNumber());
                recordDO.setInvoiceCode(detailsDTO.getCode());
                recordDO.setInvoiceDate(DateUtil.stringToString(detailsDTO.getDate()));
                if (StringUtils.isNotBlank(detailsDTO.getDate())) {
                    recordDO.setGmtCreate(cn.hutool.core.date.DateUtil.parse(detailsDTO.getDate()).getTime());
                }
                recordDO.setTotal(detailsDTO.getTotal());
                recordDO.setSeller(detailsDTO.getSeller());
                recordDO.setSellerTaxId(detailsDTO.getSeller_tax_id());
                recordDO.setBuyer(detailsDTO.getBuyer());

                String buyerTaxId = BuyerTaxIdEnum.getTaxIdByBuyer(detailsDTO.getBuyer());
                if(StringUtils.isBlank(buyerTaxId)){
                    buyerTaxId=detailsDTO.getBuyer_tax_id();
                }
                recordDO.setBuyerTaxId(buyerTaxId);

                recordDO.setCheckCode(detailsDTO.getCheck_code());
                Integer invoiceType = InvoiceTypeEnum.getTypeByBizType(detailAndExtraDTO.getType());
                recordDO.setInvoiceType(invoiceType);
                if (Objects.nonNull(invoiceType)) {
                    recordDO.setInvoiceTypeStr(InvoiceTypeEnum.getField(invoiceType));
                }
                recordDO.setPretaxAmount(detailsDTO.getPretax_amount());
                recordDO.setTax(detailsDTO.getTax());
                recordDO.setDetails(detailsDTO.getItem_names());
                recordDO.setKind(detailsDTO.getKind());
                recordDO.setResult(JSON.toJSONString(detailAndExtraDTO));
                return BizResult.create(recordDO);
            }
            log.error("发票类型当前不满足，类型：{}", detailAndExtraDTO.getType());
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_CANNOT_BE_IDENTIFIED_PLEASE_ENTER_BY_HAND);
        } catch (IOException e) {
            log.error("发票识别异常，url:{}", imageUrl, e);
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "发票识别失败，url:{}" + imageUrl);
        }
    }

    public synchronized RRateLimiter getInvoiceIdentifyRateLimiter() {
        synchronized (this) {
            if (this.invoiceIdentifyRateLimiter == null) {
                invoiceIdentifyRateLimiter = redisson.getRateLimiter(CacheEnum.INVOICE_IDENTIFY_RATE_LIMITER.getKey());
                invoiceIdentifyRateLimiter.trySetRate(RateType.OVERALL, 1, 1, RateIntervalUnit.SECONDS);
            }
        }
        return invoiceIdentifyRateLimiter;
    }

    /**
     * 获取发票识别信息
     *
     * @throws IOException
     */
    private InvoiceResultDTO getInvoiceInfoAndValidate(String imageUrl, boolean validate) throws IOException {

        OkHttpClient client = new OkHttpClient();
        long timestamp = System.currentTimeMillis() / 1000;
        String token = new Md5Hash(appKey + "+" + timestamp + "+" + appSecret).toString();
        String url = validate ? host_validate : host;
        Request request = new Request.Builder()
                .url(url)
                .post(new FormBody.Builder()
                        .add("app_key", appKey)
                        .add("timestamp", String.valueOf(timestamp))
                        .add("token", token)
                        .add("image_url", imageUrl)
                        .build())
                .build();
        Response response = client.newCall(request).execute();
        String resultBody = response.body().string();

        InvoiceResultDTO invoiceResultDTO = JSON.parseObject(resultBody, new TypeReference<InvoiceResultDTO>() {
        });
        log.info("发票识别信息url:{} 返回结果:{}", imageUrl, JSON.toJSONString(invoiceResultDTO));

        return invoiceResultDTO;
    }

    /**
     * 获取发票识别验真信息
     *
     * @throws IOException
     */
    @Override
    public ValidationDTO handleInvoiceValidate(InvoiceValidateDetailParams params) throws IOException {
        params.setInvoiceDate(DateUtil.stringReversalToString(params.getInvoiceDate()));
        params.setInvoiceTypeStr(InvoiceTypeEnum.getBizTypeByType(params.getInvoiceType()));
        //校验参数
        if (validateParam(params)) {
            return null;
        }
        OkHttpClient client = new OkHttpClient();
        long timestamp = System.currentTimeMillis() / 1000;
        String token = new Md5Hash(appKey + "+" + timestamp + "+" + appSecret).toString();
        String url = host_validate_single;
        if (StringUtil.isNotBlank(params.getCheckCode()) && params.getCheckCode().length() >= 6) {
            params.setCheckCode(params.getCheckCode().substring(params.getCheckCode().length() - 6));
        }
        FormBody formBody = getFormBody(params.getInvoiceCode(), params.getInvoiceNo(), params.getCheckCode(),
                params.getInvoiceDate(), params.getPretaxAmount(),params.getTotal(), params.getInvoiceTypeStr(), timestamp, token);
        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();
        Response response = client.newCall(request).execute();
        String resultBody = response.body().string();
        log.error("发票验真信息参数:{},返回结果:{}", params, resultBody);
        InvoiceResultDTO invoiceResultDTO = JSON.parseObject(resultBody, new TypeReference<InvoiceResultDTO>() {
        });
        // 构建 InvoiceValidateStatusDO 对象
        // Date now = new Date();
        // String validateResult = JSON.toJSONString(invoiceResultDTO).substring(0, 300);
        // InvoiceValidateStatusDO statusDO = InvoiceValidateStatusDO.of(params, now, validateResult);

        if (InvoiceIdentifyRecordEnum.InvoiceResultEnum.FAIL.getCode().intValue() == invoiceResultDTO.getResult()) {
            // statusDO.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
            // invoiceValidateStatusDAO.insert(statusDO);
            return null;
        }
        InvoiceDataDTO data = invoiceResultDTO.getResponse().getData();
        List<DetailAndExtraDTO> identifyResults = data.getIdentify_results();
        for (DetailAndExtraDTO detailAndExtraDTO : identifyResults) {
            ValidationDTO validationDTO = detailAndExtraDTO.getValidation();
            // statusDO.setValidateCode(validationDTO.getCode());
            // invoiceValidateStatusDAO.insert(statusDO);
            return validationDTO;
        }
        // invoiceValidateStatusDAO.insert(statusDO);
        return null;
    }

    private boolean validateParam(InvoiceValidateDetailParams params) {
        if (params == null || StringUtil.isBlank(params.getInvoiceNo())
                || StringUtil.isBlank(params.getInvoiceDate())
                || params.getInvoiceType() == null) {
            log.error("发票验真信息参数必填信息为空:{},", params);
            return true;
        }
        if (InvoiceTypeEnum.VAT.getBizType().equals(params.getInvoiceTypeStr())) {
            if (StringUtil.isBlank(params.getPretaxAmount())) {
                log.error("发票验真信息参数税前金额信息为空:{},", params);
                return true;
            }
            return false;
        }
        if (!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(Integer.valueOf(params.getInvoiceType()))) && (StringUtil.isBlank(params.getCheckCode()) || StringUtil.isBlank(params.getInvoiceCode()))) {
            log.error("发票验真信息参数校验码信息为空:{},", params);
            return true;
        }
        if (InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(Integer.valueOf(params.getInvoiceType())))) {
            if (StringUtil.isBlank(params.getTotal())) {
                log.error("发票验真信息参数总金额信息为空:{},", params);
                return true;
            }
        }
        return false;
    }

    private FormBody getFormBody(String invoiceCode, String invoiceNo, String checkCode, String invoiceDate,
                                 String pretaxAmount,String total, String invoiceType, long timestamp, String token) {
        if (InvoiceIdentifyRecordEnum.InvoiceIdentifyTypeEnum.增值税专用发票.getCode().equals(invoiceType)) {
            return new FormBody.Builder()
                    .add("app_key", appKey)
                    .add("timestamp", String.valueOf(timestamp))
                    .add("token", token)
                    .add("code", invoiceCode)
                    .add("number", invoiceNo)
                    .add("date", invoiceDate)
                    .add("type", invoiceType)
                    .add("pretax_amount", pretaxAmount)
                    .build();
        }
        if(InvoiceIdentifyRecordEnum.InvoiceIdentifyTypeEnum.电子发票专用发票.getCode().equals(invoiceType) || InvoiceIdentifyRecordEnum.InvoiceIdentifyTypeEnum.电子发票普通发票.getCode().equals(invoiceType) ){
            return new FormBody.Builder()
                    .add("app_key", appKey)
                    .add("timestamp", String.valueOf(timestamp))
                    .add("token", token)
                    .add("number", invoiceNo)
                    .add("total", total)
                    .add("date", invoiceDate)
                    .add("type", invoiceType)
                    .build();
        }
        return new FormBody.Builder()
                .add("app_key", appKey)
                .add("timestamp", String.valueOf(timestamp))
                .add("token", token)
                .add("code", invoiceCode)
                .add("number", invoiceNo)
                .add("date", invoiceDate)
                .add("type", invoiceType)
                .add("check_code", checkCode == null ? "" : checkCode)
                .build();
    }

    /**
     * 发票识别记录列表
     */
    @Override
    public ServiceResult<JSONObject> list(InvoiceValidateParams params) {
        ServiceResult<JSONObject> result = new ServiceResult();
        try {
            Integer totalCount = recordDAO.getListCount(params.getIsReal(), params.getIsIdentify(), params.getCreator());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("totalCount", totalCount);
            if (totalCount == 0) {
                jsonObject.put("list", Lists.newArrayList());
                jsonObject.put("totalCount", totalCount);
                return result.success(jsonObject);
            }
            List<InvoiceIdentifyRecordDO> list =
                    recordDAO.getList(params.getIsReal(), params.getIsIdentify(), params.getCreator());
            List<InvoiceIdentifyRecordDTO> dtoList = Lists.newArrayList();
            DO2DTO(list, dtoList);
            jsonObject.put("list", dtoList);
            return result.success(jsonObject);
        } catch (Exception e) {
            log.error("获取发票识别记录列表异常", e);
            return result.fail("获取发票识别记录列表异常");
        }
    }

    /**
     * DO转DTO
     */
    private void DO2DTO(List<InvoiceIdentifyRecordDO> list, List<InvoiceIdentifyRecordDTO> dtoList) {
        for (InvoiceIdentifyRecordDO identifyRecordDO : list) {
            InvoiceIdentifyRecordDTO identifyRecordDTO = new InvoiceIdentifyRecordDTO();
            BeanUtils.copyProperties(identifyRecordDO, identifyRecordDTO);
            identifyRecordDTO.setInvoiceTypeStr(InvoiceTypeEnum.getField(identifyRecordDTO.getInvoiceType()));
            InvoiceIdentifyRecordEnum.IsRealEnum isRealEnum = InvoiceIdentifyRecordEnum.IsRealEnum
                    .getByCode(identifyRecordDTO.getIsReal());
            identifyRecordDTO.setIsRealStr(isRealEnum == null ? null : isRealEnum.getDesc());
            InvoiceIdentifyRecordEnum.IsIdentifyEnum isIdentifyEnum = InvoiceIdentifyRecordEnum.IsIdentifyEnum
                    .getByCode(identifyRecordDTO.getIsIdentify());
            identifyRecordDTO.setIsIdentifyStr(isIdentifyEnum == null ? null : isIdentifyEnum.getDesc());
            dtoList.add(identifyRecordDTO);
            identifyRecordDTO.setDetails(identifyRecordDO.getDetails() == null ? "" : identifyRecordDO.getDetails());

            if (StringUtils.isNotBlank(identifyRecordDO.getResult())
                    && identifyRecordDO.getResult().startsWith("{")) {
                DetailAndExtraDTO detailAndExtraDTO = null;
                try {
                    detailAndExtraDTO = JSON.parseObject(identifyRecordDO.getResult(), DetailAndExtraDTO.class);
                } catch (Exception e) {
                    log.error("------ 发票识别记录有误。identifyRecordDO = {}, id = {}", identifyRecordDO.getResult(), identifyRecordDO.getId(), e);
                }
                if (null != detailAndExtraDTO
                        && null != detailAndExtraDTO.getDetails()
                        && CollectionUtils.isNotEmpty(detailAndExtraDTO.getDetails().getItems())) {
                    String taxRate = StringUtil.isBlank(detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate())
                            ? "" : detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate().replace("%", "");
                    identifyRecordDTO.setTaxRate(DecimalUtil.transBigDecimal(taxRate, 4));
                }
            }
        }
    }

    /**
     * 删除发票识别记录
     */
    @Override
    public ServiceResult<String> delete(InvoiceValidateParams params) {
        ServiceResult<String> result = new ServiceResult();
        try {
            InvoiceIdentifyRecordDO recordDO = new InvoiceIdentifyRecordDO();
            recordDO.setId(params.getId());
            recordDO.setUpdater(params.getLoginUserId());
            recordDO.setDeleted(InvoiceIdentifyRecordEnum.DeletedEnum.DELETED.getCode());
            recordDAO.updateByPrimaryKeySelective(recordDO);
        } catch (Exception e) {
            log.error("删除发票识别记录异常,id:{}", params.getId(), e);
            return result.fail("删除发票识别记录异常,id:" + params.getId());
        }
        return result.success("删除成功");
    }

    /**
     * 批量删除发票识别记录
     */
    @Override
    public ServiceResult<String> batchDelete(InvoiceValidateParams params) {
        List<Integer> idList = params.getIds();
        try {
            recordDAO.batchDelete(idList, params.getLoginUserId());
        } catch (Exception e) {
            log.error("批量删除发票识别记录异常，参数：{}", JSON.toJSONString(idList), e);
            return ServiceResult.failResult("删除发票识别记录异常，请稍后重试！");
        }

        return ServiceResult.successResult("删除成功！");
    }

    @Override
    public ServiceResult<String> getBatchNo() {
        ServiceResult<String> result = new ServiceResult();
        long start = System.currentTimeMillis();
        //   log.error("获取批次号开始");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Date date = new Date();
            String formatDate = sdf.format(date);
            String key = "invoice_batch_no" + formatDate;
            Long incr = getIncr(key, getCurrent2TodayEndMillisTime());
            if (incr == 0) {
                incr = getIncr(key, getCurrent2TodayEndMillisTime());//从0001开始
            }
            DecimalFormat df = new DecimalFormat("000000");//六位序列号
            String s = formatDate + df.format(incr);
            //    log.error("获取批次号结束，耗时："+(System.currentTimeMillis()-start));
            return result.success(s);
        } catch (Exception e) {
            log.error("生成发票批次号异常", e);
            return result.fail("生成发票批次号异常");
        }
    }

    //现在到今天结束的毫秒数
    public Long getCurrent2TodayEndMillisTime() {
        Calendar todayEnd = Calendar.getInstance();
        // Calendar.HOUR 12小时制
        // HOUR_OF_DAY 24小时制
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTimeInMillis() - System.currentTimeMillis();
    }

    public Long getIncr(String key, long liveTime) {
        RedisAtomicLong entityIdCounter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        Long increment = entityIdCounter.getAndIncrement();

        if ((null == increment || increment.longValue() == 0) && liveTime > 0) {//初始设置过期时间
            entityIdCounter.expire(liveTime, TimeUnit.MILLISECONDS);//单位毫秒
        }
        return increment;
    }

    @Override
    public ServiceResult<String> batchValidate(InvoiceValidateParams invoiceValidateParams) {
        ServiceResult<String> result = new ServiceResult();
        try {
            for (InvoiceValidateDetailParams params : invoiceValidateParams.getList()) {
                InvoiceDO invoiceDO = invoiceDAO
                        .selectInvoiceByInvoiceCodeAndNo(params.getInvoiceCode(), params.getInvoiceNo());
                if (invoiceDO == null) {
                    log.error("发票验真信息不存在，发票代码:{},发票号码:{}", params.getInvoiceCode(), params.getInvoiceNo());
                    continue;
                }
                ValidationDTO validationDTO = handleInvoiceValidate(params);
                Boolean flag = validationDTO == null || !InvoiceIdentifyRecordEnum.ValidationCodeEnum.SUCCESS.getCode()
                        .equals(validationDTO.getCode()) ? false : true;
                invoiceDO.setIsReal(flag ? InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode()
                        : InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
                invoiceDAO.updateByPrimaryKeySelective(invoiceDO);
            }
        } catch (Exception e) {
            log.error("批量验真发票异常，param：{}", invoiceValidateParams, e);
            return result.fail("批量验真发票异常");
        }
        return result.success("批量验真发票成功");
    }

    @Override
    public ServiceResult<String> update(InvoiceValidateDetailParams params) {
        log.info("InvoiceValidateServiceImpl update get params:{}",params);
        ServiceResult<String> result = new ServiceResult();
        // 校验购买方
        boolean checkResult = this.checkInvoiceBuyer(CheckInvoiceBuyerDTO.builder()
                .belongSubjectType(params.getCurrentUserReq().getBelongSubjectType())
                .belongSubjectId(Long.valueOf(params.getCurrentUserReq().getBelongSubjectId()))
                .buyer(params.getBuyer())
                .buyerTaxId(params.getBuyerTaxId()).build());
        if (!checkResult) {
            log.error("无权录入该购买方发票");
            return result.fail(InvoiceErrorCodeEnum.NO_PERMISSION_BUYER.getDesc());
        }
        try {
            InvoiceIdentifyRecordDO recordDO = recordDAO.selectByPrimaryKey(params.getId());
            if (recordDO == null) {
                return result.fail("发票识别记录不存在");
            }
            //判断校验信息有没有变更
            Boolean isUpdate = isUpdate(params, recordDO);
            Integer isReal = isUpdate ? InvoiceIdentifyRecordEnum.IsRealEnum.STAY.getCode() : recordDO.getIsReal();
            recordDO.setIsReal(isReal);

            recordDO.setInvoiceCode(params.getInvoiceCode());
            recordDO.setInvoiceNo(params.getInvoiceNo());
            recordDO.setInvoiceType(params.getInvoiceType());
            recordDO.setInvoiceDate(params.getInvoiceDate());
            recordDO.setPretaxAmount(params.getPretaxAmount());
            recordDO.setTotal(params.getTotal());
            recordDO.setSeller(params.getSeller());
            recordDO.setCheckCode(params.getCheckCode());
            recordDO.setBuyer(params.getBuyer());
            if (StringUtils.isNotBlank(params.getBuyerTaxId())){
                recordDO.setBuyerTaxId(params.getBuyerTaxId());
            } else {
                recordDO.setBuyerTaxId("");
            }

            recordDO.setDetails(params.getDetails());
            recordDO.setTax(params.getTax());

            // 更新税率
            if (null != params.getTaxRate()
                    && StringUtils.isNotBlank(recordDO.getResult())
                    && recordDO.getResult().startsWith("{")) {
                DetailAndExtraDTO detailAndExtraDTO = JSON.parseObject(recordDO.getResult(), DetailAndExtraDTO.class);
                if (null != detailAndExtraDTO
                        && null != detailAndExtraDTO.getDetails()
                        && CollectionUtils.isNotEmpty(detailAndExtraDTO.getDetails().getItems())) {
                    String taxRateStr = StringUtil.isBlank(detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate())
                            ? "" : detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate().replace("%", "");
                    BigDecimal taxRate = DecimalUtil.transBigDecimal(taxRateStr, 4);
                    // 重新设置第一条明细的税率
                    if (taxRate.compareTo(params.getTaxRate()) != 0) {
                        detailAndExtraDTO.getDetails().getItems().get(0).setTax_rate(params.getTaxRate() + "%");
                        recordDO.setResult(JSON.toJSONString(detailAndExtraDTO));
                    }
                }
            }

            recordDAO.updateByPrimaryKeySelective(recordDO);
        } catch (Exception e) {
            log.error("编辑发票识别信息异常，param：{}", params, e);
            return result.fail("编辑发票识别信息异常");
        }
        return result.success("编辑发票识别信息成功");
    }

    /**
     * 校验信息有没有变更
     */
    private boolean isUpdate(InvoiceValidateDetailParams params, InvoiceIdentifyRecordDO recordDO) {
        String invoiceNo = recordDO.getInvoiceNo() == null ? "" : recordDO.getInvoiceNo();
        String newInvoiceNo = params.getInvoiceNo() == null ? "" : params.getInvoiceNo();
        String invoiceCode = recordDO.getInvoiceCode() == null ? "" : recordDO.getInvoiceCode();
        String newInvoiceCode = params.getInvoiceCode() == null ? "" : params.getInvoiceCode();
        //增值税专票  发票代码，发票号码，发票类型，发票日期 税前金额
        if (InvoiceTypeEnum.VAT.getType() == params.getInvoiceType()) {
            String pretaxAmount = recordDO.getPretaxAmount() == null ? "" : recordDO.getPretaxAmount();
            String newRawPrice = params.getPretaxAmount() == null ? "" : params.getPretaxAmount();
            if (invoiceNo.equals(newInvoiceNo)
                    && invoiceCode.equals(newInvoiceCode)
                    && recordDO.getInvoiceDate().equals(params.getInvoiceDate())
                    && recordDO.getInvoiceType().equals(params.getInvoiceType())
                    && pretaxAmount.equals(newRawPrice)) {
                return false;
            }
            return true;
        }
        String checkCode = recordDO.getCheckCode() == null ? "" : recordDO.getCheckCode();
        String newCheckCode = params.getCheckCode() == null ? "" : params.getCheckCode();
        //非增值税专票  发票代码，发票号码，发票类型，发票日期 校验码
        if (invoiceNo.equals(newInvoiceNo)
                && invoiceCode.equals(newInvoiceCode)
                && recordDO.getInvoiceDate().equals(params.getInvoiceDate())
                && recordDO.getInvoiceType().equals(params.getInvoiceType())
                && checkCode.equals(newCheckCode)) {
            return false;
        }

        return true;
    }

    @Override
    public ServiceResult<String> validate(InvoiceValidateDetailParams params) {
        ServiceResult<String> result = new ServiceResult();
        try {
            //校验是否是支持验真发票类型
            String invoiceTypeStr = InvoiceTypeEnum.getBizTypeByType(params.getInvoiceType());
            if (!InvoiceConstants.NEED_VALIDATE_INVOICE_BIZ_TYPE.contains(invoiceTypeStr)) {
                return result.fail("不支持验真发票类型");
            }
            //识别记录
            if (InvoiceIdentifyRecordEnum.IdentifyTypeEnum.RECORD.getCode().equals(params.getType())) {
                return handleRecordValidate(params, result);
            }
            //发票验真
            return handleInvoiceValidate(params, result);
        } catch (Exception e) {
            log.error("批量验真发票异常，param：{}", params, e);
            return result.fail("发票识别记录验真异常");
        }
    }

    /**
     * 发票验真
     *
     */
    private ServiceResult<String> handleInvoiceValidate(InvoiceValidateDetailParams params,
                                                        ServiceResult<String> result) {
        InvoiceBO validateInvoiceBO = invoiceManager.getInvoiceById(params.getId());
        if (validateInvoiceBO == null) {
            return result.fail("发票记录不存在");
        }
        if (InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode().equals(validateInvoiceBO.getIsReal())) {
            return result.fail("发票已验真");
        }

        // count 传 3 则表示不进行重试
        InvoiceValidateStatusDO statusDO = this.requireGlority(validateInvoiceBO, 3,
                InvoiceValidateTypeEnum.MANUAL_VERIFICATION);
        if (statusDO == null || statusDO.getValidateCode() == null) {
            return result.fail("发票验真失败");
        }
        if (Objects.equals(InvoiceIdentifyRecordEnum.ValidationCodeEnum.SUCCESS.getCode(), statusDO.getValidateCode())) {
            return result.success("发票验真成功");
        }
        return result.fail("发票验真失败：" + InvoiceIdentifyRecordEnum.ValidationCodeEnum.getDescByCode(statusDO.getValidateCode()));
    }

    /**
     * 识别记录验真
     *
     * @throws IOException
     */
    private ServiceResult<String> handleRecordValidate(InvoiceValidateDetailParams params, ServiceResult<String> result)
            throws IOException {
        InvoiceIdentifyRecordDO recordDO = recordDAO.selectByPrimaryKey(params.getId());
        if (recordDO == null) {
            return result.fail("发票识别记录不存在");
        }
        if (InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode().equals(recordDO.getIsReal())) {
            return result.fail("发票已验真");
        }
        //纳税人识别号为空或不正确判断
        if (StringUtil.isBlank(recordDO.getBuyerTaxId()) ) {
            return result.fail("购买方纳税号为空");
        }
        CurrentUserReq currentUserReq = params.getCurrentUserReq();

        if (!InvoiceBuyerValidator.checkTaxId(currentUserReq.getBelongSubjectType(),recordDO.getBuyerTaxId())) {
            return result.fail("购买方纳税号不正确");
        }
        // params.setCreateTime(recordDO.getCreateTime());
        ValidationDTO validationDTO = handleInvoiceValidate(params);
        Boolean flag = validationDTO == null || !InvoiceIdentifyRecordEnum.ValidationCodeEnum.SUCCESS.getCode()
                .equals(validationDTO.getCode()) ? false : true;
        recordDO.setIsReal(flag ? InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode()
                : InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
        if (!flag) {
            recordDO.setReason(InvoiceIdentifyRecordEnum.ValidationCodeEnum.getDescByCode(validationDTO.getCode()));
        }
        recordDAO.updateByPrimaryKeySelective(recordDO);
        String message = "发票识别记录验真成功,结果：" + InvoiceIdentifyRecordEnum.IsRealEnum.getDescByCode(recordDO.getIsReal());
        if (!flag) {
            message += ",错误详情:" + InvoiceIdentifyRecordEnum.ValidationCodeEnum.getDescByCode(validationDTO.getCode());
        }
        return result.success(message);
    }

    @Override
    public InvoiceValidateStatusDO requireGlority(InvoiceBO validateInvoiceBO, Integer count, InvoiceValidateTypeEnum typeEnum) {
        // 发票税号先判断(因为手工录入的时候未填税号，所以当税号不为空时判断税号是否正确)
        // 小电需要校验，
        if (StringUtils.isNotBlank(validateInvoiceBO.getBuyerTaxId()) &&
                !InvoiceBuyerValidator.checkTaxId(validateInvoiceBO.getBelongSubjectType(),validateInvoiceBO.getBuyerTaxId())) {
            InvoiceValidateStatusDO statusDOForInsert = InvoiceValidateStatusDO.of(validateInvoiceBO, DateTime.now(),
                    "", typeEnum);
            statusDOForInsert.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
            statusDOForInsert.setValidateCode(InvoiceIdentifyRecordEnum.ValidationCodeEnum.BUYER_TAX_ID_ERROR.getCode());

            validateInvoiceBO.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
            validateInvoiceBO.setValidateCode(InvoiceIdentifyRecordEnum.ValidationCodeEnum.BUYER_TAX_ID_ERROR.getCode());

            // 校验 statusDOForInsert 参数
            validateInsertParam(statusDOForInsert);
            invoiceService.invoiceValidate(validateInvoiceBO, statusDOForInsert);
            return statusDOForInsert;
        }
        InvoiceResultDTO invoiceResultDTO = null;
        //调用睿琪验真
        if(InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(validateInvoiceBO.getInvoiceType()))){
            GlorityFullPowerParam glorityFullPowerParam = GlorityFullPowerParam.of(validateInvoiceBO, appKey, appSecret);
            invoiceResultDTO = glorityClient.invoiceFullPowerValidation(glorityFullPowerParam);
            log.info("invoiceFullPowerValidation glorityClient require, param:{}, result:{}", JSON.toJSON(glorityFullPowerParam),
                    JSON.toJSON(invoiceResultDTO));
        }else if(InvoiceTypeEnum.VAT.getCode().equals(validateInvoiceBO.getInvoiceType())){
            GlorityParam glorityParam = GlorityParam.of(validateInvoiceBO, appKey, appSecret);
            invoiceResultDTO = glorityClient.invoiceValidation(glorityParam);
            log.info("invoiceValidation glorityClient require, param:{}, result:{}", JSON.toJSON(glorityParam),
                    JSON.toJSON(invoiceResultDTO));
        }else{
            GlorityDefaultParam glorityDefaultParam = GlorityDefaultParam.of(validateInvoiceBO, appKey, appSecret);
            invoiceResultDTO = glorityClient.invoiceValidDefaultation(glorityDefaultParam);
            log.info("invoiceValidDefaultation glorityClient require, param:{}, result:{}", JSON.toJSON(glorityDefaultParam),
                    JSON.toJSON(invoiceResultDTO));
        }
        if (invoiceResultDTO == null) {
            throw BizException.create(ErrorCodeEnum.REQUEST_TIMEOUT);
        }
        ValidationDTO validationDTO = null;
        String validateResult = null;
        Boolean flag = false;
        if (Objects.equals(InvoiceIdentifyRecordEnum.InvoiceResultEnum.SUCCESS.getCode(),
                invoiceResultDTO.getResult())) {
            InvoiceDataDTO data = invoiceResultDTO.getResponse().getData();
            List<DetailAndExtraDTO> identifyResults = data.getIdentify_results();
            //请求成功未返回结果
            if (CollectionUtils.isEmpty(identifyResults)) {
                throw BizException.create(ErrorCodeEnum.REQUEST_TIMEOUT);
            }
            for (DetailAndExtraDTO detailAndExtraDTO : identifyResults) {
                validationDTO = detailAndExtraDTO.getValidation();
                // 如果为 10006 则重试3次
                if (Objects.nonNull(validationDTO) && InvoiceIdentifyRecordEnum.ValidationCodeEnum.OTHER.getCode()
                        .equals(validationDTO.getCode())) {
                    if (count < retryCount) {
                        try {
                            Thread.sleep(1000L);
                        } catch (InterruptedException e) {
                            log.error("invoiceValidationJob 发票验真错误代码10006重试睡眠出错", e.getMessage());
                        }
                        requireGlority(validateInvoiceBO, ++count, typeEnum);
                        return null;
                    }
                }
                if (Objects.nonNull(validationDTO) && Objects
                        .equals(validationDTO.getCode(), InvoiceIdentifyRecordEnum.ValidationCodeEnum.SUCCESS.getCode())) {
                    flag = true;
                }
                // 请求成功，保存验真结果代码
                validateResult = org.apache.commons.lang3.StringUtils.substring(JSON.toJSONString(validationDTO), 0, 300);
                break;
            }
        } else {
            // 请求失败， 保存错误码
            validateResult = org.apache.commons.lang3.StringUtils.substring(JSON.toJSONString(invoiceResultDTO), 0, 300);
        }
        InvoiceValidateStatusDO statusDOForInsert = InvoiceValidateStatusDO.of(validateInvoiceBO,
                DateTime.now(), validateResult, typeEnum);

        if (validationDTO != null) {
            statusDOForInsert.setValidateCode(validationDTO.getCode());
            validateInvoiceBO.setValidateCode(validationDTO.getCode());
        }
        if (flag) {
            statusDOForInsert.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode());
            validateInvoiceBO.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode());
        } else {
            statusDOForInsert.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
            validateInvoiceBO.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
        }
        // 校验 statusDOForInsert 参数
        validateInsertParam(statusDOForInsert);
        invoiceService.invoiceValidate(validateInvoiceBO, statusDOForInsert);
        return statusDOForInsert;
    }

    /**
     * 校验参数
     */
    private void validateInsertParam(InvoiceValidateStatusDO invoiceValidateStatusForInsert) {
        Set<ConstraintViolation<InvoiceValidateStatusDO>> validate = validator.validate(invoiceValidateStatusForInsert);
        if (CollectionUtil.isNotEmpty(validate)) {
            for (ConstraintViolation<InvoiceValidateStatusDO> constraintViolation : validate) {
                log.info("invoiceValidationJob 参数校验失败：{}", constraintViolation.getMessage());
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR);
            }
        }
    }
}

