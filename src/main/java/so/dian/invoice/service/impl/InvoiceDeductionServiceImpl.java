package so.dian.invoice.service.impl;

import static so.dian.invoice.constant.InvoiceConstants.MANUAL_DEDUCT_BUSINESS_NO;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.meidalife.common.exception.BizException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.common.logger.util.JsonUtils;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.commons.eden.util.LocalMapUtils;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.InvoiceDeductionAssemble;
import so.dian.invoice.converter.InvoiceDeductionConverter;
import so.dian.invoice.converter.InvoiceOperateLogConverter;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.dao.InvoiceSubjectRelationDAO;
import so.dian.invoice.enums.BelongSubjectTypeEnum;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.enums.BuyerTaxIdEnum;
import so.dian.invoice.enums.InvoiceStatusEnum;
import so.dian.invoice.enums.LeoExcs;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.manager.InvoiceDeductionManager;
import so.dian.invoice.manager.InvoiceOperateLogManager;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.bo.InvoiceQueryBO;
import so.dian.invoice.pojo.bo.UserBO;
import so.dian.invoice.pojo.dto.InvoiceDeductionDTO;
import so.dian.invoice.pojo.dto.InvoiceRollBackResultDTO;
import so.dian.invoice.pojo.dto.InvoiceRollBackResultDTO.RollBackStatus;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.param.ApplyInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceDeductBatchParam;
import so.dian.invoice.pojo.param.InvoiceDeductOperationParam;
import so.dian.invoice.pojo.param.InvoiceDeductParam;
import so.dian.invoice.pojo.param.InvoiceManualDeductParam;
import so.dian.invoice.pojo.param.InvoiceParam;
import so.dian.invoice.pojo.param.InvoiceRecoverBatchParam;
import so.dian.invoice.pojo.param.InvoiceRecoverOperationParam;
import so.dian.invoice.pojo.param.InvoiceRecoverParam;
import so.dian.invoice.pojo.param.OperatorParam;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.InvoiceDeductionService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.specification.BusinessTypeRollbackSupportSpec;
import so.dian.invoice.specification.InvoiceDivideRollbackSpec;
import so.dian.invoice.specification.InvoiceHandleWorkRollbackSpec;
import so.dian.invoice.specification.InvoicePreDivideRollbackSpec;
import so.dian.invoice.util.StringUtil;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.invoice.volidator.InvoiceValidator;

@Service
@Slf4j(topic = "biz")
public class InvoiceDeductionServiceImpl implements InvoiceDeductionService {

    @Resource
    private InvoiceDeductionDAO invoiceDeductionDAO;

    @Resource
    private InvoiceDAO invoiceDAO;

    @Resource
    private InvoiceSubjectRelationDAO invoiceSubjectRelationDAO;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private InvoiceOperateLogManager invoiceOperateLogManager;
    @Resource
    private AgentEmployeeService agentEmployeeService;

    @Autowired
    private InvoiceDeductionManager invoiceDeductionManager;

    @Autowired
    private BusinessTypeRollbackSupportSpec businessTypeRollbackSupportSpec;

    @Autowired
    private InvoiceDivideRollbackSpec invoiceDivideRollbackSpec;

    @Autowired
    private InvoiceHandleWorkRollbackSpec invoiceHandleWorkRollbackSpec;

    @Autowired
    private InvoicePreDivideRollbackSpec invoicePreDivideRollbackSpec;


    @Override
    public List<InvoiceDeductionDO> getInvoiceDeductionList(InvoiceDeductOperationParam param) {

        return invoiceDeductionDAO.selectInvoiceDeductionList(param.getBusinessNo(), param.getInvoiceCode(),
                param.getInvoiceNo(), param.getBusinessType(), OperateTypeEnum.DEDUCT.getType());
    }

    /**
     * 批量核销发票
     */
    @Override
    @Transactional
    public BizResult<List<InvoiceDeductionDTO>> batchDeductInvoice(InvoiceDeductBatchParam params) {
        if (Objects.isNull(params) || CollectionUtils.isEmpty(params.getList())) {
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }
        //根据发票号和发票代码批量查询发票
        List<InvoiceDeductParam> list = params.getList();
        List<InvoiceQueryBO> queryBOList = LocalListUtils.transferList(list,
                invoiceDeductParam -> InvoiceQueryBO.builder()
                        .invoiceCode(
                                invoiceDeductParam.getInvoiceCode() == null ? "" : invoiceDeductParam.getInvoiceCode())
                        .invoiceNo(invoiceDeductParam.getInvoiceNo())
                        .build()
        );
        List<InvoiceDO> invoiceDOList = invoiceDAO.findByInvoiceNoAndInvoiceCode(queryBOList);
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            log.error("批量核销发票，发票不存在。params: {}", list);
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR, "发票不存在");
        }

        List<InvoiceDeductionDTO> reList = new ArrayList<>();
        Map<String, InvoiceDO> map = LocalMapUtils.listAsHashMap(invoiceDOList,
                obj -> obj.getInvoiceCode() + StrUtil.UNDERLINE + obj.getInvoiceNo());
        for (InvoiceDeductParam invoiceDeductParam : list) {
            OperatorParam operatorParam = new OperatorParam();
            operatorParam.setOperatorId(params.getOperatorId());
            operatorParam.setOperatorName(params.getOperatorName());
            operatorParam.setRemark(params.getRemark());
            InvoiceDO invoiceDO =
                    map.get(invoiceDeductParam.getInvoiceCode() + StrUtil.UNDERLINE
                            + invoiceDeductParam.getInvoiceNo());
            reList.add(deductInvoice(invoiceDeductParam, operatorParam, invoiceDO));
        }
        return BizResult.create(reList);
    }

    @Override
    @Transactional
    public BizResult<InvoiceDeductionDTO> deductInvoice(InvoiceDeductOperationParam param) {
        if (StringUtil.isBlank(param.getBusinessNo())
                || StringUtil.isBlank(param.getInvoiceNo())
                || param.getBusinessType() == null
                || BusinessTypeEnum.getField(param.getBusinessType()) == null
                || param.getAmount() == null
                || param.getAmount().compareTo(new BigDecimal(0)) <= 0) {
            log.error("发票核销，参数错误。{}", param);
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }

        OperatorParam operatorParam = new OperatorParam();
        operatorParam.setOperatorId(param.getOperatorId());
        operatorParam.setOperatorName(param.getOperatorName());
        operatorParam.setRemark(param.getRemark());

        InvoiceDO invoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(
                param.getInvoiceCode(), param.getInvoiceNo());

        return BizResult.create(deductInvoice(param, operatorParam, invoiceDO));
    }

    private InvoiceDeductionDTO deductInvoice(InvoiceDeductParam param, OperatorParam operatorParam,
            InvoiceDO invoiceDO) {
        if (Objects.isNull(invoiceDO)) {
            log.error("批量核销发票,发票不存在！param: {}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST,
                    "发票不存在！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        if (StringUtils.isBlank(param.getInvoiceCode()) && !InvoiceValidator.checkFullPowerInvoice(
                InvoiceTypeEnum.getByType(invoiceDO.getType()))) {
            log.error("批量核销发票,发票类型不是全电发票，却没传InvoiceCode参数！param: {}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_REVIEW_FAILED,
                    "发票核销失败！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        invoiceDO.setSubjectName(StringUtil.trimFull2Half(invoiceDO.getSubjectName()));
        invoiceDO.setBuyer(StringUtil.trimFull2Half(invoiceDO.getBuyer()));
        //匹配业务类型
        if (!businessTypeCheck(param.getBusinessType(), invoiceDO.getSubjectType())) {
            log.error("批量核销发票,发票业务类型不一致！param: {}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_SUBJECT_TYPE_ERROR,
                    "发票业务类型不一致！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        //匹配金额
        BigDecimal sourceUsedAmount = invoiceDO.getUsedAmount();
        BigDecimal totalAmount = invoiceDO.getUsedAmount().add(param.getAmount()).setScale(2,
                RoundingMode.HALF_DOWN);
        if (totalAmount.compareTo(invoiceDO.getPrice()) > 0) {
            log.error("批量核销发票,发票余额不足！param: {}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票余额不足！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        // 增加新增分公司购买方校验
        // 我司发票核销判断发票购买方和发票时间 2020-03-25 北京伊电园->杭州伊电园
        String validInvoiceBuyerResult = InvoiceBuyerValidator.validInvoiceBuyer(invoiceDO.getBelongSubjectType(),
                invoiceDO.getBuyer(),
                invoiceDO.getGmtCreate(), true);
        if (StringUtils.isNotBlank(validInvoiceBuyerResult)) {
            log.error("发票核销，发票购买方不正确.{}", invoiceDO);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_BUYER_INVALID,
                    validInvoiceBuyerResult);
        }

        //购买方（1.结算方类型：小电；2.2019年10月1日起开始判断）
        if (Objects.equals(invoiceDO.getBelongSubjectType(), BelongSubjectTypeEnum.XIAODIAN.getCode())
                && Objects.nonNull(invoiceDO.getGmtCreate())
                && !DateUtil.parse("20191001").after(invoiceDO.getGmtCreate())) {
            if (!Objects.equals(param.getBuyerName(), invoiceDO.getBuyer())) {
                if (BuyerTaxIdEnum.checkYDYbuyer(param.getBuyerName())
                        && BuyerTaxIdEnum.checkYDYbuyer(invoiceDO.getBuyer())) {
                    // 伊电园发票 校验不分北京伊电园和杭州伊电园和杭州小电
                } else {
                    log.error("批量核销发票,发票购买方和签约主体不一致！param: {}", param);
                    throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                            "发票购买方和我方签约主体不一致！发票代码:" + param.getInvoiceCode() + "，发票号码："
                                    + param.getInvoiceNo());
                }
            }
        }
//        else{
//            if (!Objects.equals(param.getBuyerName(), invoiceDO.getBuyer())) {
//                log.error("批量核销发票,发票购买方和核销购买方不一致！param: {}", param);
//                throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
//                        "发票购买方和核销购买方不一致！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
//            }
//        }

        //主体名称
        if (StringUtils.isBlank(param.getSubjectName())) {
            log.error("批量核销发票,发票主体名称不能为空！param: {}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票主体名称不能为空！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        //是否需要发票主体关联
        String subjectName = StringUtil.trimFull2Half(param.getSubjectName());

        if (!Objects.equals(subjectName, invoiceDO.getSubjectName())) {
            //提现不区分关联主体
            if (Objects.equals(BusinessTypeEnum.DEVIDE.getType(), invoiceDO.getSubjectType())
                    && Objects.equals(BusinessTypeEnum.CHANNEL.getType(), invoiceDO.getSubjectType())) {
                log.error("批量核销发票,发票主体名称不匹配！param: {}", param);
                throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                        "发票主体名称不匹配！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
            } else {
                if (param.getNeedSubjectRelation()) {
                    //判断主体名称有没关联发票主体
                    List<String> subjectNameList = invoiceSubjectRelationDAO.getSubjectNameList(subjectName);
                    if (!subjectNameList.contains(invoiceDO.getSubjectName())) {
                        log.error("批量核销发票,发票主体名称不匹配！param: {}", param);
                        throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                                "发票主体名称不匹配！发票代码:" + param.getInvoiceCode() + "，发票号码："
                                        + param.getInvoiceNo());
                    }
                } else {
                    log.error("批量核销发票,发票主体名称不匹配！param: {}", param);
                    throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                            "发票主体名称不匹配！发票代码:" + param.getInvoiceCode() + "，发票号码："
                                    + param.getInvoiceNo());
                }
            }
        }
        //核销发票
        invoiceDO.setUsedAmount(totalAmount);
        invoiceDO.setStatus(InvoiceStatusEnum.PART.getType());
        if (invoiceDO.getPrice().compareTo(totalAmount) == 0) {
            invoiceDO.setStatus(InvoiceStatusEnum.ALL.getType());
        }
        invoiceDAO.updateByPrimaryKeySelective(invoiceDO);

        //核销记录
        InvoiceDeductionDO invoiceDeductionDO =
                InvoiceDeductionConverter.deductInvoice(invoiceDO, param, operatorParam);
        invoiceDeductionDAO.insertSelective(invoiceDeductionDO);
        //核销日志
        InvoiceOperateLogBO invoiceOperateLogBO =
                InvoiceOperateLogConverter.deductInvoiceLog(invoiceDO.getId(), sourceUsedAmount, invoiceDeductionDO);
        invoiceOperateLogManager.insert(invoiceOperateLogBO);
        return InvoiceDeductionConverter.DO2DTO(invoiceDeductionDO);
    }

    /**
     * 发票业务类型与单据业务类型一致性检查
     *
     * @param billBusinessType    单据业务类型（例如付款申请单）
     * @param invoiceBuisnessType 发票业务类型
     * @return
     */
    private boolean businessTypeCheck(Integer billBusinessType, Integer invoiceBuisnessType) {
        if (billBusinessType == null || invoiceBuisnessType == null) {
            return false;
        }
        if (Objects.equals(billBusinessType, invoiceBuisnessType)) {
            return true;
        }
        BusinessTypeEnum billBusinessTypeEnum = BusinessTypeEnum.findByType(billBusinessType);
        BusinessTypeEnum invoiceBuisnessTypeEnum = BusinessTypeEnum.findByType(invoiceBuisnessType);
        if (billBusinessTypeEnum == null || invoiceBuisnessTypeEnum == null) {
            return false;
        }
        boolean check = BusinessTypeEnum.businessTypeCheck().stream()
                .anyMatch(lists -> lists.contains(billBusinessTypeEnum) && lists.contains(invoiceBuisnessTypeEnum));
        return check;
    }

    @Override
    @Transactional
    public BizResult<List<InvoiceDeductionDTO>> batchRecoverInvoice(InvoiceRecoverBatchParam param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getList())) {
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }
        //根据发票号和发票代码批量查询发票
        List<InvoiceRecoverParam> list = param.getList();
        List<InvoiceQueryBO> queryBOList = LocalListUtils.transferList(list,
                invoiceRecoverParam -> InvoiceQueryBO.builder()
                        .invoiceCode(invoiceRecoverParam.getInvoiceCode() == null ? ""
                                : invoiceRecoverParam.getInvoiceCode())
                        .invoiceNo(invoiceRecoverParam.getInvoiceNo())
                        .build()
        );
        List<InvoiceDO> invoiceDOList = invoiceDAO.findByInvoiceNoAndInvoiceCode(queryBOList);
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            log.error("回滚发票，发票不存在。params: {}", list);
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR, "发票不存在");
        }

        List<InvoiceDeductionDTO> reList = Lists.newArrayList();
        Map<String, InvoiceDO> map = LocalMapUtils.listAsHashMap(invoiceDOList,
                obj -> obj.getInvoiceCode() + StrUtil.UNDERLINE + obj.getInvoiceNo());
        for (InvoiceRecoverParam invoiceRecoverParam : list) {
            OperatorParam operatorParam = new OperatorParam();
            operatorParam.setOperatorId(param.getOperatorId());
            operatorParam.setOperatorName(param.getOperatorName());
            operatorParam.setRemark(param.getRemark());
            InvoiceDO invoiceDO = map.get(
                    invoiceRecoverParam.getInvoiceCode() + StrUtil.UNDERLINE + invoiceRecoverParam.getInvoiceNo());
            reList.add(recoverInvoice(invoiceRecoverParam, operatorParam, invoiceDO));
        }
        return BizResult.create(reList);
    }

    @Override
    @Transactional
    public BizResult<InvoiceDeductionDTO> recoverInvoice(InvoiceRecoverOperationParam param) {
        if (Objects.isNull(param)
                || StringUtils.isBlank(param.getInvoiceNo())
                || StringUtils.isBlank(param.getBusinessNo())
                || Objects.isNull(param.getAmount())) {
            log.error("发票回滚，参数错误。{}", param);
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }
        OperatorParam operatorParam = new OperatorParam();
        operatorParam.setOperatorId(param.getOperatorId());
        operatorParam.setOperatorName(param.getOperatorName());
        operatorParam.setRemark(param.getRemark());

        InvoiceDO invoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(param.getInvoiceCode(), param.getInvoiceNo());
        InvoiceDeductionDTO invoiceDeductionDTO = recoverInvoice(param, operatorParam, invoiceDO);
        return BizResult.create(invoiceDeductionDTO);
    }

    private InvoiceDeductionDTO recoverInvoice(InvoiceRecoverParam param, OperatorParam operatorParam,
            InvoiceDO invoiceDO) {
        if (invoiceDO == null) {
            log.error("发票回滚，发票不存在。{}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票不存在！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        if (!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(invoiceDO.getType()))
                && StringUtil.isBlank(param.getInvoiceCode())) {
            log.error("发票回滚，发票参数错误。{}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票类型和参数不正确！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.findByType(param.getBusinessType());
        if (businessTypeEnum == null) {
            log.error("发票回滚，业务类型错误。{}", param);
            throw so.dian.commons.eden.exception.BizException.create(ErrorCodeEnum.PARAMS_ERROR,
                    "发票业务类型错误！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }

        deductAmountCompare(param);

        //修改发票记录
        InvoiceStatusEnum invoiceStatusEnum = InvoiceStatusEnum.PART;
        if (invoiceDO.getUsedAmount().compareTo(param.getAmount()) == 0) {
            invoiceStatusEnum = InvoiceStatusEnum.WAIT;
        }
        invoiceDAO.recoverInvoice(invoiceDO.getId(), invoiceStatusEnum.getType(), param.getAmount());

        //新增回滚明细
        InvoiceDeductionDO invoiceDeductionDO =
                InvoiceDeductionConverter.recoverInvoice(invoiceDO, param, operatorParam);
        invoiceDeductionDAO.insertSelective(invoiceDeductionDO);
        //添加回滚日志
        InvoiceOperateLogBO invoiceOperateLogBO =
                InvoiceOperateLogConverter.recoverInvoiceLog(invoiceDO, invoiceDeductionDO);
        invoiceOperateLogManager.insert(invoiceOperateLogBO);

        return InvoiceDeductionConverter.DO2DTO(invoiceDeductionDO);
    }

    private void deductAmountCompare(InvoiceRecoverParam param) {
        //计算核销金额
        List<InvoiceDeductionDO> deductionDOList =
                invoiceDeductionDAO.selectInvoiceDeductionList(param.getBusinessNo(), param.getInvoiceCode(),
                        param.getInvoiceNo(), param.getBusinessType(), OperateTypeEnum.DEDUCT.getType());
        if (CollectionUtils.isEmpty(deductionDOList)) {
            log.error("发票回滚，找不到对应的核销记录。{}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "找不到对应的核销记录！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }

        BigDecimal deductTotal = new BigDecimal(0);
        for (InvoiceDeductionDO deductionDO : deductionDOList) {
            deductTotal = deductTotal.add(deductionDO.getAmount());
        }
        //计算回滚的金额
        List<InvoiceDeductionDO> recoverDOList =
                invoiceDeductionDAO.selectInvoiceDeductionList(param.getBusinessNo(), param.getInvoiceCode(),
                        param.getInvoiceNo(), param.getBusinessType(), OperateTypeEnum.RECOVER.getType());
        BigDecimal recoverTotal = new BigDecimal(0);
        if (CollectionUtils.isNotEmpty(recoverDOList)) {
            for (InvoiceDeductionDO deductionDO : recoverDOList) {
                recoverTotal = recoverTotal.add(deductionDO.getAmount());
            }
        }
        recoverTotal = recoverTotal.add(param.getAmount());
        if (deductTotal.compareTo(recoverTotal) != 0) {
            log.error("发票回滚，回滚金额错误。{}", param);
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR,
                    "发票回滚金额错误！发票代码:" + param.getInvoiceCode() + "，发票号码：" + param.getInvoiceNo());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceManualDeduct(InvoiceManualDeductParam param) {
        if (param == null) {
            throw new BizException(LeoExcs.PARAM_RESOLVER_FAIL);
        }
        //查询当前登录人
        AgentEmployeeDTO agentEmployeeDTO = agentEmployeeService.getEmployeeById(param.getCreator());
        if (agentEmployeeDTO == null) {
            log.error("当前登录用户异常,请稍后再试。userId：{}", param.getCreator());
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.DEDUCT_ERROR);
        }
        // 查询发票信息
        InvoiceDO invoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(param.getInvoiceCode(), param.getInvoiceNo());
        if (invoiceDO == null) {
            throw new BizException(LeoExcs.INVOICE_NON_ERROR);
        }

        // 如果为我司发票，需判断发票购买方
        boolean buyerValid = InvoiceBuyerValidator.validDianInvoiceBuyer(invoiceDO.getBelongSubjectType(),
                invoiceDO.getBuyer(),
                invoiceDO.getGmtCreate(), false);
        if (!buyerValid) {
            throw so.dian.commons.eden.exception.BizException.create(InvoiceErrorCodeEnum.INVOICE_BUYER_INVALID);
        }

        //发票原核销金额
        BigDecimal sourceUsedAmount = invoiceDO.getUsedAmount();
        // 校验发票归属人
        invoiceService.checkInvoiceBelong(invoiceDO, param.getCreator());
        // 计算发票可核销余额
        BigDecimal canDeductAmount = invoiceDO.getPrice().subtract(invoiceDO.getUsedAmount());
        // 判断发票余额
        if (canDeductAmount.compareTo(param.getAmount()) < 0) {
            throw new BizException(LeoExcs.DEDUCT_AMOUNT_ERROR);
        }
        // 构建核销对象
        InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setInvoiceCode(param.getInvoiceCode() == null ? "" : param.getInvoiceCode());
        invoiceDeductionDO.setInvoiceNo(param.getInvoiceNo());
        invoiceDeductionDO.setOperateType(OperateTypeEnum.DEDUCT.getType());
        invoiceDeductionDO.setCreator(param.getCreator().longValue());
        invoiceDeductionDO.setCreateName(agentEmployeeDTO.getNickNameOrName());
        invoiceDeductionDO.setAmount(param.getAmount());
        // 设置操作类型
        invoiceDeductionDO.setOperateType(OperateTypeEnum.DEDUCT.getType());
        // 设置业务编号
        if (StringUtils.isNotBlank(param.getBusinessNo())) {
            invoiceDeductionDO.setBusinessNo(param.getBusinessNo());
        } else {
            invoiceDeductionDO.setBusinessNo(MANUAL_DEDUCT_BUSINESS_NO);
        }
        // 设置业务类型
        invoiceDeductionDO.setReason(StringUtils.defaultString(param.getReason()));
        invoiceDeductionDO.setBusinessType(invoiceDO.getSubjectType());
        // 保存核销数据
        invoiceDeductionDAO.insertSelective(invoiceDeductionDO);

        // 设置发票核销状态
        if (canDeductAmount.compareTo(param.getAmount()) == 0) {
            invoiceDO.setStatus(InvoiceStatusEnum.ALL.getType());
        } else {
            invoiceDO.setStatus(InvoiceStatusEnum.PART.getType());
        }
        // 设置发票核销金额
        BigDecimal usedAmount = invoiceDO.getUsedAmount().add(param.getAmount());
        invoiceDO.setUsedAmount(usedAmount);
        // 更新发票状态
        invoiceDAO.updateByPrimaryKeySelective(invoiceDO);

        //操作日志
        InvoiceOperateLogBO invoiceOperateLogBO = InvoiceOperateLogConverter.buildInvoiceManualDeductParam2BO(
                invoiceDeductionDO, sourceUsedAmount, invoiceDO.getId());
        invoiceOperateLogManager.insert(invoiceOperateLogBO);
    }

    @Override
    public List<InvoiceInfoVO> getApplyInvoiceList(ApplyInvoiceParam param) {
        List<InvoiceInfoVO> invoiceInfoList = new ArrayList<>();

        if (param == null) {
            throw new BizException(LeoExcs.PARAM_RESOLVER_FAIL);
        }
        // 查询付款申请单对应的发票核销信息
        List<InvoiceDeductionDO> invoiceDeductionList =
                invoiceDeductionDAO.findPayInvoiceByBusinessNo(param.getApplyNo(), OperateTypeEnum.DEDUCT.getType());
        if (CollectionUtils.isEmpty(invoiceDeductionList)) {
            return invoiceInfoList;
        }
        // 抽出发票代码和发票号码集合
        List<String> invoiceCodeList = new ArrayList<>();
        List<String> invoiceNoList = new ArrayList<>();
        for (InvoiceDeductionDO invoiceDeduction : invoiceDeductionList) {
            invoiceCodeList.add(invoiceDeduction.getInvoiceCode() == null ? "" : invoiceDeduction.getInvoiceCode());
            invoiceNoList.add(invoiceDeduction.getInvoiceNo());
        }
        // 查询发票信息
        InvoiceParam invoiceParam = new InvoiceParam();
        invoiceParam.setInvoiceCodeList(Arrays.asList(invoiceCodeList.toArray(new String[]{})));
        invoiceParam.setInvoiceNoList(Arrays.asList(invoiceNoList.toArray(new String[]{})));
        // 这样查询有一定的误差，后面需要校正
        List<InvoiceInfoVO> midResult = invoiceService.getInvoiceInfoList(invoiceParam);
        if (CollectionUtils.isEmpty(midResult)) {
            return invoiceInfoList;
        }
        // 将结果List转化为MAP
        Map<String, InvoiceInfoVO> invoiceInfoMap = new HashMap<>();
        for (InvoiceInfoVO invoiceInfo : midResult) {
            String key = invoiceInfo.getInvoiceNo() + invoiceInfo.getInvoiceCode();
            invoiceInfoMap.put(key, invoiceInfo);
        }
        // 创建结果集
        InvoiceInfoVO invoiceInfo;
        // 防重Set
        Set<String> keySet = new HashSet<>();
        for (InvoiceDeductionDO invoiceDeduction : invoiceDeductionList) {
            String key = invoiceDeduction.getInvoiceNo() + invoiceDeduction.getInvoiceCode();
            if (keySet.contains(key)) {
                continue;
            }
            // 获取结果对象
            invoiceInfo = invoiceInfoMap.get(key);
            if (invoiceInfo != null) {
                invoiceInfoList.add(invoiceInfo);
                keySet.add(key);
            }
        }

        return invoiceInfoList;
    }

    @Override
    public BizResult<List<InvoiceDeductionDTO>> getDeductInvoiceList(String businessNo, Integer businessType) {
        if (StringUtils.isBlank(businessNo) || Objects.isNull(businessType)) {
            log.error("根据业务单号查询发票核销记录，参数不能为空。businessNo:{}, businessType:{}", businessNo,
                    businessType);
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }
        List<InvoiceDeductionDO> list =
                invoiceDeductionDAO.findByBusinessNo(businessNo, businessType, null);
        return BizResult.create(InvoiceDeductionConverter.DO2DTOList(list));
    }

    /**
     * 获取业务关联的发票
     */
    @Override
    public List<InvoiceDeductionDO> getInvoiceByBusinessRelation(String invoiceNo, String invoiceCode) {
        if (StringUtils.isBlank(invoiceNo)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isBlank(invoiceCode)) {
            invoiceCode = "";
        }
        List<InvoiceDeductionDO> list = invoiceDeductionDAO.findTopByInvoiceNoAndInvoiceCode(invoiceNo, invoiceCode);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        //去除回滚的发票，剩下的就是关联的发票。如果出现部分回滚的业务，需要修改本逻辑，应该在发票上增加字段
        return list.stream()
                .filter(obj -> Objects.equals(obj.getOperateType(), OperateTypeEnum.DEDUCT.getType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceRollBackResultDTO> rollback(UserBO userBO, List<Long> invoiceDeductIds) {
        List<InvoiceDeductionDO> invoiceDeductList = invoiceDeductionManager.findByIdIn(invoiceDeductIds);
        if (CollectionUtils.isEmpty(invoiceDeductList)) {
            throw so.dian.commons.eden.exception.BizException.create(ErrorCodeEnum.PARAMS_ERROR, "核销列表为空");
        }
        List<InvoiceRollBackResultDTO> invoiceRollBackResultDTOS = Lists.newArrayList();

        for (InvoiceDeductionDO invoiceDeductionDO : invoiceDeductList) {
            InvoiceRollBackResultDTO rollbackResult = new InvoiceRollBackResultDTO();

            invoiceRollBackResultDTOS.add(rollbackResult);
            InvoiceDeductionService invoiceDeductionService = (InvoiceDeductionService) AopContext.currentProxy();
            try {
                if (invoiceHandleWorkRollbackSpec.isSatisfiedBy(invoiceDeductionDO)) {
                    invoiceDeductionService.rollbackSingle(userBO, invoiceDeductionDO);
                    rollbackResult.setRetVal("");
                    rollbackResult.setRollbackStatus(RollBackStatus.SUCCESS.getCode());

                }else if (businessTypeRollbackSupportSpec.not().isSatisfiedBy(invoiceDeductionDO)) {
                    rollbackResult.setRetVal("业务类型不支持");
                    rollbackResult.setRollbackStatus(RollBackStatus.FAILED.getCode());
                } else if (invoiceDivideRollbackSpec.or(invoicePreDivideRollbackSpec)
                        .isSatisfiedBy(invoiceDeductionDO)) {
                    invoiceDeductionService.rollbackSingle(userBO, invoiceDeductionDO);
                    rollbackResult.setRetVal("");
                    rollbackResult.setRollbackStatus(RollBackStatus.SUCCESS.getCode());
                } else {
                    rollbackResult.setRetVal("关联业务单已核销发票,不允许回滚");
                    rollbackResult.setRollbackStatus(RollBackStatus.FAILED.getCode());
                }
                rollbackResult.setId(Long.valueOf(invoiceDeductionDO.getId()));
            } catch (so.dian.commons.eden.exception.BizException e) {
                rollbackResult.setRollbackStatus(RollBackStatus.FAILED.getCode());
                rollbackResult.setRetVal(e.getMessage());
            } catch (Exception e) {
                log.error("roll back invoice error : {}", JsonUtils.objectToString(invoiceDeductionDO), e);
                rollbackResult.setRollbackStatus(RollBackStatus.FAILED.getCode());
                rollbackResult.setRetVal("回滚系统异常");
            }
        }
        return invoiceRollBackResultDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackSingle(UserBO userBO, InvoiceDeductionDO invoiceDeductionDO) {
        if (Objects.isNull(invoiceDeductionDO)) {
            throw so.dian.commons.eden.exception.BizException.create(ErrorCodeEnum.PARAMS_ERROR, "发票核销记录不存在");
        }

        InvoiceDO invoiceDO = invoiceDAO.selectInvoiceByInvoiceCodeAndNo(invoiceDeductionDO.getInvoiceCode(),
                invoiceDeductionDO.getInvoiceNo());
        if (Objects.isNull(invoiceDO)) {
            throw so.dian.commons.eden.exception.BizException.create(ErrorCodeEnum.PARAMS_ERROR, "发票不存在");
        }

        List<InvoiceDeductionDO> deductionDOS = invoiceDeductionDAO.selectInvoiceDeductionList(
                invoiceDeductionDO.getBusinessNo(), invoiceDeductionDO.getInvoiceCode(),
                invoiceDeductionDO.getInvoiceNo(), invoiceDeductionDO.getBusinessType(),
                OperateTypeEnum.DEDUCT.getType());
        BigDecimal totalDeductAmount = new BigDecimal(0);
        for (InvoiceDeductionDO deductionDO : deductionDOS) {
            totalDeductAmount = totalDeductAmount.add(deductionDO.getAmount());
        }

        List<InvoiceDeductionDO> rollbackList = invoiceDeductionDAO.selectInvoiceDeductionList(
                invoiceDeductionDO.getBusinessNo(), invoiceDeductionDO.getInvoiceCode(),
                invoiceDeductionDO.getInvoiceNo(), invoiceDeductionDO.getBusinessType(),
                OperateTypeEnum.RECOVER.getType());
        BigDecimal recoverAmount = new BigDecimal(0);
        for (InvoiceDeductionDO deductionDO : rollbackList) {
            recoverAmount = recoverAmount.add(deductionDO.getAmount());
        }
        recoverAmount = recoverAmount.add(invoiceDeductionDO.getAmount());
        if (recoverAmount.compareTo(totalDeductAmount) > 0) {
            throw so.dian.commons.eden.exception.BizException.create(ErrorCodeEnum.PARAMS_ERROR,
                    "回滚金额不能大于核销金额");
        }

        //修改发票记录
        InvoiceStatusEnum invoiceStatusEnum = InvoiceStatusEnum.PART;
        if (invoiceDO.getUsedAmount().compareTo(invoiceDeductionDO.getAmount()) == 0) {
            invoiceStatusEnum = InvoiceStatusEnum.WAIT;
        }
        invoiceDAO.recoverInvoice(invoiceDO.getId(), invoiceStatusEnum.getType(), invoiceDeductionDO.getAmount());

        //新增回滚明细
        InvoiceDeductionDO rollbackDO =
                InvoiceDeductionAssemble.assembleDO(userBO, invoiceDeductionDO);
        invoiceDeductionDAO.insertSelective(rollbackDO);
        //添加回滚日志
        InvoiceOperateLogBO invoiceOperateLogBO =
                InvoiceOperateLogConverter.recoverInvoiceLog(invoiceDO, rollbackDO);
        invoiceOperateLogManager.insert(invoiceOperateLogBO);

    }
}
