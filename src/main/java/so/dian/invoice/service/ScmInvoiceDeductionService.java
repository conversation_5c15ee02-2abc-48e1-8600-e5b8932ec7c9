package so.dian.invoice.service;

import java.util.List;
import so.dian.invoice.pojo.dto.ScmInvoiceDeductionDTO;
import so.dian.invoice.pojo.dto.ScmInvoiceInfoDTO;
import so.dian.invoice.pojo.param.ScmInvoiceDeductBatchParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductQueryParam;
import so.dian.invoice.pojo.param.ScmInvoiceRecoverBatchParam;

public interface ScmInvoiceDeductionService {
    /**
     * 根据ID查询发票
     */
    List<ScmInvoiceInfoDTO> queryByInvoiceId(List<Long> invoiceIds);

    /**
     * 批量核销发票-查询发票列表
     */
    List<ScmInvoiceInfoDTO> queryInvoiceList(ScmInvoiceDeductQueryParam param);

    /**
     * 批量核销发票
     */
    List<ScmInvoiceDeductionDTO> batchDeductInvoice(ScmInvoiceDeductBatchParam params);

    /**
     * 批量回滚核销的发票
     */
    List<Long> batchRecoverInvoice(ScmInvoiceRecoverBatchParam param);

    /**
     * 查询发票核销记录
     */
    List<ScmInvoiceDeductionDTO> getDeductInvoiceList(List<Long> deductionIds);
}
