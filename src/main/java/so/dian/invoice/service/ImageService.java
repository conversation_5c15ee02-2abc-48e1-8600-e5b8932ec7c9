package so.dian.invoice.service;

import java.io.IOException;

public interface ImageService {
    String LEO="leo";
    String LHC="lhc";
    String INVOICE="invoice";
    String uploadImageQCloud(String rootName, String originalFilename, byte[] imageBytes, String contentType, long size) throws IOException;

    String uploadFileQCloud(String rootName, String originalFilename, byte[] fileBytes, String contentType, long size) throws IOException;
}
