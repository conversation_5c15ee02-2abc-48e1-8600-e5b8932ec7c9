package so.dian.invoice.converter;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yuechuan
 * @Date: 2025/3/18 9:32 PM
 * @Description:
 */
@Slf4j
public class InvoiceChangeRecordConverter {
//
//    public static InvoiceChangeRecord toDTO(ChannelRepairChangeDTO model) {
//        InvoiceChangeRecord record = new InvoiceChangeRecord();
//        record.setOutBizId(model.getRepairNo());
//        record.setOutBizType(OutOrderTypeEnum.REPAIR_ORDER_COMPLETE.getCode());
////        dto.setType(ChangeTypeEnum.INCREASE.getCode());
//        record.setTotalAmount(Long.valueOf(model.getPayAmount()));
//        record.setChangeAmount(record.getTotalAmount());
//        List<InvoiceChangeRecordDetail> details = new ArrayList<>();
//        for(ChannelRepairDetailDTO repairDetailDTO : model.getRepairDetailDTOList()){
//            details.add(toDetailDTO(repairDetailDTO));
//        }
//        record.setChangeDetails(details);
//        return record;
//    }
//
//    public static InvoiceChangeRecordDetail toDetailDTO(ChannelRepairDetailDTO model) {
//        InvoiceChangeRecordDetail detail = new InvoiceChangeRecordDetail();
//        detail.setProductCode(model.getSpuCode());
//        detail.setProductCount(model.getReceiveNum());
//        detail.setAmount(model.getRepairFeeFinal().longValue());
//        detail.setExpectedInvoiceAmount(0l);
//        return detail;
//    }
//

//
//    public static InvoiceChangeRecordDTO toDTO(TradeOrderChangeDTO model) {
//        InvoiceChangeRecordDTO dto = new InvoiceChangeRecordDTO();
//        dto.setOutBizId(model.getOrderNo());
//        //实收金额，扣除运费
//        BigDecimal amount = FractionUtils.subtract(model.getRealPayAmount(), model.getLogisticsFee());
//        Long amountFen = FractionUtils.conventAmountToFen(amount);
//        dto.setAmount(amountFen);
//
//        List<InvoiceChangeRecordDetailDTO> changeRecordDetailDTOList = new ArrayList<>();
//        for(TradeOrderDetailDTO tradeOrderDetailDTO : model.getOrderDetailDTOList()){
//            changeRecordDetailDTOList.add(toDetailDTO(dto.getType(),tradeOrderDetailDTO));
//        }
//        dto.setChangeRecordDetailDTOList(changeRecordDetailDTOList);
//        return dto;
//    }
//
//    public static InvoiceChangeRecordDetailDTO toDetailDTO(String type,TradeOrderDetailDTO model) {
//        InvoiceChangeRecordDetailDTO dto = new InvoiceChangeRecordDetailDTO();
//        dto.setProductCode(model.getSpuCode());
//        dto.setProductCount(model.getQuantity());
//        dto.setType(type);
//        dto.setAmount(FractionUtils.conventAmountToFen(model.getSettleTotalAmount()));
//        return dto;
//    }
}
