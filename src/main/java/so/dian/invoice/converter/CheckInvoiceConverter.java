package so.dian.invoice.converter;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.MapUtils;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.hr.api.entity.employee.CityDepartmentDTO;
import so.dian.invoice.constant.CheckInvoiceConstants;
import so.dian.invoice.enums.CheckInvoiceStatusEnum;
import so.dian.invoice.enums.InvoiceStatusEnum;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.bo.CheckInvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.dto.CheckInvoiceDTO;
import so.dian.invoice.pojo.entity.CheckInvoiceConclusionDO;
import so.dian.invoice.pojo.entity.CheckInvoiceDO;
import so.dian.invoice.pojo.entity.CheckInvoiceEmployeeDO;
import so.dian.invoice.pojo.entity.InvoiceConfigDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO;
import so.dian.invoice.pojo.param.CheckInvoiceParam;
import so.dian.invoice.pojo.vo.CheckInvoiceCountVO;
import so.dian.invoice.pojo.vo.CheckInvoiceExportVO;
import so.dian.invoice.pojo.vo.CheckInvoiceVO;
import so.dian.invoice.pojo.vo.CheckerDetailsVO;
import so.dian.invoice.pojo.vo.InvoiceCheckDetailsVO;
import so.dian.invoice.pojo.vo.NoCheckInvoiceExportVO;
import so.dian.lvy.pojo.enums.InvoiceIsRealEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:13 AM
 * @Description:
 */
public class CheckInvoiceConverter {

	public static List<CheckInvoiceBO> convertDO2BO(List<CheckInvoiceDO> checkInvoiceDOList) {
		return LocalListUtils.transferList(checkInvoiceDOList, CheckInvoiceConverter::convertDO2BO);
	}
	public static List<CheckInvoiceBO> convertDTO2BO(List<CheckInvoiceDTO> checkInvoiceDTOList) {
		return LocalListUtils.transferList(checkInvoiceDTOList, CheckInvoiceConverter::convertDTO2BO);
	}
	public static List<NoCheckInvoiceExportVO> convertEVO2NEVOBO(List<CheckInvoiceExportVO> checkInvoiceExportVOS) {
		return LocalListUtils.transferList(checkInvoiceExportVOS, CheckInvoiceConverter::convertEVO2NEVOBO);
	}

	public static CheckInvoiceBO convertDO2BO(CheckInvoiceDO checkInvoiceDO) {
	    CheckInvoiceBO checkInvoiceBO = new CheckInvoiceBO();
	    checkInvoiceBO.setId(checkInvoiceDO.getId());
	    checkInvoiceBO.setConclusionId(checkInvoiceDO.getConclusionId());
	    checkInvoiceBO.setRemark(checkInvoiceDO.getRemark());
	    checkInvoiceBO.setCheckInvoiceStatusEnum(LocalEnumUtils.findByCodeWithoutDefault(CheckInvoiceStatusEnum
				.class, checkInvoiceDO.getStatus()));
	    checkInvoiceBO.setChecker(checkInvoiceDO.getChecker());
	    checkInvoiceBO.setCheckTime(checkInvoiceDO.getCheckTime());
	    checkInvoiceBO.setInvoiceId(checkInvoiceDO.getInvoiceId());
	    checkInvoiceBO.setInvoiceNo(checkInvoiceDO.getInvoiceNo());
	    checkInvoiceBO.setInvoiceCode(checkInvoiceDO.getInvoiceCode());
	    checkInvoiceBO.setExpressNo(checkInvoiceDO.getExpressNo());
	    checkInvoiceBO.setGmtCreate(checkInvoiceDO.getGmtCreate());
	    checkInvoiceBO.setGmtUpdate(checkInvoiceDO.getGmtUpdate());
	    checkInvoiceBO.setOperator(checkInvoiceDO.getOperator());
	    return checkInvoiceBO;
	}

	public static CheckInvoiceBO convertDTO2BO(CheckInvoiceDTO checkInvoiceDTO) {
		CheckInvoiceBO checkInvoiceBO = new CheckInvoiceBO();
		checkInvoiceBO.setId(checkInvoiceDTO.getId());
		checkInvoiceBO.setConclusionId(checkInvoiceDTO.getConclusionId());
		checkInvoiceBO.setRemark(checkInvoiceDTO.getRemark());
		checkInvoiceBO.setCheckInvoiceStatusEnum(LocalEnumUtils.findByCodeWithoutDefault(CheckInvoiceStatusEnum
				.class, checkInvoiceDTO.getStatus()));
		checkInvoiceBO.setChecker(checkInvoiceDTO.getChecker());
		checkInvoiceBO.setCheckTime(checkInvoiceDTO.getCheckTime());
		checkInvoiceBO.setInvoiceId(checkInvoiceDTO.getInvoiceId());
		checkInvoiceBO.setInvoiceNo(checkInvoiceDTO.getInvoiceNo());
		checkInvoiceBO.setInvoiceCode(checkInvoiceDTO.getInvoiceCode());
		checkInvoiceBO.setExpressNo(checkInvoiceDTO.getExpressNo());
		checkInvoiceBO.setGmtCreate(checkInvoiceDTO.getGmtCreate());
		checkInvoiceBO.setGmtUpdate(checkInvoiceDTO.getGmtUpdate());
		checkInvoiceBO.setOperator(checkInvoiceDTO.getOperator());
		checkInvoiceBO.setCityCode(checkInvoiceDTO.getCityCode());
		return checkInvoiceBO;
	}

	public static NoCheckInvoiceExportVO convertEVO2NEVOBO(CheckInvoiceExportVO checkInvoiceExportVO) {
		NoCheckInvoiceExportVO noCheckInvoiceExportVO = new NoCheckInvoiceExportVO();
		noCheckInvoiceExportVO.setCheckerName(checkInvoiceExportVO.getCheckerName());
		noCheckInvoiceExportVO.setCreatorNick(checkInvoiceExportVO.getCreatorNick());
		noCheckInvoiceExportVO.setDeductStatusStr(checkInvoiceExportVO.getDeductStatusStr());
		noCheckInvoiceExportVO.setExpressInfoStr(checkInvoiceExportVO.getExpressInfoStr());
		noCheckInvoiceExportVO.setInvoiceCode(checkInvoiceExportVO.getInvoiceCode());
		noCheckInvoiceExportVO.setInvoiceCreateTimeStr(checkInvoiceExportVO.getInvoiceCreateTimeStr());
		noCheckInvoiceExportVO.setInvoiceId(checkInvoiceExportVO.getInvoiceId());
		noCheckInvoiceExportVO.setInvoiceNo(checkInvoiceExportVO.getInvoiceNo());
		noCheckInvoiceExportVO.setPrice(checkInvoiceExportVO.getPrice());
		noCheckInvoiceExportVO.setRegion(checkInvoiceExportVO.getRegion());
		noCheckInvoiceExportVO.setSupplierName(checkInvoiceExportVO.getSupplierName());
		noCheckInvoiceExportVO.setTypeStr(checkInvoiceExportVO.getTypeStr());
		return noCheckInvoiceExportVO;
	}

	/**
	 * 质检列表参数构建
	 * @param checkInvoiceBOList 质检发票列表
	 * @param invoiceBOList 发票列表
	 * @param relationDOList 发票物流信息列表
	 * @param agentEmployeeNickMap 员工信息MAP
	 * @param conclusionDOList 质检结论列表
	 * @param deductionDOList 关联业务单号列表
	 * @param regionCityNameMap 城市code和大区对应的map
	 * @param regionNickMap 大区和大区所对应财务经营花名对应map
	 * @return checkInvoiceVOList
	 */
	public static List<CheckInvoiceVO> convertVO(List<CheckInvoiceBO> checkInvoiceBOList,
			List<InvoiceBO> invoiceBOList, List<InvoiceExpressesRelationDO> relationDOList,
			Map<Integer, String> agentEmployeeNickMap, List<CheckInvoiceConclusionDO> conclusionDOList,
			List<InvoiceDeductionDO> deductionDOList, Map<Integer, CityDepartmentDTO> regionCityNameMap,
			Map<String, String> regionNickMap) {
		List<CheckInvoiceVO> checkInvoiceVOList = Lists.newArrayList();
		//质检结论Map
		Map<Long, String> conclusionMap = conclusionDOList.stream().
				collect(Collectors.toMap(CheckInvoiceConclusionDO::getId, CheckInvoiceConclusionDO::getCode));
		//发票物流信息Map
		Map<Long, InvoiceExpressesRelationDO> relationMap = relationDOList.stream()
				.collect(Collectors.toMap(InvoiceExpressesRelationDO::getInvoiceId, Function.identity()));
		//发票台账Map
		Map<Integer, InvoiceBO> invoiceBOMap =
				invoiceBOList.stream().collect(Collectors.toMap(InvoiceBO::getId, Function.identity()));

		checkInvoiceBOList.forEach(checkInvoiceBO -> {
			CheckInvoiceVO checkInvoiceVO = convertVO(checkInvoiceBO, conclusionMap, agentEmployeeNickMap, relationMap,
					invoiceBOMap,regionCityNameMap,deductionDOList,regionNickMap);
			checkInvoiceVOList.add(checkInvoiceVO);
		});
		return checkInvoiceVOList;
	}

	public static List<CheckInvoiceCountVO> convertCountVO(List<CheckInvoiceBO> checkInvoiceBOList,
			List<CheckInvoiceConclusionDO> conclusionDOList) {
		List<CheckInvoiceBO> alreadyCheckInvoiceList = checkInvoiceBOList.stream()
				.filter(checkInvoiceBO -> Objects.equals(checkInvoiceBO.getCheckInvoiceStatusEnum
						(), CheckInvoiceStatusEnum.ALREADY_CHECK)).collect(Collectors.toList());
		List<CheckInvoiceCountVO> countVOList = Lists.newArrayList();
		Map<Long, String> conclusionMap = conclusionDOList.stream().collect(Collectors.toMap(CheckInvoiceConclusionDO::getId,
				CheckInvoiceConclusionDO::getCode));
		Map<Long, List<CheckInvoiceBO>> checkInvoiceConclusionMap =
				alreadyCheckInvoiceList.stream().collect(Collectors.groupingBy(CheckInvoiceBO::getConclusionId));
		checkInvoiceConclusionMap.forEach((conclusionId, checkInvoiceList)->{
			CheckInvoiceCountVO countVO = new CheckInvoiceCountVO();
			countVO.setConclusionId(conclusionId);
			String conclusionCode = conclusionMap.get(conclusionId);
			countVO.setConclusionCode(conclusionCode);
			countVO.setCheckInvoiceCount(checkInvoiceList.size());
			countVOList.add(countVO);
		});
		return countVOList;
	}

	private static CheckInvoiceVO convertVO(CheckInvoiceBO checkInvoiceBO, Map<Long, String> conclusionMap,
			Map<Integer, String> agentEmployeeNickMap, Map<Long, InvoiceExpressesRelationDO> relationMap,
			Map<Integer, InvoiceBO> invoiceBOMap, Map<Integer, CityDepartmentDTO> regionCityNameMap,
			List<InvoiceDeductionDO> deductionDOList,Map<String, String> regionNickMap) {
		CheckInvoiceVO checkInvoiceVO = new CheckInvoiceVO();
		checkInvoiceVO.setId(checkInvoiceBO.getId());
		checkInvoiceVO.setInvoiceId(checkInvoiceBO.getInvoiceId());
		checkInvoiceVO.setInvoiceCode(checkInvoiceBO.getInvoiceCode());
		checkInvoiceVO.setInvoiceNo(checkInvoiceBO.getInvoiceNo());
		checkInvoiceVO.setCreateTime(checkInvoiceBO.getGmtCreate());
		checkInvoiceVO.setCheckTime(
				checkInvoiceBO.getCheckTime() == null ? 0L : checkInvoiceBO.getCheckTime().getTime());
		if (Objects.nonNull(checkInvoiceBO.getConclusionId())) {
			checkInvoiceVO.setCheckResultStr(conclusionMap.get(checkInvoiceBO.getConclusionId()));
		}
		if (Objects.nonNull(checkInvoiceBO.getOperator())) {
			checkInvoiceVO.setCreatorNick(agentEmployeeNickMap.get(checkInvoiceBO.getOperator().intValue()));
		}

		if(Objects.nonNull(regionCityNameMap.get(checkInvoiceBO.getCityCode()))){
			checkInvoiceVO.setRegion(regionCityNameMap.get(checkInvoiceBO.getCityCode()).getDepartmentName());
		}
		if(checkInvoiceBO.getCheckInvoiceStatusEnum() == CheckInvoiceStatusEnum.NOT_CHECK){
			checkInvoiceVO.setCheckerName(regionNickMap.get(checkInvoiceVO.getRegion()));
		}else{
			if (Objects.nonNull(checkInvoiceBO.getChecker())) {
				checkInvoiceVO.setCheckerName(agentEmployeeNickMap.get(checkInvoiceBO.getChecker().intValue()));
			}
		}
		checkInvoiceVO.setExpressInfo(buildExpressInfo(checkInvoiceBO.getInvoiceId(), relationMap));
		//获取发票信息
		InvoiceBO invoiceBO = invoiceBOMap.get(checkInvoiceBO.getInvoiceId().intValue());
		if (Objects.nonNull(invoiceBO)) {
			checkInvoiceVO.setInvoiceTime(invoiceBO.getGmtCreate().getTime());
			checkInvoiceVO.setIsRealStr(InvoiceIsRealEnum.getDescByCode(invoiceBO.getIsReal()));
			checkInvoiceVO.setPrice(invoiceBO.getPrice());
			checkInvoiceVO.setProcessStatusStr(invoiceBO.getInvoiceProcessStatusEnum().getDesc());
			BigDecimal deductPrice = invoiceBO.getPrice().subtract(invoiceBO.getUsedAmount());
			checkInvoiceVO.setRemainderAmount(deductPrice);
			checkInvoiceVO.setSupplierName(invoiceBO.getSubjectName() == null ? "" : invoiceBO.getSubjectName());
			checkInvoiceVO.setTypeStr(InvoiceTypeEnum.getField(invoiceBO.getInvoiceType()));
			checkInvoiceVO.setBatchNo(invoiceBO.getBatchNo() == null ? "" : invoiceBO.getBatchNo());
			checkInvoiceVO.setDeductStatusStr(InvoiceStatusEnum.getField(invoiceBO.getInvoiceStatus()));
			checkInvoiceVO.setInvoiceCreateTime(invoiceBO.getCreateTime().getTime());
		}

		List<String> billNoList = Lists.newArrayList();
		for (InvoiceDeductionDO invoiceDeductionDO : deductionDOList) {
			if (Objects.equals(checkInvoiceBO.getInvoiceNo(), invoiceDeductionDO.getInvoiceNo()) &&
					Objects.equals(checkInvoiceBO.getInvoiceCode(), invoiceDeductionDO.getInvoiceCode())) {
				billNoList.add(invoiceDeductionDO.getBusinessNo());
			}
		}
		checkInvoiceVO.setBillNoList(billNoList);

		return checkInvoiceVO;
	}



	/**
	 * 构建发票物流信息
	 * @param invoiceId 发票ID
	 * @param relationMap 物流关系Map
	 * @return expressInfo
	 */
	private static CheckInvoiceVO.ExpressInfo buildExpressInfo(Long invoiceId,
			Map<Long, InvoiceExpressesRelationDO> relationMap) {
		CheckInvoiceVO.ExpressInfo expressInfo = new CheckInvoiceVO.ExpressInfo();
		InvoiceExpressesRelationDO invoiceExpressesRelationDO = relationMap.get(invoiceId);
		if (Objects.nonNull(invoiceExpressesRelationDO)) {
			expressInfo.setExpressName(invoiceExpressesRelationDO.getExpressName());
			expressInfo.setExpressNo(invoiceExpressesRelationDO.getExpressTrackingNo());
		}
	    return expressInfo;
	}

	public static CheckInvoiceDO buildUpdateParam2DO(CheckInvoiceParam param, Integer beforeStatus) {
	    CheckInvoiceDO checkInvoiceDO = new CheckInvoiceDO();
	    checkInvoiceDO.setId(param.getId());
	    checkInvoiceDO.setConclusionId(param.getCheckResult());
	    checkInvoiceDO.setRemark(param.getRemark()==null?"":param.getRemark());
	    checkInvoiceDO.setStatus(CheckInvoiceStatusEnum.ALREADY_CHECK.getCode());
	    checkInvoiceDO.setChecker(param.getEmployeeId());
	    checkInvoiceDO.setCheckTime(new Date());
	    checkInvoiceDO.setGmtUpdate(System.currentTimeMillis());
	    checkInvoiceDO.setBeforeStatus(beforeStatus);
	    checkInvoiceDO.setGmtUpdate(System.currentTimeMillis());
	    return checkInvoiceDO;
	}

	public static InvoiceCheckDetailsVO buildDetailsVO(CheckInvoiceDO checkInvoiceDO, String conclusionCode,
			String nickName) {
	    InvoiceCheckDetailsVO invoiceCheckDetailsVO = new InvoiceCheckDetailsVO();
	    invoiceCheckDetailsVO.setCheckResultStr(conclusionCode);
	    invoiceCheckDetailsVO.setCheckTime(checkInvoiceDO.getCheckTime().getTime());
	    invoiceCheckDetailsVO.setCheckerName(nickName);
	    invoiceCheckDetailsVO.setRemark(checkInvoiceDO.getRemark());
	    return invoiceCheckDetailsVO;
	}

	/**
	 * 发票质检配置数据组装
	 * @param checkInvoiceEmployeeDOList 质检人员配置列表
	 * @param employeeNickMap 员工信息Map
	 * @return invoiceCheckConfigVO
	 */
	public static List<CheckerDetailsVO> convertCheckerDO2VO(List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList,
			Map<Integer, String> employeeNickMap) {
		List<CheckerDetailsVO> voList = Lists.newArrayList();
		Map<Long, List<CheckInvoiceEmployeeDO>> checkInvoiceEmployeeMap =
				checkInvoiceEmployeeDOList.stream().collect(Collectors.groupingBy(CheckInvoiceEmployeeDO::getUserId));
		checkInvoiceEmployeeMap.forEach((employeeId, configList)->{
			voList.add(convertCheckerDO2VO(configList, employeeId, employeeNickMap));
		});
		return voList;
	}

	private static CheckerDetailsVO convertCheckerDO2VO(List<CheckInvoiceEmployeeDO> doList, Long employeeId,
			Map<Integer, String> employeeNickMap) {
	    CheckerDetailsVO checkerDetailsVO = new CheckerDetailsVO();
	    checkerDetailsVO.setChecker(employeeId);
		String nickName = employeeNickMap.get(employeeId.intValue());
		checkerDetailsVO.setCheckerName(nickName);
		List<String> regionList = doList.stream().map(CheckInvoiceEmployeeDO::getRegion).collect(Collectors.toList());
		checkerDetailsVO.setRegionList(regionList);
	    return checkerDetailsVO;

	}



	public static List<CheckInvoiceDO> convertCheckInvoiceDO(List<InvoiceBO> invoiceBOList, InvoiceConfigDO invoiceConfigDO,
			List<InvoiceExpressesRelationDO> relationDOList) {
		List<CheckInvoiceDO> checkInvoiceDOList = Lists.newLinkedList();
		Map<Integer, List<InvoiceBO>> creatorInvoiceMap =
				invoiceBOList.stream().collect(Collectors.groupingBy(InvoiceBO::getCreator));
		BigDecimal rate;
		if (Objects.isNull(invoiceConfigDO)) {
			rate = CheckInvoiceConstants.DEFAULT_CHECK_INVOICE_CHECK_RATE;
		} else {
			rate = new BigDecimal(invoiceConfigDO.getValue()).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
		}
		creatorInvoiceMap.forEach((creator, invoiceList)->{
			//随机抽取需要质检的发票
			Integer randomNum = rate.multiply(BigDecimal.valueOf(invoiceList.size()))
					.setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
			Collections.shuffle(invoiceList);
			List<InvoiceBO> invoiceBOS = invoiceList.subList(0, randomNum);
			checkInvoiceDOList.addAll(buildCheckInvoiceDO(invoiceBOS, relationDOList));
		});
		return checkInvoiceDOList;
	}

	private static List<CheckInvoiceDO> buildCheckInvoiceDO(List<InvoiceBO> invoiceBOS,
			List<InvoiceExpressesRelationDO> relationDOList) {
		List<CheckInvoiceDO> checkInvoiceDOList = Lists.newLinkedList();
		Map<Long, InvoiceExpressesRelationDO> relationDOMap = relationDOList.stream()
				.collect(Collectors.toMap(InvoiceExpressesRelationDO::getInvoiceId, Function.identity()));
		invoiceBOS.forEach(invoiceBO -> {
			long now = System.currentTimeMillis();
			CheckInvoiceDO checkInvoiceDO = new CheckInvoiceDO();
			checkInvoiceDO.setGmtCreate(now);
			checkInvoiceDO.setGmtUpdate(now);
			checkInvoiceDO.setStatus(CheckInvoiceStatusEnum.NOT_CHECK.getCode());
			checkInvoiceDO.setInvoiceId(invoiceBO.getId().longValue());
			checkInvoiceDO.setInvoiceNo(invoiceBO.getInvoiceNo());
			checkInvoiceDO.setInvoiceCode(invoiceBO.getInvoiceCode() == null ? "" : invoiceBO.getInvoiceCode());
			if (MapUtils.isNotEmpty(relationDOMap)) {
				InvoiceExpressesRelationDO relationDO = relationDOMap.get(invoiceBO.getId().longValue());
				checkInvoiceDO.setExpressNo(Objects.nonNull(relationDO) ? relationDO.getExpressTrackingNo() : "");
			}
			checkInvoiceDO.setOperator(invoiceBO.getCreator().longValue());
			checkInvoiceDOList.add(checkInvoiceDO);
		});
		return checkInvoiceDOList;
	}



	public static List<CheckInvoiceExportVO> convert(List<CheckInvoiceVO> voList) {
		List<CheckInvoiceExportVO> invoiceExportVOList = Lists.newArrayList();
		voList.forEach(item -> {
			CheckInvoiceExportVO checkInvoiceExportVO = new CheckInvoiceExportVO();
			checkInvoiceExportVO.setId(item.getId());
			checkInvoiceExportVO.setInvoiceId(item.getInvoiceId());
			checkInvoiceExportVO.setInvoiceCode(item.getInvoiceCode());
			checkInvoiceExportVO.setInvoiceNo(item.getInvoiceNo());
			if (Objects.nonNull(item.getInvoiceTime())) {
				checkInvoiceExportVO.setInvoiceTimeStr(DateUtil.format(new Date(item.getInvoiceTime()), "yyyy-MM-dd HH:mm:ss"));
			}
			if (Objects.nonNull(item.getInvoiceCreateTime())) {
				checkInvoiceExportVO.setInvoiceCreateTimeStr(DateUtil.format(new Date(item.getInvoiceCreateTime()),
						"yyyy-MM-dd HH:mm:ss"));
			}
			if (Objects.nonNull(item.getCreateTime())) {
				checkInvoiceExportVO.setCreateTimeStr(DateUtil.format(new Date(item.getCreateTime()), "yyyy-MM-dd HH:mm:ss"));
			}
			checkInvoiceExportVO.setIsRealStr(item.getIsRealStr());
			checkInvoiceExportVO.setPrice(item.getPrice());
			checkInvoiceExportVO.setProcessStatusStr(item.getProcessStatusStr());
			checkInvoiceExportVO.setRemainderAmount(item.getRemainderAmount());
			checkInvoiceExportVO.setSupplierName(item.getSupplierName());
			checkInvoiceExportVO.setTypeStr(item.getTypeStr());
			checkInvoiceExportVO.setBatchNo(item.getBatchNo());
			checkInvoiceExportVO.setBillNoList(item.getBillNoList());
			checkInvoiceExportVO.setCheckResultStr(item.getCheckResultStr());
			if (Objects.nonNull(item.getCheckTime())) {
				checkInvoiceExportVO.setCheckTimeStr(DateUtil.format(new Date(item.getCheckTime()), "yyyy-MM-dd HH:mm:ss"));
			}
			checkInvoiceExportVO.setCheckerName(item.getCheckerName());
			checkInvoiceExportVO.setCreatorNick(item.getCreatorNick());
			checkInvoiceExportVO.setDeductStatusStr(item.getDeductStatusStr());
			Map content = Maps.newHashMap();
			content.put("快递名称",
					item.getExpressInfo().getExpressName() == null ? "" : item.getExpressInfo().getExpressName());
			content.put("快递单号",
					item.getExpressInfo().getExpressNo() == null ? "" : item.getExpressInfo().getExpressNo());
			checkInvoiceExportVO.setExpressInfoStr(JSON.toJSONString(content));
			checkInvoiceExportVO.setRegion(item.getRegion());
			invoiceExportVOList.add(checkInvoiceExportVO);
		});
		return invoiceExportVOList;
	}

}
