package so.dian.invoice.converter;

import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import so.dian.commons.eden.util.DateUtils;
import so.dian.invoice.enums.AgentTypeEnum;
import so.dian.invoice.enums.BizTypeEnum;
import so.dian.invoice.enums.InvoiceManageStatusEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceManage;
import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceManageDetail;
import so.dian.invoice.pojo.entity.InvoiceManageDO;
import so.dian.invoice.pojo.entity.InvoiceManageDetailDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageDetailExcel;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageExcel;
import so.dian.invoice.util.FractionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: yuechuan
 * @Date: 2015/3/13 9:32 PM
 * @Description:
 */

@Slf4j
public class InvoiceManageConverter {

    public static List<InvoiceManage> build(List<InvoiceManageDO> dos, List<InvoiceManageDetailDO> detailDOS, List<InvoiceRequestRecordDO> requestRecordDOS, List<InvoiceRequestRecordDetailDO> requestRecordDetailDOS) {

        Map<Long, List<InvoiceManageDetailDO>> detailsMap = detailDOS.stream().collect(Collectors.groupingBy(InvoiceManageDetailDO::getManageId));
        Map<Long, List<InvoiceRequestRecordDO>> requestRecordMap = requestRecordDOS.stream().collect(Collectors.groupingBy(InvoiceRequestRecordDO::getManageId));

        List<InvoiceManage> invoiceManages = new ArrayList<>();
        for (InvoiceManageDO manageDO : dos) {
            //开票管理详情
            List<InvoiceManageDetailDO> invoiceManageDetailDOS = detailsMap.get(manageDO.getId());
            //开票管理对应的开票申请记录
            List<InvoiceRequestRecordDO> invoiceRequestRecordDOS = requestRecordMap.get(manageDO.getId());
            //构建开票管理DTO对象并计算
            InvoiceManage invoiceManage = InvoiceManage.build(manageDO, invoiceManageDetailDOS, invoiceRequestRecordDOS, requestRecordDetailDOS);
            invoiceManages.add(invoiceManage);
        }
        return invoiceManages;
    }

    public static List<InvoiceManageDetail> build(List<InvoiceManageDetailDO> detailDOS , List<InvoiceRequestRecordDetailDO> requestRecordDetailDOS) {

        Map<Long, List<InvoiceRequestRecordDetailDO>> requestRecordDetailMap = requestRecordDetailDOS.stream().collect(Collectors.groupingBy(InvoiceRequestRecordDetailDO::getManageDetailId));

        List<InvoiceManageDetail> invoiceManages = new ArrayList<>();
        for (InvoiceManageDetailDO manageDetailDO : detailDOS) {
            //开票申请详情，产品维度
            List<InvoiceRequestRecordDetailDO> recordDetailDOS = requestRecordDetailMap.get(manageDetailDO.getId());
            //构建开票管理DTO对象
            InvoiceManageDetail invoiceManageDetail = InvoiceManageDetail.build(manageDetailDO, recordDetailDOS);
            invoiceManages.add(invoiceManageDetail);
        }
        return invoiceManages;
    }



    public static List<ExportInvoiceManageExcel> toExcelBean(List<InvoiceManageDTO> aggs){
        List<ExportInvoiceManageExcel> excels = new ArrayList<>();
        for(InvoiceManageDTO agg : aggs) {
            ExportInvoiceManageExcel manageExcel = toExcelBean(agg);
            excels.add(manageExcel);
        }

        return excels;
    }

    public static List<ExportInvoiceManageDetailExcel> toDetailExcelBean(List<InvoiceManageDTO> manageDTOS){
        List<ExportInvoiceManageDetailExcel> excels = new ArrayList<>();
        for(InvoiceManageDTO agg : manageDTOS) {
            List<ExportInvoiceManageDetailExcel> list = toDetailExcelBean(agg);
            excels.addAll(list);
        }

        return excels;
    }

    public static List<ExportInvoiceManageDetailExcel> toDetailExcelBean(InvoiceManageDTO agg){
        List<ExportInvoiceManageDetailExcel> list = new ArrayList<>();
        for(InvoiceManageDetailDTO detailDTO : agg.getManageDetailDTOS()) {
            ExportInvoiceManageDetailExcel excel = new ExportInvoiceManageDetailExcel();
            ExportInvoiceManageExcel manageExcel = toExcelBean(agg);
            BeanUtils.copyProperties(manageExcel, excel);
            excel.setProductName(detailDTO.getProductName());
            excel.setProductCount(detailDTO.getProductCount());
            excel.setProductTotalAmount(FractionUtils.conventStrAmountInFen(detailDTO.getAmount()));
            excel.setProductExpectedInvoiceAmount(FractionUtils.conventStrAmountInFen(detailDTO.getExpectedInvoiceAmount()));
            excel.setProductOngoingInvoiceAmount(FractionUtils.conventStrAmountInFen(detailDTO.getOngoingInvoiceAmount()));
            excel.setProductCompletedInvoiceAmount(FractionUtils.conventStrAmountInFen(detailDTO.getCompletedInvoiceAmount()));
            excel.setProductPendingInvoiceAmount(FractionUtils.conventStrAmountInFen(detailDTO.getPendingInvoiceAmount()));
            list.add(excel);
        }
        return list;
    }


    private static ExportInvoiceManageExcel toExcelBean(InvoiceManageDTO agg){
        ExportInvoiceManageExcel manageExcel = new ExportInvoiceManageExcel();
        manageExcel.setBizNo(agg.getBizNo());

        BizTypeEnum bizTypeEnum = BizTypeEnum.getByCode(agg.getBizType());
        if (Objects.nonNull(bizTypeEnum)) {
            manageExcel.setBizType(bizTypeEnum.getDesc());
        }
        AgentTypeEnum agentTypeEnum = AgentTypeEnum.getAgentType(agg.getSubjectType());
        if (Objects.nonNull(agentTypeEnum)) {
            manageExcel.setSubjectType(agentTypeEnum.getName());
        }
        manageExcel.setSubjectName(agg.getSubjectName());
        manageExcel.setSubjectId(agg.getSubjectId());
        manageExcel.setTotalAmount(FractionUtils.conventStrAmountInFen(agg.getTotalAmount()));
        manageExcel.setExpectedInvoiceTotalAmount(FractionUtils.conventStrAmountInFen(agg.getExpectedInvoiceAmount()));
        manageExcel.setOngoingInvoiceTotalAmount(FractionUtils.conventStrAmountInFen(agg.getOngoingInvoiceAmount()));
        manageExcel.setCompletedInvoiceTotalAmount(FractionUtils.conventStrAmountInFen(agg.getCompletedInvoiceAmount()));
        manageExcel.setPendingInvoiceTotalAmount(FractionUtils.conventStrAmountInFen(agg.getPendingInvoiceAmount()));
        InvoiceManageStatusEnum statusEnum = InvoiceManageStatusEnum.from(agg.getStatus());
        if (Objects.nonNull(statusEnum)) {
            manageExcel.setStatus(statusEnum.getDesc());
        }
        if (agg.getBizCreateTime() != null) {
            DateTime dateTime = DateUtils.date(agg.getBizCreateTime());
            manageExcel.setBizCreateTime(DateUtils.formatDateTime(dateTime));
        }
        if (agg.getPaymentTime() != null) {
            DateTime dateTime = DateUtils.date(agg.getPaymentTime());
            manageExcel.setPaymentTime(DateUtils.formatDateTime(dateTime));
        }
        return manageExcel;
    }

}
