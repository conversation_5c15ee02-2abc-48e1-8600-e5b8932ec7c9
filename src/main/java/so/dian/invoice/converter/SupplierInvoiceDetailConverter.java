package so.dian.invoice.converter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.invoice.enums.SupplierInvoiceBillNoEnum;
import so.dian.invoice.enums.SupplierInvoiceStatusEnum;
import so.dian.invoice.enums.SupplierInvoiceTypeEnum;
import so.dian.invoice.pojo.dto.BillNoDTO;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailBillNoDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.param.SupplierInvoiceImportParam;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailExcelVO;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailPageVO;
import so.dian.invoice.util.LocalDateUtil;
import so.dian.jinyun.client.pojo.response.scm.PurchaseOrderDetailRsp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static so.dian.invoice.util.LocalDateUtil.yyyy_MM_dd;

/**
 * 供应商发票明细表
 *
 * <AUTHOR>
 * @date 2021-03-17 17:03:12
 */
@Slf4j
public class SupplierInvoiceDetailConverter {

    public static List<SupplierInvoiceDetailPageVO> toPageRspList(
            List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList, Map<Integer, Map<Integer, String>> detailIdBillNoListMap) {

        List<SupplierInvoiceDetailPageVO> pageVOList = new ArrayList<>(supplierInvoiceDetailDOList.size());

        for (SupplierInvoiceDetailDO supplierInvoiceDetailDO : supplierInvoiceDetailDOList) {
            SupplierInvoiceDetailPageVO pageVO = new SupplierInvoiceDetailPageVO();

            pageVO.setId(supplierInvoiceDetailDO.getId());
            SupplierInvoiceTypeEnum invoiceTypeEnum = LocalEnumUtils.findByCode(SupplierInvoiceTypeEnum.class, supplierInvoiceDetailDO.getInvoiceType());
            if (Objects.nonNull(invoiceTypeEnum)) {
                pageVO.setInvoiceTypeStr(invoiceTypeEnum.getDesc());
            }
            pageVO.setInvoiceCode(supplierInvoiceDetailDO.getInvoiceCode());
            pageVO.setInvoiceNo(supplierInvoiceDetailDO.getInvoiceNo());
            pageVO.setInvoiceDate(LocalDateUtil.localDateToStr(supplierInvoiceDetailDO.getInvoiceDate(),yyyy_MM_dd));
            pageVO.setSellerName(supplierInvoiceDetailDO.getSellerName());
            pageVO.setSellerTaxId(supplierInvoiceDetailDO.getSellerTaxId());
            pageVO.setCheckCode(supplierInvoiceDetailDO.getCheckCode());
            pageVO.setBuyer(supplierInvoiceDetailDO.getBuyer());
            pageVO.setBuyerTaxId(supplierInvoiceDetailDO.getBuyerTaxId());
            pageVO.setMaterialName(supplierInvoiceDetailDO.getMaterialName());
            pageVO.setMaterialSpec(supplierInvoiceDetailDO.getMaterialSpec());
            pageVO.setUnit(supplierInvoiceDetailDO.getUnit());
            pageVO.setQuantity(supplierInvoiceDetailDO.getQuantity());
            if (Objects.nonNull(supplierInvoiceDetailDO.getUnitPrice())) {
                pageVO.setUnitPriceStr(supplierInvoiceDetailDO.getUnitPrice().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getRawPrice())) {
                pageVO.setRawPriceStr(supplierInvoiceDetailDO.getRawPrice().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getTaxRate())) {
                pageVO.setTaxRateStr(supplierInvoiceDetailDO.getTaxRate().stripTrailingZeros().toPlainString()+"%");
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getTax())) {
                pageVO.setTaxStr(supplierInvoiceDetailDO.getTax().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getPrice())) {
                pageVO.setPriceStr(supplierInvoiceDetailDO.getPrice().stripTrailingZeros().toPlainString());
            }
            SupplierInvoiceStatusEnum statusEnum = LocalEnumUtils.findByCode(SupplierInvoiceStatusEnum.class, supplierInvoiceDetailDO.getStatus());
            if (Objects.nonNull(statusEnum)) {
                pageVO.setStatusStr(statusEnum.getDesc());
            }

            Map<Integer, String> billTypeNoMap = detailIdBillNoListMap.get(supplierInvoiceDetailDO.getId());

            pageVO.setPurchaseBatchBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.PURCHASE_BATCH.getCode()));
            pageVO.setPurchaseOrderBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.PURCHASE_ORDER.getCode()));
            pageVO.setVerifyBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.VERIFY.getCode()));

            pageVOList.add(pageVO);
        }

        return pageVOList;
    }

    public static List<SupplierInvoiceDetailExcelVO> toExcel(
            List<SupplierInvoiceDetailDO> invoiceDetailDOList, Map<Integer, Map<Integer, String>> detailIdBillNoListMap) {

        List<SupplierInvoiceDetailExcelVO> excelVOList = new ArrayList<>(invoiceDetailDOList.size());

        for (SupplierInvoiceDetailDO supplierInvoiceDetailDO : invoiceDetailDOList) {
            SupplierInvoiceDetailExcelVO excelVO = new SupplierInvoiceDetailExcelVO();

            SupplierInvoiceTypeEnum invoiceTypeEnum = LocalEnumUtils.findByCode(SupplierInvoiceTypeEnum.class, supplierInvoiceDetailDO.getInvoiceType());
            if (Objects.nonNull(invoiceTypeEnum)) {
                excelVO.setInvoiceTypeStr(invoiceTypeEnum.getDesc());
            }
            excelVO.setInvoiceCode(supplierInvoiceDetailDO.getInvoiceCode());
            excelVO.setInvoiceNo(supplierInvoiceDetailDO.getInvoiceNo());
            if(Objects.nonNull(supplierInvoiceDetailDO.getInvoiceDate())){
                excelVO.setInvoiceDateStr(supplierInvoiceDetailDO.getInvoiceDate().toString());
            }

            excelVO.setSellerName(supplierInvoiceDetailDO.getSellerName());
            excelVO.setSellerTaxId(supplierInvoiceDetailDO.getSellerTaxId());
            excelVO.setCheckCode(supplierInvoiceDetailDO.getCheckCode());
            excelVO.setBuyer(supplierInvoiceDetailDO.getBuyer());
            excelVO.setBuyerTaxId(supplierInvoiceDetailDO.getBuyerTaxId());
            excelVO.setMaterialName(supplierInvoiceDetailDO.getMaterialName());
            excelVO.setMaterialSpec(supplierInvoiceDetailDO.getMaterialSpec());
            excelVO.setUnit(supplierInvoiceDetailDO.getUnit());
            excelVO.setQuantity(supplierInvoiceDetailDO.getQuantity());
            if (Objects.nonNull(supplierInvoiceDetailDO.getUnitPrice())) {
                excelVO.setUnitPriceStr(supplierInvoiceDetailDO.getUnitPrice().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getRawPrice())) {
                excelVO.setRawPriceStr(supplierInvoiceDetailDO.getRawPrice().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getTaxRate())) {
                excelVO.setTaxRateStr(supplierInvoiceDetailDO.getTaxRate().stripTrailingZeros().toPlainString()+ "%");
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getTax())) {
                excelVO.setTaxStr(supplierInvoiceDetailDO.getTax().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDetailDO.getPrice())) {
                excelVO.setPriceStr(supplierInvoiceDetailDO.getPrice().stripTrailingZeros().toPlainString());
            }

            Map<Integer, String> billTypeNoMap = detailIdBillNoListMap.get(supplierInvoiceDetailDO.getId());

            excelVO.setPurchaseBatchBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.PURCHASE_BATCH.getCode()));
            excelVO.setPurchaseOrderBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.PURCHASE_ORDER.getCode()));
            excelVO.setVerifyBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.VERIFY.getCode()));

            excelVOList.add(excelVO);
        }

        return excelVOList;
    }

    /**
     * 采购批次、采购订单、对账单号转换
     */
    public static List<BillNoDTO> toBillNoList(SupplierInvoicePageParam param) {
        List<BillNoDTO> billNoList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getPurchaseBatchBillNo())) {
            BillNoDTO billNoDO = new BillNoDTO();
            billNoDO.setBillType(SupplierInvoiceBillNoEnum.PURCHASE_BATCH.getCode());
            billNoDO.setBillNo(param.getPurchaseBatchBillNo());
            billNoList.add(billNoDO);
        }
        if (StringUtils.isNotBlank(param.getPurchaseOrderBillNo())) {
            BillNoDTO billNoDO = new BillNoDTO();
            billNoDO.setBillType(SupplierInvoiceBillNoEnum.PURCHASE_ORDER.getCode());
            billNoDO.setBillNo(param.getPurchaseOrderBillNo());
            billNoList.add(billNoDO);
        }
        if (StringUtils.isNotBlank(param.getVerifyBillNo())) {
            BillNoDTO billNoDO = new BillNoDTO();
            billNoDO.setBillType(SupplierInvoiceBillNoEnum.VERIFY.getCode());
            billNoDO.setBillNo(param.getVerifyBillNo());
            billNoList.add(billNoDO);
        }
        return billNoList;
    }

    public static List<SupplierInvoiceDetailBillNoDO> toBillNoDOList(SupplierInvoiceImportParam param) {
        List<SupplierInvoiceDetailBillNoDO> billNoList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getPurchaseBatchBillNo())) {
            SupplierInvoiceDetailBillNoDO billNoDO = new SupplierInvoiceDetailBillNoDO();
            billNoDO.setBillType(SupplierInvoiceBillNoEnum.PURCHASE_BATCH.getCode());
            billNoDO.setBillNo(param.getPurchaseBatchBillNo());
            billNoList.add(billNoDO);
        }
        if (StringUtils.isNotBlank(param.getPurchaseOrderBillNo())) {
            SupplierInvoiceDetailBillNoDO billNoDO = new SupplierInvoiceDetailBillNoDO();
            billNoDO.setBillType(SupplierInvoiceBillNoEnum.PURCHASE_ORDER.getCode());
            billNoDO.setBillNo(param.getPurchaseOrderBillNo());
            billNoList.add(billNoDO);
        }
        if (StringUtils.isNotBlank(param.getVerifyBillNo())) {
            SupplierInvoiceDetailBillNoDO billNoDO = new SupplierInvoiceDetailBillNoDO();
            billNoDO.setBillType(SupplierInvoiceBillNoEnum.VERIFY.getCode());
            billNoDO.setBillNo(param.getVerifyBillNo());
            billNoList.add(billNoDO);
        }
        return billNoList;
    }

    public static SupplierInvoiceDetailDO toDO(SupplierInvoiceImportParam param, PurchaseOrderDetailRsp purchaseOrderDetailRsp) {
        SupplierInvoiceDetailDO invoiceDetailDO = new SupplierInvoiceDetailDO();

        invoiceDetailDO.setInvoiceNo(param.getInvoiceNo());
        invoiceDetailDO.setInvoiceCode(param.getInvoiceCode() == null ? "" : param.getInvoiceCode());
        invoiceDetailDO.setCheckCode(param.getCheckCode() == null ? "" : param.getCheckCode());

        SupplierInvoiceTypeEnum invoiceTypeEnum = LocalEnumUtils.findByDesc(SupplierInvoiceTypeEnum.class, param.getInvoiceTypeStr());
        invoiceDetailDO.setInvoiceType(invoiceTypeEnum.getCode());

        LocalDate invoiceDate = null;
        try {
            invoiceDate = LocalDateUtil.parseToDate(param.getInvoiceDateStr(), LocalDateUtil.yyyy_slash_MM_slash_dd);
        } catch (Exception e) {
            log.warn("供应商发票导入日期yyyy/MM/dd解析错误警告,invoiceDateStr:{}", param.getInvoiceDateStr(), e);
        }
        if (Objects.isNull(invoiceDate)) {
            try {
                invoiceDate = LocalDateUtil.parseToDate(param.getInvoiceDateStr(), LocalDateUtil.yyyy_MM_dd);
            } catch (Exception e) {
                log.warn("供应商发票导入日期yyyy_MM_dd解析错误警告,invoiceDateStr:{}", param.getInvoiceDateStr(), e);
            }
        }
        invoiceDetailDO.setInvoiceDate(invoiceDate);

        invoiceDetailDO.setSellerName(param.getSellerName());
        invoiceDetailDO.setSellerTaxId(param.getSellerTaxId());
        invoiceDetailDO.setBuyer(param.getBuyer());
        invoiceDetailDO.setBuyerTaxId(param.getBuyerTaxId());

        String taxRateStr = param.getTaxRateStr().replace("%", "");
        BigDecimal taxRate = new BigDecimal(taxRateStr)
                .setScale(18, RoundingMode.DOWN);
        BigDecimal rawPrice = new BigDecimal(param.getRawPriceStr())
                .setScale(4, RoundingMode.DOWN);
        BigDecimal tax = new BigDecimal(param.getTaxStr())
                .setScale(4, RoundingMode.DOWN);
        BigDecimal price = new BigDecimal(param.getPriceStr())
                .setScale(4, RoundingMode.DOWN);

        invoiceDetailDO.setRawPrice(rawPrice);
        invoiceDetailDO.setTax(tax);
        invoiceDetailDO.setTaxRate(taxRate);
        invoiceDetailDO.setPrice(price);

        invoiceDetailDO.setStatus(SupplierInvoiceStatusEnum.INITIALIZE.getCode());
        invoiceDetailDO.setMaterialName(param.getMaterialName());
        invoiceDetailDO.setMaterialSpec(StringUtils.defaultString(param.getMaterialSpec()));
        invoiceDetailDO.setUnit(StringUtils.defaultString(param.getUnit()));
        BigDecimal unitPrice = new BigDecimal(param.getUnitPriceStr())
                .setScale(18, RoundingMode.DOWN);
        invoiceDetailDO.setUnitPrice(unitPrice);
        invoiceDetailDO.setQuantity(Integer.parseInt(param.getQuantity()));
        invoiceDetailDO.setSupplierNo(purchaseOrderDetailRsp.getSupplierNo());

        invoiceDetailDO.setDetailBillNoDOList(SupplierInvoiceDetailConverter.toBillNoDOList(param));
        return invoiceDetailDO;
    }


    public static List<InvoiceDetailDO> toInvoiceDetailList(List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList) {
        if(CollectionUtils.isEmpty(supplierInvoiceDetailDOList)){
            return null;
        }

        List<InvoiceDetailDO> invoiceDetailDOList = new ArrayList<>(supplierInvoiceDetailDOList.size());
        for (SupplierInvoiceDetailDO supplierInvoiceDetailDO : supplierInvoiceDetailDOList) {
            InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();
            invoiceDetailDO.setSupplierInvoiceDetailId(supplierInvoiceDetailDO.getId());
            invoiceDetailDO.setInvoiceNo(supplierInvoiceDetailDO.getInvoiceNo());
            invoiceDetailDO.setInvoiceCode(supplierInvoiceDetailDO.getInvoiceCode());
            invoiceDetailDO.setMaterialName(supplierInvoiceDetailDO.getMaterialName());
            invoiceDetailDO.setMaterialSpec(supplierInvoiceDetailDO.getMaterialSpec());
            invoiceDetailDO.setUnit(supplierInvoiceDetailDO.getUnit());
            invoiceDetailDO.setUnitPrice(supplierInvoiceDetailDO.getUnitPrice());
            invoiceDetailDO.setQuantity(supplierInvoiceDetailDO.getQuantity());
            invoiceDetailDO.setRawPrice(supplierInvoiceDetailDO.getRawPrice());
            invoiceDetailDO.setTaxRate(supplierInvoiceDetailDO.getTaxRate());
            invoiceDetailDO.setTaxPrice(supplierInvoiceDetailDO.getTax());
            invoiceDetailDO.setPrice(supplierInvoiceDetailDO.getPrice());
            invoiceDetailDO.setGmtCreate(new Date());
            invoiceDetailDO.setGmtModified(new Date());
            invoiceDetailDO.setIsDelete(0);

            invoiceDetailDOList.add(invoiceDetailDO);
        }

        return invoiceDetailDOList;
    }

}
