package so.dian.invoice.converter;

import java.util.Date;
import so.dian.invoice.pojo.entity.InvoiceConfigDO;
import so.dian.invoice.pojo.param.ConfigParam;

/**
 * @Author: jiaoge
 * @Date: 2019/12/26 2:56 PM
 * @Description:
 */
public class InvoiceConfigConverter {

	public static InvoiceConfigDO buildDO(String key, String value) {
		Date now = new Date();
	    InvoiceConfigDO invoiceConfigDO = new InvoiceConfigDO();
	    invoiceConfigDO.setKey(key);
	    invoiceConfigDO.setValue(value);
	    invoiceConfigDO.setStatus(1);
	    invoiceConfigDO.setVersion(1);
	    invoiceConfigDO.setCreateTime(now);
	    invoiceConfigDO.setUpdateTime(now);
	    return invoiceConfigDO;
	}

	public static InvoiceConfigDO buildParam2DO(ConfigParam param) {
		Date now = new Date();
	    InvoiceConfigDO invoiceConfigDO = new InvoiceConfigDO();
	    invoiceConfigDO.setKey(param.getKey());
	    invoiceConfigDO.setValue(param.getValue());
	    invoiceConfigDO.setStatus(param.getStatus());
	    invoiceConfigDO.setVersion(param.getVersion());
	    invoiceConfigDO.setCreateTime(now);
	    invoiceConfigDO.setUpdateTime(now);
	    return invoiceConfigDO;
	}
}
