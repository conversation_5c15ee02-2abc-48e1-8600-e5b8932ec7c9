package so.dian.invoice.converter;

import so.dian.commons.eden.enums.DeletedEnum;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.invoice.pojo.bo.InvoiceExpressesRelationBO;
import so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO;
import so.dian.invoice.pojo.vo.InvoiceExpressesRelationVO;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class InvoiceExpressesRelationConverter {


    public static List<InvoiceExpressesRelationBO> convertDO2BOFromList(List<InvoiceExpressesRelationDO> doList) {
        return LocalListUtils.transferList(doList, invoiceExpressesRelationDO -> convertDO2BO(invoiceExpressesRelationDO));
    }

    public static List<InvoiceExpressesRelationVO> convertBO2VOFromList(List<InvoiceExpressesRelationBO> boList) {
        return LocalListUtils.transferList(boList, invoiceExpressesRelationBO -> convertBO2VO(invoiceExpressesRelationBO));
    }

    private static InvoiceExpressesRelationVO buildInvoiceExpressesRelationVO(InvoiceExpressesRelationBO invoiceExpressesRelationBO) {
        if (Objects.isNull(invoiceExpressesRelationBO)) {
            return null;
        }
        InvoiceExpressesRelationVO invoiceExpressesRelationVO = InvoiceExpressesRelationVO.builder()
                .build();
        return invoiceExpressesRelationVO;
    }

    public static InvoiceExpressesRelationVO convertBO2VO(InvoiceExpressesRelationBO bo) {
        if (Objects.isNull(bo)) {
            return null;
        }
        InvoiceExpressesRelationVO invoiceExpressesRelationVO = InvoiceExpressesRelationVO.builder()
                .expressName(bo.getExpressName())
                .expressTrackingNo(bo.getExpressTrackingNo())
                .updateTime(bo.getUpdateTime())
                .operatorId(bo.getOperatorId())
                .operatorName(bo.getOperatorName())
                .expressImg(bo.getExpressImg())
                .serialNo(bo.getSerialNo())
                .invoiceNumbers(bo.getInvoiceNumbers())
                .build();
        return invoiceExpressesRelationVO;
    }

    public static InvoiceExpressesRelationBO convertDO2BO(InvoiceExpressesRelationDO invoiceExpressesRelationDO) {
        if (Objects.isNull(invoiceExpressesRelationDO)) {
            return null;
        }
        InvoiceExpressesRelationBO invoiceExpressesRelationBO = InvoiceExpressesRelationBO.builder()
                .id(invoiceExpressesRelationDO.getId())
                .invoiceId(invoiceExpressesRelationDO.getInvoiceId())
                .expressId(invoiceExpressesRelationDO.getExpressId())
                .expressName(invoiceExpressesRelationDO.getExpressName())
                .expressTrackingNo(invoiceExpressesRelationDO.getExpressTrackingNo())
                .createTime(invoiceExpressesRelationDO.getCreateTime())
                .updateTime(invoiceExpressesRelationDO.getUpdateTime())
                .deleted(LocalEnumUtils.findByCode(DeletedEnum.class, invoiceExpressesRelationDO.getDeleted()))
                .operatorId(invoiceExpressesRelationDO.getOperatorId())
                .operatorName(invoiceExpressesRelationDO.getOperatorName())
                .expressImg(invoiceExpressesRelationDO.getExpressImg())
                .serialNo(invoiceExpressesRelationDO.getSerialNo())
                .invoiceNumbers(invoiceExpressesRelationDO.getInvoiceNumbers())
                .build();
        return invoiceExpressesRelationBO;
    }
}
