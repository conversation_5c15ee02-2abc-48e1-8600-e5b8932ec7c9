package so.dian.invoice.converter;

import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.invoice.pojo.bo.InvoiceDetailBO;
import so.dian.invoice.pojo.dto.InvoiceDetailDto;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;

/**
 * @Author: jiaoge
 * @Date: 2019/9/16 11:32 AM
 * @Description:
 */
public class InvoiceDetailConverter {

	public static List<InvoiceDetailBO> convertDO2BO(List<InvoiceDetailDO> invoiceDetailDOList) {
		return LocalListUtils.transferList(invoiceDetailDOList, InvoiceDetailConverter::convertDO2BO);
	}

	public static InvoiceDetailBO convertDO2BO(InvoiceDetailDO detailDO) {
		InvoiceDetailBO detailBO = new InvoiceDetailBO();
		detailBO.setId(detailDO.getId());
		detailBO.setInvoiceCode(detailDO.getInvoiceCode());
		detailBO.setInvoiceNo(detailDO.getInvoiceNo());
		detailBO.setMaterialName(detailDO.getMaterialName());
		detailBO.setMaterialSpec(detailDO.getMaterialSpec());
		detailBO.setUnit(detailDO.getUnit());
		detailBO.setUnitPrice(detailDO.getUnitPrice());
		detailBO.setQuantity(detailDO.getQuantity());
		detailBO.setRawPrice(detailDO.getRawPrice());
		detailBO.setTaxRate(detailDO.getTaxRate());
		detailBO.setTaxPrice(detailDO.getTaxPrice());
		detailBO.setGmtCreate(detailDO.getGmtCreate());
		detailBO.setGmtModified(detailDO.getGmtModified());
		detailBO.setIsDelete(detailDO.getIsDelete());
		detailBO.setPrice(detailDO.getPrice());
		return detailBO;
	}

	public static InvoiceDetailDO initInvoiceDetail(BigDecimal zero,String invoiceCode,String invoiceNo) {
		InvoiceDetailDO invoiceDetail = new InvoiceDetailDO();
		invoiceDetail.setUnit("");
		invoiceDetail.setUnitPrice(zero);
		invoiceDetail.setTaxRate(zero);
		invoiceDetail.setRawPrice(zero);
		invoiceDetail.setTaxPrice(zero);
		invoiceDetail.setPrice(zero);
		invoiceDetail.setQuantity(0);
		invoiceDetail.setMaterialName("");
		invoiceDetail.setMaterialSpec("");
		invoiceDetail.setInvoiceCode(invoiceCode == null ? "" : invoiceCode);
		invoiceDetail.setInvoiceNo(invoiceNo);
		return invoiceDetail;
	}

	public static InvoiceDetailDto initInvoiceDetailDto(String materialName,String invoiceCode,String invoiceNo) {
	    InvoiceDetailDto invoiceDetailDto = new InvoiceDetailDto();
	    BigDecimal zero = new BigDecimal(0);
	    invoiceDetailDto.setInvoiceNo(invoiceNo);
	    invoiceDetailDto.setInvoiceCode(invoiceCode);
	    invoiceDetailDto.setMaterialName(materialName);
	    invoiceDetailDto.setUnitPrice(zero);
	    invoiceDetailDto.setQuantity(0);
	    invoiceDetailDto.setUnit("");
	    invoiceDetailDto.setRawPrice(zero);
	    invoiceDetailDto.setTaxRate(zero);
	    invoiceDetailDto.setTaxPrice(zero);
	    invoiceDetailDto.setPrice(zero);
	    return invoiceDetailDto;

	}

	public static List<InvoiceDetailDO> convertDTO2DO(List<InvoiceDetailDto> invoiceDetailDtoList, BigDecimal zero,
			String invoiceCode, String invoiceNo) {
		List<InvoiceDetailDO> invoiceDetailDOList = Lists.newArrayList();
		if (CollectionUtils.isEmpty(invoiceDetailDtoList)) {
			invoiceDetailDOList.add(initInvoiceDetail(zero, invoiceCode, invoiceNo));
			return invoiceDetailDOList;
		}
		invoiceDetailDtoList.forEach(invoiceDetailDto -> {
			InvoiceDetailDO detailDO = new InvoiceDetailDO();
			detailDO.setInvoiceNo(invoiceNo);
			detailDO.setMaterialName(invoiceDetailDto.getMaterialName());
			detailDO.setMaterialSpec(invoiceDetailDto.getMaterialSpec());
			detailDO.setUnit(invoiceDetailDto.getUnit());
			detailDO.setUnitPrice(invoiceDetailDto.getUnitPrice());
			detailDO.setQuantity(invoiceDetailDto.getQuantity());
			detailDO.setRawPrice(invoiceDetailDto.getRawPrice());
			detailDO.setTaxRate(invoiceDetailDto.getTaxRate());
			detailDO.setTaxPrice(invoiceDetailDto.getTaxPrice());
			detailDO.setGmtCreate(invoiceDetailDto.getGmtCreate());
			detailDO.setGmtModified(invoiceDetailDto.getGmtModified());
			detailDO.setIsDelete(invoiceDetailDto.getIsDelete());
			detailDO.setPrice(invoiceDetailDto.getPrice());
			detailDO.setInvoiceCode(invoiceCode == null ? "" : invoiceCode);
			invoiceDetailDOList.add(detailDO);
		});
		return invoiceDetailDOList;
	}


}
