package so.dian.invoice.converter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import so.dian.commons.eden.enums.DeletedEnum;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.enums.InvoiceOperateLogTypeEnum;
import so.dian.invoice.enums.SubjectTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.dto.InvoiceOperateLogDTO;
import so.dian.invoice.pojo.entity.CheckInvoiceConclusionDO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.entity.InvoiceOperateLogDO;
import so.dian.invoice.pojo.param.CheckInvoiceParam;
import so.dian.invoice.pojo.param.DeletedInvoiceCheckerParam;
import so.dian.invoice.pojo.param.InvoiceCheckConclusionParam;
import so.dian.invoice.pojo.param.InvoiceCheckRateParam;
import so.dian.invoice.pojo.param.InvoiceCheckerParam;
import so.dian.invoice.pojo.param.InvoiceObsoleteParam;
import so.dian.invoice.pojo.param.InvoiceReviewParam;
import so.dian.invoice.pojo.param.OperatorParam;

/**
 * @Author: jiaoge
 * @Date: 2019/9/12 11:52 AM
 * @Description:
 */
public class InvoiceOperateLogConverter {

    public static InvoiceOperateLogDO buildBO2DO(InvoiceOperateLogBO invoiceOperateLogBO) {
        InvoiceOperateLogDO logDO = new InvoiceOperateLogDO();
        logDO.setInvoiceId(invoiceOperateLogBO.getInvoiceId());
        logDO.setType(invoiceOperateLogBO.getType().getCode());
        logDO.setContent(JSON.toJSONString(invoiceOperateLogBO.getContent()));
        logDO.setOperatorId(invoiceOperateLogBO.getOperatorId());
        logDO.setOperatorName(StringUtils.defaultIfBlank(invoiceOperateLogBO.getOperatorName(), ""));
        Date now = new Date();
        logDO.setCreateTime(now);
        logDO.setUpdateTime(now);
        logDO.setDeleted(InvoiceIdentifyRecordEnum.DeletedEnum.UN_DELETED.getCode());
        return logDO;
    }

    public static List<InvoiceOperateLogDO> buildBOList2DOList(List<InvoiceOperateLogBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(list, obj -> buildBO2DO(obj));
    }

    public static InvoiceOperateLogBO DO2BO(InvoiceOperateLogDO invoiceOperateLogDO) {
        if (Objects.isNull(invoiceOperateLogDO)) {
            return null;
        }
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setId(invoiceOperateLogDO.getId());
        invoiceOperateLogBO.setInvoiceId(invoiceOperateLogDO.getInvoiceId());
        invoiceOperateLogBO.setType(
              LocalEnumUtils.findByCode(InvoiceOperateLogTypeEnum.class, invoiceOperateLogDO.getType()));
        invoiceOperateLogBO.setContent(JSON.parseObject(invoiceOperateLogDO.getContent()));
        invoiceOperateLogBO.setOperatorId(invoiceOperateLogDO.getOperatorId());
        invoiceOperateLogBO.setOperatorName(invoiceOperateLogDO.getOperatorName());
        invoiceOperateLogBO.setCreateTime(invoiceOperateLogDO.getCreateTime());
        invoiceOperateLogBO.setUpdateTime(invoiceOperateLogDO.getUpdateTime());
        invoiceOperateLogBO.setDeleted(DeletedEnum.isDeleted(invoiceOperateLogDO.getDeleted()));
        return invoiceOperateLogBO;
    }

    public static List<InvoiceOperateLogBO> toBOList(List<InvoiceOperateLogDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(list, obj -> DO2BO(obj));
    }

    public static InvoiceOperateLogDTO toDTO(InvoiceOperateLogBO invoiceOperateLogBO) {
        if (Objects.isNull(invoiceOperateLogBO)) {
            return null;
        }
        Map<String, Object> content = invoiceOperateLogBO.getContent();
        InvoiceOperateLogDTO invoiceOperateLogDTO = new InvoiceOperateLogDTO();
        invoiceOperateLogDTO.setId(invoiceOperateLogBO.getId());
        invoiceOperateLogDTO.setInvoiceId(invoiceOperateLogBO.getInvoiceId());
        invoiceOperateLogDTO.setType(invoiceOperateLogBO.getType().getDesc());
        invoiceOperateLogDTO.setComment(content.getOrDefault("comment", "").toString());
        invoiceOperateLogDTO.setOperatorId(invoiceOperateLogBO.getOperatorId());
        invoiceOperateLogDTO.setOperatorName(invoiceOperateLogBO.getOperatorName());
        invoiceOperateLogDTO.setCreateTime(invoiceOperateLogBO.getCreateTime());
        return invoiceOperateLogDTO;
    }

    public static List<InvoiceOperateLogDTO> toDTOList(List<InvoiceOperateLogBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(list, obj -> toDTO(obj));
    }

    /**
     * 批量手动录入发票日志
     */
    public static List<InvoiceOperateLogBO> buildBatchInsertLog(List<InvoiceDO> invoiceDOList) {
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            return Lists.newArrayList();
        }
        return invoiceDOList.stream().map(invoiceDO -> {
            Map content = Maps.newHashMap();
            content.put("subjectType", SubjectTypeEnum.getField(invoiceDO.getSubjectType()));
            content.put("invoiceNo", invoiceDO.getInvoiceNo());
            content.put("invoiceCode", invoiceDO.getInvoiceCode());
            content.put("comment", invoiceDO.getMemo());

            InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
            invoiceOperateLogBO.setInvoiceId(invoiceDO.getId());
            invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.INVOICE_MANUAL_ENTRY);
            invoiceOperateLogBO.setContent(content);
            invoiceOperateLogBO.setOperatorId(Long.valueOf(invoiceDO.getCreator()));
            invoiceOperateLogBO.setOperatorName(invoiceDO.getCreateName());

            return invoiceOperateLogBO;
        }).collect(Collectors.toList());
    }

    /**
     * 发票OCR识别录入数据组装
     */
    public static InvoiceOperateLogBO buildOCRInsertParam2BO(
          InvoiceIdentifyRecordDO identifyRecordDO, InvoiceDO invoiceDO) {
        Map content = Maps.newHashMap();
        content.put("subjectType",SubjectTypeEnum.getField(invoiceDO.getSubjectType()));
        content.put("invoiceNo", invoiceDO.getInvoiceNo());
        content.put("invoiceCode", invoiceDO.getInvoiceCode());
        content.put("identifyRecordId", identifyRecordDO.getId());
        content.put("comment", "");

        InvoiceOperateLogBO logBO = new InvoiceOperateLogBO();
        logBO.setInvoiceId(invoiceDO.getId());
        logBO.setType(InvoiceOperateLogTypeEnum.INVOICE_OCR_ENTRY);
        logBO.setContent(content);
        logBO.setOperatorId(Long.valueOf(invoiceDO.getCreator()));
        logBO.setOperatorName(invoiceDO.getCreateName());
        return logBO;
    }

    /**
     * 小二端自动录入日志
     */
    public static InvoiceOperateLogBO buildInvoiceInsertParam2BO(
          InvoiceIdentifyRecordDO identifyRecordDO, InvoiceDO invoiceDO) {
        Map content = Maps.newHashMap();
        content.put("subjectType", SubjectTypeEnum.getField(invoiceDO.getSubjectType()));
        content.put("identifyRecordId", identifyRecordDO.getId());
        content.put("invoiceNo", invoiceDO.getInvoiceNo());
        content.put("invoiceCode", invoiceDO.getInvoiceCode());
        content.put("comment", "");

        InvoiceOperateLogBO logBO = new InvoiceOperateLogBO();
        logBO.setInvoiceId(invoiceDO.getId());
        logBO.setType(InvoiceOperateLogTypeEnum.INVOICE_XIAOER_ENTRY);
        logBO.setContent(content);
        logBO.setOperatorId(Long.valueOf(invoiceDO.getCreator()));
        logBO.setOperatorName(invoiceDO.getCreateName());
        return logBO;
    }

    /**
     * 发票编辑日志
     */
    public static InvoiceOperateLogBO editLog(InvoiceDO source, InvoiceDO target) {
        Map content = Maps.newHashMap();
        content.put("source", source);
        content.put("target", target);
        content.put("comment", target.getMemo());

        InvoiceOperateLogBO logBO = new InvoiceOperateLogBO();
        logBO.setInvoiceId(target.getId());
        logBO.setType(InvoiceOperateLogTypeEnum.INVOICE_EDIT);
        logBO.setContent(content);
        logBO.setOperatorId(Long.valueOf(target.getCreator()));
        logBO.setOperatorName(target.getCreateName());
        return logBO;
    }


    /**
     * 发票手动核销
     */
    public static InvoiceOperateLogBO buildInvoiceManualDeductParam2BO(InvoiceDeductionDO invoiceDeductionDO,
          BigDecimal sourceUsedAmount, Integer invoiceId) {
        Map content = Maps.newHashMap();
        content.put("usedAmount", sourceUsedAmount);
        content.put("amount", invoiceDeductionDO.getAmount());
        content.put("comment", invoiceDeductionDO.getReason());

        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(invoiceId);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.INVOICE_MANUAL_DEDUCT);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(invoiceDeductionDO.getCreator());
        invoiceOperateLogBO.setOperatorName(invoiceDeductionDO.getCreateName());

        return invoiceOperateLogBO;
    }

    /**
     * 发票核销
     */
    public static InvoiceOperateLogBO deductInvoiceLog(Integer invoiceId, BigDecimal sourceUsedAmount,
          InvoiceDeductionDO invoiceDeductionDO) {
        Map content = Maps.newHashMap();
        content.put("usedAmount", sourceUsedAmount);
        content.put("amount", invoiceDeductionDO.getAmount());
        content.put("comment", invoiceDeductionDO.getReason());
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(invoiceId);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.INVOICE_DEDUCT);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(invoiceDeductionDO.getCreator());
        invoiceOperateLogBO.setOperatorName(invoiceDeductionDO.getCreateName());

        return invoiceOperateLogBO;
    }

    /**
     * 发票回滚
     */
    public static InvoiceOperateLogBO recoverInvoiceLog(InvoiceDO invoiceDO, InvoiceDeductionDO invoiceDeductionDO) {
        Map<String,Object> content = Maps.newHashMap();
        content.put("usedAmount", invoiceDO.getUsedAmount());
        content.put("amount", invoiceDeductionDO.getAmount());
        content.put("comment", invoiceDeductionDO.getReason());

        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(invoiceDO.getId());
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.INVOICE_REVERT);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(invoiceDeductionDO.getCreator());
        invoiceOperateLogBO.setOperatorName(invoiceDeductionDO.getCreateName());
        return invoiceOperateLogBO;
    }

    public static InvoiceOperateLogBO recoverInvoiceLog(Integer invoiceId, BigDecimal usedAmount, BigDecimal amount,
          OperatorParam operatorParam) {
        Map content = Maps.newHashMap();
        content.put("usedAmount", usedAmount);
        content.put("amount",amount);
        content.put("comment", StringUtils.defaultString(operatorParam.getRemark()));

        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(invoiceId);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.INVOICE_REVERT);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(operatorParam.getOperatorId());
        invoiceOperateLogBO.setOperatorName(operatorParam.getOperatorName());
        return invoiceOperateLogBO;
    }

    /**
     * 发票复核日志
     */
    public static InvoiceOperateLogBO invoiceReviewLog(InvoiceReviewParam param, AgentEmployeeDTO agentEmployeeDTO) {

        Map content = Maps.newHashMap();
        content.put("status", param.getStatus());
        content.put("comment", param.getSuggestion());

        InvoiceOperateLogBO logBO = new InvoiceOperateLogBO();
        logBO.setInvoiceId(param.getId());
        logBO.setType(InvoiceOperateLogTypeEnum.INVOICE_REVIEW);
        logBO.setContent(content);
        logBO.setOperatorId(Long.valueOf(param.getUserId()));
        logBO.setOperatorName(agentEmployeeDTO.getNickName());
        return logBO;
    }

    /**
     * 发票删除日志
     */
    public static InvoiceOperateLogBO obsoleteLog(InvoiceObsoleteParam param) {
        Map content = Maps.newHashMap();
        content.put("comment", param.getRemark());

        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(param.getInvoiceId().intValue());
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.INVOICE_OBSOLETE);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(param.getOperatorId());
        invoiceOperateLogBO.setOperatorName(param.getOperatorName());
        return invoiceOperateLogBO;
    }

    /**
     * 发票质检比例配置LOG
     * @param param
     * @param nickName
     * @param beforeRate
     * @return
     */
    public static InvoiceOperateLogBO invoiceCheckRateConfigLog(InvoiceCheckRateParam param, String nickName,
            String beforeRate) {
        Map content = Maps.newHashMap();
        content.put("beforeRate", beforeRate + "%");
        content.put("afterRate", param.getCheckRate() + "%");
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(0);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.CHECK_INVOICE_RATE_CONFIG);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(param.getEmployeeId());
        invoiceOperateLogBO.setOperatorName(nickName);
        invoiceOperateLogBO.setUpdateTime(new Date());
        return invoiceOperateLogBO;
    }

    /**
     * 发票质检员工配置LOG
     * @param param
     * @param nickName
     * @return
     */
    public static InvoiceOperateLogBO invoiceCheckerConfigLog(InvoiceCheckerParam param, String nickName) {
        Map content = Maps.newHashMap();
        content.put("checker", param.getChecker());
        content.put("regionList", param.getRegionList());
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(0);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.CHECK_INVOICE_USER_REGION_CONFIG);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(param.getEmployeeId());
        invoiceOperateLogBO.setOperatorName(nickName);
        invoiceOperateLogBO.setUpdateTime(new Date());
        return invoiceOperateLogBO;
    }

    /**
     * 修改员工大区配置
     * @param param
     * @param nickName
     * @param oldRegionList
     * @return
     */
    public static InvoiceOperateLogBO modifyInvoiceCheckerRegionConfigLog(InvoiceCheckerParam param, String nickName,
            List<String> oldRegionList) {
        Map content = Maps.newHashMap();
        content.put("checker", param.getChecker());
        content.put("beforeRegionList", oldRegionList);
        content.put("afterRegionList", param.getRegionList());
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(0);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.CHECK_INVOICE_USER_REGION_CONFIG);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(param.getEmployeeId());
        invoiceOperateLogBO.setOperatorName(nickName);
        invoiceOperateLogBO.setUpdateTime(new Date());
        return invoiceOperateLogBO;
    }

    /**
     * 删除人员大区配置log
     * @param param
     * @param nickName
     * @return
     */
    public static InvoiceOperateLogBO deletedInvoiceCheckerRegionConfigLog(DeletedInvoiceCheckerParam param,
            String nickName) {
        Map content = Maps.newHashMap();
        content.put("deleted checker", param.getChecker());
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(0);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.CHECK_INVOICE_USER_REGION_CONFIG);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(param.getEmployeeId());
        invoiceOperateLogBO.setOperatorName(nickName);
        invoiceOperateLogBO.setUpdateTime(new Date());
        return invoiceOperateLogBO;
    }

    /**
     * 质检结论配置log
     * @param param
     * @param nickName
     * @return
     */
    public static InvoiceOperateLogBO invoiceCheckConclusionConfigLog(InvoiceCheckConclusionParam param,
            String nickName) {
        Map content = Maps.newHashMap();
        content.put("id", param.getId()==null?"":param.getId());
        content.put("conclusion", param.getCode());
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(0);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.CHECK_INVOICE_CONCLUSION_CONFIG);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(param.getEmployeeId());
        invoiceOperateLogBO.setOperatorName(nickName);
        invoiceOperateLogBO.setUpdateTime(new Date());
        return invoiceOperateLogBO;
    }

    /**
     * 质检结论配置log
     * @param conclusionDO
     * @param nickName
     * @return
     */
    public static InvoiceOperateLogBO deletedInvoiceCheckConclusionLog(CheckInvoiceConclusionDO conclusionDO,
            String nickName, Long operatorId) {
        Map content = Maps.newHashMap();
        content.put("id",conclusionDO.getId());
        content.put("code",conclusionDO.getCode());
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(0);
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.DELETED_CHECK_CONCLUSION);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(operatorId);
        invoiceOperateLogBO.setOperatorName(nickName);
        invoiceOperateLogBO.setUpdateTime(new Date());
        return invoiceOperateLogBO;
    }

    /**
     * 发票质检log
     * @param param
     * @param nickName
     * @param invoiceId
     * @return
     */
    public static InvoiceOperateLogBO checkInvoice(CheckInvoiceParam param, String nickName, Long invoiceId) {
        Map content = Maps.newHashMap();
        content.put("id", param.getId()==null?"":param.getId());
        content.put("conclusion", param.getCheckResult());
        content.put("remark", param.getRemark());
        InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
        invoiceOperateLogBO.setInvoiceId(invoiceId.intValue());
        invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.CHECK_INVOICE);
        invoiceOperateLogBO.setContent(content);
        invoiceOperateLogBO.setOperatorId(param.getEmployeeId());
        invoiceOperateLogBO.setOperatorName(nickName);
        invoiceOperateLogBO.setUpdateTime(new Date());
        return invoiceOperateLogBO;
    }
}
