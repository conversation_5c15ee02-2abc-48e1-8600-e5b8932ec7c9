package so.dian.invoice.converter;

import cn.hutool.core.date.DateUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceIdentifyRecordBO;
import so.dian.invoice.pojo.dto.identify.DetailsDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceIdentifyRecordDTO;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.param.InvoiceInfoParam;
import so.dian.invoice.pojo.vo.InvoiceIdentifyRecordVO;

/**
 * @Author: jiaoge
 * @Date: 2019/9/27 11:07 AM
 * @Description:
 */
public class InvoiceValidateConverter {

	public static InvoiceIdentifyRecordVO buildRecordDTO2VO(InvoiceIdentifyRecordDTO recordDTO) {
	    InvoiceIdentifyRecordVO invoiceIdentifyRecordVO = new InvoiceIdentifyRecordVO();
	    invoiceIdentifyRecordVO.setInvoiceId(recordDTO.getId());
	    invoiceIdentifyRecordVO.setInvoiceNo(recordDTO.getInvoiceNo());
	    invoiceIdentifyRecordVO.setInvoiceCode(recordDTO.getInvoiceCode());
	    invoiceIdentifyRecordVO.setType(recordDTO.getInvoiceType());
	    invoiceIdentifyRecordVO.setCheckCode(recordDTO.getCheckCode());
	    invoiceIdentifyRecordVO.setRawPrice(recordDTO.getPretaxAmount());
	    invoiceIdentifyRecordVO.setPrice(recordDTO.getTotal());
	    invoiceIdentifyRecordVO.setTax(recordDTO.getTax());
	    invoiceIdentifyRecordVO.setSeller(recordDTO.getSeller());
	    invoiceIdentifyRecordVO.setSellerTaxId(recordDTO.getSellerTaxId());
	    invoiceIdentifyRecordVO.setBuyer(recordDTO.getBuyer());
	    invoiceIdentifyRecordVO.setBuyerTaxId(recordDTO.getBuyerTaxId());
	    invoiceIdentifyRecordVO.setKind(recordDTO.getKind());
	    invoiceIdentifyRecordVO.setIsReal(recordDTO.getIsReal());
	    invoiceIdentifyRecordVO.setIsRelated(recordDTO.getIsRelated());
	    invoiceIdentifyRecordVO.setUrl(recordDTO.getUrl());
	    invoiceIdentifyRecordVO.setCreateTime(recordDTO.getCreateTime());
	    invoiceIdentifyRecordVO.setCreator(recordDTO.getCreator());
	    invoiceIdentifyRecordVO.setUpdateTime(recordDTO.getUpdateTime());
	    invoiceIdentifyRecordVO.setUpdater(recordDTO.getUpdater());
	    invoiceIdentifyRecordVO.setDeleted(recordDTO.getDeleted());
	    invoiceIdentifyRecordVO.setTypeStr(recordDTO.getInvoiceTypeStr());
	    invoiceIdentifyRecordVO.setIsRealStr(recordDTO.getIsRealStr());
	    invoiceIdentifyRecordVO.setBatchNo(recordDTO.getBatchNo());
	    invoiceIdentifyRecordVO.setReason(recordDTO.getReason());
	    invoiceIdentifyRecordVO.setIsIdentify(recordDTO.getIsIdentify());
	    invoiceIdentifyRecordVO.setIsIdentifyStr(recordDTO.getIsIdentifyStr());
	    invoiceIdentifyRecordVO.setDetails(recordDTO.getDetails());
	    if(Objects.nonNull(recordDTO.getGmtCreate())) {
		    invoiceIdentifyRecordVO.setGmtCreate(new Date(recordDTO.getGmtCreate()));
	    }
	    return invoiceIdentifyRecordVO;
	}

	/**
	 * 新增发票识别记录与缓存中的发票识别记录对比
	 * @param cacheRecordBO 缓存中的识别记录
	 * @param insertRecordDO 新增的识别记录
	 * @return
	 */
	public static Boolean invoiceIdentifyCompared(InvoiceIdentifyRecordBO cacheRecordBO,
			InvoiceIdentifyRecordDO insertRecordDO) {
		//发票检验码对比
		boolean checkCodeIsEquals =
				Objects.equals(cacheRecordBO.getCheckCode() == null ? "" : cacheRecordBO.getCheckCode(), insertRecordDO
						.getCheckCode() == null ? "" : insertRecordDO.getCheckCode());
		if (!checkCodeIsEquals) {
			return false;
		}
		//发票号码对比
		boolean invoiceNoIsEquals =
				Objects.equals(cacheRecordBO.getInvoiceNo() == null ? "" : cacheRecordBO.getInvoiceNo(),
						insertRecordDO.getInvoiceNo());
		if (!invoiceNoIsEquals) {
			return false;
		}
		//发票代码对比
		boolean invoiceCodeIsEquals =
				Objects.equals(cacheRecordBO.getInvoiceCode() == null ? "" : cacheRecordBO.getInvoiceCode(),
						insertRecordDO.getInvoiceCode());
		if (!invoiceCodeIsEquals) {
			return false;
		}
		//发票开票时间对比
		String insertInvoiceDate = DateUtil.parse(insertRecordDO.getInvoiceDate(), "yyyy-MM-dd").toDateStr();
		boolean invoiceDateIsEquals = Objects.equals(cacheRecordBO.getInvoiceDate() == null ? "" : cacheRecordBO
				.getInvoiceDate(), insertInvoiceDate);
		if (!invoiceDateIsEquals) {
			return false;
		}
		Integer invoiceType = insertRecordDO.getInvoiceType();
		//发票税前金额对比
		if(InvoiceTypeEnum.ORD_ROLL.getType() != invoiceType){
			boolean pretaxAmountIsEquals = Objects.equals(cacheRecordBO.getPretaxAmount() == null ? "" : cacheRecordBO
					.getPretaxAmount(), insertRecordDO.getPretaxAmount());
			if (!pretaxAmountIsEquals) {
				return false;
			}
		}
		//发票总金额对比
		boolean totalIsEquals = Objects.equals(cacheRecordBO.getTotal() == null ? "" : cacheRecordBO
				.getTotal(), insertRecordDO.getTotal());
		if (!totalIsEquals) {
			return false;
		}
		//发票税额对比
		if(InvoiceTypeEnum.ORD_ROLL.getType() != invoiceType){
			boolean taxIsEquals = Objects.equals(cacheRecordBO.getTax() == null ? "" : cacheRecordBO
					.getTax(), insertRecordDO.getTax());
			if (!taxIsEquals) {
				return false;
			}
		}
		//开票明细对比
		boolean detailsIsEquals = Objects.equals(cacheRecordBO.getDetails() == null ? "" : cacheRecordBO
				.getDetails(), insertRecordDO.getDetails());
		if (!detailsIsEquals) {
			return false;
		}
		//购买方是否相等
		boolean buyerIsEquals = Objects.equals(cacheRecordBO.getBuyer() == null ? "" : cacheRecordBO
				.getBuyer(), insertRecordDO.getBuyer());
		if (!buyerIsEquals) {
			return false;
		}
		//销售方是否相等
		boolean sellerIsEquals = Objects.equals(cacheRecordBO.getSeller() == null ? "" : cacheRecordBO
				.getSeller(), insertRecordDO.getSeller());
		if (!sellerIsEquals) {
			return false;
		}
		return true;
	}


	public static Boolean updateInvoiceCompared(DetailsDTO details, InvoiceInfoParam param) {
		//发票检验码对比
		boolean checkCodeIsEquals =
				Objects.equals(details.getCheck_code(), param.getCheckCode());
		if (!checkCodeIsEquals) {
			return false;
		}
		//发票号码对比
		boolean invoiceNoIsEquals = Objects.equals(details.getNumber(), param.getInvoiceNo());
		if (!invoiceNoIsEquals) {
			return false;
		}
		//发票代码对比
		boolean invoiceCodeIsEquals = Objects.equals(details.getCode() , param.getInvoiceCode());
		if (!invoiceCodeIsEquals) {
			return false;
		}
		//发票开票时间对比
		String updateInvoiceDate = DateUtil.format(param.getGmtCreate(), "yyyy年MM月dd日");
		boolean invoiceDateIsEquals = Objects.equals(updateInvoiceDate, details.getDate());
		if (!invoiceDateIsEquals) {
			return false;
		}
		//发票税前金额对比
		boolean pretaxAmountIsEquals = Objects.equals(details.getPretax_amount(), param.getRawPrice().setScale(2,
				BigDecimal.ROUND_HALF_UP).toString());
		if (!pretaxAmountIsEquals) {
			return false;
		}
		//发票总金额对比
		boolean totalIsEquals = Objects.equals(details.getTotal(), param.getPrice().setScale(2,
				BigDecimal.ROUND_HALF_UP).toString());
		if (!totalIsEquals) {
			return false;
		}
		//发票税额对比
		boolean taxIsEquals = Objects.equals(details.getTax(), param.getTax().setScale(2,
				BigDecimal.ROUND_HALF_UP).toString());
		if (!taxIsEquals) {
			return false;
		}
		//开票明细对比
		boolean detailsIsEquals = Objects.equals(details.getItem_names(), param.getDetails());
		if (!detailsIsEquals) {
			return false;
		}
		//购买方是否相等
		boolean buyerIsEquals = Objects.equals(details.getBuyer(), param.getBuyer());
		if (!buyerIsEquals) {
			return false;
		}
		//销售方是否相等
		boolean sellerIsEquals = Objects.equals(details.getSeller(), param.getSeller());
		if (!sellerIsEquals) {
			return false;
		}
		return true;
	}




}
