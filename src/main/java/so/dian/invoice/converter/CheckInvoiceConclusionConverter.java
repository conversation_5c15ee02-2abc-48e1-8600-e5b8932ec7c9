package so.dian.invoice.converter;

import java.util.List;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.invoice.enums.ConclusionStatusEnum;
import so.dian.invoice.pojo.entity.CheckInvoiceConclusionDO;
import so.dian.invoice.pojo.param.InvoiceCheckConclusionParam;
import so.dian.invoice.pojo.vo.CheckInvoiceConclusionVO;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 7:59 PM
 * @Description:
 */
public class CheckInvoiceConclusionConverter {

	public static CheckInvoiceConclusionDO buildParam2DO(InvoiceCheckConclusionParam param) {
		long now = System.currentTimeMillis();
		CheckInvoiceConclusionDO checkInvoiceConclusionDO = new CheckInvoiceConclusionDO();
	    checkInvoiceConclusionDO.setIsUsed(ConclusionStatusEnum.NOT_USED.getCode());
	    checkInvoiceConclusionDO.setCode(param.getCode());
	    checkInvoiceConclusionDO.setGmtCreate(now);
	    checkInvoiceConclusionDO.setGmtUpdate(now);
	    return checkInvoiceConclusionDO;
	}

	public static List<CheckInvoiceConclusionVO> convertDO2VO(List<CheckInvoiceConclusionDO> conclusionDOList) {
		return LocalListUtils.transferList(conclusionDOList,CheckInvoiceConclusionConverter::convertDO2VO);
	}

	public static CheckInvoiceConclusionVO convertDO2VO(CheckInvoiceConclusionDO conclusionDO) {
	    CheckInvoiceConclusionVO checkInvoiceConclusionVO = new CheckInvoiceConclusionVO();
	    checkInvoiceConclusionVO.setId(conclusionDO.getId());
	    checkInvoiceConclusionVO.setCode(conclusionDO.getCode());
	    checkInvoiceConclusionVO.setIsUsed(conclusionDO.getIsUsed());
	    return checkInvoiceConclusionVO;
	}
}
