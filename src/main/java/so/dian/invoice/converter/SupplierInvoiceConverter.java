package so.dian.invoice.converter;

import org.apache.commons.collections4.CollectionUtils;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;
import so.dian.invoice.enums.InvoiceSourceEnum;
import so.dian.invoice.enums.InvoiceStatusEnum;
import so.dian.invoice.enums.SubjectTypeEnum;
import so.dian.invoice.enums.SupplierInvoiceStatusEnum;
import so.dian.invoice.enums.SupplierInvoiceTypeEnum;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceAttachmentDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.SupplierInvoicePageVO;
import so.dian.invoice.util.LocalDateUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static so.dian.invoice.util.LocalDateUtil.yyyy_MM_dd;

/**
 * 供应商发票主表
 *
 * <AUTHOR>
 * @date 2021-03-17 14:03:28
 */
public class SupplierInvoiceConverter {


    public static List<SupplierInvoicePageVO> toVOList(List<SupplierInvoiceDO> supplierInvoiceDOList, Map<Integer, SupplierInvoiceAttachmentDO> invoiceIdMap) {
        if (CollectionUtils.isEmpty(supplierInvoiceDOList)) {
            return null;
        }
        List<SupplierInvoicePageVO> supplierInvoicePageVOList = new ArrayList<>(supplierInvoiceDOList.size());
        for (SupplierInvoiceDO supplierInvoiceDO : supplierInvoiceDOList) {
            SupplierInvoicePageVO supplierInvoicePageVO = new SupplierInvoicePageVO();
            supplierInvoicePageVO.setId(supplierInvoiceDO.getId());
            SupplierInvoiceTypeEnum invoiceTypeEnum = LocalEnumUtils.findByCode(SupplierInvoiceTypeEnum.class, supplierInvoiceDO.getInvoiceType());
            if (Objects.nonNull(invoiceTypeEnum)) {
                supplierInvoicePageVO.setInvoiceTypeStr(invoiceTypeEnum.getDesc());
            }
            supplierInvoicePageVO.setInvoiceCode(supplierInvoiceDO.getInvoiceCode());
            supplierInvoicePageVO.setInvoiceNo(supplierInvoiceDO.getInvoiceNo());

            supplierInvoicePageVO.setInvoiceDate(LocalDateUtil.localDateToStr(supplierInvoiceDO.getInvoiceDate(),yyyy_MM_dd));
            supplierInvoicePageVO.setSellerName(supplierInvoiceDO.getSellerName());
            supplierInvoicePageVO.setSellerTaxId(supplierInvoiceDO.getSellerTaxId());
            supplierInvoicePageVO.setCheckCode(supplierInvoiceDO.getCheckCode());
            supplierInvoicePageVO.setBuyer(supplierInvoiceDO.getBuyer());
            supplierInvoicePageVO.setBuyerTaxId(supplierInvoiceDO.getBuyerTaxId());

            if (Objects.nonNull(supplierInvoiceDO.getRawPrice())) {
                supplierInvoicePageVO.setRawPriceStr(supplierInvoiceDO.getRawPrice().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDO.getTaxRate())) {
                supplierInvoicePageVO.setTaxRateStr(supplierInvoiceDO.getTaxRate().stripTrailingZeros().toPlainString()+ "%");
            }
            if (Objects.nonNull(supplierInvoiceDO.getTax())) {
                supplierInvoicePageVO.setTaxStr(supplierInvoiceDO.getTax().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(supplierInvoiceDO.getPrice())) {
                supplierInvoicePageVO.setPriceStr(supplierInvoiceDO.getPrice().stripTrailingZeros().toPlainString());
            }

            supplierInvoicePageVO.setStatus(supplierInvoiceDO.getStatus());
            SupplierInvoiceStatusEnum statusEnum = LocalEnumUtils.findByCode(SupplierInvoiceStatusEnum.class, supplierInvoiceDO.getStatus());
            if (Objects.nonNull(statusEnum)) {
                supplierInvoicePageVO.setStatusStr(statusEnum.getDesc());
            }


            SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO = invoiceIdMap.get(supplierInvoiceDO.getId());
            if (Objects.nonNull(supplierInvoiceAttachmentDO)) {
                supplierInvoicePageVO.setInvoiceUrl(supplierInvoiceAttachmentDO.getUrl());
                supplierInvoicePageVO.setAttachmentId(supplierInvoiceAttachmentDO.getId());
            }

            supplierInvoicePageVOList.add(supplierInvoicePageVO);
        }
        return supplierInvoicePageVOList;
    }

    public static void appendSupplierInvoice(SupplierInvoiceDO supplierInvoiceDO, SupplierInvoiceDetailDO invoiceDetailDO,Integer userId) {
        supplierInvoiceDO.setInvoiceNo(invoiceDetailDO.getInvoiceNo());
        supplierInvoiceDO.setInvoiceCode(invoiceDetailDO.getInvoiceCode() == null ? "" : invoiceDetailDO.getInvoiceCode());
        supplierInvoiceDO.setCheckCode(invoiceDetailDO.getCheckCode() == null ? "" : invoiceDetailDO.getCheckCode());
        supplierInvoiceDO.setInvoiceType(invoiceDetailDO.getInvoiceType());
        supplierInvoiceDO.setInvoiceDate(invoiceDetailDO.getInvoiceDate());
        supplierInvoiceDO.setSellerName(invoiceDetailDO.getSellerName());
        supplierInvoiceDO.setSellerTaxId(invoiceDetailDO.getSellerTaxId());
        supplierInvoiceDO.setBuyer(invoiceDetailDO.getBuyer());
        supplierInvoiceDO.setBuyerTaxId(invoiceDetailDO.getBuyerTaxId());
        supplierInvoiceDO.setStatus(SupplierInvoiceStatusEnum.INITIALIZE.getCode());
        supplierInvoiceDO.setSupplierNo(invoiceDetailDO.getSupplierNo());
        supplierInvoiceDO.setCreator(userId);

        supplierInvoiceDO.setTaxRate(invoiceDetailDO.getTaxRate());

        if(Objects.isNull(supplierInvoiceDO.getRawPrice())){
            supplierInvoiceDO.setRawPrice(invoiceDetailDO.getRawPrice());
        }else {
            supplierInvoiceDO.setRawPrice(supplierInvoiceDO.getRawPrice().add(invoiceDetailDO.getRawPrice()));
        }

        if(Objects.isNull(supplierInvoiceDO.getTax())){
            supplierInvoiceDO.setTax(invoiceDetailDO.getTax());
        }else {
            supplierInvoiceDO.setTax(supplierInvoiceDO.getTax().add(invoiceDetailDO.getTax()));
        }

        if(Objects.isNull(supplierInvoiceDO.getPrice())){
            supplierInvoiceDO.setPrice(invoiceDetailDO.getPrice());
        }else {
            supplierInvoiceDO.setPrice(supplierInvoiceDO.getPrice().add(invoiceDetailDO.getPrice()));
        }
    }

    public static InvoiceDO toInvoice(CurrentUserReq userReq, SupplierInvoiceDO supplierInvoiceDO, SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO, AgentDTO agentDTO) {
        InvoiceDO invoiceDO = new InvoiceDO();

        invoiceDO.setInvoiceNo(supplierInvoiceDO.getInvoiceNo());
        invoiceDO.setInvoiceCode(supplierInvoiceDO.getInvoiceCode());
        invoiceDO.setSupplierNo(supplierInvoiceDO.getSupplierNo());
        invoiceDO.setType(supplierInvoiceDO.getInvoiceType());
        invoiceDO.setRawPrice(supplierInvoiceDO.getRawPrice());
        invoiceDO.setTaxPrice(supplierInvoiceDO.getTax());
        invoiceDO.setTax(supplierInvoiceDO.getTax());
        invoiceDO.setTaxRate(supplierInvoiceDO.getTaxRate());
        invoiceDO.setPrice(supplierInvoiceDO.getPrice());
        invoiceDO.setCheckCode(supplierInvoiceDO.getCheckCode());

        invoiceDO.setSeller(supplierInvoiceDO.getSellerName());
        invoiceDO.setSellerTaxId(supplierInvoiceDO.getSellerTaxId());
        invoiceDO.setBuyer(supplierInvoiceDO.getBuyer());
        invoiceDO.setBuyerTaxId(supplierInvoiceDO.getBuyerTaxId());
        // 跳过发票验真
        invoiceDO.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode());
        // 业务类型 设备采购发票
        invoiceDO.setSubjectType(SubjectTypeEnum.EQUIPMENT_PROCUREMENT.getType());
        // 主体名称
        // 看纳税人识别号 CompanySubjectEnum
        invoiceDO.setSubjectName(supplierInvoiceDO.getSellerName());

        // 填充发票归属信息
        invoiceDO.setBelongSubjectType(agentDTO.getType());
        invoiceDO.setBelongSubjectId(agentDTO.getAgentId().intValue());

        if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(invoiceDO.getBelongSubjectId())) {
            invoiceDO.setCreateName(userReq.getNickName());
        } else {
            invoiceDO.setCreateName(agentDTO.getAgentName() == null ? "" : agentDTO.getAgentName());
        }

        invoiceDO.setCreator(userReq.getUserId());
        invoiceDO.setUrl(supplierInvoiceAttachmentDO.getUrl());

        invoiceDO.setValidateCode("");
        invoiceDO.setReviewer(null);
        invoiceDO.setMemo("");
        invoiceDO.setCityCode(null);
        invoiceDO.setUsedAmount(new BigDecimal("0"));

        invoiceDO.setBillNo(null);
        // 发票来源 SUPPLIER_TRANSMIT(4,"供应商发票转入")
        invoiceDO.setSource(InvoiceSourceEnum.SUPPLIER_TRANSMIT.getType());
        invoiceDO.setReviewRemark(null);
        // 发票状态 WAIT(1, "未核销"),
        invoiceDO.setStatus(InvoiceStatusEnum.WAIT.getType());

        // 发票日期，取导入的发票的日期
        // 21.7.5,发票台账的发票日期，取得是这个字段
        Date gmtCreateDate = LocalDateUtil.localDateToDate(supplierInvoiceDO.getInvoiceDate());
        invoiceDO.setGmtCreate(gmtCreateDate);
        invoiceDO.setGmtModified(new Date());
        // 发票录入时间，取当前时间
        invoiceDO.setCreateTime(new Date());
        // 发票接收时间
        invoiceDO.setReceiveTime(new Date());
        // 发票复核时间
        invoiceDO.setReviewTime(new Date());

        invoiceDO.setIsDelete(0);

        invoiceDO.setKind(null);
        invoiceDO.setBatchNo(null);
        // 发票流转状态 ALREADY_ENTRY(0, "已录入"),
        invoiceDO.setProcessStatus(InvoiceProcessStatusEnum.ALREADY_ENTRY.getCode());
        invoiceDO.setNeedCheck(0);
        invoiceDO.setInCheckPool(0);
        return invoiceDO;
    }


}
