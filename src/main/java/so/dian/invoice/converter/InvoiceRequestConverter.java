package so.dian.invoice.converter;

import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.util.DateUtils;
import so.dian.invoice.enums.*;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDetailDTO;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceRequestRecordDetailExcel;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceRequestRecordExcel;
import so.dian.invoice.util.FractionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: yuechuan
 * @Date: 2015/3/13 9:32 PM
 * @Description:
 */

@Slf4j
public class InvoiceRequestConverter {


    public static List<ExportInvoiceRequestRecordExcel> toRecordExcelBean(List<InvoiceRequestDTO> aggs){
        List<ExportInvoiceRequestRecordExcel> excels = new ArrayList<>();
        for(InvoiceRequestDTO agg : aggs) {
            List<ExportInvoiceRequestRecordExcel> list = toRecordExcelBean(agg);
            excels.addAll(list);
        }

        return excels;
    }

    public static List<ExportInvoiceRequestRecordDetailExcel> toRecordDetailExcelBean(List<InvoiceRequestDTO> aggs){
        List<ExportInvoiceRequestRecordDetailExcel> excels = new ArrayList<>();
        for(InvoiceRequestDTO agg : aggs) {
            List<ExportInvoiceRequestRecordDetailExcel> list = toRecordDetailExcelBean(agg);
            excels.addAll(list);
        }

        return excels;
    }

    public static List<ExportInvoiceRequestRecordExcel> toRecordExcelBean(InvoiceRequestDTO agg){
        List<ExportInvoiceRequestRecordExcel> list = new ArrayList<>();
        for(InvoiceRequestRecordDTO requestRecordDTO : agg.getRequestRecords()) {
            ExportInvoiceRequestRecordExcel excel = new ExportInvoiceRequestRecordExcel();

            excel.setRequestId(agg.getId());
            AgentTypeEnum agentTypeEnum = AgentTypeEnum.getAgentType(agg.getSubjectType());
            if (Objects.nonNull(agentTypeEnum)) {
                excel.setSubjectType(agentTypeEnum.getName());
            }
            excel.setSubjectId(agg.getSubjectId());
            excel.setSubjectName(agg.getSubjectName());
            excel.setBizNo(requestRecordDTO.getBizNo());

            BizTypeEnum bizTypeEnum = BizTypeEnum.getByCode(requestRecordDTO.getBizType());
            if (Objects.nonNull(bizTypeEnum)) {
                excel.setBizType(bizTypeEnum.desc());
            }
            excel.setAmount(FractionUtils.conventStrAmountInFen(requestRecordDTO.getAmount()));
            // 小电开票主体
            // 如果单据时代理商，则小电开票主体为 小电
            // 如果单据时合资公司，则小电开票主体为 友电
            if (AgentTypeEnum.AGENT_TYPE.getId().equals(agg.getSubjectType())) {
                excel.setInvoiceSubjectName(CompanySubjectEnum.XIAODIAN.getDesc());
            }else if (AgentTypeEnum.JV_COMPANY_TYPE.getId().equals(agg.getSubjectType())) {
                excel.setInvoiceSubjectName(CompanySubjectEnum.YOUDIAN.getDesc());
            }

            excel.setTitle(agg.getTitle());
            TitleTypeEnum titleTypeEnum = TitleTypeEnum.getByCode(agg.getTitleType());
            excel.setTitleType(titleTypeEnum.getName());
            excel.setType(InvoiceTypeEnum.getByCode(agg.getType()).getName());

            excel.setEmail(agg.getEmail());
            excel.setApplicantName(agg.getApplicantName());

            DateTime dateTime = DateUtils.date(agg.getGmtCreate());
            excel.setGmtCreate(DateUtils.formatDateTime(dateTime));
            if (Objects.nonNull(agg.getInvoiceCompletedTime())) {
                DateTime completedTime = DateUtils.date(agg.getInvoiceCompletedTime());
                excel.setInvoiceCompleteTime(DateUtils.formatDateTime(completedTime));
            }
            excel.setFinanceFeedback(agg.getFinanceFeedback());
            if (Objects.nonNull(agg.getStatus())) {
                InvoiceRequestStatusEnum statusEnum = InvoiceRequestStatusEnum.getByCode(agg.getStatus());
                excel.setStatus(statusEnum.getDesc());
            }
            //脱敏
//            excel.setInvoiceNo(DesensitizedUtils.around(agg.getInvoiceNo(), 4, 2));
            excel.setInvoiceNo(agg.getInvoiceNo());
            list.add(excel);
        }

        return list;
    }

    public static List<ExportInvoiceRequestRecordDetailExcel> toRecordDetailExcelBean(InvoiceRequestDTO agg){
        List<ExportInvoiceRequestRecordDetailExcel> list = new ArrayList<>();
        for(InvoiceRequestRecordDTO requestRecordDTO : agg.getRequestRecords()) {
            for(InvoiceRequestRecordDetailDTO detailDTO : requestRecordDTO.getDetails()){
                ExportInvoiceRequestRecordDetailExcel excel = new ExportInvoiceRequestRecordDetailExcel();

                excel.setRequestId(agg.getId());
                AgentTypeEnum agentTypeEnum = AgentTypeEnum.getAgentType(agg.getSubjectType());
                if (Objects.nonNull(agentTypeEnum)) {
                    excel.setSubjectType(agentTypeEnum.getName());
                }
                excel.setSubjectId(agg.getSubjectId());
                excel.setSubjectName(agg.getSubjectName());
                excel.setBizNo(requestRecordDTO.getBizNo());

                BizTypeEnum bizTypeEnum = BizTypeEnum.getByCode(requestRecordDTO.getBizType());
                if (Objects.nonNull(bizTypeEnum)) {
                    excel.setBizType(bizTypeEnum.desc());
                }
                excel.setAmount(FractionUtils.conventStrAmountInFen(requestRecordDTO.getAmount()));
                // 小电开票主体
                // 如果单据时代理商，则小电开票主体为 小电
                // 如果单据时合资公司，则小电开票主体为 友电
                if (AgentTypeEnum.AGENT_TYPE.getId().equals(agg.getSubjectType())) {
                    excel.setInvoiceSubjectName(CompanySubjectEnum.XIAODIAN.getDesc());
                }else if (AgentTypeEnum.JV_COMPANY_TYPE.getId().equals(agg.getSubjectType())) {
                    excel.setInvoiceSubjectName(CompanySubjectEnum.YOUDIAN.getDesc());
                }
                excel.setTitle(agg.getTitle());
                TitleTypeEnum titleTypeEnum = TitleTypeEnum.getByCode(agg.getTitleType());
                excel.setTitleType(titleTypeEnum.getName());
                InvoiceTypeEnum.getByCode(agg.getType());
                excel.setType(InvoiceTypeEnum.getByCode(agg.getType()).getName());

                excel.setEmail(agg.getEmail());
                excel.setApplicantName(agg.getApplicantName());

                DateTime dateTime = DateUtils.date(agg.getGmtCreate());
                excel.setGmtCreate(DateUtils.formatDateTime(dateTime));
                if (Objects.nonNull(agg.getInvoiceCompletedTime())) {
                    DateTime completedTime = DateUtils.date(agg.getInvoiceCompletedTime());
                    excel.setInvoiceCompleteTime(DateUtils.formatDateTime(completedTime));
                }
                excel.setFinanceFeedback(agg.getFinanceFeedback());
                if (Objects.nonNull(agg.getStatus())) {
                    InvoiceRequestStatusEnum statusEnum = InvoiceRequestStatusEnum.getByCode(agg.getStatus());
                    excel.setStatus(statusEnum.getDesc());
                }
                //脱敏
//                excel.setInvoiceNo(DesensitizedUtils.around(agg.getInvoiceNo(), 4, 2));
                excel.setInvoiceNo(agg.getInvoiceNo());
                excel.setProductName(detailDTO.getProductName());
                excel.setProductCount(detailDTO.getProductCount());
                excel.setProductInvoiceAmount(FractionUtils.conventStrAmountInFen(detailDTO.getAmount()));

                list.add(excel);
            }
        }
        return list;
    }
}
