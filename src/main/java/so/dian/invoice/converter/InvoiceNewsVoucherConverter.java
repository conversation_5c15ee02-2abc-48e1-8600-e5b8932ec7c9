package so.dian.invoice.converter;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import so.dian.fis.credit.dto.response.CreditMessageDTO;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.enums.NewsVoucherEnum;
import so.dian.invoice.enums.OutBizTypeEnum;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;
import so.dian.songshan.client.pojo.dto.ChannelRepairChangeDTO;
import so.dian.taishan.client.pojo.mq.TradeOrderChangeDTO;

import java.util.Objects;

/**
 * @Author: yuechuan
 * @Date: 2025/3/18 9:32 PM
 * @Description:
 */
@Slf4j
public class InvoiceNewsVoucherConverter {

    public static InvoiceNewsVoucherDTO toDTO(ChannelRepairChangeDTO model) {
        InvoiceNewsVoucherDTO dto = new InvoiceNewsVoucherDTO();
        dto.setOutBizId(model.getRepairNo());
        dto.setOutBizType(OutBizTypeEnum.REPAIR_ORDER_COMPLETE.getCode());
        dto.setStatus(NewsVoucherEnum.CREATE.code());
        dto.setReceiveTime(System.currentTimeMillis());
        dto.setRetryTime(0);
        dto.setEventVoucher(JSON.toJSONString(model));
        return dto;
    }
    public static InvoiceNewsVoucherDTO toDTO(TradeOrderChangeDTO model) {
        InvoiceNewsVoucherDTO dto = new InvoiceNewsVoucherDTO();
        dto.setOutBizId(model.getOrderNo());
//        OutBizTypeEnum outBizTypeEnum = OutStatusToOutBizType.TradeEnum.getByCode(model.getOrderStatus());
//        dto.setOutBizType(outBizTypeEnum.getCode());
        if (Objects.nonNull(model.getOrderStatus()) && model.getOrderStatus() == InvoiceConstants.CLOSED) {
            dto.setOutBizType(OutBizTypeEnum.PURCHASE_CANCEL.getCode());
        }else {
            dto.setOutBizType(OutBizTypeEnum.PURCHASE_PAYMENT.getCode());
        }
        dto.setStatus(NewsVoucherEnum.CREATE.code());
        dto.setReceiveTime(System.currentTimeMillis());
        dto.setRetryTime(0);
        dto.setEventVoucher(JSON.toJSONString(model));
        return dto;
    }


    public static InvoiceNewsVoucherDTO toDTO(CreditMessageDTO model) {
        InvoiceNewsVoucherDTO dto = new InvoiceNewsVoucherDTO();
        dto.setOutBizId(String.valueOf(model.getApplyId()));

        CreditMessageDTO.Type type = CreditMessageDTO.Type.getByCode(model.getType());
        if (Objects.isNull(type)) {
            log.error("CreditMessageDTO.Type is null,type:{}", model.getType());
        }
//        OutStatusToOutBizType.CreditEnum outBizTypeEnum = OutStatusToOutBizType.CreditEnum.getByCode(model.getType());
//        dto.setOutBizType(outBizTypeEnum.getOutBizTypeEnum().getCode());
        if (CreditMessageDTO.Type.REPAY_SUCCESS.equals(type)) {
            dto.setOutBizType(OutBizTypeEnum.CREDIT_REPAYMENT_SUCCESS.getCode());
        }else if (CreditMessageDTO.Type.REFUND_SUCCESS.equals(type)) {
            dto.setOutBizType(OutBizTypeEnum.CREDIT_REPAYMENT_REFUND.getCode());
        }
        dto.setStatus(NewsVoucherEnum.CREATE.code());
        dto.setReceiveTime(System.currentTimeMillis());
        dto.setRetryTime(0);
        dto.setEventVoucher(JSON.toJSONString(model));
        return dto;
    }
}
