package so.dian.invoice.converter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.pojo.dto.InvoiceDeductionDTO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.param.InvoiceDeductParam;
import so.dian.invoice.pojo.param.InvoiceRecoverParam;
import so.dian.invoice.pojo.param.OperatorParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductParam;
import so.dian.invoice.util.biz.BusinessTypeUtils;
import so.dian.yandang.client.pojo.enums.BizTypeEnum;
import so.dian.yandang.client.pojo.request.PayBillBatchReq;

/**
 * 发票核销 <br/>
 *
 * <AUTHOR>
 * @date 2019-10-30 15:38
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
public class InvoiceDeductionConverter {

    public static InvoiceDeductionDTO DO2DTO(InvoiceDeductionDO invoiceDeductionDO) {
        if (invoiceDeductionDO == null) {
            return null;
        }
        InvoiceDeductionDTO invoiceDeductionDTO = new InvoiceDeductionDTO();
        invoiceDeductionDTO.setId(invoiceDeductionDO.getId());
        invoiceDeductionDTO.setInvoiceCode(invoiceDeductionDO.getInvoiceCode() == null ? "" : invoiceDeductionDO.getInvoiceCode());
        invoiceDeductionDTO.setInvoiceNo(invoiceDeductionDO.getInvoiceNo());
        invoiceDeductionDTO.setBusinessNo(invoiceDeductionDO.getBusinessNo());
        invoiceDeductionDTO.setBusinessType(invoiceDeductionDO.getBusinessType());
        invoiceDeductionDTO.setOperateType(invoiceDeductionDO.getOperateType());
        invoiceDeductionDTO.setAmount(invoiceDeductionDO.getAmount());
        invoiceDeductionDTO.setReason(invoiceDeductionDO.getReason());
        invoiceDeductionDTO.setCreator(invoiceDeductionDO.getCreator());
        invoiceDeductionDTO.setCreateName(invoiceDeductionDO.getCreateName());
        invoiceDeductionDTO.setCreateTime(invoiceDeductionDO.getCreateTime());
        return invoiceDeductionDTO;
    }

    public static List<InvoiceDeductionDTO> DO2DTOList(List<InvoiceDeductionDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return LocalListUtils.transferList(list, invoiceDeductionDO -> DO2DTO(invoiceDeductionDO));
    }

    /**
     * 核销发票
     */
    public static InvoiceDeductionDO deductInvoice(InvoiceDO invoiceDO,
          InvoiceDeductParam param, OperatorParam operatorParam) {
        if (Objects.isNull(invoiceDO) || Objects.isNull(param) || Objects.isNull(operatorParam)) {
            return null;
        }
        InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setInvoiceCode(invoiceDO.getInvoiceCode() == null ? "" : invoiceDO.getInvoiceCode());
        invoiceDeductionDO.setInvoiceNo(invoiceDO.getInvoiceNo());
        invoiceDeductionDO.setBusinessNo(param.getBusinessNo());
        invoiceDeductionDO.setBusinessType(invoiceDO.getSubjectType());
        invoiceDeductionDO.setOperateType(OperateTypeEnum.DEDUCT.getType());
        invoiceDeductionDO.setCreator(ObjectUtils.defaultIfNull(operatorParam.getOperatorId(), 0L));
        invoiceDeductionDO.setCreateName(StringUtils.defaultIfBlank(operatorParam.getOperatorName(), ""));
        invoiceDeductionDO.setAmount(param.getAmount());
        invoiceDeductionDO.setReason(StringUtils.defaultIfBlank(operatorParam.getRemark(), ""));
        return invoiceDeductionDO;
    }

    /**
     * 核销发票
     */
    public static InvoiceDeductionDO deductInvoice(ScmInvoiceDeductParam param, OperatorParam operatorParam) {
        if (Objects.isNull(param) || Objects.isNull(operatorParam)) {
            return null;
        }
        InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setInvoiceCode(param.getInvoiceCode() == null ? "" : param.getInvoiceCode());
        invoiceDeductionDO.setInvoiceNo(param.getInvoiceNo());
        invoiceDeductionDO.setBusinessNo(param.getBusinessNo());
        invoiceDeductionDO.setBusinessType(param.getBusinessType());
        invoiceDeductionDO.setOperateType(OperateTypeEnum.DEDUCT.getType());
        invoiceDeductionDO.setCreator(ObjectUtils.defaultIfNull(operatorParam.getOperatorId(), 0L));
        invoiceDeductionDO.setCreateName(StringUtils.defaultIfBlank(operatorParam.getOperatorName(), ""));
        invoiceDeductionDO.setAmount(param.getAmount());
        invoiceDeductionDO.setReason(StringUtils.defaultIfBlank(operatorParam.getRemark(), ""));
        return invoiceDeductionDO;
    }

    /**
     * 核销回滚
     */
    public static InvoiceDeductionDO recoverInvoice(InvoiceDO invoiceDO,
          InvoiceRecoverParam param, OperatorParam operatorParam) {
        if (Objects.isNull(invoiceDO) || Objects.isNull(param) || Objects.isNull(operatorParam)) {
            return null;
        }
        InvoiceDeductionDO invoiceDeductionDO = new InvoiceDeductionDO();
        invoiceDeductionDO.setInvoiceCode(param.getInvoiceCode() == null ? "" : param.getInvoiceCode());
        invoiceDeductionDO.setInvoiceNo(param.getInvoiceNo());
        invoiceDeductionDO.setBusinessNo(param.getBusinessNo());
        invoiceDeductionDO.setBusinessType(param.getBusinessType());
        invoiceDeductionDO.setOperateType(OperateTypeEnum.RECOVER.getType());

        invoiceDeductionDO.setCreator(ObjectUtils.defaultIfNull(operatorParam.getOperatorId(), 0L));
        invoiceDeductionDO.setCreateName(StringUtils.defaultIfBlank(operatorParam.getOperatorName(), ""));
        invoiceDeductionDO.setAmount(param.getAmount());
        String reason = StringUtils.truncate(operatorParam.getRemark(), 250);
        invoiceDeductionDO.setReason(StringUtils.defaultIfBlank(reason, ""));
        return invoiceDeductionDO;
    }


    public static PayBillBatchReq getPayBillBatchReq(List<InvoiceDeductionDO> list){
        //组装付款单参数
        List<PayBillBatchReq.PayBillBusinessReq> reqList = list.stream().filter(obj->{
            BizTypeEnum bizTypeEnum = BusinessTypeUtils.toPayBillType(obj.getBusinessType());
            return Objects.nonNull(bizTypeEnum);
        }).map(obj -> {
            PayBillBatchReq.PayBillBusinessReq payBillBusinessReq = new PayBillBatchReq.PayBillBusinessReq();
            payBillBusinessReq.setOutBizNo(obj.getBusinessNo());
            BizTypeEnum bizTypeEnum= BusinessTypeUtils.toPayBillType(obj.getBusinessType());
            payBillBusinessReq.setBizType(bizTypeEnum.getCode());
            return payBillBusinessReq;
        }).collect(Collectors.toList());
        PayBillBatchReq payBillBatchReq = new PayBillBatchReq();
        payBillBatchReq.setBusinessList(reqList);
        return payBillBatchReq;
    }


}
