package so.dian.invoice.converter;

import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.hr.api.entity.common.HrCommonDTO;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.pojo.bo.InvoiceSubjectRelationBO;
import so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO;
import so.dian.invoice.enums.SystemUserEnum;
import so.dian.invoice.pojo.query.InvoiceSubjectRelationQuery;
import so.dian.invoice.enums.InvoiceSubjectRelationEnum;
import so.dian.invoice.enums.InvoiceSubjectRelationStatusEnum;
import so.dian.invoice.pojo.param.AddInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvoiceSubjectRelationPageParam;
import so.dian.invoice.pojo.vo.InvoiceSubjectRelationPageVO;

import static so.dian.invoice.constant.InvoiceSubjectRelationConstants.FINANCIAL_MANAGER_INSERT;

/**
 * SubjectRelationConverter
 *
 * <AUTHOR>
 */
public class SubjectRelationConverter {

    public static List<InvoiceSubjectRelationBO> convertDO2BO(List<InvoiceSubjectRelationDO> relationDOList) {
        return LocalListUtils.transferList(relationDOList, SubjectRelationConverter::convertDO2BO);
    }

    public static InvoiceSubjectRelationBO convertDO2BO(InvoiceSubjectRelationDO relationDO) {
        InvoiceSubjectRelationBO relationBO = new InvoiceSubjectRelationBO();
        relationBO.setId(relationDO.getId());
        relationBO.setBizId(relationDO.getBizId());
        relationBO.setBizType(relationDO.getBizType());
        relationBO.setSubjectName(relationDO.getSubjectName());
        relationBO.setRelationSubjectName(relationDO.getRelationSubjectName());
        relationBO.setRemark(relationDO.getRemark() == null ? "" : relationDO.getRemark());
        relationBO.setCreateTime(relationDO.getCreateTime().getTime());
        relationBO.setCreator(relationDO.getCreator());
        relationBO.setUpdateTime(relationDO.getUpdateTime().getTime());
        relationBO.setUpdater(relationDO.getUpdater());
        relationBO.setDeleted(relationDO.getDeleted());
        relationBO.setStatusEnum(LocalEnumUtils.findByCodeWithoutDefault(InvoiceSubjectRelationStatusEnum.class,
                relationDO.getStatus()));
        return relationBO;
    }

    public static InvoiceSubjectRelationQuery buildParam2Query(InvoiceSubjectRelationPageParam param) {
        InvoiceSubjectRelationQuery query = new InvoiceSubjectRelationQuery();
        query.setPageNo(param.getPageNo());
        query.setPageSize(param.getPageSize());
        if (StringUtils.isNotBlank(param.getSignSubjectName())) {
            query.setRelationSubjectName(param.getSignSubjectName());
        }
        if (StringUtils.isNotBlank(param.getSubjectName())) {
            query.setSubjectName(param.getSubjectName());
        }
        if (Objects.nonNull(param.getMerchantId())) {
            query.setBizId(param.getMerchantId());
        }
        InvoiceSubjectRelationStatusEnum status = LocalEnumUtils
                .findByCode(InvoiceSubjectRelationStatusEnum.class, param.getStatus());
        if (Objects.nonNull(status)) {
            query.setStatus(status.getCode());
        }
        return query;
    }

    public static HrCommonDTO buildIds2DTO(List<Integer> ids) {
        HrCommonDTO hrCommonDTO = new HrCommonDTO();
        hrCommonDTO.setIds(ids);
        return hrCommonDTO;
    }

    public static List<InvoiceSubjectRelationPageVO> convertBO2VO(
            List<InvoiceSubjectRelationBO> relationBOList,
            Map<Integer, AgentEmployeeDTO> employeeInfoMap) {

        List<InvoiceSubjectRelationPageVO> voList = Lists.newArrayList();
        relationBOList.forEach(relationBO->{
            InvoiceSubjectRelationPageVO vo = new InvoiceSubjectRelationPageVO();
            vo.setId(relationBO.getId());
            vo.setMerchantId(relationBO.getBizId());
            vo.setSignSubjectName(relationBO.getRelationSubjectName());
            vo.setSubjectName(relationBO.getSubjectName());
            vo.setStatus(relationBO.getStatusEnum().getCode());
            vo.setStatusStr(relationBO.getStatusEnum().getDesc());
            vo.setCreateTime(relationBO.getCreateTime());
            vo.setUpdateTime(relationBO.getUpdateTime());
            if (MapUtils.isNotEmpty(employeeInfoMap)) {
                AgentEmployeeDTO creatorInfo = employeeInfoMap.get(relationBO.getCreator());
                if (Objects.nonNull(creatorInfo) && Objects.nonNull(creatorInfo.getNickNameOrName())) {
                    if (Objects.equals(relationBO.getCreator(), SystemUserEnum.SYSTEM_USER.getCode())) {
                        vo.setCreator(SystemUserEnum.SYSTEM_USER.getDesc());
                    } else {
                        vo.setCreator(creatorInfo.getNickNameOrName());
                    }
                }
                AgentEmployeeDTO updaterInfo = employeeInfoMap.get(relationBO.getUpdater());
                if (Objects.nonNull(updaterInfo) && Objects.nonNull(updaterInfo.getNickNameOrName())) {
                    if (Objects.equals(relationBO.getUpdater(), SystemUserEnum.SYSTEM_USER.getCode())) {
                        vo.setUpdater(SystemUserEnum.SYSTEM_USER.getDesc());
                    } else {
                        vo.setUpdater(creatorInfo.getNickNameOrName());
                    }
                }
            }
            voList.add(vo);
        });
        return voList;
    }

    public static List<InvoiceSubjectRelationDO> buildParam2DO(AddInvoiceSubjectRelationParam param) {
        List<InvoiceSubjectRelationDO> relationDOList = Lists.newArrayList();
        param.getSubjectNameList().forEach(subjectName->{
            Date now = new Date();
            InvoiceSubjectRelationDO relationDO = new InvoiceSubjectRelationDO();
            relationDO.setBizId(param.getMerchantId());
            relationDO.setSubjectName(subjectName);
            relationDO.setRelationSubjectName(param.getSignSubjectName());
            relationDO.setCreateTime(now);
            relationDO.setCreator(param.getUserId());
            relationDO.setUpdateTime(now);
            relationDO.setUpdater(param.getUserId());
            relationDO.setRemark(FINANCIAL_MANAGER_INSERT);
            relationDO.setBizType(InvoiceSubjectRelationEnum.BizTypeEnum.MERCHANT.getCode());
            relationDO.setDeleted(InvoiceSubjectRelationEnum.DeletedEnum.UN_DELETED.getCode());
            relationDO.setStatus(InvoiceSubjectRelationStatusEnum.VALID.getCode());
            relationDOList.add(relationDO);
        });
        return relationDOList;
    }

    public static InvoiceSubjectRelationDO buildUpdateParam2DO(Integer id, Integer userId, InvoiceSubjectRelationStatusEnum status,
            InvoiceSubjectRelationStatusEnum beforeStatus) {
        InvoiceSubjectRelationDO relationDO = new InvoiceSubjectRelationDO();
        relationDO.setId(id);
        relationDO.setUpdater(userId);
        relationDO.setUpdateTime(new Date());
        relationDO.setStatus(status.getCode());
        relationDO.setBeforeStatus(beforeStatus.getCode());
        return relationDO;
    }
}
