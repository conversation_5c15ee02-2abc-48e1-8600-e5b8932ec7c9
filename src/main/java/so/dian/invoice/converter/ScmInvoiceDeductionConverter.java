package so.dian.invoice.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.invoice.enums.InvoiceStatusEnum;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.enums.SubjectTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.dto.ScmInvoiceDeductionDTO;
import so.dian.invoice.pojo.dto.ScmInvoiceInfoDTO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;

public class ScmInvoiceDeductionConverter {

    public static ScmInvoiceInfoDTO buildByInvoiceBO(InvoiceBO invoiceBO, List<SupplierInvoiceDetailDO> list) {
        ScmInvoiceInfoDTO scmInvoiceInfoDTO = new ScmInvoiceInfoDTO();

        scmInvoiceInfoDTO.setId(invoiceBO.getId().longValue());
        scmInvoiceInfoDTO.setInvoiceCode(invoiceBO.getInvoiceCode());
        scmInvoiceInfoDTO.setInvoiceNo(invoiceBO.getInvoiceNo());
        scmInvoiceInfoDTO.setSellerName(invoiceBO.getSeller());
        scmInvoiceInfoDTO.setBuyerName(invoiceBO.getBuyer());
        scmInvoiceInfoDTO.setInvoiceDate(invoiceBO.getGmtCreate().getTime());
        scmInvoiceInfoDTO.setInvoiceImg(invoiceBO.getUrl());
        SubjectTypeEnum businessTypeEnum = SubjectTypeEnum.findByType(invoiceBO.getSubjectType());
        scmInvoiceInfoDTO.setBusinessTypeStr(businessTypeEnum.getField());
        scmInvoiceInfoDTO.setBusinessType(invoiceBO.getSubjectType());

        InvoiceStatusEnum statusEnum = InvoiceStatusEnum.findByType(invoiceBO.getInvoiceStatus());
        scmInvoiceInfoDTO.setStatusStr(statusEnum.getField());
        scmInvoiceInfoDTO.setPrice(invoiceBO.getPrice());
        scmInvoiceInfoDTO.setUsedAmount(invoiceBO.getUsedAmount());
        scmInvoiceInfoDTO.setRemainAmount(invoiceBO.getPrice().subtract(invoiceBO.getUsedAmount()));
        scmInvoiceInfoDTO.setBatchNo(invoiceBO.getBatchNo());
        scmInvoiceInfoDTO.setInvoiceType(invoiceBO.getInvoiceType());

        if (CollectionUtil.isNotEmpty(list)) {
            List<String> verifyBillNoList = list.stream()
                  .map(SupplierInvoiceDetailDO::getVerifyBillNo)
                  .filter(StringUtils::isNotBlank)
                  .collect(Collectors.toList());
            scmInvoiceInfoDTO.setVerifyBillNoList(verifyBillNoList);
        }
        return scmInvoiceInfoDTO;
    }

    public static List<ScmInvoiceInfoDTO> buildInvoiceBOList(List<InvoiceBO> list, List<SupplierInvoiceDetailDO> detailList) {
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        if (CollectionUtil.isEmpty(detailList)) {
            detailList = Lists.newArrayList();
        }
        Map<String, List<SupplierInvoiceDetailDO>> listMap =
              detailList.stream().collect(Collectors.toMap(obj -> getUk(obj.getInvoiceNo(), obj.getInvoiceCode()),
                    Lists::newArrayList,
                    (v1, v2) -> {
                        v1.addAll(v2);
                        return v1;
                    }
              ));

        return LocalListUtils.transferList(list, obj -> ScmInvoiceDeductionConverter.buildByInvoiceBO(obj,
              listMap.get(getUk(obj.getInvoiceNo(), obj.getInvoiceCode()))));
    }

    public static String getUk(String invoiceNo, String invoiceCode) {
        return invoiceNo.concat(":").concat(invoiceCode);
    }

    public static ScmInvoiceDeductionDTO buildByInvoiceDeductionDO(Integer invoiceId, InvoiceDeductionDO invoiceDeductionDO) {
        if (Objects.isNull(invoiceDeductionDO)) {
            return null;
        }
        ScmInvoiceDeductionDTO scmInvoiceDeductionDTO = new ScmInvoiceDeductionDTO();
        scmInvoiceDeductionDTO.setDeductionId(invoiceDeductionDO.getId());
        scmInvoiceDeductionDTO.setInvoiceId(invoiceId);
        scmInvoiceDeductionDTO.setInvoiceCode(invoiceDeductionDO.getInvoiceCode());
        scmInvoiceDeductionDTO.setInvoiceNo(invoiceDeductionDO.getInvoiceNo());
        scmInvoiceDeductionDTO.setBusinessNo(invoiceDeductionDO.getBusinessNo());

        scmInvoiceDeductionDTO.setBusinessType(invoiceDeductionDO.getBusinessType());
        scmInvoiceDeductionDTO.setBusinessTypeStr(SubjectTypeEnum.getField(invoiceDeductionDO.getBusinessType()));

        scmInvoiceDeductionDTO.setOperateType(invoiceDeductionDO.getOperateType());
        scmInvoiceDeductionDTO.setOperateTypeStr(OperateTypeEnum.getField(invoiceDeductionDO.getOperateType()));
        scmInvoiceDeductionDTO.setAmount(invoiceDeductionDO.getAmount());
        scmInvoiceDeductionDTO.setReason(invoiceDeductionDO.getReason());
        scmInvoiceDeductionDTO.setCreator(invoiceDeductionDO.getCreator());
        scmInvoiceDeductionDTO.setCreateName(invoiceDeductionDO.getCreateName());
        scmInvoiceDeductionDTO.setCreateTime(invoiceDeductionDO.getCreateTime().getTime());
        return scmInvoiceDeductionDTO;
    }

    public static List<ScmInvoiceDeductionDTO> buildByInvoiceDeductionDOList(List<InvoiceDeductionDO> list,
          List<InvoiceBO> invoiceBOList) {
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        Map<String, InvoiceBO> invoiceBOMap = invoiceBOList.stream()
              .collect(Collectors.toMap(obj -> getUk(obj.getInvoiceNo(), obj.getInvoiceCode()), obj -> obj, (v1, v2) -> v1));
        return LocalListUtils.transferList(list, obj -> buildByInvoiceDeductionDO(
              invoiceBOMap.get(getUk(obj.getInvoiceNo(), obj.getInvoiceCode())).getId(), obj));
    }
}
