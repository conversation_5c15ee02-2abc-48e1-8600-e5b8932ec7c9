package so.dian.invoice.converter;

import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import so.dian.invoice.pojo.entity.CheckInvoiceEmployeeDO;
import so.dian.invoice.pojo.param.InvoiceCheckerParam;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 4:13 PM
 * @Description:
 */
public class CheckInvoiceEmployeeConverter {

	public static List<CheckInvoiceEmployeeDO> convertParam2DO(InvoiceCheckerParam param) {
		List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList = Lists.newLinkedList();
		param.getRegionList().forEach(region->{
			checkInvoiceEmployeeDOList.add(convertParam2DO(param.getChecker(), region));
		});
		return checkInvoiceEmployeeDOList;
	}

	public static List<CheckInvoiceEmployeeDO> convert2DO(List<String> regionList , Long checker) {
		List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList = Lists.newLinkedList();
		regionList.forEach(region->{
			checkInvoiceEmployeeDOList.add(convertParam2DO(checker, region));
		});
		return checkInvoiceEmployeeDOList;
	}

	private static CheckInvoiceEmployeeDO convertParam2DO(Long userId, String region) {
		Long now = System.currentTimeMillis();
		CheckInvoiceEmployeeDO checkInvoiceEmployeeDO = new CheckInvoiceEmployeeDO();
	    checkInvoiceEmployeeDO.setUserId(userId);
	    checkInvoiceEmployeeDO.setRegion(region);
	    checkInvoiceEmployeeDO.setGmtCreate(now);
	    checkInvoiceEmployeeDO.setGmtUpdate(now);
	    return checkInvoiceEmployeeDO;
	}


}
