package so.dian.invoice.converter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.invoice.enums.*;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceDetailBO;
import so.dian.invoice.pojo.dto.identify.DetailAndExtraDTO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.param.AddSingleInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceFilterTimeParam;
import so.dian.invoice.pojo.param.InvoiceInfoParam;
import so.dian.invoice.pojo.query.InvoiceFilterTimeQuery;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.InvoiceExportInfoVO;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;
import so.dian.invoice.pojo.vo.InvoiceVO;
import so.dian.invoice.util.DecimalUtil;
import so.dian.invoice.util.StringUtil;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.yandang.client.pojo.enums.PayBillStatusEnum;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 9:32 PM
 * @Description:
 */
@Slf4j(topic = "biz")
public class InvoiceConverter {

    public static List<InvoiceBO> convertDO2BO(List<InvoiceDO> invoiceDOList) {
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(invoiceDOList, InvoiceConverter::convertDO2BO);
    }

    /**
     * 不会返回null，只会返回null集合
     *
     * @param invoiceDOList
     * @return
     */
    public static List<InvoiceBO> convertDO2BONotNUll(List<InvoiceDO> invoiceDOList) {
        if (CollectionUtil.isEmpty(invoiceDOList)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(invoiceDOList, InvoiceConverter::convertDO2BO);
    }

    public static InvoiceBO convertDO2BO(InvoiceDO invoiceDO) {
        InvoiceBO invoiceBO = new InvoiceBO();
        invoiceBO.setId(invoiceDO.getId());
        invoiceBO.setInvoiceNo(invoiceDO.getInvoiceNo());
        invoiceBO.setSupplierNo(invoiceDO.getSupplierNo());
        invoiceBO.setInvoiceCode(invoiceDO.getInvoiceCode());
        invoiceBO.setSubjectType(invoiceDO.getSubjectType());
        invoiceBO.setSubjectName(invoiceDO.getSubjectName());
        invoiceBO.setBillNo(invoiceDO.getBillNo());
        invoiceBO.setInvoiceType(invoiceDO.getType());
        invoiceBO.setRawPrice(invoiceDO.getRawPrice());
        invoiceBO.setTaxPrice(invoiceDO.getTax());
        invoiceBO.setPrice(invoiceDO.getPrice());
        invoiceBO.setReceiveTime(invoiceDO.getReceiveTime());
        invoiceBO.setMemo(invoiceDO.getMemo());
        invoiceBO.setInvoiceStatus(invoiceDO.getStatus());
        invoiceBO.setUsedAmount(invoiceDO.getUsedAmount());
        invoiceBO.setGmtCreate(invoiceDO.getGmtCreate());
        invoiceBO.setGmtModified(invoiceDO.getGmtModified());
        invoiceBO.setIsDelete(invoiceDO.getIsDelete());
        invoiceBO.setCreator(invoiceDO.getCreator());
        invoiceBO.setCreateTime(invoiceDO.getCreateTime());
        invoiceBO.setInvoiceSource(invoiceDO.getSource());
        invoiceBO.setKind(invoiceDO.getKind());
        invoiceBO.setCheckCode(invoiceDO.getCheckCode());
        invoiceBO.setTax(invoiceDO.getTax());
        invoiceBO.setSeller(invoiceDO.getSeller() == null ? "" : invoiceDO.getSeller());
        invoiceBO.setSellerTaxId(invoiceDO.getSellerTaxId());
        invoiceBO.setBuyer(invoiceDO.getBuyer() == null ? "" : invoiceDO.getBuyer());
        invoiceBO.setBuyerTaxId(invoiceDO.getBuyerTaxId());
        invoiceBO.setUrl(invoiceDO.getUrl());
        invoiceBO.setIsReal(invoiceDO.getIsReal());
        invoiceBO.setBatchNo(invoiceDO.getBatchNo());
        invoiceBO.setBelongSubjectType(invoiceDO.getBelongSubjectType());
        invoiceBO.setBelongSubjectId(invoiceDO.getBelongSubjectId());
        invoiceBO.setInvoiceProcessStatusEnum(LocalEnumUtils.findByCodeWithoutDefault(InvoiceProcessStatusEnum.class,
                invoiceDO.getProcessStatus()));
        invoiceBO.setReviewer(invoiceDO.getReviewer());
        invoiceBO.setReceiveTime(invoiceDO.getReceiveTime());
        invoiceBO.setReviewRemark(invoiceDO.getReviewRemark());
        invoiceBO.setValidateCode(invoiceDO.getValidateCode());
        invoiceBO.setTaxRate(invoiceDO.getTaxRate());
        return invoiceBO;
    }

    public static List<InvoiceDetailDO> convertRecord2DOList(InvoiceIdentifyRecordDO identifyRecordDO) {
        if (Objects.isNull(identifyRecordDO) || Objects.isNull(identifyRecordDO.getDetails())) {
            return Lists.newArrayList();
        }
        List<String> itemNameList = Arrays.asList(identifyRecordDO.getDetails().split(","));
        List<InvoiceDetailDO> invoiceDetailDOList = Lists.newArrayList();
        BigDecimal zero = new BigDecimal(0);
        itemNameList.forEach(itemName -> {
            InvoiceDetailDO invoiceDetail = new InvoiceDetailDO();
            invoiceDetail.setUnit("");
            invoiceDetail.setUnitPrice(zero);
            invoiceDetail.setTaxRate(zero);
            invoiceDetail.setRawPrice(zero);
            invoiceDetail.setTaxPrice(zero);
            invoiceDetail.setPrice(zero);
            invoiceDetail.setQuantity(0);
            invoiceDetail.setMaterialName(itemName);
            invoiceDetail.setMaterialSpec("");
            invoiceDetail.setInvoiceCode(identifyRecordDO.getInvoiceCode() == null ? "" : identifyRecordDO.getInvoiceCode());
            invoiceDetail.setInvoiceNo(identifyRecordDO.getInvoiceNo());
            invoiceDetailDOList.add(invoiceDetail);
        });
        return invoiceDetailDOList;
    }

    public static List<InvoiceExportInfoVO> convertBO2VO(
            List<InvoiceBO> invoiceBOList,
            Map<Integer, String> employeeNickMap,
            List<InvoiceDetailBO> invoiceDetailBOList) {
        List<InvoiceExportInfoVO> invoiceExportInfoVOList = new LinkedList<>();
        invoiceBOList.forEach(invoiceBO -> {
            InvoiceExportInfoVO invoiceExportInfoVO = new InvoiceExportInfoVO();
            invoiceExportInfoVO.setInvoiceId(invoiceBO.getId());
            invoiceExportInfoVO.setInvoiceNo(invoiceBO.getInvoiceNo());
            invoiceExportInfoVO.setInvoiceCode(invoiceBO.getInvoiceCode());
            invoiceExportInfoVO.setSubjectName(invoiceBO.getSubjectName());
            if (Objects.nonNull(invoiceBO.getInvoiceType())) {
                invoiceExportInfoVO.setInvoiceType(InvoiceTypeEnum.getField(invoiceBO.getInvoiceType()));
            }
            if (Objects.nonNull(invoiceBO.getIsReal())) {
                invoiceExportInfoVO.setIsRealStr(InvoiceIdentifyRecordEnum.IsRealEnum.getDescByCode(invoiceBO.getIsReal()));
            }
            invoiceExportInfoVO.setRawPrice(invoiceBO.getRawPrice());
            invoiceExportInfoVO.setTaxPrice(invoiceBO.getTaxPrice());
            invoiceExportInfoVO.setPrice(invoiceBO.getPrice());
            invoiceExportInfoVO.setGmtCreate(invoiceBO.getGmtCreate());
            invoiceExportInfoVO.setSubjectTypeStr(SubjectTypeEnum.getField(invoiceBO.getSubjectType()));
            invoiceExportInfoVO.setValidateCodeStr(InvoiceIdentifyRecordEnum.ValidationCodeEnum.getDescByCode(invoiceBO.getValidateCode()));
            String creatorName = employeeNickMap.get(invoiceBO.getCreator());
            if (StringUtils.isNotBlank(creatorName)) {
                invoiceExportInfoVO.setCreatorName(creatorName);
            } else {
                invoiceExportInfoVO.setCreatorName("");
            }
            invoiceExportInfoVO.setSeller(invoiceBO.getSubjectName());
            invoiceExportInfoVO.setBuyer(invoiceBO.getBuyer());
            if (CollectionUtils.isNotEmpty(invoiceDetailBOList)) {
                invoiceExportInfoVO.setDetails(buildFirstInvoiceDetail(invoiceDetailBOList, invoiceBO));
            }
            if (Objects.nonNull(invoiceBO.getInvoiceStatus())) {
                invoiceExportInfoVO.setStatus(InvoiceStatusEnum.getField(invoiceBO.getInvoiceStatus()));
            }
            invoiceExportInfoVO.setUsedAmount(invoiceBO.getUsedAmount());
            if (Objects.nonNull(invoiceBO.getInvoiceProcessStatusEnum())) {
                invoiceExportInfoVO.setProcessStatus(invoiceBO.getInvoiceProcessStatusEnum().getDesc());
            }
            invoiceExportInfoVO.setInvoiceCreateTime(invoiceBO.getCreateTime());
            invoiceExportInfoVO.setUrl(invoiceBO.getUrl());
            if(Objects.nonNull(invoiceBO.getTaxRate())){
                invoiceExportInfoVO.setTaxRate(invoiceBO.getTaxRate().stripTrailingZeros().toPlainString()+"%");
            }
            invoiceExportInfoVOList.add(invoiceExportInfoVO);
        });
        return invoiceExportInfoVOList;
    }

    private static String buildFirstInvoiceDetail(List<InvoiceDetailBO> invoiceDetailBOList, InvoiceBO invoiceBO) {
        List<InvoiceDetailBO> newInvoiceDetailBOList = Lists.newArrayList();
        String invoiceOnlyCode = invoiceBO.getInvoiceNo() + "_" + invoiceBO.getInvoiceCode();
        invoiceDetailBOList.forEach(invoiceDetailBO -> {
            String invoiceDetailOnlyCode = invoiceDetailBO.getInvoiceNo() + "_" + invoiceDetailBO.getInvoiceCode();
            if (Objects.equals(invoiceOnlyCode, invoiceDetailOnlyCode)) {
                InvoiceDetailBO detailBO = new InvoiceDetailBO();
                detailBO.setId(invoiceDetailBO.getId());
                detailBO.setInvoiceNo(invoiceDetailBO.getInvoiceNo());
                detailBO.setMaterialName(invoiceDetailBO.getMaterialName());
                detailBO.setMaterialSpec(invoiceDetailBO.getMaterialSpec());
                detailBO.setUnit(invoiceDetailBO.getUnit());
                detailBO.setUnitPrice(invoiceDetailBO.getUnitPrice());
                detailBO.setQuantity(invoiceDetailBO.getQuantity());
                detailBO.setRawPrice(invoiceDetailBO.getRawPrice());
                detailBO.setTaxRate(invoiceDetailBO.getTaxRate());
                detailBO.setTaxPrice(invoiceDetailBO.getTaxPrice());
                detailBO.setGmtCreate(invoiceDetailBO.getGmtCreate());
                detailBO.setGmtModified(invoiceDetailBO.getGmtModified());
                detailBO.setIsDelete(invoiceDetailBO.getIsDelete());
                detailBO.setPrice(invoiceDetailBO.getPrice());
                detailBO.setInvoiceCode(invoiceDetailBO.getInvoiceCode());
                newInvoiceDetailBOList.add(detailBO);
            }
        });
        if (CollectionUtils.isEmpty(newInvoiceDetailBOList)) {
            return "";
        }
        InvoiceDetailBO detailBO = newInvoiceDetailBOList.get(0);
        return detailBO.getMaterialName() == null ? "" : detailBO.getMaterialName();
    }

    public static List<Map<String, String>> convertByBOList(List<InvoiceBO> invoiceBOList) {
        List<Map<String, String>> invoiceNoAndCodeMapList = Lists.newArrayList();
        invoiceBOList.forEach(invoiceBO -> {
            Map<String, String> invoiceNoAndCodeMap = Maps.newHashMap();
            invoiceNoAndCodeMap.put("invoiceNo", invoiceBO.getInvoiceNo());
            invoiceNoAndCodeMap.put("invoiceCode", invoiceBO.getInvoiceCode());
            invoiceNoAndCodeMapList.add(invoiceNoAndCodeMap);
        });
        return invoiceNoAndCodeMapList;
    }

    public static InvoiceFilterTimeQuery buildParam2Query(InvoiceFilterTimeParam param, CurrentUserReq currentUserReq) {
        InvoiceFilterTimeQuery query = new InvoiceFilterTimeQuery();
        Long offset = (param.getPageNo().intValue() - 1L) * param.getPageSize();
        DateTime dateTime = DateUtil.beginOfMonth(new Date(param.getFilterTime()));
        query.setFilterTime(dateTime);
        query.setLoginUserId(currentUserReq.getUserId());
        query.setBelongSubjectType(currentUserReq.getBelongSubjectType());
        query.setPageNo(param.getPageNo());
        query.setPageSize(param.getPageSize());
        query.setOffset(offset);
        return query;
    }

    public static List<InvoiceVO> convertBO2VO(List<InvoiceBO> invoiceBOList) {
        return LocalListUtils.transferList(invoiceBOList, InvoiceConverter::convertBO2VO);
    }

    public static InvoiceVO convertBO2VO(InvoiceBO invoiceBO) {
        InvoiceVO invoiceVO = new InvoiceVO();
        invoiceVO.setCreateTime(invoiceBO.getCreateTime());
        invoiceVO.setInvoiceCode(invoiceBO.getInvoiceCode());
        invoiceVO.setInvoiceNo(invoiceBO.getInvoiceNo());
        invoiceVO.setInvoiceId(invoiceBO.getId());
        invoiceVO.setSeller(invoiceBO.getSubjectName());
        invoiceVO.setStatusStr(InvoiceStatusEnum.getField(invoiceBO.getInvoiceStatus()));
        invoiceVO.setPrice(invoiceBO.getPrice());
        return invoiceVO;
    }

    public static InvoiceIdentifyRecordDO buildParam2RecordDO(AddSingleInvoiceParam param, String result) {
        InvoiceIdentifyRecordDO invoiceIdentifyRecordDO = new InvoiceIdentifyRecordDO();
        Date now = new Date();
        invoiceIdentifyRecordDO.setInvoiceNo(param.getInvoiceNo());
        invoiceIdentifyRecordDO.setInvoiceCode(param.getInvoiceCode());
        invoiceIdentifyRecordDO.setInvoiceType(param.getType());
        invoiceIdentifyRecordDO.setInvoiceDate(DateUtil.formatDateTime(param.getGmtCreate()));
        invoiceIdentifyRecordDO.setCheckCode(param.getCheckCode());
        invoiceIdentifyRecordDO.setPretaxAmount(param.getRawPrice());
        invoiceIdentifyRecordDO.setTotal(param.getPrice());
        invoiceIdentifyRecordDO.setTax(param.getTax());
        invoiceIdentifyRecordDO.setSeller(param.getSeller());
        invoiceIdentifyRecordDO.setSellerTaxId(param.getSellerTaxId());
        invoiceIdentifyRecordDO.setBuyer(param.getBuyer());
        invoiceIdentifyRecordDO.setBuyerTaxId(param.getBuyerTaxId());
        invoiceIdentifyRecordDO.setKind(param.getKind());
        invoiceIdentifyRecordDO.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.STAY.getCode());
        invoiceIdentifyRecordDO.setIsRelated(InvoiceIdentifyRecordEnum.IsRelatedEnum.YES.getCode());
        invoiceIdentifyRecordDO.setUrl(param.getUrl());
        invoiceIdentifyRecordDO.setCreateTime(now);
        invoiceIdentifyRecordDO.setCreator(param.getLoginUserId());
        invoiceIdentifyRecordDO.setUpdateTime(now);
        invoiceIdentifyRecordDO.setUpdater(param.getLoginUserId());
        invoiceIdentifyRecordDO.setDeleted(InvoiceIdentifyRecordEnum.DeletedEnum.UN_DELETED.getCode());
        invoiceIdentifyRecordDO.setDetails(param.getDetails());
        invoiceIdentifyRecordDO.setIsIdentify(InvoiceIdentifyRecordEnum.IsIdentifyEnum.SUC.getCode());
        invoiceIdentifyRecordDO.setResult(result);

        CurrentUserReq currentUserReq = param.getCurrentUserReq();
        // 跟小电抬头以及税号做校验，校验失败时，记录错误原因
        String buyerTaxId = BuyerTaxIdEnum.getTaxIdByBuyer(param.getBuyer());
        if (!InvoiceBuyerValidator.checkInvoiceBuyerAndTaxId(currentUserReq.getBelongSubjectType(),buyerTaxId, param.getBuyerTaxId())) {
            invoiceIdentifyRecordDO.setReason("抬头或税号信息校验失败");
        }
        if (StringUtils.isBlank(buyerTaxId)) {
            buyerTaxId = param.getBuyerTaxId();
        }
        invoiceIdentifyRecordDO.setBuyerTaxId(buyerTaxId);
        return invoiceIdentifyRecordDO;
    }

    public static InvoiceDO buildAddParam2DO(AddSingleInvoiceParam param, InvoiceIdentifyRecordDO recordDO,
                                             Integer belongSubjectId, Integer belongSubjectType, String employeeNick, Boolean comparedResult) {
        InvoiceDO invoiceDO = new InvoiceDO();
        Date now = new Date();
        invoiceDO.setProcessStatus(InvoiceProcessStatusEnum.ALREADY_ENTRY.getCode());
        invoiceDO.setKind(param.getKind());
        invoiceDO.setInCheckPool(InCheckPoolStatusEnum.NOT_ENTERED.getCode());
        invoiceDO.setCheckCode(param.getCheckCode());
        invoiceDO.setTax(new BigDecimal(param.getTax()));
        invoiceDO.setSeller(StringUtil.trimFull2Half(param.getSeller()));
        invoiceDO.setSellerTaxId(param.getSellerTaxId());
        invoiceDO.setBuyer(StringUtil.trimFull2Half(param.getBuyer()));
        String buyerTaxId = BuyerTaxIdEnum.getTaxIdByBuyer(param.getBuyer());
        if(StringUtils.isBlank(buyerTaxId)){
            buyerTaxId=param.getBuyerTaxId();
        }
        invoiceDO.setBuyerTaxId(buyerTaxId);

        invoiceDO.setUrl(param.getUrl());
        invoiceDO.setIsReal(recordDO.getIsReal());
        invoiceDO.setInvoiceNo(param.getInvoiceNo());
        invoiceDO.setInvoiceCode(param.getInvoiceCode());
        invoiceDO.setSupplierNo("");
        invoiceDO.setType(recordDO.getInvoiceType());
        invoiceDO.setRawPrice(new BigDecimal(param.getRawPrice()));
        invoiceDO.setPrice(new BigDecimal(param.getPrice()));
        invoiceDO.setReceiveTime(now);
        invoiceDO.setMemo(param.getMemo());
        invoiceDO.setStatus(InvoiceStatusEnum.WAIT.getType());
        invoiceDO.setUsedAmount(BigDecimal.valueOf(0));
        invoiceDO.setGmtCreate(param.getGmtCreate());
        invoiceDO.setGmtModified(param.getGmtCreate());
        invoiceDO.setIsDelete(InvoiceIdentifyRecordEnum.DeletedEnum.UN_DELETED.getCode());
        invoiceDO.setTax(new BigDecimal(param.getTax()));
        invoiceDO.setCreator(param.getLoginUserId());
        invoiceDO.setCreateTime(now);
        invoiceDO.setSource(InvoiceSourceEnum.AUTO.getType());
        invoiceDO.setSubjectType(param.getSubjectType());
        invoiceDO.setSubjectName(StringUtil.trimFull2Half(param.getSeller()));
        invoiceDO.setBelongSubjectType(belongSubjectType);
        invoiceDO.setBelongSubjectId(belongSubjectId);
        invoiceDO.setCreateName(employeeNick == null ? "" : employeeNick);
        if (comparedResult) {
            invoiceDO.setNeedCheck(0);
        } else {
            invoiceDO.setNeedCheck(1);
        }

        // 设置发票列表的税率
        try {
            if (StringUtils.isNotBlank(recordDO.getResult()) && recordDO.getResult().startsWith("{")) {
                DetailAndExtraDTO detailAndExtraDTO = JSON.parseObject(recordDO.getResult(), DetailAndExtraDTO.class);
                if (null != detailAndExtraDTO
                        && null != detailAndExtraDTO.getDetails()
                        && CollectionUtils.isNotEmpty(detailAndExtraDTO.getDetails().getItems())) {
                    String taxRate = StringUtil.isBlank(detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate())
                            ? "" : detailAndExtraDTO.getDetails().getItems().get(0).getTax_rate().replace("%", "");
                    invoiceDO.setTaxRate(DecimalUtil.transBigDecimal(taxRate, 4));
                }
            }
        } catch (Exception e) {
            log.error("------ 设置发票列表的税率，发票识别结果解析出错。", e);
            invoiceDO.setTaxRate(new BigDecimal(0.0));
        }

        return invoiceDO;
    }

    public static InvoiceDO buildInvoiceParam2DO(InvoiceInfoParam param, Boolean comparedResult) {
        InvoiceDO invoiceDO = new InvoiceDO();
        invoiceDO.setBatchNo(param.getBatchNo());
        invoiceDO.setKind(param.getKind());
        invoiceDO.setCheckCode(param.getCheckCode());
        invoiceDO.setTax(param.getTax());
        invoiceDO.setSeller(param.getSeller());
        invoiceDO.setSellerTaxId(param.getSellerTaxId());
        invoiceDO.setBuyer(param.getBuyer());
        invoiceDO.setUrl(param.getUrl());
        invoiceDO.setIsReal(param.getIsReal());
        invoiceDO.setId(param.getId());
        invoiceDO.setInvoiceNo(param.getInvoiceNo());
        invoiceDO.setInvoiceCode(param.getInvoiceCode());
        invoiceDO.setSupplierNo(param.getSupplierNo());
        invoiceDO.setBillNo(param.getBillNo());
        invoiceDO.setType(param.getType());
        invoiceDO.setRawPrice(param.getRawPrice());
        invoiceDO.setPrice(param.getPrice());
        invoiceDO.setReceiveTime(param.getReceiveTime());
        invoiceDO.setMemo(param.getMemo());
        invoiceDO.setStatus(param.getStatus());
        invoiceDO.setUsedAmount(param.getUsedAmount());
        invoiceDO.setGmtCreate(param.getGmtCreate());
        invoiceDO.setGmtModified(param.getGmtModified());
        invoiceDO.setIsDelete(param.getIsDelete());
        invoiceDO.setTax(param.getTaxPrice());
        invoiceDO.setCreator(param.getCreator());
        invoiceDO.setCreateTime(param.getCreateTime());
        invoiceDO.setSource(param.getSource());
        invoiceDO.setSubjectType(param.getSubjectType());
        invoiceDO.setSubjectName(param.getSubjectName());
        invoiceDO.setBelongSubjectType(param.getBelongSubjectType());
        invoiceDO.setBelongSubjectId(param.getBelongSubjectId());
        if (!comparedResult) {
            invoiceDO.setNeedCheck(1);
        }
        String buyerTaxId = BuyerTaxIdEnum.getTaxIdByBuyer(param.getBuyer());
        if (StringUtils.isBlank(buyerTaxId)) {
            buyerTaxId = param.getBuyerTaxId();
        }
        invoiceDO.setBuyerTaxId(buyerTaxId);
        return invoiceDO;
    }

    public static List<InvoiceInfoVO> convertDO2VOList(List<InvoiceDO> list, Long agentId) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(list, obj -> convertDO2VO(obj, agentId));
    }

    public static InvoiceInfoVO convertDO2VO(InvoiceDO invoice, Long agentId) {
        if (Objects.isNull(invoice)) {
            return null;
        }
        InvoiceInfoVO invoiceInfo = new InvoiceInfoVO();
        // 设置对象属性
        invoiceInfo.setInvoiceId(invoice.getId());
        invoiceInfo.setInvoiceSellerName(invoice.getSubjectName());
        invoiceInfo.setBusinessTypeCode(invoice.getSubjectType());
        invoiceInfo.setBusinessTypeName(BusinessTypeEnum.getField(invoice.getSubjectType()));
        invoiceInfo.setInvoiceCode(invoice.getInvoiceCode() == null ? "" : invoice.getInvoiceCode());
        invoiceInfo.setInvoiceNo(invoice.getInvoiceNo());
        invoiceInfo.setInvoiceDate(DateUtil.format(invoice.getGmtCreate(), "yyyy-MM-dd"));
        invoiceInfo.setTotalAmount(invoice.getPrice());
        invoiceInfo.setUnUseAmount(invoice.getPrice().subtract(invoice.getUsedAmount()));
        invoiceInfo.setInvoiceStatus(invoice.getStatus());
        invoiceInfo.setInvoiceStatusName(InvoiceStatusEnum.getField(invoice.getStatus()));
        invoiceInfo.setBatchNo(invoice.getBatchNo());
        invoiceInfo.setBuyer(invoice.getBuyer());
        invoiceInfo.setNeedShow(computeNeedShow(invoice.getBelongSubjectType(), agentId));
        return invoiceInfo;
    }

    /**
     * 发票台账导出填充核销信息
     *
     * @param invoiceExportInfoVO
     * @param deductionDO
     * @return
     */
    public static void fillInvoiceExportInfoVOByInvocieDeduction(InvoiceExportInfoVO invoiceExportInfoVO, InvoiceDeductionDO deductionDO) {
        if (Objects.isNull(deductionDO)) {
            return;
        }
        invoiceExportInfoVO.setBusinessNo(deductionDO.getBusinessNo());
        invoiceExportInfoVO.setDeductionCreateTime(deductionDO.getCreateTime());
        invoiceExportInfoVO.setAmount(deductionDO.getAmount());
    }

    /**
     * 发票台账导出填充付款单信息
     *
     * @param invoiceExportInfoVO
     * @param payBillInfoRsp
     * @return
     */
    public static void fillInvoiceExportInfoVOByPayBillInfoRsp(InvoiceExportInfoVO invoiceExportInfoVO, PayBillInfoRsp payBillInfoRsp) {
        if (Objects.isNull(payBillInfoRsp)) {
            return;
        }
        invoiceExportInfoVO.setBillNo(payBillInfoRsp.getBillNo());
        if (Objects.nonNull(payBillInfoRsp.getTradeState())) {
            PayBillStatusEnum payBillStatusEnum = so.dian.himalaya.util.LocalEnumUtils.findByCodeWithoutDefault(PayBillStatusEnum.class, payBillInfoRsp.getTradeState());
            if (Objects.nonNull(payBillStatusEnum)) {
                invoiceExportInfoVO.setTradeStatus(payBillStatusEnum.getDesc());
            }
        }
        if (!Objects.isNull(payBillInfoRsp.getPayFinishTime())) {
            invoiceExportInfoVO.setPayTime(new Date(payBillInfoRsp.getPayFinishTime()));
        }

        invoiceExportInfoVO.setMerchantId(payBillInfoRsp.getObjectId());
        invoiceExportInfoVO.setMerchantName(payBillInfoRsp.getObjectName());
    }

    private static boolean computeNeedShow(Integer belongSubjectType, Long agentId) {
        if (BelongSubjectTypeEnum.XIAODIAN.getCode().equals(belongSubjectType)) {
            return agentId == 0L || agentId == 1L || agentId == 3L;
        } else if (BelongSubjectTypeEnum.JV_CORP.getCode().equals(belongSubjectType)) {
            return agentId != 0L && agentId != 1L && agentId != 3L;
        }
        return false;
    }

}
