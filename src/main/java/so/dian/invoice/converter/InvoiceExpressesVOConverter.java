package so.dian.invoice.converter;

import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.dto.InvoiceExpressesDto;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;
import so.dian.invoice.pojo.vo.InvoiceExpressesVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class InvoiceExpressesVOConverter {
    final static BigDecimal hundred=BigDecimal.valueOf(100);
    public static InvoiceExpressesVO fromInvoiceExpressesDTO(InvoiceExpressesDto invoiceExpressesDto){

        InvoiceExpressesVO invoiceExpressesVO=new InvoiceExpressesVO();
        if(null!=invoiceExpressesDto) {
            BeanUtils.copyProperties(invoiceExpressesDto, invoiceExpressesVO);
            InvoiceProcessStatusEnum invoiceProcessStatusEnum = InvoiceProcessStatusEnum.getByCode(invoiceExpressesDto.getProcessStatus());
            if (null != invoiceProcessStatusEnum) {
                invoiceExpressesVO.setProcessStatusName(invoiceProcessStatusEnum.getDesc());
            }else{
                invoiceExpressesVO.setProcessStatusName(InvoiceProcessStatusEnum.ALREADY_ENTRY.getDesc());
            }
            invoiceExpressesVO.setCreateTime(invoiceExpressesDto.getCreateTime().getTime());
            invoiceExpressesVO.setGmtCreate(invoiceExpressesDto.getGmtCreate().getTime());
            invoiceExpressesVO.setCreatorName(invoiceExpressesDto.getCreateName());

            if(Objects.nonNull(invoiceExpressesDto.getPrice())&&Objects.nonNull(invoiceExpressesDto.getUsedAmount())){
                //未核销金额
               BigDecimal unUsedAmount= invoiceExpressesDto.getPrice().subtract(invoiceExpressesDto.getUsedAmount()).multiply(hundred).setScale(0, RoundingMode.DOWN);
               invoiceExpressesVO.setUnusedAmount(unUsedAmount.longValue());

            }
            //单位分
            if(Objects.nonNull(invoiceExpressesDto.getPrice())) {
                BigDecimal price= invoiceExpressesDto.getPrice().multiply(hundred).setScale(0, RoundingMode.DOWN);
                invoiceExpressesVO.setPrice(price.longValue());
                invoiceExpressesVO.setSeller(invoiceExpressesDto.getSubjectName());
                //invoiceExpressesVO.setPrice(invoiceExpressesDto.getPrice().longValue());
            }

            return  invoiceExpressesVO;
        }
       return null;
    }
}
