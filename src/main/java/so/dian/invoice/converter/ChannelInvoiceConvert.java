package so.dian.invoice.converter;

import org.apache.commons.lang3.math.NumberUtils;
import so.dian.invoice.enums.ChannelInvoiceStatus;
import so.dian.invoice.pojo.vo.ChannelInvoiceVO;
import so.dian.invoice.pojo.entity.ChannelInvoiceDO;
import so.dian.invoice.pojo.param.AddChannelInvoiceParam;

import java.util.Optional;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2022/10/28 14:31
 * @description:
 */
public class ChannelInvoiceConvert {

    public ChannelInvoiceDO from(AddChannelInvoiceParam param) {
        ChannelInvoiceDO invoiceDO = new ChannelInvoiceDO();
        invoiceDO.setId(param.getId());
        invoiceDO.setStatus(ChannelInvoiceStatus.ENABLE.getCode());
        invoiceDO.setTitle(param.getTitle());
        invoiceDO.setTaxNo(param.getTaxNo());
        invoiceDO.setProvinceCode(NumberUtils.createInteger(param.getProvinceCode()));
        invoiceDO.setCityCode(NumberUtils.createInteger(param.getCityCode()));
        invoiceDO.setAreaCode(NumberUtils.createInteger(param.getAreaCode()));
        invoiceDO.setAddressDetail(param.getAddressDetail());
        invoiceDO.setReceiver(param.getReceiver());
        invoiceDO.setReceiverPhone(param.getReceiverPhone());
        return invoiceDO;
    }

    public ChannelInvoiceVO from(ChannelInvoiceDO invoiceDO) {

        ChannelInvoiceVO invoiceDTO = new ChannelInvoiceVO();
        invoiceDTO.setId(invoiceDO.getId());
        invoiceDTO.setTitle(invoiceDO.getTitle());
        invoiceDTO.setTaxNo(invoiceDO.getTaxNo());
        invoiceDTO.setReceiver(invoiceDO.getReceiver());
        invoiceDTO.setReceiverPhone(invoiceDO.getReceiverPhone());
        invoiceDTO.setProvinceCode(Optional.ofNullable(invoiceDO.getProvinceCode()).map(Object::toString).orElse(null));
        invoiceDTO.setCityCode(Optional.ofNullable(invoiceDO.getCityCode()).map(Object::toString).orElse(null));
        invoiceDTO.setAreaCode(Optional.ofNullable(invoiceDO.getAreaCode()).map(Object::toString).orElse(null));
        invoiceDTO.setAddressDetail(invoiceDO.getAddressDetail());
        return invoiceDTO;
    }
}
