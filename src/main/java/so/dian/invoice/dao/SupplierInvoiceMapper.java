package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.invoice.pojo.dto.BillNoDTO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;

import java.util.List;
import java.util.Set;

/**
 * 供应商发票主表
 *
 * <AUTHOR>
 * @date 2021-03-16 19:03:50
 */
public interface SupplierInvoiceMapper {

    /**
     * 新增SupplierInvoice
     *
     * @param supplierInvoiceDO
     */
    int insert(@Param("do") SupplierInvoiceDO supplierInvoiceDO);

    int insertFetchId(@Param("do") SupplierInvoiceDO supplierInvoiceDO);

    /**
     * 新增SupplierInvoice（非空字段）
     *
     * @param supplierInvoiceDO
     */
    int insertSelective(@Param("do") SupplierInvoiceDO supplierInvoiceDO);

    /**
     * 批量新增SupplierInvoice
     *
     * @param supplierInvoiceDOList
     * @return
     */
    int insertBatch(@Param("doList") List<SupplierInvoiceDO> supplierInvoiceDOList);

    /**
     * 根据id更新SupplierInvoice
     *
     * @param supplierInvoiceDO
     */
    int updateById(@Param("do") SupplierInvoiceDO supplierInvoiceDO);

    /**
     * 根据条件查询SupplierInvoice总数
     *
     * @param supplierInvoicePageReq
     */
    Long countByReq(@Param("do") SupplierInvoicePageParam param,
                    @Param("billNoDTOList") List<BillNoDTO> billNoList,@Param("supplierNos") Set<String> supplierNos);

    /**
     * 条件分页查询所有未删除记录
     *
     * @param supplierInvoicePageReq 页面查询条件
     * @param pageRequest            分页
     */
    List<SupplierInvoiceDO> findPageByReq(@Param("do") SupplierInvoicePageParam param, @Param("billNoDTOList") List<BillNoDTO> billNoList,
                                          @Param("supplierNos")Set<String> supplierNos,@Param("page") PageRequest pageRequest);

    /**
     * 根据id查询SupplierInvoice
     *
     * @param id
     */
    SupplierInvoiceDO findById(@Param("id") Integer id);

    int deleteById(@Param("id") Integer supplierInvoiceId);

    SupplierInvoiceDO findSupplierNoCode(@Param("invoiceNo")String invoiceNo,@Param("invoiceCode") String invoiceCode);

    SupplierInvoiceDO findByIdAndStatus(@Param("id")Integer supplierInvoiceId,@Param("status") Integer status);
}