package so.dian.invoice.dao;

import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestDTO;
import so.dian.invoice.pojo.entity.InvoiceRequestDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestQueryParam;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【invoice_request(开票申请)】的数据库操作Mapper
 * @createDate 2025-03-06 14:25:21
 * @Entity so.dian.invoice.pojo.entity.InvoiceChangeRecord
 */
public interface InvoiceRequestDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceRequestDO record);

    int insertSelective(InvoiceRequestDO record);

    InvoiceRequestDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceRequestDO record);

    int updateByPrimaryKey(InvoiceRequestDO record);

    List<InvoiceRequestDTO> listByParam(InvoiceRequestQueryParam param);
}
