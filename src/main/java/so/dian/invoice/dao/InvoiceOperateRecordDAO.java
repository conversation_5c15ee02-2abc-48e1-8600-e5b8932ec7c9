package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceOperateRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【invoice_operate_record(发票操作记录表)】的数据库操作Mapper
 * @createDate 2025-09-04 14:25:21
 * @Entity so.dian.invoice.pojo.entity.InvoiceOperateRecordDO
 */
public interface InvoiceOperateRecordDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceOperateRecordDO record);

    int insertSelective(InvoiceOperateRecordDO record);

    InvoiceOperateRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceOperateRecordDO record);

    int updateByPrimaryKey(InvoiceOperateRecordDO record);

    List<InvoiceOperateRecordDO> listByRequestId(@Param("requestId") Long requestId);

    int batchInsert(@Param("records") List<InvoiceOperateRecordDO> records);
}
