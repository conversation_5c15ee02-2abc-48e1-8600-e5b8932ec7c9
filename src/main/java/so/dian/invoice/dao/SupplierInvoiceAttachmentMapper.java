package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.enums.SupplierAttachmentTypeEnum;
import so.dian.invoice.pojo.entity.SupplierInvoiceAttachmentDO;

import java.util.List;

/**
 * 供应商发票明细单据号
 *
 * <AUTHOR>
 * @date 2021-03-17 16:03:51
 */
public interface SupplierInvoiceAttachmentMapper {

    /**
     * 新增SupplierInvoiceAttachment
     *
     * @param supplierInvoiceAttachmentDO
     */
    int insert(@Param("do") SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO);

    /**
     * 根据id更新SupplierInvoiceAttachment
     *
     * @param supplierInvoiceAttachmentDO
     */
    int updateById(@Param("do") SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO);

    /**
     * 查询发票图片
     * @see SupplierAttachmentTypeEnum
     */
    List<SupplierInvoiceAttachmentDO> findListBySupplierInvoiceId(@Param("supplierInvoiceId") Integer supplierInvoiceId,@Param("type") Integer type);

    /**
     * 根据id查询SupplierInvoiceAttachment
     *
     * @param id
     */
    SupplierInvoiceAttachmentDO findById(@Param("id") Integer id);

    int deleteBySupplierInvoiceId(@Param("supplierInvoiceId") Integer supplierInvoiceId);
}