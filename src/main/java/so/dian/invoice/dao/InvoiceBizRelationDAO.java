package so.dian.invoice.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceBizRelationDO;

public interface InvoiceBizRelationDAO {

    int insert(InvoiceBizRelationDO invoiceBizRelationDO);

    int batchInsert(List<InvoiceBizRelationDO> list);

    int update(InvoiceBizRelationDO invoiceBizRelationDO);

    InvoiceBizRelationDO findOne(@Param("invoiceCode") String invoiceCode, @Param("invoiceNo") String invoiceNo,
          @Param("businessNo") String businessNo, @Param("businessType") Integer businessType);
}