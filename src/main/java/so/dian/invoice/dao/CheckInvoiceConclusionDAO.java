package so.dian.invoice.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.CheckInvoiceConclusionDO;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 6:49 PM
 * @Description:
 */
public interface CheckInvoiceConclusionDAO {

	/**
	 * 新增质检结论
	 * @param checkInvoiceConclusionDO
	 * @return
	 */
	Integer insert(CheckInvoiceConclusionDO checkInvoiceConclusionDO);

	/**
	 * 查询质检结论
	 * @return
	 */
	List<CheckInvoiceConclusionDO> selectAll();

	/**
	 * 更新质检结论
	 * @param id 主键ID
	 * @param code 结论code
	 * @param gmtUpdate 更新时间
	 * @param beforeGmtUpdate 之前的更新时间
	 * @return
	 */
	Integer update(@Param("id") Long id, @Param("code") String code,
			@Param("gmtUpdate") Long gmtUpdate, @Param("beforeGmtUpdate") Long beforeGmtUpdate);

	/**
	 * 逻辑删除质检结论
	 * @param id
	 * @param gmtUpdate
	 * @return
	 */
	Integer logicDeleted(@Param("id")Long id, @Param("gmtUpdate") Long gmtUpdate);

	/**
	 * 通过ID获取质检结论
	 * @param id
	 * @return
	 */
	CheckInvoiceConclusionDO getById(@Param("id")Long id);

	/**
	 * 通过code查询质检结论
	 * @param code
	 * @return
	 */
	CheckInvoiceConclusionDO getByCode(@Param("code")String code);

	/**
	 * 更新直接结论为已使用
	 * @param id
	 * @param isUsed
	 * @param gmtUpdate
	 * @param beforeIsUsed
	 * @return
	 */
	Integer updateIsUsedById(@Param("id") Long id, @Param("isUsed") Integer isUsed,
			@Param("gmtUpdate") Long gmtUpdate, @Param("beforeIsUsed") Integer beforeIsUsed);

}
