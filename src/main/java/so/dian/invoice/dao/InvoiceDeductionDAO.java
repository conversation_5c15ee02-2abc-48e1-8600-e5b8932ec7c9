package so.dian.invoice.dao;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;

public interface InvoiceDeductionDAO {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(InvoiceDeductionDO record);

    InvoiceDeductionDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(InvoiceDeductionDO record);

    int updateByPrimaryKey(InvoiceDeductionDO record);

    /**
     * 根据业务单号查询核销的发票列表
     */
    List<InvoiceDeductionDO> findByBusinessNo(@Param("businessNo") String businessNo,
          @Param("businessType") Integer businessType, @Param("operateType") Integer operateType);

    /**
     * 查询核销的发票明细
     */
    List<InvoiceDeductionDO> selectInvoiceDeductionList(@Param("businessNo") String businessNo,
          @Param("invoiceCode") String invoiceCode, @Param("invoiceNo") String invoiceNo,
          @Param("businessType") Integer businessType, @Param("operateType") Integer operateType);

    /**
     * 根据业务单号查询付款申请单核销的发票列表
     */
    List<InvoiceDeductionDO> findPayInvoiceByBusinessNo(@Param("businessNo") String businessNo,
          @Param("operateType") Integer operateType);

    /**
     * 获取发票核销记录-按单号分组，取最后一条记录。
     */
    List<InvoiceDeductionDO> findTopByInvoiceNoAndInvoiceCode(@Param("invoiceNo") String invoiceNo,
          @Param("invoiceCode") String invoiceCode);

    /**
     * 根据发票号和发票code查询发票核销列表并按照创建时间倒序排序
     */
    List<InvoiceDeductionDO> findByInvoiceNoAndInvoiceCode(@Param("invoiceNo") String invoiceNo,
          @Param("invoiceCode") String invoiceCode);

    /**
     * 根据创建时间查询核销的发票信息（剔除重复）
     */
    List<InvoiceDeductionDO> findByCreateTimeAfter(@Param("createTime") Date createTime);

    List<String> selectByInvoiceCodeAndNo(@Param("invoiceCode") String invoiceCode, @Param("invoiceNo") String invoiceNo,
          @Param("businessNoList") List<String> businessNoList, @Param("operateType") Integer operateType);

    /**
     * 发票核销记录
     */
    List<InvoiceDeductionDO> findByIdIn(@Param("ids") List<Long> ids);

    int recoverInvoice(@Param("deductionId") Integer deductionId, @Param("source") Integer source,
          @Param("target") Integer target);
}