package so.dian.invoice.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.CheckInvoiceEmployeeDO;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 6:50 PM
 * @Description:
 */
public interface CheckInvoiceEmployeeDAO {

	/**
	 * 查询所有员工大区配置
	 * @return
	 */
	List<CheckInvoiceEmployeeDO> selectAll();

	/**
	 * 新增员工大区配置
	 * @param checkInvoiceEmployeeDOList
	 * @return
	 */
	Integer batchInsert(@Param("checkInvoiceEmployeeDOList") List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList);

	/**
	 * 修改员工大区配置
	 * @param checkInvoiceEmployeeDO
	 * @return
	 */
	Integer update(CheckInvoiceEmployeeDO checkInvoiceEmployeeDO);

	/**
	 * 通过员工ID查找配置信息
	 * @param userId
	 * @return
	 */
	List<CheckInvoiceEmployeeDO> getByUserId(@Param("userId")Long userId);

	/**
	 * 通过ID获取员工大区配置
	 * @param id
	 * @return
	 */
	CheckInvoiceEmployeeDO getById(@Param("id")Long id);

	/**
	 * 批量逻辑删除
	 * @param idList
	 * @return
	 */
	Integer batchLogicDeleted(@Param("idList")List<Long> idList);

	/**
	 * 获得所有的质检大区列表
	 * @return
	 */
	List<String> getAllRegionsDistinct();

	/**
	 * 通过userId和大区名称搜索
	 * @return
	 */
	CheckInvoiceEmployeeDO getByUserIdAndRegion(@Param("userId") Long userId,@Param("region") String region);

}
