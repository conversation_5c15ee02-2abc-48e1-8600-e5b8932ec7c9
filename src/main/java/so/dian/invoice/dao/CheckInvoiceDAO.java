package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.dto.CheckInvoiceDTO;
import so.dian.invoice.pojo.entity.CheckInvoiceDO;
import so.dian.invoice.pojo.param.InvoiceCheckPageParam;

import java.util.List;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 6:48 PM
 * @Description:
 */
public interface CheckInvoiceDAO {

	/**
	 * 分页查询质检发票
	 * @param param
	 * @return
	 */
	List<CheckInvoiceDO> selectCheckInvoiceList(InvoiceCheckPageParam param);

	/**
	 * 发票质检列表
	 * @param param
	 * @return
	 */
	List<CheckInvoiceDO> listCheckInvoice(InvoiceCheckPageParam param);

	/**
	 * 统计质检发票数量
	 * @param param
	 * @return
	 */
	Long count(InvoiceCheckPageParam param);

	/**
	 * 批量新增质检发票
	 * @param checkInvoiceDOList
	 * @return
	 */
	Integer batchInsert(@Param("checkInvoiceDOList") List<CheckInvoiceDO> checkInvoiceDOList);

	/**
	 * 新增质检发票
	 * @param checkInvoiceDO
	 * @return
	 */
	Integer insert(CheckInvoiceDO checkInvoiceDO);

	/**
	 * 更新质检发票
	 * @param checkInvoiceDO
	 * @return
	 */
	Integer update(CheckInvoiceDO checkInvoiceDO);

	/**
	 * 通过ID查询质检发票
	 * @param id
	 * @return
	 */
	CheckInvoiceDO getById(@Param("id")Long id);

	/**
	 * 查看质检结论使用条数
	 * @param conclusionId
	 * @return
	 */
	Long countByConclusionId(@Param("conclusionId")Long conclusionId);
	/**
	 * 分页查询发票质检列表
	 * @param param
	 * @return
	 */
	List<CheckInvoiceDTO> checkInvoicePage(InvoiceCheckPageParam param);

}
