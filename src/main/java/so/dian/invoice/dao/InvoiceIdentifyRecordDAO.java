package so.dian.invoice.dao;

import java.util.Date;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.param.InvoiceRecordParam;

import java.util.List;

public interface InvoiceIdentifyRecordDAO {

    int insertSelective(InvoiceIdentifyRecordDO record);

    InvoiceIdentifyRecordDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(InvoiceIdentifyRecordDO record);

    void batchDelete(@Param("idList") List<Integer> idList, @Param("updater") Integer updater);

    int getListCount(@Param("isReal") Integer isReal, @Param("isIdentify") Integer isIdentify,@Param("creator") Long creator);

    List<InvoiceIdentifyRecordDO> getList(@Param("isReal") Integer isReal, @Param("isIdentify") Integer isIdentify,@Param("creator") Long creator);

    List<InvoiceIdentifyRecordDO> getListByIds(@Param("ids") List<Integer> ids);

    Integer selectInvoiceByInvoiceCodeAndNo(@Param("invoiceCode") String invoiceCode,
            @Param("invoiceNo") String invoiceNo);

    InvoiceIdentifyRecordDO selectInvoiceRecodeByInvoiceCodeAndNo(@Param("invoiceCode") String invoiceCode,
            @Param("invoiceNo") String invoiceNo);

    int updateIsRelatedByIds(@Param("ids") List<Integer> ids, @Param("isRelated") Integer isRelated);

    /**
     * 查询发票识别记录列表（通用查询）
     *
     * @param invoiceRecordParam
     * @return
     */
    List<InvoiceIdentifyRecordDO> selectInvoiceRecordList(InvoiceRecordParam invoiceRecordParam);

    /**
     * 通过minId分页查询19-3~19-10月份的数据
     * @param minId
     * @return
     */
    List<InvoiceIdentifyRecordDO> selectInvoiceRecordByMinId(@Param("minId")Long minId);
}