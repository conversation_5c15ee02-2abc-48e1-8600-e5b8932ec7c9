package so.dian.invoice.dao;

import java.util.List;
import so.dian.invoice.pojo.entity.InvoiceOperateLogDO;

/**
 * @Author: jiaoge
 * @Date: 2019/9/12 11:23 AM
 * @Description:
 */
public interface InvoiceOperateLogDAO {

    /**
     * 新增发票日志
     */
    int insert(InvoiceOperateLogDO invoiceOperateLogDO);

    /**
     * 批量新增发票日志
     */
    int insertBatch(List<InvoiceOperateLogDO> list);

    /**
     * 根据发票ID查询发票日志
     */
    List<InvoiceOperateLogDO> findByInvoiceId(Long invoiceId);
}
