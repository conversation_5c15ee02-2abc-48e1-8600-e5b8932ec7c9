package so.dian.invoice.dao;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;

public interface InvoiceDetailDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(InvoiceDetailDO record);

    int insertBatch(List<InvoiceDetailDO> list);

    int insertSelective(InvoiceDetailDO record);

    InvoiceDetailDO selectByPrimaryKey(Integer id);

    List<InvoiceDetailDO> selectInvoiceDetailList(@Param("invoiceCode") String invoiceCode, @Param("invoiceNo") String invoiceNo);

    int updateByPrimaryKeySelective(InvoiceDetailDO record);

    int updateByPrimaryKey(InvoiceDetailDO record);

    int updateInvoiceDetailByInvoiceCodeAndNo(InvoiceDetailDO record);

    List<InvoiceDetailDO> listInvoiceDetailByNoAndCode(
          @Param("invoiceNoAndCodeMapList") List<Map<String, String>> invoiceNoAndCodeMapList);

    List<Integer> findSupplierInvoiceDetailId(@Param("list") List<Map<String, String>> list);
}