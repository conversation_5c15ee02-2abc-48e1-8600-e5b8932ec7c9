package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.ChannelInvoiceDO;

public interface ChannelInvoiceDao {
    int deleteByPrimaryKey(Long id);

    int insert(ChannelInvoiceDO record);

    int insertSelective(ChannelInvoiceDO record);

    ChannelInvoiceDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ChannelInvoiceDO record);

    int updateByPrimaryKey(ChannelInvoiceDO record);


    ChannelInvoiceDO selectBySettleSubjectId(@Param("settleSubjectId") Long settleSubjectId);
}