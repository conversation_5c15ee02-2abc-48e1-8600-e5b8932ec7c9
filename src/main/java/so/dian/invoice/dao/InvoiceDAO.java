package so.dian.invoice.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.date.DateTime;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceQueryBO;
import so.dian.invoice.pojo.dto.InvoiceDetailDto;
import so.dian.invoice.pojo.dto.InvoiceDto;
import so.dian.invoice.pojo.dto.InvoiceExpressesDto;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.param.InvoiceDeductQueryParam;
import so.dian.invoice.pojo.param.InvoiceExportParam;
import so.dian.invoice.pojo.param.InvoiceParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductQueryParam;
import so.dian.invoice.pojo.query.InvoiceExpressQuery;
import so.dian.invoice.pojo.query.InvoiceFilterTimeQuery;

public interface InvoiceDAO {

    int insertBatch(List<InvoiceDO> list);

    int insertSelective(InvoiceDO record);

    InvoiceDO selectByPrimaryKey(Integer id);

    InvoiceDO selectInvoiceByInvoiceCodeAndNo(@Param("invoiceCode") String invoiceCode, @Param("invoiceNo") String invoiceNo);

    List<InvoiceExpressesDto> selectInvoiceExpressesDto(InvoiceExpressQuery invoiceExpressQuery);

    List<String> selectCodeAndNoByInvoiceNoList(@Param("invoiceNoList") List<String> invoiceNoList);

    List<InvoiceDO> selectInvoiceByInvoiceNoList(@Param("invoiceNoList") List<String> invoiceNoList);

    int updateByPrimaryKeySelective(InvoiceDO record);

    int updateInvoiceByInvoiceCodeAndNo(InvoiceDO record);

    List<InvoiceDetailDto> selectInvoiceDetailAll(InvoiceParam param);

    List<InvoiceDto> selectInvoicePage(InvoiceParam param);

    int count(InvoiceParam param);

    /**
     * 查询发票列表（通用查询）
     *
     * @param param
     * @return
     */
    List<InvoiceDO> selectInvoiceList(InvoiceParam param);

    /**
     * 发票复核
     * @param id
     * @param reviewer
     * @param reviewTime
     * @param reviewRemark
     * @param processStatus
     * @param beforeProcessStatus
     * @return
     */
    int updateInvoiceToReview(@Param("id") Integer id, @Param("reviewer") Integer reviewer,
            @Param("reviewTime") Date reviewTime, @Param("reviewRemark") String reviewRemark,
            @Param("processStatus") Integer processStatus, @Param("beforeProcessStatus") Integer beforeProcessStatus);

    /**
     * 统计导出发票台账数量
     * @param param
     * @return
     */
    Long countInvoiceExportList(InvoiceExportParam param);


    /**
     * 分页查询导出发票列表
     * @param param
     * @return
     */
    List<InvoiceDO> listInvoiceExportPage(InvoiceExportParam param);

    int batchUpdateInvoiceProcessStatus(@Param("invoiceIds")List<Long> invoiceIds,@Param("processStatus") Integer processStatus);

    List<String> listInvoiceBySubjectName(@Param("subjectName") String subjectName,
            @Param("belongSubjectId")Integer belongSubjectId);

    Long countInvoice(InvoiceFilterTimeQuery query);

    List<InvoiceDO> selectInvoice(InvoiceFilterTimeQuery query);
    /**
     * 根据条件查询未核销完的发票列表
     */
    List<InvoiceDO> findByInvoiceDeductQueryParam(InvoiceDeductQueryParam param);
    /**
     * 批量查询发票信息
     */
    List<InvoiceDO> findByInvoiceNoAndInvoiceCode(List<InvoiceQueryBO> list);
    /**
     * 回滚发票
     */
    int recoverInvoice(@Param("id") Integer id, @Param("status") Integer status, @Param("amount") BigDecimal amount);

    int deductInvoice(@Param("id") Integer id, @Param("status") Integer status,
          @Param("source") BigDecimal source,@Param("target") BigDecimal target);

    /**
     * 根据类型批量查询发票信息
     */
    List<InvoiceDO> findBySubjectType(@Param("subjectType") Integer subjectType, @Param("lastId") Long lastId);

    /**
     * 处理特殊字符
     */
    int trimInvoice(InvoiceDO record);

    List<InvoiceDO> listNeedValidateByCreateTime(@Param("startCreateTime") Date startCreateTime,
                                                 @Param("endCreateTime") Date endCreateTime,
                                                 @Param("typeList") List<Integer> typeList,
                                                 @Param("isRealListEnum") List<InvoiceIdentifyRecordEnum.IsRealEnum> isRealListEnum);

    List<InvoiceDO> listByIds(@Param("ids") List<Long> ids);

    int batchUpdateRealStatus(@Param("isReal") Integer isReal, @Param("ids") List<Long> ids);

    /**
     * 定时任务查询上上周需要质检的发票信息
     * @param beginTime
     * @param endTime
     * @return
     */
    @Deprecated
    List<InvoiceDO> listInvoiceByTime(@Param("beginTime")Date beginTime, @Param("endTime")Date endTime);

    /**
     * 条件获取需要进入质检池的发票
     * @param invoiceIdList
     * @return
     */
    List<InvoiceDO> listNeedCheckInvoiceByIds(@Param("invoiceIdList")List<Long> invoiceIdList);

    /**
     * 批量更新进入质检池的发票状态
     * @param idList
     * @param inCheckPool
     * @param beforeInCheckPool
     * @return
     */
    Integer batchUpdateInCheckPool(@Param("idList")List<Long> idList, @Param("inCheckPool")Integer inCheckPool,
            @Param("beforeInCheckPool")Integer beforeInCheckPool);

    /**
     * 更新发票验真信息
     * @param id 发票id
     * @param isReal 验真状态
     * @param validateCode 验真错误码
     * @return
     */
    int updateValidationById(@Param("id") Integer id, @Param("isReal") Integer isReal, @Param("validateCode")String validateCode);

    List<InvoiceDO> listNotValidate(@Param("startTime") DateTime startTime,
                                    @Param("endTime") DateTime endTime,
                                    @Param("validationCode") List<InvoiceIdentifyRecordEnum.ValidationCodeEnum> validationCode);

    List<InvoiceDO> selectValidateInvoice(@Param("startTime") Date startTime,
                                          @Param("endTime") Date endTime,
                                          @Param("isReal") InvoiceIdentifyRecordEnum.IsRealEnum isReal);

    /**
     * 追溯历史需要质检的发票
     * @param endTime
     * @return
     */
    List<InvoiceDO> listInvoiceByEndTime(@Param("endTime")Date endTime);

    /**
     * 追溯历史小二端录入的发票列表(根据是否质检不为空的记录判断)
     * @return
     */
    List<InvoiceDO> listNeedCheckIsNoNull();

    /**
     * 追溯历史小二端录入的发票列表(根据是否质检不为空的记录判断)
     * @return
     */
    void batchUpdateCityCode(@Param("invoiceIds") List<Integer> invoiceIds,@Param("creatorCityCodeMap") Map<Integer,Integer> creatorCityCodeMap);

    List<InvoiceDO> findByIdGreaterThan(@Param("id")Integer id);

    List<InvoiceDO> queryInvoiceList(ScmInvoiceDeductQueryParam param);


}