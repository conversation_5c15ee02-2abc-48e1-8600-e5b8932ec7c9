package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceManageDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【invoice_manage(开票管理表)】的数据库操作Mapper
 * @createDate 2025-03-06 14:25:21
 * @Entity so.dian.invoice.pojo.entity.InvoiceChangeRecord
 */
public interface InvoiceManageDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceManageDO record);

    int insertSelective(InvoiceManageDO record);

    InvoiceManageDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceManageDO record);

    int updateByPrimaryKey(InvoiceManageDO record);

    List<InvoiceManageDO> list(InvoiceManageQueryParam param);

    List<InvoiceManageDO> listByIds(@Param("ids") List<Long> ids);

    InvoiceManageDO getByBizNoAndBizType(@Param("bizNo") String bizNo, @Param("bizType") Integer bizType);

}
