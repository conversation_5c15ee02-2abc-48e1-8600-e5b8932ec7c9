package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【invoice_change_record(应开变更记录表)】的数据库操作Mapper
* @createDate 2025-03-06 14:25:21
* @Entity so.dian.invoice.pojo.entity.InvoiceChangeRecord
*/
public interface InvoiceChangeRecordDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceChangeRecordDO record);

    int insertSelective(InvoiceChangeRecordDO record);

    InvoiceChangeRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceChangeRecordDO record);

    int updateByPrimaryKey(InvoiceChangeRecordDO record);

    List<InvoiceChangeRecordDO> listByManageId(Long manageId);

    InvoiceChangeRecordDO getByBizNoAndBizType(@Param("outBizId") String outBizId, @Param("outBizType") Integer outBizType);

}
