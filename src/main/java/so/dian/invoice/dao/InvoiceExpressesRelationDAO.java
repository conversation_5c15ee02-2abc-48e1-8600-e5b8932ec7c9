package so.dian.invoice.dao;

import java.util.Date;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.dto.InvoiceExpressesUpdateDto;
import so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO;
import so.dian.invoice.pojo.query.InvoiceExpressesRelationQuery;

import java.util.List;

public interface InvoiceExpressesRelationDAO {

    Long insert(InvoiceExpressesRelationDO invoiceExpressesRelationDO);



    void insertBatch(List<InvoiceExpressesRelationDO> list);
    InvoiceExpressesRelationDO getInvoiceExpressesRelationByInvoiceId(@Param("invoiceId") Long invoiceID);

    List<InvoiceExpressesRelationDO> findInvoiceExpressesRelations(@Param("deleted")Integer deleted,@Param("invoiceIds") List<Long> invoiceIds);

    /**
     * 只获取id和发票id字段的内容
     * @param deleted
     * @param invoiceIds
     * @return
     */
    List<InvoiceExpressesRelationDO> findSimpleInvoiceExpressesRelations(@Param("deleted")Integer deleted,@Param("invoiceIds") List<Long> invoiceIds);

    int updateInvoiceExpressesRelationById(InvoiceExpressesRelationDO invoiceExpressesRelationDO);

    int updateBatchInvoiceExpressesRelationById(InvoiceExpressesUpdateDto invoiceExpressesRelationDO);

    List<Long> listInvoiceIdsByExpressNo(@Param("expressNo") String expressNo);

    List<InvoiceExpressesRelationDO> findAllByQuery(InvoiceExpressesRelationQuery query);

    int countByQuery(InvoiceExpressesRelationQuery query);

    List<InvoiceExpressesRelationDO> listInvoiceExpressesRelationByTime(@Param("beginTime")Date beginTime, @Param("endTime")Date endTime);
}
