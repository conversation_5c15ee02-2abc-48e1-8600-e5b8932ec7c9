package so.dian.invoice.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO;
import so.dian.invoice.pojo.query.InvoiceSubjectRelationQuery;

public interface InvoiceSubjectRelationDAO {

    int insertSelective(InvoiceSubjectRelationDO record);

    InvoiceSubjectRelationDO selectByPrimaryKey(Integer id);

    /**
     * 发票主体对应关系查询
     */
    InvoiceSubjectRelationDO selectByBizIdAndBizTypeAndSubjectName(@Param("bizId") Integer bizId,
          @Param("bizType") Integer bizType, @Param("subjectName") String subjectName);

    /**
     * 根据发票关联主体名称获取发票主体名称列表
     */
    List<String> getSubjectNameList(@Param("relationSubjectName") String relationSubjectName);

    int deleteByIdList(@Param("idList") List<Integer> idList);

    Long countInvoiceSubjectRelation(InvoiceSubjectRelationQuery query);

    List<InvoiceSubjectRelationDO> selectInvoiceSubjectRelationList(InvoiceSubjectRelationQuery query);

    List<InvoiceSubjectRelationDO> listByMerchantIdAndRelationSubjectName(@Param("merchantId") Integer merchantId,
          @Param("relationSubjectName") String relationSubjectName);

    Integer batchInsert(List<InvoiceSubjectRelationDO> list);

    Integer updateInvoiceSubjectRelation(InvoiceSubjectRelationDO invoiceSubjectRelationDO);

    /**
     * 根据商户查询映射主体
     */
    List<InvoiceSubjectRelationDO> findByBizIdAndBizType(
          @Param("bizId") Long bizId, @Param("bizType") Integer bizType);

    /**
     * 处理特殊字符
     */
    int trimRelation(InvoiceSubjectRelationDO record);

    /**
     * 批量查询映射主体
     */
    List<InvoiceSubjectRelationDO> batchQuery(@Param("lastId") Long lastId);

    /**
     * 根据主体名称和业务类型查询
     */
    List<InvoiceSubjectRelationDO> listByRelationSubjectNameAndBizType(
            @Param("relationSubjectName") String relationSubjectName, @Param("bizType") Integer bizType);
}