package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【invoice_request_record(开票申请记录)】的数据库操作Mapper
 * @createDate 2025-03-06 14:25:21
 * @Entity so.dian.invoice.pojo.entity.InvoiceChangeRecord
 */
public interface InvoiceRequestRecordDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceRequestRecordDO record);

    int insertSelective(InvoiceRequestRecordDO record);

    InvoiceRequestRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceRequestRecordDO record);

    int updateByPrimaryKey(InvoiceRequestRecordDO record);

    List<InvoiceRequestRecordDO> list(InvoiceRequestRecordDO record);

    List<InvoiceRequestRecordDO> listByManageIds(@Param("manageIds") List<Long> manageIds);

    List<InvoiceRequestRecordDO> getByRequestId(@Param("requestId") Long requestId);
}
