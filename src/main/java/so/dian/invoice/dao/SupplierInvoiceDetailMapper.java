package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.dto.BillNoDTO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.param.SupplierInvoiceDetailPageParam;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;

import java.util.List;
import java.util.Set;

/**
 * 供应商发票明细表
 *
 * <AUTHOR>
 * @date 2021-03-17 17:03:12
 */
public interface SupplierInvoiceDetailMapper {

    /**
     * 条件查询所有未删除记录
     *
     * @param supplierInvoiceDetailPageParam 页面查询条件
     */
    List<SupplierInvoiceDetailDO> findListByReq(@Param("do") SupplierInvoiceDetailPageParam param, @Param("supplierNos") Set<String> supplierNos);

    /**
     * 新增SupplierInvoiceDetail
     *
     * @param supplierInvoiceDetailDO
     */
    int insert(@Param("do") SupplierInvoiceDetailDO supplierInvoiceDetailDO);

    int insertFetchId(@Param("do") SupplierInvoiceDetailDO supplierInvoiceDetailDO);

    /**
     * 批量新增SupplierInvoiceDetail
     *
     * @param supplierInvoiceDetailDOList
     * @return
     */
    int insertBatch(@Param("doList") List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList);

    /**
     * 根据id更新SupplierInvoiceDetail
     *
     * @param supplierInvoiceDetailDO
     */
    int updateById(@Param("do") SupplierInvoiceDetailDO supplierInvoiceDetailDO);

    /**
     * 根据条件查询SupplierInvoiceDetail总数
     *
     * @param supplierInvoiceDetailPageParam
     */
    Long countByReq(@Param("do") SupplierInvoicePageParam param, @Param("billNoDTOList") List<BillNoDTO> billNoDTOList, @Param("supplierNos") Set<String> supplierNos);


    List<SupplierInvoiceDetailDO> findExcelListByReq(@Param("do") SupplierInvoicePageParam param, @Param("billNoDTOList") List<BillNoDTO> billNoDTOList,
                                                     @Param("supplierNos") Set<String> supplierNos);

    /**
     * 根据id查询SupplierInvoiceDetail
     *
     * @param id
     */
    SupplierInvoiceDetailDO findById(@Param("id") Integer id);

    int deleteBySupplierInvoiceId(@Param("supplierInvoiceId") Integer supplierInvoiceId);

    List<SupplierInvoiceDetailDO> findListBySupplierInvoiceId(@Param("supplierInvoiceId") Integer supplierInvoiceId);

    int updateStatusBySupplierInvoiceId(@Param("supplierInvoiceId")Integer supplierInvoiceId,@Param("status") Integer status);

    List<SupplierInvoiceDetailDO> findListById(@Param("ids")List<Integer> ids);
}