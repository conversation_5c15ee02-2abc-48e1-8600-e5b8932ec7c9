package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.invoice.enums.SupplierInvoiceBillNoEnum;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailBillNoDO;

import java.util.List;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;

/**
 * 供应商发票明细单据号
 *
 * <AUTHOR>
 * @date 2021-03-17 20:03:23
 */
public interface SupplierInvoiceDetailBillNoMapper {

    /**
     * 新增SupplierInvoiceDetailBillNo
     */
    int insert(@Param("do") SupplierInvoiceDetailBillNoDO supplierInvoiceDetailBillNoDO);

    /**
     * 批量新增SupplierInvoiceDetailBillNo
     */
    int insertBatch(@Param("doList") List<SupplierInvoiceDetailBillNoDO> supplierInvoiceDetailBillNoDOList);

    /**
     * 根据id更新SupplierInvoiceDetailBillNo
     */
    int updateById(@Param("do") SupplierInvoiceDetailBillNoDO supplierInvoiceDetailBillNoDO);

    /**
     * 根据id查询SupplierInvoiceDetailBillNo
     */
    SupplierInvoiceDetailBillNoDO findById(@Param("id") Long id);

    int deleteBySupplierInvoiceId(@Param("supplierInvoiceId") Integer supplierInvoiceId);

    List<SupplierInvoiceDetailBillNoDO> findInDetailId(@Param("detailIdList") List<Integer> detailIdList);

    List<SupplierInvoiceDetailBillNoDO> findByBillTypeAndBillNo(@Param("billType") Integer billType, @Param("billNo") String billNo);
}