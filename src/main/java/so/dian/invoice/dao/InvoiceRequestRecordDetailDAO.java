package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【invoice_request_record_detail(开票申请记录明细)】的数据库操作Mapper
 * @createDate 2025-03-06 14:25:21
 * @Entity so.dian.invoice.pojo.entity.InvoiceChangeRecord
 */
public interface InvoiceRequestRecordDetailDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceRequestRecordDetailDO record);

    int insertSelective(InvoiceRequestRecordDetailDO record);

    InvoiceRequestRecordDetailDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceRequestRecordDetailDO record);

    int updateByPrimaryKey(InvoiceRequestRecordDetailDO record);

    List<InvoiceRequestRecordDetailDO> listByManageIds(@Param("manageIds") List<Long> manageIds);

    List<InvoiceRequestRecordDetailDO> getByManageId(@Param("manageId") Long manageId);

    List<InvoiceRequestRecordDetailDO> getByRequestRecordId(@Param("requestRecordId") Long requestRecordId);

    int updateByRequestRecordId(@Param("requestRecordId") Long requestRecordId,@Param("status") int status);

}
