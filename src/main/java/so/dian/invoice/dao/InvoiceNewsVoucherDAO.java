package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceNewsVoucherParam;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【invoice_news_voucher(发票系统消息凭证表)】的数据库操作Mapper
* @createDate 2025-03-18 17:06:07
* @Entity generator.domain.InvoiceNewsVoucher
*/
public interface InvoiceNewsVoucherDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceNewsVoucherDO record);

    int insertSelective(InvoiceNewsVoucherDO record);

    InvoiceNewsVoucherDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceNewsVoucherDO record);

    int updateByPrimaryKey(InvoiceNewsVoucherDO record);

    InvoiceNewsVoucherDO getByBizNoAndBizType(@Param("bizNo") String bizNo, @Param("bizType") Integer bizType);

    List<InvoiceNewsVoucherDO> list(InvoiceNewsVoucherParam param);

}
