package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.invoice.pojo.entity.SupplierInvoiceOperateLogDO;

import java.util.List;

/**
* 操作记录表
*
* <AUTHOR>
* @date 2021-03-23 14:03:04
*/
public interface SupplierInvoiceOperateLogMapper {

    /**
     * 新增SupplierInvoiceOperateLog
     * @param supplierInvoiceOperateLogDO
     */
    int insert(@Param("do") SupplierInvoiceOperateLogDO supplierInvoiceOperateLogDO);

    /**
     * 批量新增SupplierInvoiceOperateLog
     * @param supplierInvoiceOperateLogDOList
     * @return
     */
    int insertBatch(@Param("doList") List<SupplierInvoiceOperateLogDO> supplierInvoiceOperateLogDOList);

    /**
     * 根据id更新SupplierInvoiceOperateLog
     * @param supplierInvoiceOperateLogDO
     */
    int updateById(@Param("do") SupplierInvoiceOperateLogDO supplierInvoiceOperateLogDO);

    /**
     * 根据id查询SupplierInvoiceOperateLog
     * @param id
     */
    SupplierInvoiceOperateLogDO findById(@Param("id") Long id);

}