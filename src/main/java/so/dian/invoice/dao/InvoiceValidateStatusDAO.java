package so.dian.invoice.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.param.InvoiceValidateStatusQueryParam;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 3:39 PM
 * @Description: 发票验真状态DAO
 */
public interface InvoiceValidateStatusDAO {

	/**
	 * 新增
	 * @param invoiceValidateStatusDO
	 * @return
	 */
	Integer insert(InvoiceValidateStatusDO invoiceValidateStatusDO);

	/**
	 * 批量新增
	 * @param invoiceValidateStatusDOList
	 * @return
	 */
	Integer batchInsert(@Param("invoiceValidateStatusDOList") List<InvoiceValidateStatusDO> invoiceValidateStatusDOList);

	/**
	 * 通过ID查询
	 * @param id
	 * @return
	 */
	InvoiceValidateStatusDO getById(@Param("id")Long id);

	/**
	 * 通过发票号码和发票代码查询
	 * @param invoiceNo
	 * @param invoiceCode
	 * @return
	 */
	List<InvoiceValidateStatusDO> listByInvoiceNoAndInvoiceCode(@Param("invoiceNo")String invoiceNo,
			@Param("invoiceCode")String invoiceCode);

	List<Long> listByParam(InvoiceValidateStatusQueryParam param);


    List<InvoiceValidateStatusDO> getAllValidateInvoice();
}
