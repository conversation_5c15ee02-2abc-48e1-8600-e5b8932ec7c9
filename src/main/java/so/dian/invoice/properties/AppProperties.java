package so.dian.invoice.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class AppProperties {

    /**
     * 故障警报机器人
     */
    @Value("${dingtalk.warn}")
    private String dTalkWarnToken;
    /**
     * 故障警报机器人
     */
    @Value("${dingtalk.secret}")
    private String dTalkSecret;
}
