package so.dian.invoice.properties;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

@Service
@Configuration
@ConfigurationProperties(prefix = "system")
public class InvoiceProperties {

    private static String env;

    public static boolean isPre() {
        return "pre".equalsIgnoreCase(env);
    }

    public static boolean isDaily() {
        return "dev".equalsIgnoreCase(env);
    }

    public static boolean isReal() {
        return "real".equalsIgnoreCase(env);
    }

    public static boolean isLocal() {
        return "local".equalsIgnoreCase(env);
    }

    public static String getEnv() {
        return env;
    }

    @Value("${system.env}")
    public void setEnv(String env) {
        InvoiceProperties.env = env;
    }
}
