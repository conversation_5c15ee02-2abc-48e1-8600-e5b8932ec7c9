package so.dian.invoice.controller;

import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.enums.SubjectTypeEnum;
import so.dian.invoice.pojo.dto.KeyValueDto;

@Api(value = "ConstantApi", description = "常量API")
@RestController
@RequestMapping("/constants")
public class ConstantController {

    @ApiOperation("供应链发票业务类型")
    @GetMapping(value = {"/invoice/businessType"})
    public BizResult<List<KeyValueDto>> businessType() {
        List<KeyValueDto> list = Lists.newArrayList();
        list.add(new KeyValueDto(SubjectTypeEnum.EQUIPMENT_PROCUREMENT.getType(),
              SubjectTypeEnum.EQUIPMENT_PROCUREMENT.getField()));
        return BizResult.create(list);
    }
}
