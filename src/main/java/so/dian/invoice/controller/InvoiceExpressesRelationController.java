package so.dian.invoice.controller;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meidalife.common.exception.BizException;
import com.meidalife.common.exception.ValidateException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.invoice.annotation.MeidaRequestData;
import so.dian.invoice.converter.InvoiceExpressesRelationConverter;
import so.dian.invoice.enums.LeoExcs;
import so.dian.invoice.pojo.bo.InvoiceExpressesRelationBO;
import so.dian.invoice.pojo.dto.InvoiceExpressesMsgDto;
import so.dian.invoice.pojo.param.InvoiceExpressBatchAddParam;
import so.dian.invoice.pojo.query.ExpressesQuery;
import so.dian.invoice.pojo.query.InvoiceExpressQuery;
import so.dian.invoice.pojo.query.InvoiceExpressesRelationQuery;
import so.dian.invoice.pojo.vo.ExpressesVO;
import so.dian.invoice.pojo.vo.InvoiceExpressesRelationVO;
import so.dian.invoice.pojo.vo.InvoiceExpressesVO;
import so.dian.invoice.response.ListResponse;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.ImageService;
import so.dian.invoice.service.InvoiceExpressesRelationService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.util.ImageUtils;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 8:51 PM
 * @Description:
 */
@Api(value = "InvoiceExpressesRelationController", description = "发票快递关系API", tags = "InvoiceExpressesRelationApi")
@RestController
@RequestMapping()
@Slf4j
public class InvoiceExpressesRelationController extends BaseController {

    @Autowired
    private InvoiceExpressesRelationService invoiceExpressesRelationService;

    @Autowired
    private ImageService imageService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private AgentEmployeeService agentEmployeeService;

    @ApiOperation("流转状态列表")
    @GetMapping(value = "/invoice/expressesRelation/status/list")
    public BizResult<List<NameValueDTO>> listInvoiceSubjectRelationStatus() {
        return invoiceExpressesRelationService.listInvoiceExpressesRelationStatus();
    }

    @ApiOperation("发票物流列表")
    @GetMapping(value = "/findInvoiceExpresses")
    public BizResult<ListResponse<InvoiceExpressesVO>> findInvoiceExpressesVO(@Valid @MeidaRequestData InvoiceExpressQuery invoiceExpressQuery) {

        Assert.notNull(invoiceExpressQuery, "invoiceExpressQuery 对象为空");
        //城市行政只能查看自己
        if(UserRoleEnum.CITY_ADMINISTRATION.equals(getUserRole())){
            invoiceExpressQuery.setCreator(getUserId());
        }

        List<InvoiceExpressesVO> invoiceExpressesVOList = invoiceExpressesRelationService.findInvoiceExpressesVO(invoiceExpressQuery);
        return BizResult.create(new ListResponse(invoiceExpressesVOList, invoiceExpressesVOList.size()));
    }

    @ApiOperation("发票物流列表")
    @PostMapping(value = "/findInvoiceExpresses")
    public BizResult<ListResponse<InvoiceExpressesVO>> findInvoiceExpressesVOTest(@Valid @RequestBody InvoiceExpressQuery invoiceExpressQuery) {

        List<InvoiceExpressesVO> invoiceExpressesVOList = invoiceExpressesRelationService.findInvoiceExpressesVO(invoiceExpressQuery);
        return BizResult.create(new ListResponse(invoiceExpressesVOList, invoiceExpressesVOList.size()));
    }


    @ApiOperation("批量导入发票物流信息")
    @PostMapping(value = "/expressesRelation/batchUpsetInvoiceExpresses")
    public BizResult<Object> batchAddInvoiceExpressesVO(HttpServletRequest request, @Valid @MeidaRequestData  InvoiceExpressBatchAddParam invoiceExpressBatchAddParam) {
        // 此次操作流水号
        String serialNo = invoiceExpressesRelationService.getSerialNo();
        // 操作人id
        Integer operatorId = getUserId(request);
        // 获取当前登录者的渠道信息
//        String operatorName = getNickName();
        String operatorName = agentEmployeeService.getEmployeeNickById(operatorId);
        invoiceExpressBatchAddParam.setSerialNo(serialNo);
        invoiceExpressBatchAddParam.setOperatorId(operatorId.longValue());
        invoiceExpressBatchAddParam.setOperatorName(operatorName);
        invoiceExpressesRelationService.batchUpsetInvoiceExpresses(invoiceExpressBatchAddParam);
        return BizResult.create(true);
    }

    @ApiOperation("批量导入发票物流信息Test")
    @PostMapping(value = "/expressesRelation/batchUpsetInvoiceExpressesTest")
    public BizResult<Object> batchAddInvoiceExpressesTest(@Valid  @RequestBody  InvoiceExpressBatchAddParam invoiceExpressBatchAddParam) {
        Assert.notNull(invoiceExpressBatchAddParam,"对象不能为空");
        Assert.notNull(invoiceExpressBatchAddParam.getIds(),"ids对象不能为空");
        Assert.notNull(invoiceExpressBatchAddParam.getExpressId(),"物流id对象不能为空");
        invoiceExpressesRelationService.batchUpsetInvoiceExpresses(invoiceExpressBatchAddParam);
        return BizResult.create(true);
    }


    @ApiOperation("模糊匹配物流公司")
    @GetMapping(value = "/expresseCompanys")
    public BizResult<ListResponse<ExpressesVO>> findExpressesByName(@MeidaRequestData ExpressesQuery expressesQuery) {

        List<ExpressesVO> expressesVOList = invoiceExpressesRelationService.findExpressesVO(expressesQuery.getName());
        return BizResult.create(new ListResponse<>(expressesVOList, expressesVOList.size()));
    }

    @ApiOperation("查询发票物流信息根据发票id")
    @PostMapping(value = "/expressesRelation/findExpresses")
    BizResult<List<InvoiceExpressesMsgDto>> getExpressesInfoByInvoiceIds(@RequestBody List<Long> invoiceIdList) {

        return BizResult.create(invoiceExpressesRelationService.findExpressesByInvoiceIds(invoiceIdList));
    }

    @ApiOperation("查询所有发票物流信息")
    @GetMapping(value = "/expressesRelation/findAll")
    BizResult<Map<String, Object>> findAll(@MeidaRequestData InvoiceExpressesRelationQuery query) {
        // 获取登录人
        Long userId = getUserId();
        UserRoleEnum userRole = getUserRoleByCookie();
        // 城市行政职能查看自己寄送的发票
        if (UserRoleEnum.CITY_ADMINISTRATION == userRole) {
            query.setOperatorId(userId);
        }
        // 结束时间到当天23:59分
        if (query.getEndTime()!=null){
            query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        }
        Integer count= invoiceExpressesRelationService.countByQuery(query);
        if(count <=0){
            return BizResult.create(ImmutableMap.of("totalCount", 0, "list", Lists.newArrayList(),
                    "pageNo", query.getPageNo(), "pageSize", query.getPageSize()));
        }
        List<InvoiceExpressesRelationBO> boList = invoiceExpressesRelationService.findAllByQuery(query);
        List<InvoiceExpressesRelationVO> voList = InvoiceExpressesRelationConverter.convertBO2VOFromList(boList);
        Map<String, Object> map = ImmutableMap.of("totalCount", count, "list", voList,
                "pageNo", query.getPageNo(), "pageSize", query.getPageSize());
        return BizResult.create(map);
    }

    /**
     * 腾讯云上传图片接口
     *
     */
    @RequestMapping(value = "/expressesRelation/uploadExpressImg", method = RequestMethod.POST)
    @ResponseBody
    public String uploadExpressImg(HttpServletRequest request, @RequestParam MultipartFile file) {
        try {
            String clientFileName = file.getOriginalFilename();
            if (!ImageUtils.checkSuffixIsLegal(clientFileName)) {
                throw new ValidateException(LeoExcs.IMAGES_URL_INVALID);
            }
            String savedUri;
            String fileName = clientFileName.trim().toLowerCase();
            if (fileName.endsWith("pdf")) {
                savedUri = imageService.uploadFileQCloud(ImageService.INVOICE, clientFileName, file.getBytes(), "pdf", file.getBytes().length);
            } else {
                savedUri = imageService.uploadImageQCloud(ImageService.INVOICE, clientFileName, file.getBytes(), "jpg", file.getBytes().length);
            }
            return processResult(ImageUtils.IMAGE_DOMAIN_WITH_PROTOCOL_QCLOUD + savedUri);
        } catch (Exception e) {
            Long imageSize=file.getSize();
            log.error("fail fileSize:"+imageSize,e);
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }
    }
}
