package so.dian.invoice.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.invoice.annotation.MeidaRequestData;
import so.dian.invoice.pojo.param.AddInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvalidInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvoiceSubjectRelationPageParam;
import so.dian.invoice.service.impl.InvoiceSubjectRelationServiceImpl;
import so.dian.invoice.util.PageData;
import so.dian.invoice.pojo.vo.InvoiceSubjectRelationPageVO;

/**
 * InvoiceSubjectRelationController
 *
 * <AUTHOR>
 */
@Api(value = "InvoiceSubjectRelationController", description = "发票主体映射API", tags = "InvoiceSubjectRelationApi")
@RestController
@RequestMapping("/invoice/subjectRelation")
public class InvoiceSubjectRelationController extends BaseController{

    @Resource
    private InvoiceSubjectRelationServiceImpl invoiceSubjectRelationService;

    @ApiOperation("商户主体关系列表")
    @GetMapping(value = "/list")
    public BizResult<PageData<InvoiceSubjectRelationPageVO>> listInvoiceSubjectRelation(
            HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceSubjectRelationPageParam param) {
        getUserId(servletRequest);
        return invoiceSubjectRelationService.listInvoiceSubjectRelation(param);
    }

    @ApiOperation("状态列表")
    @GetMapping(value = "/status/list")
    public BizResult<List<NameValueDTO>> listInvoiceSubjectRelationStatus() {
        return invoiceSubjectRelationService.listInvoiceSubjectRelationStatus();
    }

    @ApiOperation("新增发票主体对应关系")
    @PostMapping(value = "/add")
    public BizResult addInvoiceSubjectRelation(HttpServletRequest servletRequest,
            @MeidaRequestData AddInvoiceSubjectRelationParam param) {
        param.setUserId(getUserId(servletRequest));
        return invoiceSubjectRelationService.addInvoiceSubjectRelation(param);
    }

    @ApiOperation("作废发票主体对应关系")
    @PostMapping(value = "/invalid")
    public BizResult invalidInvoiceSubjectRelation(HttpServletRequest servletRequest,
            @MeidaRequestData InvalidInvoiceSubjectRelationParam param) {
        param.setUserId(getUserId(servletRequest));
        return invoiceSubjectRelationService.invalidInvoiceSubjectRelation(param);
    }

}
