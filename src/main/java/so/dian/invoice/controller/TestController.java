package so.dian.invoice.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import io.swagger.annotations.ApiOperation;
import java.util.*;
import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.client.GlorityClient;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.enums.InvoiceValidateTypeEnum;
import so.dian.invoice.handle.InvoiceOCRHandle;
import so.dian.invoice.job.CheckInvoiceJob;
import so.dian.invoice.job.InvoiceValidationJob;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.manager.InvoiceValidateStatusManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.dto.identify.DetailAndExtraDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceDataDTO;
import so.dian.invoice.pojo.dto.identify.InvoiceResultDTO;
import so.dian.invoice.pojo.dto.identify.ValidationDTO;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.param.GlorityParam;
import so.dian.invoice.pojo.param.InvoiceValidateStatusJobParam;
import so.dian.invoice.service.InvoiceBizRelationService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.service.impl.InvoiceSubjectRelationServiceImpl;

@RestController
@Slf4j
public class TestController {

    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private InvoiceSubjectRelationServiceImpl invoiceSubjectRelationService;
    @Autowired
    private InvoiceValidationJob invoiceValidationJob;
    @Autowired
    private GlorityClient glorityClient;
    @Autowired
    private InvoiceManager invoiceManager;
    @Autowired
    private InvoiceValidateStatusManager invoiceValidateStatusManager;
    @Autowired
    Validator globalValidator;
    @Resource
    private InvoiceBizRelationService invoiceBizRelationService;
    @Resource
    private CheckInvoiceJob checkInvoiceJob;
    @Resource
    private InvoiceOCRHandle invoiceOCRHandle;


    @Value("${glority.appKey}")
    private String appKey;

    @Value("${glority.appSecret}")
    private String appSecret;

    @ApiOperation(value = "发票特殊字符订正-开票方")
    @GetMapping(value = "/test/invoice/trim")
    public BizResult<Integer> trimInvoice(@RequestParam(value = "invoiceId", required = false) Long invoiceId,
          @RequestParam(value = "bizType", required = false) Integer bizType,
          @RequestParam(value = "lastId", required = false, defaultValue = "0") Integer lastId) {
        return invoiceService.trimInvoice(invoiceId, bizType, lastId);
    }

    @ApiOperation(value = "发票特殊字符订正-映射主体")
    @GetMapping(value = "/test/invoice/relation/trim")
    public BizResult<Integer> trimRelation(@RequestParam(value = "bizId", required = false) Long bizId,
          @RequestParam(value = "bizType", required = false) Integer bizType,
          @RequestParam(value = "lastId", required = false, defaultValue = "0") Integer lastId) {
        return invoiceSubjectRelationService.trimRelation(bizId, bizType, lastId);
    }

    @ApiOperation(value = "测试发票验真job")
    @PostMapping(value = "/test/invoiceValidateJob/run")
    public BizResult<Boolean> invoiceValidateJob(@RequestBody InvoiceValidateStatusJobParam jobParam) {
        invoiceValidationJob.execute(JSON.toJSONString(jobParam));
        return BizResult.create(true);
    }

    /**
     * 慎用，有bug
     * @param id
     * @return
     */
    @ApiOperation(value = "测试发票验真-根据发票id")
    @PostMapping(value = "/test/glorityClient/validate")
    public BizResult<Boolean> invoiceValidation(@RequestParam("id") Integer id) {
        long timestamp = System.currentTimeMillis() / 1000;
        String token = new Md5Hash(appKey + "+" + timestamp + "+" + appSecret).toString();
        InvoiceBO invoiceBO = invoiceManager.getInvoiceById(id);
        GlorityParam param = GlorityParam.builder().app_key(appKey).token(token).code(invoiceBO.getInvoiceCode())
                .number(invoiceBO.getInvoiceNo())
                .date(DateUtil.format(invoiceBO.getGmtCreate(), "yyyy-MM-dd"))
                .type(InvoiceTypeEnum.getBizTypeByType(invoiceBO.getInvoiceType()))
                .timestamp(timestamp).build();
        InvoiceResultDTO invoiceResultDTO = glorityClient.invoiceValidation(param);

        log.info("invoiceValidationJob glorityClient require, param:{}, result:{}",JSON.toJSON(param), JSON.toJSON(invoiceResultDTO));
        if(invoiceResultDTO == null){
            return BizResult.create(false);
        }
        ValidationDTO validationDTO = null;
        String validateResult = null;
        List<InvoiceValidateStatusDO> validateStatusForInsertList = new ArrayList<>();

        if (InvoiceIdentifyRecordEnum.InvoiceResultEnum.SUCCESS.getCode().intValue() == invoiceResultDTO
                .getResult()) {
            InvoiceDataDTO data = invoiceResultDTO.getResponse().getData();
            List<DetailAndExtraDTO> identifyResults = data.getIdentify_results();
            for (DetailAndExtraDTO detailAndExtraDTO : identifyResults) {
                validationDTO = detailAndExtraDTO.getValidation();
                // 请求成功，保存验真结果代码
                validateResult = StringUtils.substring(JSON.toJSONString(validationDTO), 0, 300);
                break;
            }
        }else{
            // 请求失败， 保存错误码
            validateResult = StringUtils.substring(JSON.toJSONString(invoiceResultDTO), 0, 300);
        }
        Boolean flag = false;
        if (Objects.nonNull(validationDTO) && Objects
                .equals(validationDTO.getCode(), InvoiceIdentifyRecordEnum.ValidationCodeEnum.SUCCESS.getCode())) {
            flag = true;
        }
        // 构建 InvoiceValidateStatusDO 对象
        Date now = new Date();

        InvoiceValidateStatusDO invoiceValidateStatusForInsert = InvoiceValidateStatusDO.of(invoiceBO, now, validateResult, InvoiceValidateTypeEnum.TASK_VERIFICATION);

        if (flag) {
            invoiceValidateStatusForInsert.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.SUC.getCode());
        } else {
            invoiceValidateStatusForInsert.setIsReal(InvoiceIdentifyRecordEnum.IsRealEnum.FAIL.getCode());
        }
        if (validationDTO != null) {
            invoiceValidateStatusForInsert.setValidateCode(validationDTO.getCode());
        }
        Set<ConstraintViolation<InvoiceValidateStatusDO>> validate = globalValidator.validate(invoiceValidateStatusForInsert);
        if(CollectionUtil.isNotEmpty(validate)){
            for (ConstraintViolation<InvoiceValidateStatusDO> constraintViolation : validate) {
                log.info(constraintViolation.getMessage());
            }
        }
        validateStatusForInsertList.add(invoiceValidateStatusForInsert);
        Boolean aBoolean = invoiceValidateStatusManager.batchInsert(validateStatusForInsertList);

        return BizResult.create(true);
    }

    @ApiOperation(value = "整理发票核销单据-全量")
    @PostMapping(value = "/invoice/business/disposal")
    public BizResult<Boolean> disposalByAllInvoice(
          @RequestParam(value = "minId", required = false, defaultValue = "0") Integer minId) {
        invoiceBizRelationService.disposalByAllInvoice(minId);
        return BizResult.create(true);
    }

    @ApiOperation(value = "发票质检定时任务-测试")
    @PostMapping(value = "/invoice/checkInvoice/test")
    public BizResult<Boolean> testCheckInvoiceJob() {
        try {
            ReturnT<String> returnT = checkInvoiceJob.execute("");
        } catch (Exception e) {
            log.error("质检任务执行失败！e:{}", e);
        }
        return BizResult.create(true);
    }

    @ApiOperation(value = "OCR识别异常补偿处理")
    @GetMapping(value = "/test/invoice/ocr")
    public BizResult<Boolean> invoiceOcr() {
        invoiceOCRHandle.ocrThreadTask();
        return BizResult.create(true);
    }

}
