//package so.dian.invoice.controller;
//
//import io.swagger.annotations.Api;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RestController;
//import springfox.documentation.annotations.ApiIgnore;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///**
// * Created by dufu on 2017/10/16 15:20 <br/>
// *
// * @Copyright 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
// */
//@Api(value = "Preload Controller", description = "Preload 相关的API", tags = "PreloadApi")
//@ApiIgnore
//@Slf4j
//@RestController
//public class PreloadController {
//
//    @Autowired
//    private EurekaClient eurekaClient;
//
//
//    @GetMapping("/preload")
//    public String preload(HttpServletRequest request, HttpServletResponse response) {
//        return "ok";
//    }
//
//    @GetMapping("/shutdown")
//    public String shutdown() {
//        eurekaClient.shutdown();
//        return "ok";
//    }
//}