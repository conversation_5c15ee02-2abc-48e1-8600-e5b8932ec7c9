package so.dian.invoice.controller.invoice.manage;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageAgentParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageBatchImportParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.AgentVO;
import so.dian.invoice.pojo.vo.AuthenticationSubjectVO;
import so.dian.invoice.service.invoice.manage.InvoiceManageService;
import so.dian.invoice.util.RequestUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 14:41
 */
@Api(value = "InvoiceManageController", description = "开票管理API", tags = "invoiceManageApi")
@RestController
@RequestMapping(value = "invoice-manage")
public class InvoiceManageController {

    @Resource
    private InvoiceManageService invoiceManageService;

    @ApiOperation("应开明细列表")
    @PostMapping(value = {"/page"})
    public BizResult<PageInfo<InvoiceManageDTO>> page(@RequestBody InvoiceManageQueryParam param) {
	CurrentUserReq userReq = RequestUtils.getRemoteUser();
	PageInfo<InvoiceManageDTO> list = invoiceManageService.page(param, userReq);
	return BizResult.create(list);
    }
    @ApiOperation("公司信息下拉列表")
    @GetMapping(value = {"/getAgentInfo"})
    public BizResult<List<AgentVO>> getAgentInfo() {
	CurrentUserReq userReq = RequestUtils.getRemoteUser();
	List<AgentVO> list = invoiceManageService.getAgentInfo(userReq);
	return BizResult.create(list);
    }


    @ApiOperation("应开票金额变更金额")
    @GetMapping(value = {"/expected/change/record"})
    public BizResult<List<InvoiceChangeRecordDTO>> listExpectedInvoiceAmountChangeRecord(@RequestParam(value = "manageId") Long manageId) {
	List<InvoiceChangeRecordDTO> list = invoiceManageService.listExpectedInvoiceAmountChangeRecord(manageId);
	return BizResult.create(list);
    }

    @ApiOperation("应开票产品明细")
    @GetMapping(value = {"/detail"})
    public BizResult<List<InvoiceManageDetailDTO>> getInvoiceManageDetailById(@RequestParam(value = "manageId") Long manageId) {
	List<InvoiceManageDetailDTO> list = invoiceManageService.getInvoiceManageDetailById(manageId);
	return BizResult.create(list);
    }


    @ApiOperation("导出开票管理数据")
    @PostMapping(value = {"/export"})
    public BizResult<String> export(@RequestBody InvoiceManageQueryParam param) {
	CurrentUserReq userReq = RequestUtils.getRemoteUser();
	invoiceManageService.export(param, userReq);
	return BizResult.create("导出成功");
    }

    @ApiOperation("查询公司认证信息")
    @PostMapping(value = {"/getAuthenticationSubject"})
    public BizResult<List<AuthenticationSubjectVO>> getAuthenticationSubjectByAgentId(@RequestBody InvoiceManageAgentParam form) {
	CurrentUserReq userReq = RequestUtils.getRemoteUser();
	List<AuthenticationSubjectVO> voList = invoiceManageService.getAuthenticationSubjectByAgentId(form.getManageIds(), userReq);
	return BizResult.create(voList);
    }

    @ApiOperation("应开明细批量导入")
    @PostMapping(value = {"/batch/import"})
    public BizResult<Boolean> batchImport(@RequestBody InvoiceManageBatchImportParam param) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        boolean result = invoiceManageService.batchImport(param, userReq);
        return BizResult.create(result);
    }
}
