package so.dian.invoice.controller.invoice.manage;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.controller.BaseController;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDTO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.BatchAuditVO;
import so.dian.invoice.pojo.vo.invoice.manage.InvoiceRequestVO;
import so.dian.invoice.service.invoice.manage.InvoiceRequestService;
import so.dian.invoice.util.RequestUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 14:41
 */
@Api(value = "InvoiceRequestController", description = "开票申请记录API", tags = "invoiceRequestApi")
@RestController
@RequestMapping(value = "invoice-apply")
public class InvoiceRequestController extends BaseController {

    @Resource
    private InvoiceRequestService invoiceRequestService;

    @ApiOperation("开票申请")
    @PostMapping("/create")
    public BizResult<String> createInvoiceRequest(@RequestBody InvoiceRequestParam param) {
	CurrentUserReq userReq = RequestUtils.getRemoteUser();
	int status = invoiceRequestService.createInvoiceRequest(param, userReq);
	if (status == 1) {
	    return BizResult.create("申请成功");
	}
	return BizResult.create("申请失败");
    }

    @ApiOperation("开票申请记录")
    @PostMapping("/page")
    public BizResult<PageInfo<InvoiceRequestVO>> page(@RequestBody InvoiceRequestQueryParam param) {
	CurrentUserReq userReq = RequestUtils.getRemoteUser();
	PageInfo<InvoiceRequestVO> pageInfo = invoiceRequestService.page(param, userReq);
	return BizResult.create(pageInfo);
    }

    @ApiOperation("开票申请记录详情")
    @GetMapping("/detail/{requestId}")
    public BizResult<List<InvoiceRequestRecordDTO>> detail(@PathVariable Long requestId) {
	List<InvoiceRequestRecordDTO> list = invoiceRequestService.listRequestRecordWithDetail(requestId);
	return BizResult.create(list);
    }

    @ApiOperation("导出开票明细数据")
    @PostMapping(value = {"/export"})
    public BizResult<String> export(@RequestBody InvoiceRequestQueryParam param) {
	CurrentUserReq userReq = RequestUtils.getRemoteUser();
	invoiceRequestService.export(param, userReq);
	return BizResult.create("导出成功");
    }

    @ApiOperation("批量审核")
    @PostMapping(value = {"/batchAudit"})
    public BizResult<BatchAuditVO> batchAudit(@RequestParam MultipartFile file,@RequestParam int status, @RequestParam String financeFeedback) {
	BatchAuditVO batchAuditVO = invoiceRequestService.batchAudit(file, status, financeFeedback);
	return BizResult.create(batchAuditVO);
    }

    @ApiOperation("下载批量操作结果")
    @GetMapping(value = {"/downloadAuditResult"})
    public BizResult<String> downloadAuditResult(@RequestParam(value = "key") String key) {
	invoiceRequestService.downloadAuditResult(key);
	return BizResult.create("success");
    }

    @ApiOperation("批量审核白名单")
    @GetMapping(value = {"/batchAudit/whitelist"})
    public BizResult<List<Long>> whitelist() {
	List<Long> whiteList = invoiceRequestService.whiteList();
	return BizResult.create(whiteList);
    }
}
