package so.dian.invoice.controller;

import com.google.common.collect.Lists;
import com.meidalife.common.exception.BizException;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.invoice.annotation.MeidaRequestData;
import so.dian.invoice.enums.InvoiceSubjectRelationEnum.BizTypeEnum;
import so.dian.invoice.enums.LeoExcs;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.pojo.bo.UserBO;
import so.dian.invoice.pojo.dto.InvoiceDeductionDTO;
import so.dian.invoice.pojo.dto.InvoiceRollBackResultDTO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.param.ApplyInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceBatchRollBackParam;
import so.dian.invoice.pojo.param.InvoiceDeductBatchParam;
import so.dian.invoice.pojo.param.InvoiceDeductOperationParam;
import so.dian.invoice.pojo.param.InvoiceDeductQueryParam;
import so.dian.invoice.pojo.param.InvoiceManualDeductParam;
import so.dian.invoice.pojo.param.InvoiceRecoverBatchParam;
import so.dian.invoice.pojo.param.InvoiceRecoverOperationParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.InvoiceInfoVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.InvoiceDeductionService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.service.impl.InvoiceSubjectRelationServiceImpl;
import so.dian.invoice.util.StringUtil;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.invoice.volidator.InvoiceValidator;

/**
 * 发票核销Controller
 *
 * <AUTHOR>
 * @date 2019/7/6
 */
@RestController
public class InvoiceDeductController extends BaseController {

    @Resource
    private InvoiceDeductionService invoiceDeductionService;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private InvoiceSubjectRelationServiceImpl invoiceSubjectRelationService;

    @Resource
    private AgentEmployeeService agentEmployeeService;


    /**
     * 发票手动核销
     */
    @RequestMapping(value = {"/manual/deduct"}, method = RequestMethod.POST)
    public String invoiceManualDeduct(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceManualDeductParam param) {
        if (param == null) {
            return processErrorResult(500, "参数不能为空");
        }
//        if (StringUtils.isEmpty(param.getInvoiceCode())) {
//            return processErrorResult(500, "发票代码不能为空");
//        }
        if (StringUtils.isEmpty(param.getInvoiceNo())) {
            return processErrorResult(500, "发票号码不能为空");
        }
        if (param.getAmount() == null) {
            return processErrorResult(500, "核销金额不能为空");
        }
        // 获取操作人
        Integer userId = getUserId(servletRequest);
        param.setCreator(userId);

        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(userId);

        //发票购买方时间区间校验
        InvoiceDO invoiceDO = invoiceService.getInvoiceByInvoiceCodeAndNo(param.getInvoiceCode(), param.getInvoiceNo());
        if (Objects.isNull(invoiceDO)) {
            return processErrorResult(500, "发票台账不存在");
        }
        if (!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(invoiceDO.getType()))
                && StringUtil.isBlank(param.getInvoiceCode())) {
            return processErrorResult(500, "发票类型不是全电发票，发票代码不能为空");
        }

        //发票购买方主体时间区间校验
        if (StringUtils.isNotBlank(invoiceDO.getBuyer())) {
            String checkBuyerResult = InvoiceBuyerValidator
                    .validInvoiceBuyer(currentUserReq.getBelongSubjectType(), invoiceDO.getBuyer(),
                            invoiceDO.getGmtCreate(), false);
            if (!StringUtils.isEmpty(checkBuyerResult)) {
                return processErrorResult(500, checkBuyerResult);
            }
        }

        // 获取当前userId对应的代理商信息
        so.dian.center.common.entity.BizResult<AgentDTO> agentDTOBizResult =
                agentEmployeeService.getAgentByUserId(Long.valueOf(userId));
        if (agentDTOBizResult == null || !agentDTOBizResult.isSuccess() || null == agentDTOBizResult.getData()) {
            return processErrorResult(500, "当前登录身份无效，请重新登录！");
        }

        AgentDTO agentDTO = agentDTOBizResult.getData();

        if (agentDTO.getAgentId() != 0 && agentDTO.getAgentId() != 1 && agentDTO.getAgentId() != 3) {
            if (invoiceDO.getBelongSubjectId() != null && !invoiceDO.getBelongSubjectId()
                    .equals(agentDTO.getAgentId().intValue())) {
                return processErrorResult(InvoiceErrorCodeEnum.INVOICE_DEDUCT_PERMISSIONS.getCode(),
                        InvoiceErrorCodeEnum.INVOICE_DEDUCT_PERMISSIONS.getDesc());
            }
        }

        // 执行发票核销操作
        invoiceDeductionService.invoiceManualDeduct(param);

        return processResult(null);
    }

    @ApiOperation("批量核销发票-查询发票列表")
    @RequestMapping(value = {"/deduct/queryList"}, method = RequestMethod.POST)
    public String deductInvoiceList(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceDeductQueryParam param) {
        if (param == null) {
            return processErrorResult(500, "参数不能为空");
        }
        if (StringUtils.isEmpty(param.getInvoiceNo())
                && StringUtils.isEmpty(param.getSubjectName())
                && param.getInvoiceId() == null
                && StringUtils.isEmpty(param.getRelationSubjectName())) {
            return processErrorResult(500, "参数不能全部为空");
        }
        // 获取操作人
        Integer userId = getUserId(servletRequest);
        // 获取归属信息
        AgentDTO agentDTO = invoiceService.getInvoiceBelongInfo(userId);
        if (agentDTO == null) {
            throw new BizException(LeoExcs.AGENT_NOT_EXISTS);
        }
        //只有小电的用户才可以操作
//        if (!SettleSubjectIdEnum.containByXD(agentDTO.getAgentId())) {
//            throw new BizException(LeoExcs.USER_NO_ROLE);
//        }
        param.setBelongSubjectId(agentDTO.getAgentId());
        param.setSubjectName(StringUtil.trimFull2Half(param.getSubjectName()));
        param.setBuyerName(StringUtil.trimFull2Half(param.getBuyerName()));
        //需要主体映射的处理逻辑
        if (param.getNeedRelate() && StringUtils.isNotBlank(param.getRelationSubjectName())) {
            BizTypeEnum bizType = BizTypeEnum.getByCode(param.getRelationBizType());
            List<String> subjectNameList = invoiceSubjectRelationService
                    .listSubjectNameByRelationSubjectName(param.getRelationSubjectName(), bizType);
            if (Objects.isNull(subjectNameList)) {
                subjectNameList = Lists.newArrayList();
            }
            //包含自己（相同的不会在mapping表维护）
            subjectNameList.add(param.getRelationSubjectName());
            if (CollectionUtils.isEmpty(subjectNameList)) {
                return processResult(Lists.newArrayList());
            }
            if (StringUtils.isNotBlank(param.getSubjectName()) && !subjectNameList.contains(param.getSubjectName())) {
                return processResult(Lists.newArrayList());
            }
            List<String> subjectNameQueryList =
                    LocalListUtils.transferList(subjectNameList, subjectName -> StringUtil.trimFull2Half(subjectName));
            param.setSubjectNameList(subjectNameQueryList);
        }
        // 查询发票列表
        List<InvoiceInfoVO> invoiceInfoList = invoiceService.findByInvoiceDeductQueryParam(param, agentDTO.getAgentId());
        return processResult(invoiceInfoList);
    }

    /**
     * 付款申请单关联发票详情列表查询（leo调用，缺乏安全性）
     */
    @RequestMapping(value = {"/invoice/apply/invoiceList"}, method = RequestMethod.POST)
    public String applyInvoiceList(@RequestBody ApplyInvoiceParam param) {
        if (param == null) {
            return processErrorResult(500, "参数不能为空");
        }
        if (StringUtils.isEmpty(param.getApplyNo())) {
            return processErrorResult(500, "付款申请单编号不能为空");
        }
        // 查询发票列表
        List<InvoiceInfoVO> invoiceInfoList = invoiceDeductionService.getApplyInvoiceList(param);

        return processResult(invoiceInfoList);
    }

    @ApiOperation("根据业务单号查询发票核销记录")
    @GetMapping(value = {"/invoices/deduct/{businessNo}/{businessType}"})
    public BizResult<List<InvoiceDeductionDTO>> getDeductInvoiceList(
            @PathVariable("businessNo") String businessNo,
            @PathVariable("businessType") Integer businessType) {
        return invoiceDeductionService.getDeductInvoiceList(businessNo, businessType);
    }

    @ApiOperation(value = "核销发票", notes = "返回核销的发票明细")
    @PostMapping(value = {"/{version}/invoice/deduct"})
    public BizResult<InvoiceDeductionDTO> deductInvoice(@RequestBody InvoiceDeductOperationParam param) {
        return invoiceDeductionService.deductInvoice(param);
    }

    @ApiOperation(value = "批量核销发票", notes = "返回核销的发票明细列表")
    @PostMapping(value = "/{version}/invoice/batch/deduct")
    public BizResult<List<InvoiceDeductionDTO>> batchDeductInvoice(@RequestBody InvoiceDeductBatchParam params) {
        return invoiceDeductionService.batchDeductInvoice(params);
    }

    @ApiOperation(value = "回滚核销的发票", notes = "返回回滚的发票明细")
    @PostMapping(value = "/{version}/invoice/recover")
    public BizResult<InvoiceDeductionDTO> recoverInvoice(@RequestBody InvoiceRecoverOperationParam param) {
        return invoiceDeductionService.recoverInvoice(param);
    }

    @ApiOperation(value = "回滚核销的发票", notes = "返回回滚的结果")
    @PostMapping(value = "/deduct/rollback/batch")
    public BizResult<List<InvoiceRollBackResultDTO>> rollbackInvoices(HttpServletRequest request,@Valid @RequestBody InvoiceBatchRollBackParam param) {
        Integer userId = getUserId(request);
        String nickName = getNickName(request);
        List<InvoiceRollBackResultDTO> rollback = invoiceDeductionService.rollback(UserBO.of(userId,nickName),param.getDeductionIds());
        return BizResult.create(rollback);
    }

    @ApiOperation(value = "批量回滚核销的发票", notes = "返回回滚的发票明细列表")
    @PostMapping(value = "/{version}/invoice/batch/recover")
    public BizResult<List<InvoiceDeductionDTO>> batchRecoverInvoice(@RequestBody InvoiceRecoverBatchParam param) {
        return invoiceDeductionService.batchRecoverInvoice(param);
    }


}
