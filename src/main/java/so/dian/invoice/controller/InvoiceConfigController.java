package so.dian.invoice.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.converter.InvoiceConfigConverter;
import so.dian.invoice.manager.InvoiceConfigManager;
import so.dian.invoice.pojo.entity.InvoiceConfigDO;
import so.dian.invoice.pojo.param.ConfigParam;

import javax.annotation.Resource;
import java.util.Date;

@Api(value = "ConfigController", description = "Config相关接口", tags = "configApi")
@RestController
@Slf4j
public class InvoiceConfigController {
    @Resource
    private InvoiceConfigManager invoiceConfigManager;
    /**
     * 新增config
     */
    @ApiOperation("新增config")
    @RequestMapping(value = "/config/add", method = RequestMethod.POST)
    public BizResult<Boolean> addConfig(ConfigParam configParam) {
        log.info("addConfig configParam = {}",configParam);
        InvoiceConfigDO invoiceConfigDO = InvoiceConfigConverter.buildParam2DO(configParam);
        Boolean result = invoiceConfigManager.insert(invoiceConfigDO);
        log.info("addConfig return result = {}",result);
        return BizResult.create(result);
    }

    /**
     * 更新config
     */
    @ApiOperation("更新config")
    @RequestMapping(value = "/config/update", method = RequestMethod.POST)
    public BizResult<Boolean> updateConfig(ConfigParam configParam) {
        log.info("updateConfig configParam = {}",configParam);
        InvoiceConfigDO invoiceConfigDO = invoiceConfigManager.getByKey(configParam.getKey());
        invoiceConfigDO.setValue(configParam.getValue());
        invoiceConfigDO.setStatus(configParam.getStatus());
        invoiceConfigDO.setVersion(configParam.getVersion());
        invoiceConfigDO.setCreateTime(new Date());
        Boolean result = invoiceConfigManager.update(invoiceConfigDO);
        log.info("updateConfig return result = {}",result);
        return BizResult.create(result);
    }
}
