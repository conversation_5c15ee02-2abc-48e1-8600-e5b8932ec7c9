package so.dian.invoice.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.invoice.pojo.param.SupplierInvoiceAttachmentAddParam;
import so.dian.invoice.pojo.param.SupplierInvoiceAttachmentDeleteParam;
import so.dian.invoice.pojo.param.SupplierInvoiceDeleteParam;
import so.dian.invoice.pojo.param.SupplierInvoiceDetailPageParam;
import so.dian.invoice.pojo.param.SupplierInvoiceImportParam;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailPageVO;
import so.dian.invoice.pojo.vo.SupplierInvoicePageVO;
import so.dian.invoice.service.adaptor.SupplierInvoiceAdaptor;
import so.dian.invoice.util.RequestUtils;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:06 PM
 * @Description:
 */
@Api(value = "SupplierInvoiceController", description = "供应商发票API", tags = "SupplierInvoiceApi")
@Slf4j
@RestController
public class SupplierInvoiceController {

    @Autowired
    private SupplierInvoiceAdaptor supplierInvoiceService;

    @ApiOperation("供应商发票主体分页列表")
    @PostMapping(value = "/supplierInvoice/page")
    public BizResult<PageData<SupplierInvoicePageVO>> supplierInvoicePage(@Validated @RequestBody SupplierInvoicePageParam param) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        return BizResult.create(supplierInvoiceService.findPageByReq(userReq, param));
    }

    @ApiOperation("供应商发票详情列表")
    @PostMapping(value = "/supplierInvoice/detail/list")
    public BizResult<List<SupplierInvoiceDetailPageVO>> supplierInvoiceDetailList(@Validated @RequestBody SupplierInvoiceDetailPageParam param) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        return BizResult.create(supplierInvoiceService.findDetailListByReq(userReq, param));
    }

    @ApiOperation("供应商发票明细Excel导出")
    @PostMapping(value = "/supplierInvoice/detail/excelExport")
    public BizResult<String> detailExcelExport(@Validated @RequestBody SupplierInvoicePageParam param) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        Long count = supplierInvoiceService.excelExport(userReq, param);
        return BizResult.create(String.format("供应商发票明细单据号导出成功，共导出%d条记录", count));
    }

    @ApiOperation("供应商发票主体删除")
    @PostMapping(value = "/supplierInvoice/delete")
    public BizResult<String> deleteInvoice(@Validated @RequestBody SupplierInvoiceDeleteParam param) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        supplierInvoiceService.deleteInvoice(userReq, param);
        return BizResult.create("供应商发票主体删除成功");
    }

    @ApiOperation("供应商发票图片删除")
    @PostMapping(value = "/supplierInvoice/attachment/delete")
    public BizResult<String> deleteAttachment(@Valid @RequestBody SupplierInvoiceAttachmentDeleteParam param) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        supplierInvoiceService.deleteAttachment(userReq, param);
        return BizResult.create("供应商发票图片删除成功");
    }

    @ApiOperation("供应商发票图片新增")
    @PostMapping(value = "/supplierInvoice/attachment/add")
    public BizResult<String> addAttachment(@Validated @RequestBody SupplierInvoiceAttachmentAddParam param) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        supplierInvoiceService.addInvoicePicture(userReq, param);
        return BizResult.create("供应商发票图片新增成功");
    }

    @ApiOperation("供应商发票excel导入模板下载")
    @GetMapping(value = "/supplierInvoice/excelTemplate")
    public BizResult<String> excelTemplate(HttpServletResponse response) {
        supplierInvoiceService.excelTemplate(response);
        return BizResult.create("供应商发票excel导入模板下载成功");
    }

    @ApiOperation("供应商发票excel导入")
    @PostMapping(value = "/supplierInvoice/excelImport")
    public BizResult<String> excelImport(@RequestBody List<SupplierInvoiceImportParam> paramList) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        return BizResult.create(supplierInvoiceService.excelImport(userReq, paramList));
    }

    @ApiOperation("供应商发票转发票台账")
    @GetMapping(value = "/supplierInvoice/toInvoice")
    public BizResult<String> toInvoice(@RequestParam Integer supplierInvoiceId) {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        supplierInvoiceService.transferInvoice(userReq, supplierInvoiceId);
        return BizResult.create("供应商发票转发票台账成功");
    }

}
