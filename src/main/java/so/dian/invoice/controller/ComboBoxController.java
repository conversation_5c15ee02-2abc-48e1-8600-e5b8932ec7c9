package so.dian.invoice.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.invoice.enums.BuyerTaxIdEnum;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.enums.SupplierInvoiceStatusEnum;
import so.dian.invoice.enums.SupplierInvoiceTypeEnum;

import java.util.ArrayList;
import java.util.List;


/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:06 PM
 * @Description:
 */
@Api(value = "ComboBoxController", description = "下拉框API", tags = "ComboBoxApi")
@Slf4j
@RestController
public class ComboBoxController extends BaseController {

    @ApiOperation("供应商发票-发票类型")
    @GetMapping(value = "/comboBox/supplierInvoice/type")
    public BizResult<List<NameValueDTO>> supplierInvoiceType() {
        return BizResult.create(LocalEnumUtils.getEnumCodeAndDesc(SupplierInvoiceTypeEnum.class));
    }

    @ApiOperation("供应商发票-发票状态")
    @GetMapping(value = "/comboBox/supplierInvoice/status")
    public BizResult<List<NameValueDTO>> supplierInvoiceStatus() {
        return BizResult.create(LocalEnumUtils.getEnumCodeAndDesc(SupplierInvoiceStatusEnum.class));
    }

    @ApiOperation("供应商发票-所属公司")
    @GetMapping(value = "/comboBox/supplierInvoice/buyer")
    public BizResult<List<NameValueDTO>> supplierInvoiceBuyer() {
        List<NameValueDTO> nameValueDTOList = new ArrayList<>();
        BuyerTaxIdEnum[] buyerTaxIdEnums = BuyerTaxIdEnum.values();
        for (BuyerTaxIdEnum buyerTaxIdEnum : buyerTaxIdEnums) {
            nameValueDTOList.add(new NameValueDTO(buyerTaxIdEnum.getBuyer(),buyerTaxIdEnum.getBuyer()));
        }
        return BizResult.create(nameValueDTOList);
    }

    @ApiOperation("发票台账-发票类型")
    @GetMapping(value = "/comboBox/invoice/type")
    public BizResult<List<NameValueDTO>> invoiceType() {
        List<NameValueDTO> nameValueDTOList = new ArrayList<>();
        nameValueDTOList.add(new NameValueDTO(InvoiceTypeEnum.VAT.getField() ,String.valueOf(InvoiceTypeEnum.VAT.getType())));
        nameValueDTOList.add(new NameValueDTO(InvoiceTypeEnum.VAT_ELECTR.getField() ,String.valueOf(InvoiceTypeEnum.VAT_ELECTR.getType())));
        nameValueDTOList.add(new NameValueDTO(InvoiceTypeEnum.ORD.getField() ,String.valueOf(InvoiceTypeEnum.ORD.getType())));
        nameValueDTOList.add(new NameValueDTO(InvoiceTypeEnum.ORD_ELECTR.getField() ,String.valueOf(InvoiceTypeEnum.ORD_ELECTR.getType())));
        nameValueDTOList.add(new NameValueDTO(InvoiceTypeEnum.ORD_ROLL.getField() ,String.valueOf(InvoiceTypeEnum.ORD_ROLL.getType())));
        nameValueDTOList.add(new NameValueDTO(InvoiceTypeEnum.MACHINE.getField() ,String.valueOf(InvoiceTypeEnum.MACHINE.getType())));
        return BizResult.create(nameValueDTOList);
    }



}
