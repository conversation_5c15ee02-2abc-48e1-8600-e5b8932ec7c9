package so.dian.invoice.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.pojo.dto.ScmInvoiceDeductionDTO;
import so.dian.invoice.pojo.dto.ScmInvoiceInfoDTO;
import so.dian.invoice.pojo.param.ScmInvoiceDeductBatchParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductQueryParam;
import so.dian.invoice.pojo.param.ScmInvoiceRecoverBatchParam;
import so.dian.invoice.service.ScmInvoiceDeductionService;

@Api(value = "ScmInvoiceDeductAPI", description = "供应链发票核销")
@RestController
@RequestMapping("/scm")
public class ScmInvoiceDeductController {
    @Autowired
    private ScmInvoiceDeductionService scmInvoiceDeductionService;

    @ApiOperation("根据ID查询发票")
    @GetMapping(value = {"/invoices/ids"})
    public BizResult<List<ScmInvoiceInfoDTO>> queryByInvoiceId(@RequestParam List<Long> invoiceIds) {
        //if (StringUtils.isBlank(invoiceIds)) {
        //    return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR);
        //}
        //List<Long> ids = Arrays.stream(StringUtils.split(invoiceIds,",")).map(Long::valueOf).collect(Collectors.toList());
        List<ScmInvoiceInfoDTO> list = scmInvoiceDeductionService.queryByInvoiceId(invoiceIds);
        return BizResult.create(list);
    }

    @ApiOperation("批量核销发票-查询发票列表")
    @PostMapping(value = {"/invoices/query"})
    public BizResult<List<ScmInvoiceInfoDTO>> queryInvoiceList(@Valid @RequestBody ScmInvoiceDeductQueryParam param) {
        List<ScmInvoiceInfoDTO> list = scmInvoiceDeductionService.queryInvoiceList(param);
        return BizResult.create(list);
    }

    @ApiOperation(value = "批量核销发票", notes = "返回核销的发票明细列表")
    @PostMapping(value = "/invoices/batch/deduct")
    public BizResult<List<ScmInvoiceDeductionDTO>> batchDeductInvoice(@Valid @RequestBody ScmInvoiceDeductBatchParam param) {
        List<ScmInvoiceDeductionDTO> list = scmInvoiceDeductionService.batchDeductInvoice(param);
        return BizResult.create(list);
    }

    @ApiOperation(value = "批量回滚核销的发票", notes = "返回回滚的发票明细列表")
    @PostMapping(value = "/invoices/batch/recover")
    public BizResult<List<Long>> batchRecoverInvoice(@Valid @RequestBody ScmInvoiceRecoverBatchParam param) {
        List<Long> list = scmInvoiceDeductionService.batchRecoverInvoice(param);
        return BizResult.create(list);
    }

    @ApiOperation("查询发票核销记录")
    @GetMapping(value = {"/invoice/deduction/list"})
    public BizResult<List<ScmInvoiceDeductionDTO>> getDeductInvoiceList(@RequestParam List<Long> deductionIds) {
        //if (StringUtils.isBlank(deductionIds)) {
        //    return BizResult.error(BaseErrorCodeEnum.PARAMS_ERROR);
        //}
        //List<Long> ids = Arrays.stream(StringUtils.split(deductionIds,",")).map(Long::valueOf).collect(Collectors.toList());
        List<ScmInvoiceDeductionDTO> list = scmInvoiceDeductionService.getDeductInvoiceList(deductionIds);
        return BizResult.create(list);
    }
}
