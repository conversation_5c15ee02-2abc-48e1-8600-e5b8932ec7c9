package so.dian.invoice.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import io.swagger.annotations.ApiOperation;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import so.dian.center.common.util.CollectionUtil;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.himalaya.util.LocalListUtils;
import so.dian.himalaya.util.LocalMapUtils;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.annotation.MeidaRequestData;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.enums.*;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.facade.InvoiceFacadeImpl;
import so.dian.invoice.manager.InvoiceOperateLogManager;
import so.dian.invoice.manager.SupplierInvoiceDetailManager;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.dto.*;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.param.*;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.InvoiceVO;
import so.dian.invoice.service.*;
import so.dian.invoice.specification.InvoiceDisableRollbackSpec;
import so.dian.invoice.util.*;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.invoice.volidator.InvoiceTypeValidator;
import so.dian.lvy.pojo.enums.InvoiceIsRealEnum;

@RestController
public class InvoiceController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceController.class);

    @Resource
    private RedissonClient redisson;
    @Resource
    private InvoiceFacadeImpl invoiceFacade;
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private AgentEmployeeService agentEmployeeService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private InvoiceDeductionService invoiceDeductionService;
    @Resource
    private InvoiceOperateLogManager invoiceOperateLogManager;
    @Resource
    private InvoiceExpressesRelationService invoiceExpressesRelationService;
    @Resource
    private SupplierInvoiceDetailManager supplierInvoiceDetailManager;

    @Autowired
    private InvoiceDisableRollbackSpec disableRollbackSpec;
    @Autowired
    private InvoiceValidateService invoiceValidateService;

    @RequestMapping(value = {"/{version}/invoice/type/list", "/{version}/h5/invoice/type/list"})
    public String getInvoiceTypeList(HttpServletRequest servletRequest) {

        List<KeyValueDto> list = new ArrayList<>();

        for (InvoiceTypeEnum invoiceType : InvoiceTypeEnum.values()) {

            KeyValueDto dto = new KeyValueDto();
            dto.setKey(invoiceType.getType());
            dto.setValue(invoiceType.getField());

            list.add(dto);
        }

        return processResultFastjson(list);
    }

    @RequestMapping(value = {"/invoiceType/list", "/h5/invoiceType/list"})
    public BizResult<List<KeyValueDto>> getInvoiceTypeForPlatformList(HttpServletRequest servletRequest) {
        List<KeyValueDto> list = new ArrayList<>();
        for (InvoiceTypeEnum invoiceType : InvoiceTypeValidator.getInvoiceTypeForPlatform()) {
            KeyValueDto dto = new KeyValueDto();
            dto.setKey(invoiceType.getType());
            dto.setValue(invoiceType.getField());
            list.add(dto);
        }
        return BizResult.create(list);
    }

    @RequestMapping(value = {"/crm/invoice/status/list"}, method = RequestMethod.GET)
    public String getInvoiceStatusList(HttpServletRequest servletRequest) {

        List<KeyValueDto> list = new ArrayList<>();

        for (InvoiceStatusEnum invoiceStatus : InvoiceStatusEnum.values()) {

            KeyValueDto dto = new KeyValueDto();
            dto.setKey(invoiceStatus.getType());
            dto.setValue(invoiceStatus.getField());

            list.add(dto);
        }

        return processResultFastjson(list);
    }

    /**
     * 业务类型列表
     *
     * @param servletRequest
     * @return
     */
    @RequestMapping(value = {"/crm/invoice/subjectType/list",
            "/h5/invoice/subjectType/list"}, method = RequestMethod.POST)
    public String getSubjectTypeList(HttpServletRequest servletRequest,
            @RequestBody(required = false) SubjectTypeParam param) {

        UserRoleEnum roleEnum = getUserRole(servletRequest);
        Integer userId = getUserId(servletRequest);

        List<KeyValueDto> list = invoiceService.getSubjectTypeList(roleEnum, userId, param);

        return processResultFastjson(list);
    }

    /**
     * 新增发票，并直接核销掉
     */
    @Deprecated
    @RequestMapping(value = {"/invoice/invoiceManage/addWithDeduct",
            "/h5/invoice/invoiceManage/addWithDeduct"}, method = RequestMethod.POST)
    public String addWithDeductInvoice(HttpServletRequest servletRequest, @RequestBody InvoiceDto dto) {

        RLock lock = redisson.getLock("addWithDeductInvoice_" + dto.getInvoiceCode() + "_" + dto.getInvoiceNo());

        try {
            if (lock.tryLock(0, 3, TimeUnit.SECONDS)) {
                invoiceService.addWithDeductInvoice(dto);
            }
        } catch (InterruptedException e) {
            logger.error(">>>addWithDeductInvoice InterruptedException {}", e);
        } finally {
            lock.unlock();
        }

        return processResult(null);
    }

    /**
     * 手动录入发票
     *
     * @param servletRequest
     * @param param
     * @return
     */
    @RequestMapping(value = {"/invoiceManage/addBatch", "/h5/invoiceManage/addBatch"})
    public String addInvoiceBatch(HttpServletRequest servletRequest, @MeidaRequestData InvoiceBatchParam param) {

        if (param == null || CollectionUtil.isEmpty(param.getInvoiceDtoList())) {
            return processResult(null);
        }
        Integer userId = getUserId(servletRequest);
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
        InvoiceDto invoiceDto = param.getInvoiceDtoList().iterator().next();
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(userId);
         RLock lock =
                redisson.getLock(
                        "invoiceAdd_" + (invoiceDto.getInvoiceCode() == null ? "" : invoiceDto.getInvoiceCode()) + "_"
                                + invoiceDto.getInvoiceNo());
        long s = System.currentTimeMillis();
        try {
            logger.error("批量手动新增发票开始");

            if (lock.tryLock(1, 3, TimeUnit.SECONDS)) {
                List<String> noList = Lists.newArrayList();
                param.setCurrentUserReq(currentUserReq);
                invoiceService.addInvoiceBatch(userId, param, noList);
            }
        } catch (Exception e) {
            logger.error("批量手动新增发票异常。param = {}", param, e);
            throw BizException.create(InvoiceErrorCodeEnum.ADD_INVOICE_ERROR, e.getMessage());
        } finally {
            lock.unlock();
            logger.info("批量手动新增发票结束，耗时 = {}", System.currentTimeMillis() - s);
        }
//            }
//        }).start();

//        return processResult("新增发票申请已经提交，任务异步进行中，请耐心等待");
        return processResult("新增发票成功");
    }


    @RequestMapping(value = {"/crm/invoice/model/downLoad"}, method = RequestMethod.GET)
    public String downLoadInvoiceModelFile(HttpServletRequest servletRequest, HttpServletResponse response) {

//        String filePathName =
//                servletRequest.getSession().getServletContext().getRealPath("") + "WEB-INF/classes/templates/invoice_model.xls";

        invoiceService.downLoadFile(servletRequest, response, "xls", "/templates/invoice_model.xls",
                "发票导入模板.xls");

        return processResult(null);
    }

    @RequestMapping(value = {"/crm/invoice/uploadexcel"}, method = RequestMethod.POST)
    public String uploadInvoiceFile(HttpServletRequest servletRequest, @RequestParam MultipartFile file) {

        String fileName = file.getOriginalFilename();
        logger.info("------ 发票导入。fileName = {}", fileName);
        String fileType = fileName.substring(fileName.lastIndexOf("."), fileName.length()).trim().toLowerCase();
        if (!fileType.equals(".xls") && !fileType.equals(".xlsx")) {
            logger.error("------ 发票导入，文件格式错误。fileName = {}", fileName);
            return processErrorResult(500, "文件类型不支持");
        }

//        String filePath = servletRequest.getSession().getServletContext().getRealPath("") + "WEB-INF/classes/";
        String filePath = servletRequest.getSession().getServletContext().getRealPath("/uploads/");

        // 判断，该路径是否存在
        File filePathFile = new File(filePath);
        if (!filePathFile.exists()) {
            // 创建该文件夹
            filePathFile.mkdirs();
        }

        final String filePathName = filePath + fileName;
        BufferedOutputStream stream = null;
        try {

            byte[] bytes = file.getBytes();
            stream = new BufferedOutputStream(new FileOutputStream(filePathName));
            stream.write(bytes);

        } catch (Exception e) {
            logger.error("------ 发票导入，流读取异常。", e);
        } finally {

            try {
                if (stream != null) {
                    stream.close();
                }
            } catch (IOException e) {
                logger.error("------ 发票导入，流关闭异常。", e);
            }
        }

        Workbook workbook = ExcelParser.getWorkbook(filePathName);
        if (workbook == null) {
            logger.error("------ 发票导入，workbook不存在。");
            invoiceService.deleteInvoiceFile(filePathName);
            return processErrorResult(500, "文件解析失败");
        }

        int pageNo = 0;
        boolean hasNextPage;
        JSONObject resJson = null;
        try {
            do {
                List<List<String>> excelData = new ArrayList<>();
                hasNextPage = ExcelParser.readExcelData(workbook, 0, pageNo, 1000, excelData);
                //parse data
                List<InvoiceDO> invoiceDOList = new ArrayList<>();
                List<InvoiceDetailDO> invoiceDetailDOList = new ArrayList<>();
                Multimap<String, Integer> invoiceNoMultimap = ArrayListMultimap.create();
                JSONObject res = invoiceService.parse(excelData, invoiceDOList, invoiceDetailDOList,
                        getUserId(servletRequest), invoiceNoMultimap);

                resJson = invoiceService.parseErrorMsg(filePathName, workbook, invoiceDOList, res, invoiceNoMultimap);
                if (resJson.containsKey("errMsg")) {
                    return processResult(resJson);
                }
                // 获取当前登录userId
                Integer userId = getUserId(servletRequest);
                // 获取当前userId对应的代理商信息
                so.dian.center.common.entity.BizResult<AgentDTO> agentDTOBizResult =
                        agentEmployeeService.getAgentByUserId(Long.valueOf(userId));
                if (agentDTOBizResult == null || !agentDTOBizResult.isSuccess()
                        || null == agentDTOBizResult.getData()) {
                    return processErrorResult(500, "当前登录身份无效，请重新登录！");
                }
                AgentDTO agentDTO = agentDTOBizResult.getData();
                for (InvoiceDO invoiceDO : invoiceDOList) {
                    // 设置当前发票的归属信息
                    if (SubjectTypeEnum.SCM.getType() != invoiceDO.getSubjectType()) {
                        invoiceDO.setBelongSubjectType(agentDTO.getType());
                        invoiceDO.setBelongSubjectId(agentDTO.getAgentId().intValue());
                    }

                }
                //save data
                invoiceService.insertInvoiceList(invoiceDOList);
                invoiceService.insertInvoiceDetailBatch(invoiceDetailDOList);

                resJson.put("num", invoiceDetailDOList.size());
                pageNo++;

            } while (hasNextPage && pageNo < 2);
            workbook.close();
        } catch (IOException e) {
            logger.error("------ 发票导入，业务异常。", e);
        }

        invoiceService.deleteInvoiceFile(filePathName);

        return processResult(resJson);
    }

    @RequestMapping(value = {"/crm/invoice/update"}, method = RequestMethod.POST)
    public String updateInvoice(HttpServletRequest servletRequest, @RequestBody InvoiceDto dto) {

        try {
            if (dto == null || dto.getId() == null) {
                return processErrorResult(500, "参数为空！");
            }
            // 获取当前登录userId
            Integer userId = getUserId(servletRequest);
            // 根据userId获取员工表的信息
            AgentEmployeeDTO agentEmployeeDO = agentEmployeeService.getEmployeeById(userId);
            if (agentEmployeeDO == null) {
                return processErrorResult(500, "当前登录身份无效，请重新登录！");
            }
            CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(userId);

            // 获取当前userId对应的代理商信息
            so.dian.center.common.entity.BizResult<AgentDTO> agentDTOBizResult =
                    agentEmployeeService.getAgentByUserId(Long.valueOf(userId));
            if (agentDTOBizResult == null || !agentDTOBizResult.isSuccess() || null == agentDTOBizResult.getData()) {
                return processErrorResult(500, "当前登录身份无效，请重新登录！");
            }

            InvoiceDO invoiceDO = invoiceService.getInvoiceById(dto.getId());
            if (invoiceDO == null) {
                return processErrorResult(500, "发票记录不存在！");
            }
            // 校验发票归属人
            invoiceService.checkInvoiceBelong(invoiceDO, userId);

            if (!invoiceDO.getId().equals(dto.getId())) {
                return processErrorResult(500, "该发票号已经存在！");
            }
            if (!invoiceDO.getStatus().equals(InvoiceStatusEnum.WAIT.getType())) {
                return processErrorResult(500, "该发票已被使用，不能修改！");
            }
            //检查修改后的发票是否已存在
            if (!Objects.equals((invoiceDO.getInvoiceCode() == null ? "" : invoiceDO.getInvoiceCode()),
                    (dto.getInvoiceCode() == null ? "" : dto.getInvoiceCode()))
                    || !Objects.equals(invoiceDO.getInvoiceNo(), dto.getInvoiceNo())) {
                InvoiceDO invoice =
                        invoiceService.getInvoiceByInvoiceCodeAndNo(dto.getInvoiceCode(), dto.getInvoiceNo());
                if (Objects.nonNull(invoice) && !invoice.getId().equals(dto.getId())) {
                    return processErrorResult(500, "目标发票已存在，不能修改！");
                }
            }
            // 购买方税号校验，小电集团的只能填写小电集团的，非小电集团的购买方税号只能填写非小电集团的
            boolean checkResult = invoiceValidateService.checkInvoiceBuyer(CheckInvoiceBuyerDTO.builder()
                    .belongSubjectType(currentUserReq.getBelongSubjectType())
                    .buyer(dto.getBuyer())
                    .belongSubjectId(Long.valueOf(currentUserReq.getBelongSubjectId()))
                    .buyerTaxId(dto.getBuyerTaxId()).build());
            if (!checkResult) {
                return processErrorResult(
                        InvoiceErrorCodeEnum.NO_PERMISSION_BUYER.getCode(),
                        InvoiceErrorCodeEnum.NO_PERMISSION_BUYER.getDesc());
            }

            AgentDTO agentDTO = agentDTOBizResult.getData();
            // 校验发票归属
            if (dto.getBelongSubjectId() == null && dto.getBelongSubjectType() == null) {
                // 设置当前发票的归属信息
                dto.setBelongSubjectType(agentDTO.getType());
                dto.setBelongSubjectId(agentDTO.getAgentId() == null ? null : agentDTO.getAgentId().intValue());
            } else {
                // 无权修改，为啥能看到
                // 新增小电能修改所有发票，其它主体只能修改自己的发票逻辑
                if (agentDTO.getAgentId() != 0 && agentDTO.getAgentId() != 1 && agentDTO.getAgentId() != 3) {
                    if ((dto.getBelongSubjectId() != null && !dto.getBelongSubjectId()
                            .equals(agentDTO.getAgentId().intValue()))
                            || (dto.getBelongSubjectType() != null && !dto.getBelongSubjectType()
                            .equals(agentDTO.getType()))) {
                        logger.error("------ 发票更新数据越权");
                        return processErrorResult(
                                InvoiceErrorCodeEnum.INVOICE_UPDATE_DATA_EXCEEDS_PERMISSIONS.getCode(),
                                InvoiceErrorCodeEnum.INVOICE_UPDATE_DATA_EXCEEDS_PERMISSIONS.getDesc());
                    }
                }
            }

            //判断校验信息有没有变更
            Boolean isUpdate = isUpdate(dto, invoiceDO);
            Integer isReal = isUpdate ? 0 : invoiceDO.getIsReal();
            dto.setIsReal(isReal);

            // 设置税额，前端传的是taxPrice
            dto.setTax(dto.getTaxPrice());
            InvoiceDO invoice = new InvoiceDO();
            BeanUtils.copyProperties(dto, invoice);

            if (SubjectTypeEnum.SCM.getType() == dto.getSubjectType()) {
                if (CompanySubjectEnum.YOUDIAN.getDesc().equals(dto.getCompanySubject())) {
                    invoice.setBelongSubjectId(0);
                } else {
                    invoice.setBelongSubjectId(1);
                }
            }
            invoice.setBuyer(dto.getCompanySubject());

            //购买方主体开票时间区间校验
            if (StringUtils.isNotBlank(invoice.getBuyer())) {
                InvoiceBuyerValidator.checkInvoiceDateRange(agentDTO.getType(), invoice.getGmtCreate(),
                        invoice.getBuyer());
            }
            if (invoice.getInvoiceCode() == null) {
                invoice.setInvoiceCode("");
            }
            if (invoiceDO.getInvoiceCode() == null) {
                invoiceDO.setInvoiceCode("");
            }
            invoiceService.updateInvoice(invoice);
            List<InvoiceDetailDto> invoiceDetailDtoList = dto.getInvoiceDetailDtoList();
            invoiceService.saveInvoiceDetail(invoice.getInvoiceCode(), invoice.getInvoiceNo(), invoiceDetailDtoList,
                    invoiceDO.getInvoiceCode(), invoiceDO.getInvoiceNo());
            //编辑日志
            Map<String, Object> content = Maps.newHashMap();
            content.put("source", invoiceDO);
            content.put("target", invoice);
            content.put("comment", invoice.getMemo());

            InvoiceOperateLogBO invoiceOperateLogBO = new InvoiceOperateLogBO();
            invoiceOperateLogBO.setInvoiceId(invoice.getId());
            invoiceOperateLogBO.setType(InvoiceOperateLogTypeEnum.INVOICE_EDIT);
            invoiceOperateLogBO.setContent(content);
            invoiceOperateLogBO.setOperatorId(Long.valueOf(userId));
            //渠道商没有花名
            invoiceOperateLogBO.setOperatorName(agentEmployeeDO.getNickNameOrName());
            invoiceOperateLogManager.insert(invoiceOperateLogBO);


        } catch (Exception e) {
            logger.error("编辑发票出错，param:{}", dto, e);
            // 为了统一逻辑，特殊处理
            if ("718 : 没有操作该发票的权限, INVOICE_PERMISSION_ERROR".equals(e.getMessage())){
                return processErrorResult(500, LeoExcs.INVOICE_PERMISSION_ERROR.getMessage());
            }
            return processErrorResult(100, "编辑发票出错");
        }
        return processResult(null);
    }

    private boolean isUpdate(InvoiceDto params, InvoiceDO recordDO) {
        String invoiceNo = recordDO.getInvoiceNo() == null ? "" : recordDO.getInvoiceNo();
        String newInvoiceNo = params.getInvoiceNo() == null ? "" : params.getInvoiceNo();
        String invoiceCode = recordDO.getInvoiceCode() == null ? "" : recordDO.getInvoiceCode();
        String newInvoiceCode = params.getInvoiceCode() == null ? "" : params.getInvoiceCode();
        //增值税专票  发票代码，发票号码，发票类型，发票日期 税前金额
        if (InvoiceTypeEnum.VAT.getType() == params.getType()) {
            BigDecimal rawPrice = recordDO.getRawPrice() == null ? new BigDecimal(0.0) : recordDO.getRawPrice();
            BigDecimal newRawPrice = params.getRawPrice() == null ? new BigDecimal(0.0)
                    : transBigDecimal(params.getRawPrice().toString(), 4);
            if (invoiceNo.equals(newInvoiceNo)
                    && invoiceCode.equals(newInvoiceCode)
                    && recordDO.getType().equals(params.getType())
                    && recordDO.getGmtCreate().equals(params.getGmtCreate())
                    && rawPrice.equals(newRawPrice)) {
                return false;
            }
            return true;
        }
        String checkCode = recordDO.getCheckCode() == null ? "" : recordDO.getCheckCode();
        String newCheckCode = params.getCheckCode() == null ? "" : params.getCheckCode();
        //非增值税专票  发票代码，发票号码，发票类型，发票日期 校验码
        if (invoiceNo.equals(newInvoiceNo)
                && invoiceCode.equals(newInvoiceCode)
                && recordDO.getType().equals(params.getType())
                && recordDO.getGmtCreate().equals(params.getGmtCreate())
                && checkCode.equals(newCheckCode)) {
            return false;
        }

        return true;
    }

    public BigDecimal transBigDecimal(String str, int newScale) {
        if (StringUtil.isBlank(str)) {
            return new BigDecimal(0.0);
        }
        //构造以字符串内容为值的BigDecimal类型的变量bd
        BigDecimal bd = new BigDecimal(str);
        bd = bd.setScale(newScale, BigDecimal.ROUND_HALF_UP);
        return bd;
    }

    /**
     * 删除发票信息
     */
    @RequestMapping(value = {"/crm/invoice/delete"}, method = RequestMethod.POST)
    public BizResult<Boolean> deleteInvoice(HttpServletRequest request, @RequestBody InvoiceObsoleteParam param) {

        // 校验只有 财务经理 和 城市行政 有权操作
        UserRoleEnum userRoleEnum = getUserRole(request);

        String invoiceCode = StringUtils.isBlank(param.getInvoiceCode()) ? "" : param.getInvoiceCode();
        String invoiceNo = param.getInvoiceNo();
        Integer userId = getUserId(request);
        String userName = getNickName(request);
        param.setOperatorId(userId.longValue());
        param.setOperatorName(userName);
        if (StringUtils.isBlank(invoiceNo)) {
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR, "发票号码或者代码不能为空");
        }

        InvoiceDO invoiceDO = invoiceService.getInvoiceByInvoiceCodeAndNo(invoiceCode, invoiceNo);
        if (Objects.isNull(invoiceDO)) {
            return BizResult.error(ErrorCodeEnum.INTERNAL_SERVER_ERROR, "无此发票！");
        }
        if (!invoiceDO.getStatus().equals(InvoiceStatusEnum.WAIT.getType())) {
            return BizResult.error(ErrorCodeEnum.INTERNAL_SERVER_ERROR, "该发票已被使用，不能删除！");
        }
        invoiceService.checkInvoiceBelong(invoiceDO, param.getOperatorId().intValue());
        param.setInvoiceId(invoiceDO.getId().longValue());
        invoiceService.deleteInvoiceByInvoiceCodeAndNo(param);
        return BizResult.create(true);
    }

    @RequestMapping(value = {"/crm/invoice/list"}, method = RequestMethod.POST)
    public String getInvoiceList(HttpServletRequest servletRequest, @RequestBody InvoiceParam param) {

        // 获取当前登录者的userId
        Integer userId = getUserId(servletRequest);
        if (userId == null) {
            logger.error("当前登录者渠道信息获取失败。userId：null");
            return processResult(null);
        }

        UserRoleEnum userRoleEnum = getUserRole(servletRequest);
        // 判断用户角色是否是采购主管、供应商管理员、采购单操作人、工厂仓管员
        if (UserRoleEnum.PURCHASE_MANAGER.equals(userRoleEnum)
                || UserRoleEnum.SUPPLIER_MANAGER.equals(userRoleEnum)
                || UserRoleEnum.PO_OPERATOR.equals(userRoleEnum)
                || UserRoleEnum.FACTORY_WAREHOUSE_MANAGER.equals(userRoleEnum)) {
            // 是的话业务类型只选择 供应链（1）
            param.setSubjectType(SubjectTypeEnum.SCM.getType());
        }

        //行政只能查看自己的发票
        if (Objects.equals(userRoleEnum, UserRoleEnum.CITY_ADMINISTRATION)||Objects.equals(userRoleEnum, UserRoleEnum.WAREHOUSE_KEEPER)) {
            param.setCreator(userId);
        }

        // 设置渠道信息
        AgentDTO agentDTO = invoiceService.getInvoiceBelongInfo(userId);
        if (agentDTO == null) {
            logger.error("当前登录者渠道信息获取失败。userId：" + userId);
            return processResult(null);
        }
        // 0代理商 1开户型服务商 2资源型服务商 3运营型服务商
        // 小电和友电也属于代理商类型, 限定查询条件为代理商的发票
        param.setBelongSubjectId(agentDTO.getAgentId().intValue());
        //param.setBelongSubjectType(agentDTO.getType());

        if (param.getSubjectType() != null && SubjectTypeEnum.SCM.getType() == param.getSubjectType()) {
            // 发票从属主体ID(小电默认为1)
            param.setBelongSubjectId(null);
            // 发票从属主体类型(0:小电;1:代理商;2:运营型服务商)
            param.setBelongSubjectType(agentDTO.getType());
        }

        // 判断快递单号是否为空
        if (StringUtils.isNotBlank(param.getExpressNo())) {
            List<Long> invoiceIdList = invoiceService.listInvoiceIdsByExpressNo(param.getExpressNo());
            if (CollectionUtils.isNotEmpty(invoiceIdList)) {
                // 如果存在，加入到查询条件
                param.setInvoiceIdList(invoiceIdList);
            } else {
                JSONObject res = new JSONObject();
                // 如果不存在，直接返回
                param.setCount(0);
                res.put("list", Lists.newArrayList());
                res.put("param", param);
                return processResultFastjson(res);
            }
        }

        // 查询
        List<InvoiceDto> invoiceDtoList = invoiceService.getInvoicePage(param);

        if (invoiceDtoList != null && invoiceDtoList.size() > 0) {
            Set<Integer> userIdSet = new HashSet<>();
            for (InvoiceDto invoiceDto : invoiceDtoList) {
                invoiceDto.setValidateCodeStr(
                        InvoiceIdentifyRecordEnum.ValidationCodeEnum.getDescByCode(invoiceDto.getValidateCode()));
                invoiceDto.setTypeStr(InvoiceTypeEnum.getField(invoiceDto.getType()));
                invoiceDto.setProcessStatusStr(
                        LocalEnumUtils.getDescByCode(InvoiceProcessStatusEnum.class, invoiceDto.getProcessStatus()));
                invoiceDto.setStatusStr(InvoiceStatusEnum.getField(invoiceDto.getStatus()));

                invoiceDto.setTaxPrice(invoiceDto.getTax());
                if (null == invoiceDto.getTaxPrice()) {
                    BigDecimal rawPrice =
                            invoiceDto.getRawPrice() == null ? new BigDecimal(0.00) : invoiceDto.getRawPrice();
                    BigDecimal price = invoiceDto.getPrice() == null ? new BigDecimal(0.00) : invoiceDto.getPrice();
                    invoiceDto.setTaxPrice(price.subtract(rawPrice));
                }

                // 未核销金额 = 总金额 - 已核销金额
                invoiceDto.setRemainderAmount(invoiceDto.getPrice().subtract(invoiceDto.getUsedAmount()));
                invoiceDto.setSubjectTypeStr(SubjectTypeEnum.getField(invoiceDto.getSubjectType()));
                if (Objects.nonNull(invoiceDto.getInCheckPool())) {
                    if (Objects.equals(invoiceDto.getInCheckPool(), 0)) {
                        invoiceDto.setIsInCheckPoolStr("否");
                    }
                    if (Objects.equals(invoiceDto.getInCheckPool(), 1)) {
                        invoiceDto.setIsInCheckPoolStr("是");
                    }
                }
                userIdSet.add(invoiceDto.getCreator());
            }

            //查询发票快递关系，取出所有发票id，用来查询发票的快递信息
            List<Integer> invoiceIds = LocalListUtils.transferList(invoiceDtoList, InvoiceDto::getId);
            List<Long> invoiceIdList = Lists.newArrayList();
            invoiceIds.forEach(invoiceId -> {
                invoiceIdList.add(Long.valueOf(invoiceId));
            });
            // 调用 发票微服务 查询发票的快递信息
            List<InvoiceExpressesMsgDto> expressesDTOList = invoiceExpressesRelationService.findExpressesByInvoiceIds(
                    invoiceIdList);
            Map<Long, InvoiceExpressesMsgDto> invoiceIdExpressesDTOMap =
                    LocalMapUtils.collectionToMap(expressesDTOList, InvoiceExpressesMsgDto::getInvoiceId);
            invoiceDtoList.forEach(invoiceDto -> {
                InvoiceExpressesMsgDto expressesDTO = invoiceIdExpressesDTOMap.get(invoiceDto.getId().longValue());
                if (Objects.nonNull(expressesDTO)) {
                    InvoiceDto.ExpressInfo expressInfo = new InvoiceDto.ExpressInfo();
                    expressInfo.setExpressName(
                            expressesDTO.getExpressName() == null ? "" : expressesDTO.getExpressName());
                    expressInfo.setExpressNo(expressesDTO.getExpressTrackingNo());
                    invoiceDto.setExpressInfo(expressInfo);
                }
            });
            // id 和 花名的映射
            Map<Integer, String> userIdNickSet = agentEmployeeService.getAgentEmployeeNickMap(userIdSet);
            for (InvoiceDto invoiceDto : invoiceDtoList) {
                invoiceDto.setCreatorNick(userIdNickSet.get(invoiceDto.getCreator()));
                // 设置发票来源
                invoiceDto.setSourceStr(InvoiceSourceEnum.getField(invoiceDto.getSource()));
                // 设置发票验真状态
                invoiceDto.setIsRealStr(InvoiceIsRealEnum.getDescByCode(invoiceDto.getIsReal()));
                // 填充业务编号
                invoiceService.fillBusinessNo(invoiceDto);
            }
            // 根据单据编号进行查询
            if (StringUtils.isNotBlank(param.getBillNo())) {
                invoiceDtoList = invoiceDtoList.stream().filter((invoiceDto) -> {
                    String billNo = invoiceDto.getBillNo();
                    if (StringUtils.isBlank(billNo)) {
                        return false;
                    }
                    String[] split = billNo.split(",");
                    return Arrays.stream(split).anyMatch(str -> param.getBillNo().equals(str));
                }).collect(Collectors.toList());
            }
        }

        JSONObject res = new JSONObject();
        res.put("list", invoiceDtoList);
        param.setCount(invoiceService.count(param));
        res.put("param", param);

        return processResultFastjson(res);
    }

    /**
     * 供应链角色，发票导出
     *
     * @param request
     * @param response
     * @param param
     * @return
     */
    @RequestMapping(value = {"/crm/invoice/downLoad"}, method = RequestMethod.POST)
    public String exportInvoiceListBySupplier(HttpServletRequest request, HttpServletResponse response,
            @RequestBody InvoiceParam param) {

        UserRoleEnum userRoleEnum = getUserRole(request);

        // 只有供应链角色能导出
        if (!UserRoleEnum.PURCHASE_MANAGER.equals(userRoleEnum)
                && !UserRoleEnum.SUPPLIER_MANAGER.equals(userRoleEnum)
                && !UserRoleEnum.PO_OPERATOR.equals(userRoleEnum)
                && !UserRoleEnum.FACTORY_WAREHOUSE_MANAGER.equals(userRoleEnum)) {

            logger.error("------ 发票导出，该接口只有供应链角色可以导出。role = {}", userRoleEnum.getRoleLabel());
            return processErrorResult(500, "用户没有权限");
        }

        // 只有供应链类型
        param.setSubjectType(SubjectTypeEnum.SCM.getType());

        if (param.getEndCreateTime() == null && param.getStartCreateTime() == null) {
            param.setEndCreateTime(param.getEndGmtCreate());
            param.setStartCreateTime(param.getStartGmtCreate());
        }

        param.setEndCreateTime(DateOptUtil.addOneDay(param.getEndCreateTime()));
        param.setEndGmtCreate(DateOptUtil.addOneDay(param.getEndGmtCreate()));

        // 获取当前登录者的userId
        Integer userId = getUserId(request);
        if (userId == null) {
            logger.error("------ 发票导出，登录信息获取失败。userId == null");
            return processErrorResult(500, "登录信息获取失败");
        }

        // 设置渠道信息
        AgentDTO agentDTO = invoiceService.getInvoiceBelongInfo(userId);
        if (agentDTO == null) {
            logger.error("------ 发票导出，渠道信息获取失败。userId = {}", userId);
            return processErrorResult(500, "渠道信息获取失败");
        }
        param.setBelongSubjectId(agentDTO.getAgentId().intValue());
        //param.setBelongSubjectType(agentDTO.getType());

        // 执行下载操作
        invoiceService.downLoadInvoiceDetail(param, request, response);

        return processResult(null);
    }

    @RequestMapping(value = {"/{version}/invoice/detail"}, method = RequestMethod.POST)
    public String getInvoiceDetailForLvy(HttpServletRequest servletRequest, @RequestBody
    so.dian.invoice.pojo.param.InvoiceParam param) {
        InvoiceDto invoiceDetails = invoiceFacade.getInvoiceDetails(param);
        if (Objects.isNull(invoiceDetails)) {
            return processErrorResult(500, "发票不存在");
        }
        if (param.getSubjectType() != null && !param.getSubjectType().equals(invoiceDetails.getSubjectType())) {

            return processErrorResult(500, "发票类型匹配失败");
        }
        invoiceDetails.setBuyer(StringUtil.trimFull2Half(invoiceDetails.getBuyer()));
        invoiceDetails.setSubjectName(StringUtil.trimFull2Half(invoiceDetails.getSubjectName()));
        return processResultFastjson(invoiceDetails);
    }

    @RequestMapping(value = {"/crm/invoice/detail"}, method = RequestMethod.POST)
    public String getInvoiceDetail(HttpServletRequest servletRequest, @RequestBody InvoiceParam param) {

        InvoiceDto dto = new InvoiceDto();
        try {
            String invoiceCode = param.getInvoiceCode() == null ? "" : param.getInvoiceCode();
            InvoiceDO invoice = invoiceService.getInvoiceByInvoiceCodeAndNo(invoiceCode, param.getInvoiceNo());
            BeanUtils.copyProperties(invoice, dto);
            dto.setBuyerTaxId(invoice.getBuyerTaxId() == null ? "" : invoice.getBuyerTaxId());
            dto.setProcessStatus(invoice.getProcessStatus());

            dto.setTaxPrice(dto.getTax());
            if (null == dto.getTaxPrice()) {
                BigDecimal rawPrice = dto.getRawPrice() == null ? new BigDecimal(0.00) : dto.getRawPrice();
                BigDecimal price = dto.getPrice() == null ? new BigDecimal(0.00) : dto.getPrice();
                dto.setTaxPrice(price.subtract(rawPrice));
            }

            String supplierName = supplierService.getSupplierNameBySupplierNo(invoice.getSupplierNo());
            dto.setSupplierName(supplierName);
            dto.setTypeStr(InvoiceTypeEnum.getField(dto.getType()));
            dto.setStatusStr(InvoiceStatusEnum.getField(dto.getStatus()));
            dto.setCreatorNick(agentEmployeeService.getEmployeeNickById(dto.getCreator()));
            dto.setSubjectTypeStr(SubjectTypeEnum.getField(dto.getSubjectType()));

            // 归属小电和友电的发票
            if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(invoice.getBelongSubjectId())) {
                if (StringUtils.isBlank(invoice.getBuyer())) {
                    if (CompanySubjectEnum.YIDIANYUAN.getCode() == dto.getBelongSubjectId()) {
//                        dto.setCompanySubject(CompanySubjectEnum.YIDIANYUAN.getName());
                        dto.setCompanySubject(CompanySubjectEnum.YIDIANYUAN.getDesc());
                        dto.setCompanySubjectStr(CompanySubjectEnum.YIDIANYUAN.getDesc());
                    } else if (CompanySubjectEnum.YOUDIAN.getCode() == dto.getBelongSubjectId()) {
//                        dto.setCompanySubject(CompanySubjectEnum.YOUDIAN.getName());
                        dto.setCompanySubject(CompanySubjectEnum.YOUDIAN.getDesc());
                        dto.setCompanySubjectStr(CompanySubjectEnum.YOUDIAN.getDesc());
                    } else if (CompanySubjectEnum.HZYIDIANYUAN.getCode() == dto.getBelongSubjectId()) {
//                        dto.setCompanySubject(CompanySubjectEnum.HZYIDIANYUAN.getName());
                        dto.setCompanySubject(CompanySubjectEnum.HZYIDIANYUAN.getDesc());
                        dto.setCompanySubjectStr(CompanySubjectEnum.HZYIDIANYUAN.getDesc());
                    }
                } else {
                    dto.setCompanySubjectStr(invoice.getBuyer());
//                    CompanySubjectEnum companySubjectEnum = CompanySubjectEnum.getEnumByDesc(invoice.getBuyer());
//                    dto.setCompanySubject(Objects.isNull(companySubjectEnum) ? "" : companySubjectEnum.getDesc());
                    dto.setCompanySubject(invoice.getBuyer());
//                    dto.setCompanySubject(Objects.isNull(companySubjectEnum) ? "" : companySubjectEnum.getName());
                }
            } else {
                dto.setCompanySubjectStr(invoice.getBuyer());
                dto.setCompanySubject(invoice.getBuyer());
//                dto.setCompanySubject("");
            }

            Integer userId = getUserId(servletRequest);
            if (userId == null) {
                logger.error("获取发票详情出错，登录者的渠道信息获取失败。userId = null");
                return processResult(null);
            }

            //财务复核通过 或者 不通过
            if (Objects.equals(invoice.getProcessStatus(), 2)
                    || Objects.equals(invoice.getProcessStatus(), 3)) {
                dto.setReviewTime(invoice.getReviewTime());
                dto.setReviewSuggestion(invoice.getReviewRemark());
                String employeeNick = agentEmployeeService.getEmployeeNickById(Math.toIntExact(invoice.getReviewer()));
                if (StringUtils.isNotBlank(employeeNick)) {
                    dto.setReviewerName(employeeNick);
                }
            }

            List<InvoiceDetailDO> invoiceDetailDOList =
                    invoiceService.getInvoiceDetailByInvoiceCodeAndNo(invoiceCode, param.getInvoiceNo());

            if (invoiceDetailDOList != null && invoiceDetailDOList.size() > 0) {
                List<Integer> supplierDetailIds = LocalListUtils.transferList(invoiceDetailDOList,
                        InvoiceDetailDO::getSupplierInvoiceDetailId);
                List<SupplierInvoiceDetailDO> supplierDetailDOList = supplierInvoiceDetailManager.findListById(
                        supplierDetailIds);
                Map<Integer, SupplierInvoiceDetailDO> supplierDetailIdMap = LocalMapUtils.listAsHashMap(
                        supplierDetailDOList, SupplierInvoiceDetailDO::getId);

                List<InvoiceDetailDto> invoiceDetailDtoList = new ArrayList<>();
                for (InvoiceDetailDO invoiceDetail : invoiceDetailDOList) {
                    InvoiceDetailDto detailDto = new InvoiceDetailDto();
                    BeanUtils.copyProperties(invoiceDetail, detailDto);

                    SupplierInvoiceDetailDO supplierInvoiceDetailDO = supplierDetailIdMap.get(
                            invoiceDetail.getSupplierInvoiceDetailId());
                    if (supplierInvoiceDetailDO != null) {
                        detailDto.setVerifyBillNo(supplierInvoiceDetailDO.getVerifyBillNo());
                    }

                    invoiceDetailDtoList.add(detailDto);
                }

                dto.setInvoiceDetailDtoList(invoiceDetailDtoList);
            }
            if (disableRollbackSpec.isSatisfiedBy(invoice)) {
                dto.setCanRollBack(false);
            } else {
                dto.setCanRollBack(true);
            }
            invoiceService.fillBusinessNo(dto);
        } catch (Exception e) {
            logger.error("获取发票详情出错，param = {}", param, e);
            return processErrorResult(100, "获取发票详情出错");
        }
        return processResultFastjson(dto);
    }

    @RequestMapping(value = {"/{version}/invoice/detail/byPayApply",
            "/{version}/h5/invoice/detail/byPayApply"}, method = RequestMethod.POST)
    public String getInvoiceDetailByPayApply(HttpServletRequest servletRequest, @RequestBody InvoiceParam param) {

        String invoiceCode = param.getInvoiceCode() == null ? "" : param.getInvoiceCode();
        InvoiceDto dto = new InvoiceDto();
        InvoiceDO invoice;

        invoice = invoiceService.getInvoiceByInvoiceCodeAndNo(invoiceCode, param.getInvoiceNo());

        if (invoice == null ||
                param.getSubjectType() != null && !param.getSubjectType().equals(invoice.getSubjectType())) {

            return processResult(null);
        }

        BeanUtils.copyProperties(invoice, dto);

        dto.setTaxPrice(dto.getTax());
        if (null == dto.getTaxPrice()) {
            BigDecimal rawPrice = dto.getRawPrice() == null ? new BigDecimal(0.00) : dto.getRawPrice();
            BigDecimal price = dto.getPrice() == null ? new BigDecimal(0.00) : dto.getPrice();
            dto.setTaxPrice(price.subtract(rawPrice));
        }

        String supplierName = supplierService.getSupplierNameBySupplierNo(invoice.getSupplierNo());
        dto.setSupplierName(supplierName);
        dto.setTypeStr(InvoiceTypeEnum.getField(dto.getType()));
        dto.setStatusStr(InvoiceStatusEnum.getField(dto.getStatus()));
        dto.setCreatorNick(agentEmployeeService.getEmployeeNickById(dto.getCreator()));
        dto.setSubjectTypeStr(SubjectTypeEnum.getField(dto.getSubjectType()));
        List<InvoiceDetailDO> invoiceDetailDOList =
                invoiceService.getInvoiceDetailByInvoiceCodeAndNo(invoice.getInvoiceCode(), invoice.getInvoiceNo());
        if (invoiceDetailDOList != null && invoiceDetailDOList.size() > 0) {

            List<InvoiceDetailDto> invoiceDetailDtoList = new ArrayList<>();
            for (InvoiceDetailDO invoiceDetail : invoiceDetailDOList) {
                InvoiceDetailDto detailDto = new InvoiceDetailDto();
                BeanUtils.copyProperties(invoiceDetail, detailDto);

                invoiceDetailDtoList.add(detailDto);
            }

            dto.setInvoiceDetailDtoList(invoiceDetailDtoList);
        }

        return processResultFastjson(dto);
    }

    @Deprecated
    @RequestMapping(value = {"/{version}/invoice/deduct/list",
            "/{version}/h5/invoice/deduct/list"}, method = RequestMethod.POST)
    public String deductInvoiceList(HttpServletRequest servletRequest, @RequestBody InvoiceDeductOperationParam param) {
        logger.error("接口废弃:/invoice/deduct/list");
        List<InvoiceDeductionDO> invoiceDeductionDOList = invoiceDeductionService.getInvoiceDeductionList(param);

        return processResultFastjson(invoiceDeductionDOList);
    }

    /**
     * 自动新增发票(批量)
     */
    @RequestMapping(value = {"/invoiceManage/autoAddBatch", "/h5/invoiceManage/autoAddBatch"})
    public String autoAddBatch(HttpServletRequest servletRequest, @MeidaRequestData InvoiceValidateParams params) {

        if (params == null || params.getSubjectType() == null) {
            return processErrorResult(0, "参数为空");
        }
        Integer userId = getUserId(servletRequest);
        params.setLoginUserId(userId);
        String employeeNick = agentEmployeeService.getEmployeeNickById(getUserId(servletRequest));
        if (StringUtils.isNotBlank(employeeNick)) {
            params.setLoginUserName(employeeNick);
        }
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(userId);
        params.setCurrentUserReq(currentUserReq);

        String result = invoiceService.autoAddBatch(params);
        return processResult(result);
    }

    /**
     * 自动新增发票(单张)
     */
    @RequestMapping(value = {"/invoiceManage/autoAddBatchSingle", "/h5/invoiceManage/autoAddBatchSingle"})
    public String autoAddBatchSingle(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceValidateParams params) {

        if (params == null || params.getSubjectType() == null || params.getId() == null) {
            return processErrorResult(0, "参数为空");
        }
        Integer userId = getUserId(servletRequest);
        params.setLoginUserId(userId);
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(userId);
        params.setLoginUserName(currentUserReq.getNickName());
        params.setCurrentUserReq(currentUserReq);
        ServiceResult<String> result = invoiceService.autoAddBatchSingle(params);
        if (!result.isSuccess()) {
            return processErrorResult(100, result.getMsg());
        }
        return processResult(result.getData());
    }

    /**
     * 发票主体对应关系查询
     */
    @RequestMapping(value = {"/invoice/subjectRelation", "/h5/invoice/subjectRelation"}, method = RequestMethod.POST)
    public BizResult<Boolean> subjectRelation(@RequestBody InvoiceSubjectRelationParam param) {
        ServiceResult<Boolean> result = invoiceService.getSubjectRelation(param);
        if (!result.isSuccess()) {
            return BizResult.error(ErrorCodeEnum.FALLBACK);
        }
        return BizResult.create(result.getData());
    }

    /**
     * 根据关键字前置模糊匹配查询提现主体列表
     */
    @RequestMapping(value = {"/invoice/querySubjectName"}, method = RequestMethod.POST)
    public BizResult<List<String>> queryWithdrawSubjectNameList(@MeidaRequestData WithdrawSubjectNameParam param) {
        if (param == null) {
            return BizResult.create(new ArrayList<>());
        }
        List<String> subjectNameList = invoiceService.queryWithdrawSubjectNameList(param.getKeyword());
        return BizResult.create(subjectNameList);
    }

    /**
     * 发票复核
     */
    @RequestMapping(value = {"/invoice/review"}, method = RequestMethod.POST)
    public BizResult<Boolean> invoiceReview(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceReviewParam param) {
        param.setUserId(getUserId(servletRequest));
        return invoiceService.invoiceReview(param);
    }

    /**
     * 发票台账导出
     */
    @RequestMapping(value = "/invoiceList/export", method = RequestMethod.POST)
    public BizResult<Boolean> exportInvoiceList(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceExportParam param) {
        Integer userId = getUserId(servletRequest);
        param.setUserId(userId);
        long startTime = System.currentTimeMillis();
        RLock lock = redisson.getLock("export_invoice_list" + userId);
        boolean lockSuccess = false;

        // 设置渠道信息
        AgentDTO agentDTO = invoiceService.getInvoiceBelongInfo(userId);
        if (agentDTO == null || agentDTO.getAgentId() == null) {
            logger.error("当前登录者渠道信息获取失败。userId：" + userId);
            return BizResult.error(ErrorCodeEnum.SYSTEM_BUSY, "当前登录者渠道信息获取失败");
        }
        // 0代理商 1开户型服务商 2资源型服务商 3运营型服务商
        // 小电和友电也属于代理商类型, 限定查询条件为代理商的发票
        param.setBelongSubjectId(agentDTO.getAgentId().intValue());
        //param.setBelongSubjectType(agentDTO.getType());

        try {
            logger.info("当前线程id:{}", Thread.currentThread().getId());
            //尝试加锁，最多等待1秒，上锁以后5秒自动解锁
            logger.info("线程开始时间:{}", System.currentTimeMillis());
            if (lockSuccess = lock.tryLock(0, 5, TimeUnit.SECONDS)) {
                //行政只能查看自己的发票
                if (Objects.equals(getUserRole(), UserRoleEnum.CITY_ADMINISTRATION)) {
                    param.setCreator(Long.valueOf(userId));
                }
                invoiceService.exportInvoiceList(param);
            } else {
                throw BizException.create(ErrorCodeEnum.SYSTEM_BUSY, "发票正在导出，请稍后");
            }
        } catch (InterruptedException e) {
            logger.error(">>>initDeliveryDO InterruptedException {}", param, e);
        } finally {
            if (lockSuccess && lock.isHeldByCurrentThread()) {
                lock.unlock();
                long endTime = System.currentTimeMillis();
                logger.info("耗时:{}", endTime - startTime);
            }
        }
        return BizResult.create(true);
    }

    @RequestMapping(value = "/invoice/subjectName/list", method = RequestMethod.GET)
    public BizResult<List<String>> listSubjectName(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceSubjectInfoParam param) {
        Integer userId = getUserId(servletRequest);
        param.setLoginUserId(userId);
        return invoiceService.listSubjectName(param);
    }

    /**
     * 获取发票详情（小二端）
     */
    @RequestMapping(value = "/h5/invoice/details", method = RequestMethod.GET)
    public BizResult<InvoiceDto> getInvoiceDetails(@MeidaRequestData InvoiceParam param) {
        InvoiceDto invoiceDetails = invoiceFacade.getInvoiceDetails(param);
        if (Objects.isNull(invoiceDetails)) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST);
        }
        if (param.getSubjectType() != null && !param.getSubjectType().equals(invoiceDetails.getSubjectType())) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_TYPE_MATCH_FAILED);
        }
        return BizResult.create(invoiceDetails);
    }

    /**
     * 获取发票详情（小二端）
     */
    @RequestMapping(value = "/h5/invoice/details/new", method = RequestMethod.POST)
    public BizResult<InvoiceDto> getInvoiceDetailsNew(@RequestBody InvoiceParam param) {
        InvoiceDto invoiceDetails = invoiceFacade.getInvoiceDetails(param);
        if (Objects.isNull(invoiceDetails)) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST);
        }
        if (param.getSubjectType() != null && !param.getSubjectType().equals(invoiceDetails.getSubjectType())) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_TYPE_MATCH_FAILED);
        }
        return BizResult.create(invoiceDetails);
    }

    /**
     * 发票列表（小二端）
     */
    @RequestMapping(value = "/h5/invoice/list", method = RequestMethod.GET)
    public BizResult<PageData<InvoiceVO>> pageInvoiceList(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceFilterTimeParam param) {
        Integer loginUserId = getUserId(servletRequest);
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(loginUserId);
        return invoiceService.pageInvoiceList(param, currentUserReq);
    }


    /**
     * 发票列表（小二端）
     */
    @RequestMapping(value = "/h5/invoice/list/new", method = RequestMethod.POST)
    public BizResult<PageData<InvoiceVO>> pageInvoiceListNew(HttpServletRequest servletRequest,
            @RequestBody InvoiceFilterTimeParam param) {
        Integer loginUserId = getUserId(servletRequest);
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(loginUserId);
        return invoiceService.pageInvoiceList(param, currentUserReq);
    }

    /**
     * 发票新增单张（小二端） 这个应该已经没用了
     */
    @RequestMapping(value = "/h5/add/singleInvoice", method = RequestMethod.POST)
    public BizResult<Boolean> addSingleInvoice(HttpServletRequest servletRequest,
            @MeidaRequestData AddSingleInvoiceParam param) {
        Integer loginUserId = getUserId(servletRequest);
        param.setLoginUserId(loginUserId);
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(loginUserId);
        param.setCurrentUserReq(currentUserReq);

        RLock lock = redisson.getLock("add_single_invoice" + loginUserId);
        boolean lockSuccess = false;
        try {
            //尝试加锁，最多等待0秒，上锁以后3秒自动解锁
            if (lockSuccess = lock.tryLock(0, 3, TimeUnit.SECONDS)) {
                invoiceFacade.addSingleInvoiceAndRecord(param);
            } else {
                throw BizException.create(ErrorCodeEnum.SYSTEM_BUSY, "发票正在提交，请稍后");
            }
        } catch (InterruptedException e) {
            logger.error(">>>initDeliveryDO InterruptedException {}", e);
        } finally {
            if (lockSuccess) {
                lock.unlock();
            }
        }
        return BizResult.create(true);
    }

    /**
     * 发票新增单张（小二端）
     */
    @RequestMapping(value = "/h5/add/singleInvoice/new", method = RequestMethod.POST)
    public BizResult<Boolean> addSingleInvoiceNew(HttpServletRequest servletRequest,
            @RequestBody AddSingleInvoiceParam param) {
        Integer loginUserId = getUserId(servletRequest);
        param.setLoginUserId(loginUserId);

        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(loginUserId);
        param.setCurrentUserReq(currentUserReq);

        RLock lock = redisson.getLock("add_single_invoice" + loginUserId);
        boolean lockSuccess = false;
        try {
            //尝试加锁，最多等待0秒，上锁以后3秒自动解锁
            if (lockSuccess = lock.tryLock(0, 3, TimeUnit.SECONDS)) {
                invoiceFacade.addSingleInvoiceAndRecord(param);
            } else {
                throw BizException.create(ErrorCodeEnum.SYSTEM_BUSY, "发票正在提交，请稍后");
            }
        } catch (InterruptedException e) {
            logger.error(">>>initDeliveryDO InterruptedException ", e);
        } finally {
            if (lockSuccess) {
                lock.unlock();
            }
        }
        return BizResult.create(true);
    }

    /**
     * 发票修改（小二端）
     */
    @RequestMapping(value = "/h5/update/singleInvoice", method = RequestMethod.POST)
    public BizResult<Boolean> updateInvoiceToApp(HttpServletRequest servletRequest,
            @MeidaRequestData InvoiceInfoParam param) {
        Integer loginUserId = getUserId(servletRequest);
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(loginUserId);
        param.setCurrentUserReq(currentUserReq);
        return invoiceFacade.updateInvoiceToApp(param, loginUserId);
    }

    /**
     * 发票修改（小二端）
     */
    @RequestMapping(value = "/h5/update/singleInvoice/new", method = RequestMethod.POST)
    public BizResult<Boolean> updateInvoiceToAppNew(HttpServletRequest servletRequest,
            @RequestBody InvoiceInfoParam param) {
        Integer loginUserId = getUserId(servletRequest);
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(loginUserId);
        param.setCurrentUserReq(currentUserReq);
        return invoiceFacade.updateInvoiceToApp(param, loginUserId);
    }


    @ApiOperation(value = "发票关联业务信息")
    @GetMapping(value = "/invoices/{invoiceId}/business/relation")
    public BizResult<List<InvoiceBusinessRelationDTO>> invoiceBusinessRelation(
            @PathVariable("invoiceId") Long invoiceId) {
        return invoiceFacade.invoiceBusinessRelation(invoiceId);
    }

    @ApiOperation(value = "发票回滚列表")
    @GetMapping(value = "/business/relation/deduct/list/{invoiceId}")
    public BizResult<List<InvoiceDeductDTO>> deductList(@PathVariable("invoiceId") Integer invoiceId) {
        List<InvoiceDeductDTO> invoiceDeductDTOS = invoiceFacade.deductList(invoiceId);
        return BizResult.create(invoiceDeductDTOS);
    }

    @ApiOperation(value = "发票操作记录")
    @GetMapping(value = "/invoices/{invoiceId}/operation")
    public BizResult<List<InvoiceOperateLogDTO>> invoiceOperationLog(@PathVariable("invoiceId") Long invoiceId) {
        return invoiceFacade.invoiceOperationLog(invoiceId);
    }

    /**
     * 更新小二端录入发票的所属城市code（小二端）
     */
    @RequestMapping(value = "/invoices/update/citycode", method = RequestMethod.POST)
    public BizResult<Boolean> updateCityCode() {
        return invoiceFacade.updateCityCode();
    }

    /**
     * 供应商列表
     *
     * @param servletRequest
     * @param supplierParam
     * @return
     */
    @RequestMapping(value = {"/crm/invoice/supplier/list"}, method = RequestMethod.POST)
    public String supplierList(HttpServletRequest servletRequest, @RequestBody SupplierParam supplierParam) {

        List<SupplierDTO> supplierDTOList = supplierService.listSupplierByNoList(
                supplierParam.getSupplierNoFilterList());

        return processResultFastjson(supplierDTOList);
    }

    @RequestMapping(value = "/invoices/getBuyerList",method = RequestMethod.GET)
    @ResponseBody
    public BizResult<List<BuyerDTO>> getBuyerList() {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        return BizResult.create(invoiceService.getBuyerList(BuyerParam.builder()
                .belongSubjectId(userReq.getBelongSubjectId().longValue())
                .belongSubjectType(userReq.getBelongSubjectType()).build()));
    }

}
