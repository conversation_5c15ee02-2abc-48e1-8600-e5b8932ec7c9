package so.dian.invoice.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.meidalife.common.exception.BizException;
import com.meidalife.common.exception.ValidateException;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.map.SingletonMap;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.annotation.MeidaRequestData;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.enums.LeoExcs;
import so.dian.invoice.facade.InvoiceValidateFacadeImpl;
import so.dian.invoice.handle.InvoiceOCRHandle;
import so.dian.invoice.pojo.bo.InvoiceIdentifyRecordBO;
import so.dian.invoice.pojo.dto.identify.InvoiceIdentifyRecordDTO;
import so.dian.invoice.pojo.dto.identify.ValidationDTO;
import so.dian.invoice.pojo.param.InvoiceImgUrlParam;
import so.dian.invoice.pojo.param.InvoiceUploadParams;
import so.dian.invoice.pojo.param.InvoiceValidateDetailParams;
import so.dian.invoice.pojo.param.InvoiceValidateParams;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.InvoiceIdentifyRecordVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.service.ImageService;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.service.InvoiceValidateService;
import so.dian.invoice.util.ImageUtils;
import so.dian.invoice.util.RequestUtils;
import so.dian.invoice.util.ServiceResult;
import so.dian.invoice.util.StringUtil;

import static so.dian.invoice.util.RequestUtils.getRemoteUser;

/**
 * 发票识别
 */
@RestController
public class InvoiceValidateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceValidateController.class);
    @Resource
    private InvoiceValidateService invoiceValidateService;
    @Resource
    private AgentEmployeeService employeeService;

    @Autowired
    private InvoiceValidateFacadeImpl invoiceValidateFacade;

    @Autowired
    private InvoiceService invoiceService;

    @Resource
    private InvoiceOCRHandle invoiceOCRHandle;

    @Value("${invoice.max-upload-file:200}")
    private int maxUploadFileSize;


    /**
     * 批量上传发票图片
     *
     * @param params
     * @return
     */
    @RequestMapping(value = {"/v2/validate/upload"}, method = RequestMethod.POST)
    public String batchUpload(@RequestBody InvoiceUploadParams params) {
        try {
            Long loginUserId = getUserId();
            if (CollectionUtils.isEmpty(params.getImageUrls())) {
                return processErrorResult(100, "上传的图片数量不能少于1张");
            }
            logger.info("批量上传发票图片,上传图片数量：{},限制数量：{}", params.getImageUrls().size(), maxUploadFileSize);
            if(params.getImageUrls().size() > maxUploadFileSize){
                return processErrorResult(LeoExcs.IMAGE_SIZE_TOO_MUCH.getCode(), LeoExcs.IMAGE_SIZE_TOO_MUCH.getMessage() + "，"+ maxUploadFileSize +"张比较合适");
            }

            //  logger.error("发票文件大小:"+bytes.length/1024+"KB");
            ServiceResult<List<Pair<String,String>>> result = invoiceValidateService.batchUpload(loginUserId.intValue(), params);
            if (!result.isSuccess()) {
                logger.error("发票上传错误信息：" + result.getMsg());
                return processErrorResult(100, result.getMsg());
            }
            // 异步队列执行
            invoiceOCRHandle.ocrThreadTask();
            return processResultFastjson(result.getData());
        } catch (Exception e) {
            logger.error("批量上传发票异常", e);
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }
    }


    /**
     * 批量上传发票图片
     *
     * @param request
     * @param file
     * @return
     */
    @RequestMapping(value = {"/validate/upload"}, method = RequestMethod.POST)
    public String batchUpload(HttpServletRequest request, HttpServletResponse response,
                              @RequestParam MultipartFile file) {
        try {
            response.addHeader("Access-Control-Allow-Headers",
                    "Origin, No-Cache, X-Requested-With, If-Modified-Since, Pragma, Last-Modified, Cache-Control, Expires, Content-Type, X-E4M-With");
            String clientFileName = file.getOriginalFilename();
            if (!ImageUtils.checkSuffixIsLegal(clientFileName)) {
                throw new ValidateException(LeoExcs.IMAGES_URL_INVALID);
            }
            String fileName = clientFileName.trim().toLowerCase();
            String contentType;
            if (fileName.endsWith("gif")) {
                contentType = "gif";
            } else if (fileName.endsWith("pdf")) {
                contentType = "pdf";
            } else {
                contentType = "jpg";
            }
            Integer loginUserId = getUserId(request);
            byte[] bytes = file.getBytes();
            //  logger.error("发票文件大小:"+bytes.length/1024+"KB");
            ServiceResult<String> result = invoiceValidateService
                    .batchUpload(loginUserId, clientFileName, bytes, contentType, bytes.length);
            if (!result.isSuccess()) {
                logger.error("发票上传错误信息：" + result.getMsg());
                return processErrorResult(100, result.getMsg());
            }
            // 异步队列执行
            invoiceOCRHandle.ocrThreadTask();
            return processResultFastjson(result.getData());
        } catch (Exception e) {
            logger.error("批量上传发票异常", e);
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }
    }

    /**
     * 批量上传发票图片
     *
     * @param request
     * @param file
     * @return
     */
    @RequestMapping(value = {"/validate/autoUpload"}, method = RequestMethod.POST)
    public String batchUpload2(HttpServletRequest request, HttpServletResponse response,
                               @RequestParam MultipartFile file) {
        try {

            String clientFileName = file.getOriginalFilename();
            if (!ImageUtils.checkSuffixIsLegal(clientFileName)) {
                throw new ValidateException(LeoExcs.IMAGES_URL_INVALID);
            }
            String fileName = clientFileName.trim().toLowerCase();
            String contentType = fileName.endsWith("gif") ? "gif" : "jpg";
            //操作人
            Integer loginUserId = getOperator(request);
            byte[] bytes = file.getBytes();
            ServiceResult<String> result = invoiceValidateService
                    .batchUpload(loginUserId, clientFileName, bytes, contentType, bytes.length);
            if (!result.isSuccess()) {
                logger.error("发票识别错误信息：" + result.getMsg());
                return processErrorResult(100, result.getMsg());
            }
            return processResultFastjson(result.getData());
        } catch (Exception e) {
            logger.error("批量上传发票异常", e);
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }
    }

    private Integer getOperator(HttpServletRequest request) {
        String nickName = request.getParameter("nickName");
        if (StringUtil.isNotBlank(nickName)) {
            nickName = nickName.trim();
        }
        AgentEmployeeDTO agentEmployeeDTO = employeeService.getByNickName(nickName);
        return agentEmployeeDTO == null ? 0 : agentEmployeeDTO.getId();
    }

    /**
     * 发票识别记录列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = {"/validate/list"})
    public String list(HttpServletRequest request, @MeidaRequestData InvoiceValidateParams params) {

        //城市行政只能查看自己
        if (UserRoleEnum.CITY_ADMINISTRATION.equals(getUserRole()) || UserRoleEnum.JOINTAFFAIRSMANAGER.equals(getUserRole())
                || UserRoleEnum.WAREHOUSE_KEEPER.equals(getUserRole())) {
            params.setCreator(getUserId());
        }

        ServiceResult<JSONObject> result = invoiceValidateService.list(params);

        if (!result.isSuccess()) {
            return processErrorResult(0, result.getMsg());
        }
        return processResultFastjson(result.getData());

    }

    /**
     * 删除发票识别记录
     *
     * @param params
     * @return
     */
    @RequestMapping(value = {"/validate/delete"})
    public String delete(HttpServletRequest request, @MeidaRequestData InvoiceValidateParams params) {
        if (params == null || params.getId() == null) {
            return processErrorResult(0, "参数为空");
        }
        ServiceResult<String> result = invoiceValidateService.delete(params);

        if (!result.isSuccess()) {
            return processErrorResult(0, result.getMsg());
        }
        return processResultFastjson(result.getData());

    }

    @RequestMapping(value = {"/validate/batchDelete"})
    public String batchDelete(HttpServletRequest request, @MeidaRequestData InvoiceValidateParams params) {
        if (params == null || CollectionUtils.isEmpty(params.getIds())) {
            return processErrorResult(0, "参数为空");
        }
        Integer loginUserId = getUserId(request);
        if (Objects.isNull(loginUserId)) {
            return processErrorResult(0, "需要登录！");
        }
        params.setLoginUserId(loginUserId);
        ServiceResult<String> result = invoiceValidateService.batchDelete(params);

        if (!result.isSuccess()) {
            return processErrorResult(0, result.getMsg());
        }
        return processResultFastjson(result.getData());

    }


    /**
     * 编辑发票识别记录
     *
     * @param params
     * @return
     */
    @RequestMapping(value = {"/validate/update"})
    public String update(HttpServletRequest request, @MeidaRequestData InvoiceValidateDetailParams params) {
        if (params == null || params.getId() == null) {
            return processErrorResult(0, "参数为空");
        }
        CurrentUserReq currentUserReq = getRemoteUser(request);
        params.setCurrentUserReq(currentUserReq);
        ServiceResult<String> result = invoiceValidateService.update(params);

        if (!result.isSuccess()) {
            return processErrorResult(0, result.getMsg());
        }
        return processResultFastjson(result.getData());

    }

    /**
     * 获取发票识别信息（商家端）(微服务)
     *
     * @param imageUrl
     * @return
     */
    @RequestMapping(value = {"/getInvoiceIdentifyDetail"}, method = RequestMethod.GET)
    public BizResult<InvoiceIdentifyRecordDTO> getInvoiceIdentifyDetail(@RequestParam("imageUrl") String imageUrl) {
        return invoiceValidateService.getInvoiceIdentifyDetail(imageUrl);
    }


    /**
     * 供应链发票验证信息(微服务)
     *
     * @param params
     * @return
     */
    @RequestMapping(value = {"/getInvoiceValidateDetail"}, method = RequestMethod.POST)
    public BizResult<ValidationDTO> getInvoiceIdentifyDetail(@RequestBody InvoiceValidateDetailParams params) {
        ValidationDTO validationDTO = null;
        try {
            validationDTO = invoiceValidateService.handleInvoiceValidate(params);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return BizResult.create(validationDTO);
    }

    /**
     * 生成发票批次号(微服务)
     *
     * @return
     */
    @RequestMapping(value = {"/getBatchNo"}, method = RequestMethod.GET)
    public BizResult<String> getBatchNo2() {
        ServiceResult<String> result = invoiceValidateService.getBatchNo();
        if (!result.isSuccess()) {
            return BizResult.error(ErrorCodeEnum.FALLBACK);
        }
        return BizResult.create(result.getData());
    }

    /**
     * 生成发票批次号
     *
     * @return
     */
    @RequestMapping(value = {"/invoiceManage/getBatchNo"})
    public String getBatchNo(HttpServletRequest request) {
        ServiceResult<String> result = invoiceValidateService.getBatchNo();
        if (!result.isSuccess()) {
            return processErrorResult(0, result.getMsg());
        }
        return processResultFastjson(result.getData());
    }

    /**
     * 发票验证(批量)
     *
     * @return
     */
    @RequestMapping(value = {"/invoiceManage/batchValidate"})
    public String batchValidate(HttpServletRequest request, @MeidaRequestData InvoiceValidateParams params) {
        if (params == null || CollectionUtils.isEmpty(params.getList())) {
            return processErrorResult(0, "参数为空");
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                ServiceResult<String> result = invoiceValidateService.batchValidate(params);
            }
        }).start();
        return processResultFastjson("调用成功，执行为异步执行，请耐心等耐");
    }

    @Autowired
    private AgentEmployeeService agentEmployeeService;
    /**
     * 发票验证(单条)
     *
     * @return
     */
    @RequestMapping(value = {"/validate/validate"})
    public String validate(HttpServletRequest request, @MeidaRequestData InvoiceValidateDetailParams params) {
        if (params == null || params.getId() == null) {
            return processErrorResult(0, "参数为空");
        }
        Integer userId = getUserId(request);
        CurrentUserReq currentUserReq = agentEmployeeService.getRemoteUser(userId);
        params.setCurrentUserReq(currentUserReq);

        ServiceResult<String> result = invoiceValidateService.validate(params);
        if (!result.isSuccess()) {
            return processErrorResult(0, result.getMsg());
        }
        return processResultFastjson(result.getData());
    }

    /**
     * 获取发票识别信息（小二端上传发票）(微服务)
     *
     * @param param
     * @return
     */
    @RequestMapping(value = {"/h5/invoice/identify/record"}, method = RequestMethod.GET)
    public BizResult<InvoiceIdentifyRecordVO> getInvoiceIdentifyRecord(HttpServletRequest request,
                                                                       @MeidaRequestData InvoiceImgUrlParam param) {
        Integer userId = getUserId(request);
        return invoiceValidateFacade.getInvoiceIdentifyDetail(param, userId);
    }

    /**
     * 获取发票识别信息（小二端上传发票）(微服务)
     *
     * @param param
     * @return
     */
    @RequestMapping(value = {"/h5/invoice/identify/record/new"}, method = RequestMethod.POST)
    public BizResult<InvoiceIdentifyRecordVO> getInvoiceIdentifyRecordNew(HttpServletRequest request,
                                                                          @RequestBody InvoiceImgUrlParam param) {
        Integer userId = getUserId(request);
        return invoiceValidateFacade.getInvoiceIdentifyDetail(param, userId);
    }

    /**
     * 获取小二端发票识别缓存（内部接口）
     *
     * @param request
     * @return
     */
    @RequestMapping(value = {"/invoice/identify/cache"}, method = RequestMethod.GET)
    public BizResult<InvoiceIdentifyRecordBO> getInvoiceIdentifyCache(HttpServletRequest request) {
        return invoiceValidateFacade.getInvoiceIdentifyCache(getUserId(request));
    }

    @ApiOperation("验真结果列表")
    @GetMapping(value = "/invoice/validateCode/list")
    public BizResult<List<Map<String, String>>> validateCodeList() {
        InvoiceIdentifyRecordEnum.ValidationCodeEnum[] values = InvoiceIdentifyRecordEnum.ValidationCodeEnum.values();
        List<Map<String, String>> collect = Arrays.stream(values).map(value -> ImmutableMap.of("key",
                value.getCode(), "value", value.getDesc()))
                .collect(Collectors.toList());
        return BizResult.create(collect);
    }

    @ApiOperation(value = "发票验真历史数据处理")
    @PostMapping(value = "/invoice/processValidateData")
    public BizResult<Boolean> processValidateData(@RequestParam("startTimeStr") String startTimeStr,
                                                  @RequestParam("endTimeStr") String endTimeStr) {

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startTime = sdf.parse(startTimeStr);
            Date endTime = sdf.parse(endTimeStr);
            invoiceService.processValidateData(startTime, endTime);
            return BizResult.create(true);
        } catch (ParseException e) {
            e.printStackTrace();
            return BizResult.create(false);
        }
    }

//    /**
//     *
//     * 商家端OCR识别入口，商家端需要换成这个入口，现在走的是Leo的/scm/invoice/autoValidate
//     *
//     * @param request
//     * @param file
//     * @param settleSubjectId
//     * @param settleSubjectType
//     * @return
//     */
//    @RequestMapping(value = {"/shop/invoice/autoValidate"})
//    public String getInvoiceDetail(HttpServletRequest request, @RequestParam MultipartFile file,
//                                   @RequestParam Integer settleSubjectId, @RequestParam Integer settleSubjectType) {
//        try {
//
//            String clientFileName = file.getOriginalFilename();
//            if (!ImageUtils.checkSuffixIsLegal(clientFileName)) {
//                throw new ValidateException(LeoExcs.IMAGES_URL_INVALID);
//            }
//
//            String savedUri;
//            String fileName = clientFileName.trim().toLowerCase();
//            if (fileName.endsWith("gif")) {
//                savedUri = imageService.uploadImageQCloud(ImageService.LHC, clientFileName, file.getBytes(), "gif", file.getBytes().length);
//            } else {
//                savedUri = imageService.uploadImageQCloud(ImageService.LHC, clientFileName, file.getBytes(), "jpg", file.getBytes().length);
//            }
//
//            String imageUrl = ImageUtils.IMAGE_DOMAIN_WITH_PROTOCOL_QCLOUD + savedUri;
//            so.dian.commons.eden.entity.BizResult<InvoiceIdentifyRecordDTO> result = invoiceValidateService.getInvoiceIdentifyDetail(imageUrl);
//            if (!result.isSuccess() || null == result.getData()) {
//                return processErrorResult(0, result.getMsg());
//            }
//
//            InvoiceIdentifyRecordDTO invoiceIdentifyRecordDTO = result.getData();
//            InvoiceIdentifyRecordShopVO invoiceIdentifyRecordShopVO = new InvoiceIdentifyRecordShopVO();
//            BeanUtils.copyProperties(invoiceIdentifyRecordDTO, invoiceIdentifyRecordShopVO);
//            invoiceIdentifyRecordShopVO.setInvoiceImg(imageUrl);
//
//            // 当提现的结算方为伊电园时，发票购买方必须为伊电园
//            if (SettleSubjectTypeEnum.小电.getCode().equals(settleSubjectType) && Integer.compare(1, settleSubjectId) == 0) {
//                if (!"北京伊电园网络科技有限公司".equals(invoiceIdentifyRecordShopVO.getBuyer())
//                        || !InvoiceConstants.DIAN_INVOICE_BUYER_TAX_ID.equals(invoiceIdentifyRecordShopVO.getBuyerTaxId())) {
//                    return processErrorResult(0, "请重新上传发票，抬头：北京伊电园网络科技有限公司，税号：" + InvoiceConstants.DIAN_INVOICE_BUYER_TAX_ID);
//                }
//            }
//            // 当提现的结算方为友电时，发票购买方必须为友电
//            if (SettleSubjectTypeEnum.小电.getCode().equals(settleSubjectType) && Integer.compare(0, settleSubjectId) == 0) {
//                if (!"杭州友电科技有限公司".equals(invoiceIdentifyRecordShopVO.getBuyer())
//                        || !InvoiceConstants.YOUDIAN_INVOICE_BUYER_TAX_ID.equals(invoiceIdentifyRecordShopVO.getBuyerTaxId())) {
//                    return processErrorResult(0, "请重新上传发票，抬头：杭州友电科技有限公司，税号：" + InvoiceConstants.YOUDIAN_INVOICE_BUYER_TAX_ID);
//                }
//            }
//
//            return processResult(invoiceIdentifyRecordShopVO);
//        } catch (Exception e) {
//            logger.error("商家端识别发票异常。fileSize = {}", file.getSize(), e);
//            return processErrorResult(0, "商家端识别发票异常");
//        }
//
//    }
}
