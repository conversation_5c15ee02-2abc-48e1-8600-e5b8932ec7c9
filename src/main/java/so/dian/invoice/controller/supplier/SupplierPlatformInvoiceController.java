package so.dian.invoice.controller.supplier;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceAttachmentAddParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceAttachmentDeleteParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceDeleteParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceDetailParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceImportParam;
import so.dian.invoice.pojo.param.supplier.SupplierPlatformInvoiceParam;
import so.dian.invoice.pojo.vo.supplier.SupplierPlatformInvoiceDetailPageVO;
import so.dian.invoice.pojo.vo.supplier.SupplierPlatformInvoicePageVO;
import so.dian.invoice.service.adaptor.SupplierPlatformInvoiceAdaptor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: miaoshuai
 * @create: 2024/01/18 14:56
 * @description: 供应商协同平台专用
 */
@Api(value = "SupplierPlatformInvoiceController", description = "供应商平台发票API", tags = "SupplierPlatformInvoiceApi")
@RestController
public class SupplierPlatformInvoiceController {

    @Autowired
    private SupplierPlatformInvoiceAdaptor invoiceAdaptor;

    @ApiOperation("供应商平台发票主体分页列表")
    @PostMapping(value = "supplier/supplierInvoice/page")
    public BizResult<PageData<SupplierPlatformInvoicePageVO>> supplierInvoicePage(@Validated @RequestBody SupplierPlatformInvoiceParam param) {
        PageData<SupplierPlatformInvoicePageVO> supplierInvoicePage = invoiceAdaptor.supplierInvoicePage(param);
        return BizResult.create(supplierInvoicePage);
    }

    @ApiOperation("供应商平台发票详情列表")
    @PostMapping(value = "supplier/supplierInvoice/detail/list")
    public BizResult<List<SupplierPlatformInvoiceDetailPageVO>> supplierInvoiceDetailList(@Validated @RequestBody SupplierPlatformInvoiceDetailParam param) {
        List<SupplierPlatformInvoiceDetailPageVO> detailPageVOS = invoiceAdaptor.supplierInvoiceDetailList(param);
        return BizResult.create(detailPageVOS);
    }

    @ApiOperation("供应商平台发票明细Excel导出")
    @PostMapping(value = "supplier/supplierInvoice/detail/excelExport")
    public BizResult<String> detailExcelExport(@Validated @RequestBody SupplierPlatformInvoiceParam param) {
        Long count = invoiceAdaptor.detailExcelExport(param);
        return BizResult.create(String.format("供应商发票明细单据号导出成功，共导出%d条记录", count));
    }

    @ApiOperation("供应商平台发票主体删除")
    @PostMapping(value = "supplier/supplierInvoice/delete")
    public BizResult<String> deleteInvoice(@Validated @RequestBody SupplierPlatformInvoiceDeleteParam param) {
        invoiceAdaptor.deleteInvoice(param);
        return BizResult.create("供应商发票主体删除成功");
    }

    @ApiOperation("供应商平台发票图片删除")
    @PostMapping(value = "supplier/supplierInvoice/attachment/delete")
    public BizResult<String> deleteAttachment(@Valid @RequestBody SupplierPlatformInvoiceAttachmentDeleteParam param) {
        invoiceAdaptor.deleteAttachment(param);
        return BizResult.create("供应商发票图片删除成功");
    }

    @ApiOperation("供应商平台发票图片新增")
    @PostMapping(value = "supplier/supplierInvoice/attachment/add")
    public BizResult<String> addAttachment(@Validated @RequestBody SupplierPlatformInvoiceAttachmentAddParam param) {
        invoiceAdaptor.addAttachment(param);
        return BizResult.create("供应商发票图片新增成功");
    }

    @ApiOperation("供应商平台发票excel导入模板下载")
    @GetMapping(value = "supplier/supplierInvoice/excelTemplate")
    public BizResult<String> excelTemplate(HttpServletResponse response) {
        invoiceAdaptor.excelTemplate(response);
        return BizResult.create("供应商发票excel导入模板下载成功");
    }

    @ApiOperation("供应商平台发票excel导入")
    @PostMapping(value = "supplier/supplierInvoice/excelImport")
    public BizResult<String> excelImport(@RequestBody List<SupplierPlatformInvoiceImportParam> paramList) {
        String tips = invoiceAdaptor.excelImport(paramList);
        return BizResult.create(tips);
    }

}
