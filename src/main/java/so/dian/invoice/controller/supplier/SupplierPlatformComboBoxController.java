package so.dian.invoice.controller.supplier;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.NameValueDTO;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.invoice.controller.BaseController;
import so.dian.invoice.enums.BuyerTaxIdEnum;
import so.dian.invoice.enums.SupplierInvoiceStatusEnum;
import so.dian.invoice.enums.SupplierInvoiceTypeEnum;

import java.util.ArrayList;
import java.util.List;


/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:06 PM
 * @Description:
 */
@Api(value = "SupplierPlatformComboBoxController", description = "供应商平台下拉框API", tags = "ComboBoxApi")
@Slf4j
@RestController
public class SupplierPlatformComboBoxController extends BaseController {

    @ApiOperation("供应商平台发票-发票类型")
    @GetMapping(value = "supplier/comboBox/supplierInvoice/type")
    public BizResult<List<NameValueDTO>> supplierInvoiceType() {
        return BizResult.create(LocalEnumUtils.getEnumCodeAndDesc(SupplierInvoiceTypeEnum.class));
    }

    @ApiOperation("供应商平台发票-发票状态")
    @GetMapping(value = "supplier/comboBox/supplierInvoice/status")
    public BizResult<List<NameValueDTO>> supplierInvoiceStatus() {
        return BizResult.create(LocalEnumUtils.getEnumCodeAndDesc(SupplierInvoiceStatusEnum.class));
    }

    @ApiOperation("供应商平台发票-所属公司")
    @GetMapping(value = "supplier/comboBox/supplierInvoice/buyer")
    public BizResult<List<NameValueDTO>> supplierInvoiceBuyer() {
        List<NameValueDTO> nameValueDTOList = new ArrayList<>();
        BuyerTaxIdEnum[] buyerTaxIdEnums = BuyerTaxIdEnum.values();
        for (BuyerTaxIdEnum buyerTaxIdEnum : buyerTaxIdEnums) {
            nameValueDTOList.add(new NameValueDTO(buyerTaxIdEnum.getBuyer(),buyerTaxIdEnum.getBuyer()));
        }
        return BizResult.create(nameValueDTOList);
    }
}
