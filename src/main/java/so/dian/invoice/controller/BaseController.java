package so.dian.invoice.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meidalife.common.exception.BizException;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import so.dian.center.common.entity.UserSessionDO;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.invoice.enums.LeoExcs;
import so.dian.invoice.properties.InvoiceProperties;
import so.dian.invoice.util.SessionUtil;
import so.dian.invoice.util.StringUtil;
import so.dian.zuul.common.util.CookieUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

public class BaseController {

    @Resource
    private SessionUtil sessionUtil;

    protected Gson gson = new Gson();

    public Integer getUserId(HttpServletRequest request){

        String userIdStr = CookieUtils.getCookieValue(request, "userId");
        if(StringUtils.isBlank(userIdStr)){

            userIdStr = CookieUtils.getCookieValue(request,"z_userId");
            if (StringUtils.isBlank(userIdStr) && !InvoiceProperties.isReal()) {
                userIdStr = request.getParameter("userId");
            }

            if(StringUtil.isBlank(userIdStr)){
                throw new BizException(LeoExcs.NEED_LOGIN);
            }
        }

        return Integer.valueOf(userIdStr);
    }

    /**
     * 获取代理商id
     */
    public Integer getUserAgentId(HttpServletRequest request) {
        String userAgentId = CookieUtils.getCookieValue(request, "userAgentId");
        return userAgentId == null ? null : Integer.parseInt(userAgentId);
    }
    protected UserRoleEnum getUserRole(HttpServletRequest request) {

        UserSessionDO userSessionDO = sessionUtil.getSessionDO(request);

        Integer role = userSessionDO == null ? null : userSessionDO.getRole();
        if (role == null && InvoiceProperties.isDaily()) {
            String roleStr = request.getParameter("role");
            if (StringUtil.isNotBlank(roleStr)) {
                role = Integer.parseInt(roleStr);
            }
        }

        if (role == null) {
            return null;
        }

        UserRoleEnum userRoleEnum = UserRoleEnum.get(role);

        return userRoleEnum;
    }

    protected String getNickName() {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String nickName = CookieUtils.getCookieValue(request, "nickName");
        return nickName;
    }

    public String getNickName(HttpServletRequest request){
        String nickName = sessionUtil.getNickName(request);
        if (nickName == null){
            throw new BizException(LeoExcs.NEED_LOGIN);
        }
        return nickName;
    }

    protected UserRoleEnum getUserRoleByCookie() {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String roleName = CookieUtils.getCookieValue(request, "current_role");
        UserRoleEnum role = UserRoleEnum.parse(roleName);
        return role;
    }

    public Long getUserId(){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String userIdStr = CookieUtils.getCookieValue(request, "userId");
        if(StringUtils.isBlank(userIdStr)){

            userIdStr = CookieUtils.getCookieValue(request,"z_userId");
            if (StringUtils.isBlank(userIdStr) && !InvoiceProperties.isReal()) {
                userIdStr = request.getParameter("userId");
            }

            if(StringUtil.isBlank(userIdStr)){
                throw new BizException(LeoExcs.NEED_LOGIN);
            }
        }

        return Long.valueOf(userIdStr);
    }

    protected UserRoleEnum getUserRole() {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        UserSessionDO userSessionDO = sessionUtil.getSessionDO(request);

        Integer role = userSessionDO == null ? null : userSessionDO.getRole();
        if (role == null && InvoiceProperties.isDaily()) {
            String roleStr = request.getParameter("role");
            if (StringUtil.isNotBlank(roleStr)) {
                role = Integer.parseInt(roleStr);
            }
        }

        if (role == null) {
            return null;
        }

        UserRoleEnum userRoleEnum = UserRoleEnum.get(role);

        return userRoleEnum;
    }




    public String processResult(Object object) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("success", true);
        if (object == null) {
            object = new Object();
        }
        JsonElement data = new JsonParser().parse(gson.toJson(object));
        jsonObject.add("data", data);
        return jsonObject.toString();
    }


    public String processErrorResult(int errCode, String errMsg) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("success", false);
        jsonObject.addProperty("code", errCode);
        jsonObject.addProperty("msg", errMsg);
        return jsonObject.toString();
    }

    public String processResultFastjson(Object object) {
        JSONObject json = new JSONObject();
        json.put("success", true);
        json.put("data", object);
        return JSON.toJSONString(json, SerializerFeature.DisableCircularReferenceDetect);
    }

}
