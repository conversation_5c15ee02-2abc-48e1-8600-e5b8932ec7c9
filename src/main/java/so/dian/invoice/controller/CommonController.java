package so.dian.invoice.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.pojo.vo.BelongSubjectVO;
import so.dian.invoice.service.CommonService;
import so.dian.invoice.util.RequestUtils;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/10/28 16:16
 * @description:
 */
@RestController
public class CommonController {

    @Autowired
    private CommonService commonService;

    @RequestMapping("common/current/belong/subject")
    public BizResult<BelongSubjectVO> belongSubject() {
        return BizResult.create(commonService.get(RequestUtils.getRemoteUser()));
    }
}
