package so.dian.invoice.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.invoice.annotation.MeidaRequestData;
import so.dian.invoice.job.CheckInvoiceJob;
import so.dian.invoice.pojo.param.CheckInvoiceParam;
import so.dian.invoice.pojo.param.ConfigParam;
import so.dian.invoice.pojo.param.DeletedInvoiceCheckerParam;
import so.dian.invoice.pojo.param.InvoiceCheckConclusionParam;
import so.dian.invoice.pojo.param.InvoiceCheckDetailsParam;
import so.dian.invoice.pojo.param.InvoiceCheckPageParam;
import so.dian.invoice.pojo.param.InvoiceCheckRateParam;
import so.dian.invoice.pojo.param.InvoiceCheckerParam;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;
import so.dian.invoice.pojo.vo.*;
import so.dian.invoice.service.impl.CheckInvoiceServiceImpl;
import so.dian.invoice.util.PageData;
import so.dian.invoice.util.biz.AssertUtils;

import static so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:06 PM
 * @Description:
 */
@Api(value = "InvoiceCheckController", description = "发票质检API", tags = "invoiceCheckApi")
@Slf4j
@RestController
public class CheckInvoiceController extends BaseController {

	@Autowired
	private CheckInvoiceServiceImpl checkInvoiceService;

	@Resource
	private RedissonClient redisson;

	@ApiOperation("发票质检列表")
	@PostMapping(value = "/check/list")
	public BizResult<PageData<CheckInvoiceVO>> listInvoiceCheck(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckPageParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.listInvoiceCheck(param);
	}

	@ApiOperation("获得当前登录人负责的大区列表")
	@ResponseBody
	@RequestMapping(value = "/check/regions", method = RequestMethod.GET)
	public BizResult<List<String>> getRegions(HttpServletRequest servletRequest) {
		Long userId = getUserId(servletRequest).longValue();
		return checkInvoiceService.getRegions(userId);
	}

	@ApiOperation("发票质检")
	@PostMapping(value = "/check")
	public BizResult<Boolean> checkInvoice(HttpServletRequest servletRequest,
			 @MeidaRequestData CheckInvoiceParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		RLock lock = redisson.getLock("check_invoice" + param.getEmployeeId());
		boolean lockSuccess = false;
		try {
			if (lockSuccess = lock.tryLock(0, 3, TimeUnit.SECONDS)) {
				checkInvoiceService.checkInvoice(param);
			} else {
				throw BizException.create(ErrorCodeEnum.SYSTEM_BUSY, "操作过于频繁");
			}
		} catch (InterruptedException e) {
			log.error(">>> checkInvoice:{},exception:{}", param, e);
		} finally {
			if (lockSuccess) {
				lock.unlock();
			}
		}
		return BizResult.create(true);
	}

	@ApiOperation("发票质检详情")
	@GetMapping(value = "/check/details")
	public BizResult<InvoiceCheckDetailsVO> invoiceCheckDetails(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckDetailsParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.invoiceCheckDetails(param);
	}

	@ApiOperation("发票质检比例配置")
	@PostMapping(value = "/check/rate/config")
	public BizResult<Boolean> invoiceCheckRateConfig(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckRateParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.invoiceCheckRateConfig(param);
	}

	@ApiOperation("获取发票质检比例")
	@GetMapping(value = "/check/rate")
	public BizResult<Integer> getCheckRate() {
		BaseEmpRoleReq req = new BaseEmpRoleReq();
		req.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.getCheckRate(req);
	}

	@ApiOperation("发票质检员工大区配置")
	@PostMapping(value = "/check/user/config")
	public BizResult<Boolean> addInvoiceCheckerRegionConfig(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckerParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.invoiceCheckerConfig(param);
	}

	@ApiOperation("修改质检员工大区配置")
	@PostMapping(value = "/check/checkerConfig/modify")
	public BizResult<Boolean> modifyInvoiceCheckerRegionConfig(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckerParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.modifyInvoiceCheckerRegionConfig(param);
	}

	@ApiOperation("删除质检员工大区配置")
	@PostMapping(value = "/check/checkerConfig/deleted")
	public BizResult<Boolean> deletedInvoiceCheckerRegionConfig(HttpServletRequest servletRequest,
			@MeidaRequestData DeletedInvoiceCheckerParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.deletedInvoiceCheckerRegionConfig(param);
	}

	@ApiOperation("发票质检结论配置")
	@PostMapping(value = "/check/conclusion/config")
	public BizResult<Boolean> invoiceCheckConclusionConfig(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckConclusionParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.invoiceCheckConclusionConfig(param);
	}

	@ApiOperation("删除发票质检结论")
	@PostMapping(value = "/check/conclusion/deleted")
	public BizResult<Boolean> deletedInvoiceCheckConclusion(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckConclusionParam param) {
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.deletedInvoiceCheckConclusion(param);
	}

	@ApiOperation("发票质检结论列表")
	@GetMapping(value = "/check/conclusion/list")
	public BizResult<List<CheckInvoiceConclusionVO>> listCheckInvoiceConclusion() {
		BaseEmpRoleReq req = new BaseEmpRoleReq();
		req.setRole(getUserRoleByCookie().getRoleName());
		return checkInvoiceService.listCheckInvoiceConclusion(req);
	}

	@ApiOperation("发票质检员工大区配置列表")
	@GetMapping(value = "/check/checker/list")
	public BizResult<List<CheckerDetailsVO>> listCheckerRegionRelation(HttpServletRequest servletRequest) {
		BaseEmpRoleReq req = new BaseEmpRoleReq();
		req.setRole(getUserRoleByCookie().getRoleName());
		req.setEmployeeId(getUserId(servletRequest).longValue());
		return checkInvoiceService.getInvoiceCheckConfigDetails(req);
	}

	@ApiOperation("初始化质检配置（内部接口）")
	@PostMapping(value = "/check/config/init")
	public BizResult<Boolean> initConfig(@RequestBody ConfigParam param) {
		return checkInvoiceService.initConfig(param);
	}

	@ApiOperation("发票质检列表导出")
	@PostMapping(value = "/check/list/export")
	public BizResult<Boolean> exportCheckInvoiceList(HttpServletRequest servletRequest,
			@MeidaRequestData InvoiceCheckPageParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
		if (Objects.isNull(param.getStatus())) {
			throw BizException.create(PARAM_RESOLVER_FAIL);
		}
		param.setEmployeeId(getUserId(servletRequest).longValue());
		param.setRole(getUserRoleByCookie().getRoleName());
		RLock lock = redisson.getLock("export_invoice_list" + param.getEmployeeId() + param.getStatus());
		boolean lockSuccess = false;
		try {
			if (lockSuccess = lock.tryLock(0, 3, TimeUnit.SECONDS)) {
				checkInvoiceService.exportCheckInvoiceList(param);
			} else {
				throw BizException.create(ErrorCodeEnum.SYSTEM_BUSY, "质检发票正在导出，请稍后");
			}
		} catch (InterruptedException e) {
			log.error(">>> exportCheckInvoiceList:{},exception:{}", param, e);
		} finally {
			if (lockSuccess) {
				lock.unlock();
			}
		}
		return BizResult.create(true);
	}

	/**
	 * 内部接口
	 * @return
	 */
	@ApiOperation("发票质检任务追溯")
	@PostMapping(value = "/check/invoice/back/track")
	public BizResult<Boolean> backTrackCheckInvoice(@RequestParam("endTime") String endTime) {
		DateTime endDateTime = DateUtil.parseDateTime(endTime);
		checkInvoiceService.backTrackCheckInvoice(endDateTime);
		return BizResult.create(true);
	}

}
