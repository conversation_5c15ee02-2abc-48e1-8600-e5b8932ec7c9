package so.dian.invoice.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.api.ChannelInvoiceApi;
import so.dian.invoice.pojo.dtos.ChannelInvoiceDTO;
import so.dian.invoice.pojo.param.AddChannelInvoiceParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.ChannelInvoiceVO;
import so.dian.invoice.service.ChannelInvoiceService;
import so.dian.invoice.util.RequestUtils;

import javax.validation.Valid;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2022/10/28 14:06
 * @description:
 */
@RestController
public class ChannelInvoiceController implements ChannelInvoiceApi {

    @Autowired
    private ChannelInvoiceService invoiceService;

    @PostMapping("channel/invoice/add")
    public BizResult<Boolean> add(@RequestBody @Valid AddChannelInvoiceParam param) {
        invoiceService.add(param, RequestUtils.getRemoteUser());
        return BizResult.create(Boolean.TRUE);
    }

    @GetMapping("channel/invoice/find")
    public BizResult<ChannelInvoiceVO> find() {
        CurrentUserReq userReq = RequestUtils.getRemoteUser();
        return BizResult.create(invoiceService.find(userReq.getBelongSubjectId().longValue()));
    }

    @Override
    public BizResult<ChannelInvoiceDTO> get(Long belongSubjectId, Integer belongSubjectType) {
        return BizResult.create(invoiceService.get(belongSubjectId));
    }
}
