package so.dian.invoice.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.util.OssUtil;

/**
 * @Author: chenan
 * @Date: 2020/11/11 11:41
 */
@Api(value = "FileApi", description = "文件API")
@RestController
@Slf4j
public class FileController {

    @ApiOperation("统一文件上传地址")
    @PostMapping(value = "/file/upload")
    public BizResult<String> upload(@RequestParam("file") MultipartFile file) {
        try {
            String url = OssUtil.uploadMultipartFile(file);
            return BizResult.create(url);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw BizException.create(InvoiceCommentErrorCodeEnum.FILE_UPLOAD_ERROR);
        }
    }
}
