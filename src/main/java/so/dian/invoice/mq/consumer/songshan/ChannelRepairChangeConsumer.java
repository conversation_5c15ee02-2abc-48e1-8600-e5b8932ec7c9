package so.dian.invoice.mq.consumer.songshan;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import so.dian.invoice.converter.InvoiceNewsVoucherConverter;
import so.dian.invoice.mq.consumer.MessageSnapshotListener;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;
import so.dian.invoice.service.AsyncMessageService;
import so.dian.invoice.service.message.context.MessageContext;
import so.dian.songshan.client.pojo.dto.ChannelRepairChangeDTO;

import javax.annotation.Resource;

/**
 * 维修单消息监听
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/14 11:32 上午
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.consumer.topic.songshan}", consumerGroup = "${rocketmq.consumer.groups.songshan}", selectorExpression = "${rocketmq.consumer.tags.channelRepairChangeTag}")
public class ChannelRepairChangeConsumer extends MessageSnapshotListener implements RocketMQListener<ChannelRepairChangeDTO> {

    @Resource
    private AsyncMessageService asyncMessageService;

    @Override
    public void onMessage(ChannelRepairChangeDTO channelRepairChangeDTO) {
        log.info("[维修单消息监听]channelRepairChangeDTO：{}", JSONUtil.toJsonStr(channelRepairChangeDTO));
        InvoiceNewsVoucherDTO invoiceNewsVoucherDTO = InvoiceNewsVoucherConverter.toDTO(channelRepairChangeDTO);
        this.saveSnapshot(invoiceNewsVoucherDTO);
        //业务逻辑处理
        MessageContext messageContext = new MessageContext();
        messageContext.setMessage(channelRepairChangeDTO);
        messageContext.setNewsID(invoiceNewsVoucherDTO.getId());
        messageContext.setOutBizType(invoiceNewsVoucherDTO.getOutBizType());
        messageContext.setOutBizId(invoiceNewsVoucherDTO.getOutBizId());
        asyncMessageService.handleMessageAsync(messageContext);

    }
}
