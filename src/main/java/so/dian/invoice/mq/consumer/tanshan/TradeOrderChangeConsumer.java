package so.dian.invoice.mq.consumer.tanshan;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import so.dian.invoice.converter.InvoiceNewsVoucherConverter;
import so.dian.invoice.mq.consumer.MessageSnapshotListener;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;
import so.dian.invoice.service.AsyncMessageService;
import so.dian.invoice.service.message.context.MessageContext;
import so.dian.taishan.client.pojo.mq.TradeOrderChangeDTO;

import javax.annotation.Resource;

/**
 * 交易单消息监听
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/14 11:32 上午
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.consumer.topic.taishan}", consumerGroup = "${rocketmq.consumer.groups.taishan}", selectorExpression = "${rocketmq.consumer.tags.tradeOrderChangeTag}")
public class TradeOrderChangeConsumer extends MessageSnapshotListener implements RocketMQListener<TradeOrderChangeDTO> {
    @Resource
    private AsyncMessageService asyncMessageService;

    @Override
    public void onMessage(TradeOrderChangeDTO tradeOrderChangeDTO) {
        log.info("[交易单消息监听]tradeOrderChangeDTO：{}", JSONUtil.toJsonStr(tradeOrderChangeDTO));
        InvoiceNewsVoucherDTO invoiceNewsVoucherDTO = InvoiceNewsVoucherConverter.toDTO(tradeOrderChangeDTO);
        this.saveSnapshot(invoiceNewsVoucherDTO);

        MessageContext messageContext = new MessageContext();
        messageContext.setMessage(tradeOrderChangeDTO);
        messageContext.setNewsID(invoiceNewsVoucherDTO.getId());
        messageContext.setOutBizType(invoiceNewsVoucherDTO.getOutBizType());
        messageContext.setOutBizId(invoiceNewsVoucherDTO.getOutBizId());
        asyncMessageService.handleMessageAsync(messageContext);
    }
}
