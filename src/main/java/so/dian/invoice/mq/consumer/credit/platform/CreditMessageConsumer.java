package so.dian.invoice.mq.consumer.credit.platform;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import so.dian.fis.credit.dto.response.CreditMessageDTO;
import so.dian.invoice.converter.InvoiceNewsVoucherConverter;
import so.dian.invoice.mq.consumer.MessageSnapshotListener;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;
import so.dian.invoice.service.AsyncMessageService;
import so.dian.invoice.service.message.context.MessageContext;

import javax.annotation.Resource;

/**
 * 还款成功消息
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-06 16:47
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.consumer.topic.credit-platform}", consumerGroup = "${rocketmq.consumer.groups.credit-platform}", selectorExpression = "${rocketmq.consumer.tags.credit_message}")
public class CreditMessageConsumer extends MessageSnapshotListener implements RocketMQListener<CreditMessageDTO> {

    @Resource
    private AsyncMessageService asyncMessageService;

    @Override
    public void onMessage(CreditMessageDTO message) {
	//todo 执行消息
	log.info("[授信消息]repaySuccessMessageDTO:{}", JSONObject.toJSONString(message));
	InvoiceNewsVoucherDTO invoiceNewsVoucherDTO = InvoiceNewsVoucherConverter.toDTO(message);
	this.saveSnapshot(invoiceNewsVoucherDTO);
	//业务逻辑处理
	MessageContext messageContext = new MessageContext();
	messageContext.setMessage(message);
	messageContext.setNewsID(invoiceNewsVoucherDTO.getId());
	messageContext.setOutBizType(invoiceNewsVoucherDTO.getOutBizType());
	messageContext.setOutBizId(invoiceNewsVoucherDTO.getOutBizId());
	asyncMessageService.handleMessageAsync(messageContext);
    }
}

