package so.dian.invoice.mq.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.invoice.manager.message.snapshot.InvoiceNewsVoucherManager;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-18 17:54
 */
@Slf4j
@Component
public class MessageSnapshotListener {
    @Resource
    private InvoiceNewsVoucherManager invoiceNewsVoucherManager;

    public void saveSnapshot(InvoiceNewsVoucherDTO invoiceNewsVoucherDTO) {
	if (StringUtils.isBlank(invoiceNewsVoucherDTO.getOutBizId())) {
	    log.warn("saveSnapshot,外部业务单号不能为空");
	    return;
	}
	if (Objects.isNull(invoiceNewsVoucherDTO.getOutBizType())) {
	    log.warn("saveSnapshot,外部业务类型不能为空");
	}
	invoiceNewsVoucherManager.save(invoiceNewsVoucherDTO);
    }
}
