package so.dian.invoice.specification;

import java.util.Arrays;
import java.util.List;
import org.springframework.stereotype.Component;
import so.dian.invoice.common.design.specification.CompositeSpecification;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;

/**
 * BusinessTypeSuppotSpec
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/28 14:06
 */
@Component
public class BusinessTypeRollbackSupportSpec extends CompositeSpecification<InvoiceDeductionDO> {

    private List<BusinessTypeEnum> businessTypeEnums =
            Arrays.asList(BusinessTypeEnum.DEVIDE,
                    BusinessTypeEnum.CHANNEL,
                    BusinessTypeEnum.ADVANCE_PAYMENT,
                    BusinessTypeEnum.JOINT_VENTURE_COMPANY,
                    BusinessTypeEnum.ENTRANCE_FEE,
                    BusinessTypeEnum.JOIN_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.KP_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.KP_SERVICE_ENTRY_FEE,
                    BusinessTypeEnum.NORMAL_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.NORMAL_ENTRY_FEE,
                    BusinessTypeEnum.RESOURCE_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.RESOURCE_ENTRY_FEE,
                    BusinessTypeEnum.BRAND_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.BRAND_ENTRY_FEE,
                    BusinessTypeEnum.JOIN_ENTRY_FEE);

    @Override
    public boolean isSatisfiedBy(InvoiceDeductionDO invoiceDeductionDO) {
        Integer businessTypeCode = invoiceDeductionDO.getBusinessType();
        BusinessTypeEnum businessType = BusinessTypeEnum.findByType(businessTypeCode);
        return businessTypeEnums.contains(businessType);
    }
}
