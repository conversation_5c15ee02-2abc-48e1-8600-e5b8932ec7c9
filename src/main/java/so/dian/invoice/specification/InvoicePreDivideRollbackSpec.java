package so.dian.invoice.specification;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.invoice.client.manager.PayApplyManager;
import so.dian.invoice.common.design.specification.CompositeSpecification;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.payapply.platform.dto.PayApplyDTO;
import so.dian.payapply.platform.enums.InvoiceArriveEnum;

/**
 * InvoicePreDivideRollbackSpec 预付分成 核销回滚判定
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 14:47
 */
@Component
public class InvoicePreDivideRollbackSpec extends CompositeSpecification<InvoiceDeductionDO> {

    @Autowired
    private PayApplyManager payApplyManager;

    @Autowired
    private InvoiceHandleWorkRollbackSpec invoiceHandleWorkRollbackSpec;

    private List<BusinessTypeEnum> businessTypeEnums =
            Arrays.asList(
                    BusinessTypeEnum.ADVANCE_PAYMENT,
                    BusinessTypeEnum.ENTRANCE_FEE,
                    BusinessTypeEnum.JOIN_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.KP_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.KP_SERVICE_ENTRY_FEE,
                    BusinessTypeEnum.NORMAL_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.NORMAL_ENTRY_FEE,
                    BusinessTypeEnum.RESOURCE_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.RESOURCE_ENTRY_FEE,
                    BusinessTypeEnum.BRAND_ENTRANCE_OTHER_FEE,
                    BusinessTypeEnum.BRAND_ENTRY_FEE,
                    BusinessTypeEnum.JOIN_ENTRY_FEE);

    @Override
    public boolean isSatisfiedBy(InvoiceDeductionDO invoiceDeductionDO) {
        if (invoiceHandleWorkRollbackSpec.isSatisfiedBy(invoiceDeductionDO)) {
            return false;
        }

        Integer businessTypeCode = invoiceDeductionDO.getBusinessType();
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.findByType(businessTypeCode);
        if (!businessTypeEnums.contains(businessTypeEnum)) {
            return false;
        }

        PayApplyDTO apply = payApplyManager.getByApplyNo(invoiceDeductionDO.getBusinessNo());
        if (Objects.isNull(apply)) {
            return true;
        }

        Integer invoiceArrive = apply.getInvoiceArrive();

        return InvoiceArriveEnum.NOT_DEDUCT.getCode().equals(invoiceArrive);
    }
}
