package so.dian.invoice.specification;

import java.util.Arrays;
import java.util.List;
import org.springframework.stereotype.Component;
import so.dian.invoice.common.design.specification.CompositeSpecification;
import so.dian.invoice.enums.SubjectTypeEnum;
import so.dian.invoice.pojo.entity.InvoiceDO;

/**
 * InvoiceDisableRollbackSpec
 *
 * <AUTHOR>
 * @desc
 * @date 2023/5/8 16:20
 */
@Component
public class InvoiceDisableRollbackSpec extends CompositeSpecification<InvoiceDO> {

    private final List<SubjectTypeEnum> subjectTypeEnums = Arrays.
            asList(SubjectTypeEnum.FEE,
                    SubjectTypeEnum.SCM,
                    SubjectTypeEnum.SHOU_JU,
                    SubjectTypeEnum.EQUIPMENT_PROCUREMENT
            );

    @Override
    public boolean isSatisfiedBy(InvoiceDO invoiceDO) {
        Integer subjectType = invoiceDO.getSubjectType();
        SubjectTypeEnum subjectTypeEnum = SubjectTypeEnum.findByType(subjectType);
        return subjectTypeEnums.contains(subjectTypeEnum);
    }
}
