package so.dian.invoice.specification;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.invoice.client.manager.WithdrawApplyBillManager;
import so.dian.invoice.common.design.specification.CompositeSpecification;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.withdraw.platform.dto.WithdrawApplyBillDetailResponse;
import so.dian.withdraw.platform.enums.WithdrawApplyBillStatusEnum;

/**
 * InvoiceDivideRollbackSpec 分成核销回滚
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 14:41
 */
@Component
public class InvoiceDivideRollbackSpec extends CompositeSpecification<InvoiceDeductionDO> {

    @Autowired
    private WithdrawApplyBillManager withdrawApplyBillManager;

    @Autowired
    private InvoiceHandleWorkRollbackSpec invoiceHandleWorkRollbackSpec;

    private List<BusinessTypeEnum> businessTypeEnums =
            Arrays.asList(BusinessTypeEnum.DEVIDE,
                    BusinessTypeEnum.CHANNEL,
                    BusinessTypeEnum.JOINT_VENTURE_COMPANY);

    private List<WithdrawApplyBillStatusEnum> ableStatus =
            Arrays.asList(WithdrawApplyBillStatusEnum.BD_CHECK,
                    WithdrawApplyBillStatusEnum.INVOICE_CHECK,
                    WithdrawApplyBillStatusEnum.INVOICE_FAILED,
                    WithdrawApplyBillStatusEnum.INVOICE_ABNORMITY);

    @Override
    public boolean isSatisfiedBy(InvoiceDeductionDO invoiceDeductionDO) {
        if (invoiceHandleWorkRollbackSpec.isSatisfiedBy(invoiceDeductionDO)) {
            return false;
        }
        Integer businessTypeCode = invoiceDeductionDO.getBusinessType();
        BusinessTypeEnum businessType = BusinessTypeEnum.findByType(businessTypeCode);
        if (!businessTypeEnums.contains(businessType)) {
            return false;
        }
        WithdrawApplyBillDetailResponse withdrawApplyBill = withdrawApplyBillManager.getByApplyNo(
                invoiceDeductionDO.getBusinessNo());
        if (Objects.isNull(withdrawApplyBill)) {
            return true;
        }

        return ableStatus.contains(withdrawApplyBill.getApplyStatus());
    }
}
