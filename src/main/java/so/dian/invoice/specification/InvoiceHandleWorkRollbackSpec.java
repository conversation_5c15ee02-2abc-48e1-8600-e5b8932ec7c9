package so.dian.invoice.specification;

import static so.dian.invoice.constant.InvoiceConstants.MANUAL_DEDUCT_BUSINESS_NO;

import java.util.Objects;
import org.springframework.stereotype.Component;
import so.dian.invoice.common.design.specification.CompositeSpecification;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;

/**
 * InvoiceHandleWorkRollbackSpec
 * 手工核销
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 14:57
 */
@Component
public class InvoiceHandleWorkRollbackSpec extends CompositeSpecification<InvoiceDeductionDO> {

    private static final String HANDLE_WORD_BUSINESS_NO = MANUAL_DEDUCT_BUSINESS_NO;

    @Override
    public boolean isSatisfiedBy(InvoiceDeductionDO invoiceDeductionDO) {
        String businessNo = invoiceDeductionDO.getBusinessNo();
        return Objects.equals(HANDLE_WORD_BUSINESS_NO, businessNo);
    }
}
