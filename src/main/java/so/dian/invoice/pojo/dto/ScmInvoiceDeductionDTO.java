package so.dian.invoice.pojo.dto;

import java.math.BigDecimal;
import lombok.Data;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.enums.OperateTypeEnum;

@Data
public class ScmInvoiceDeductionDTO {
    /**
     * 发票核销明细ID
     */
    private Integer deductionId;

    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 业务类型
     *
     * @see BusinessTypeEnum
     */
    private Integer businessType;
    private String businessTypeStr;

    /**
     * 操作类型
     *
     * @see OperateTypeEnum
     */
    private Integer operateType;
    private String operateTypeStr;

    /**
     * 核销金额
     */
    private BigDecimal amount;

    /**
     * 核销原因
     */
    private String reason;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private Long createTime;
}
