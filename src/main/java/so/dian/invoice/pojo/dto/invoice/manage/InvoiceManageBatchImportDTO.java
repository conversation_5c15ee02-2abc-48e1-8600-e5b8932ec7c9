package so.dian.invoice.pojo.dto.invoice.manage;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: invoice
 * @description: 应开明细批量导入DTO
 * @author: yuechuan
 * @create: 2025-09-04 15:00
 */
@Data
public class InvoiceManageBatchImportDTO {

    @ExcelProperty("业务单号")
    private String bizNo;

    @ExcelProperty("业务类型")
    private Integer bizType;

    @ExcelProperty("渠道商类型")
    private Integer subjectType;

    @ExcelProperty("渠道商ID")
    private Long subjectId;

    @ExcelProperty("渠道商名称")
    private String subjectName;

    @ExcelProperty("业务单总金额（元）")
    private BigDecimal totalAmount;

    @ExcelProperty("应开票总金额（元）")
    private BigDecimal expectedInvoiceAmount;

    @ExcelProperty("开票中总金额（元）")
    private BigDecimal ongoingInvoiceAmount;

    @ExcelProperty("已开票总金额（元）")
    private BigDecimal completedInvoiceAmount;

    @ExcelProperty("待开票总金额（元）")
    private BigDecimal pendingInvoiceAmount;

    @ExcelProperty("业务单创建时间")
    private String bizCreateTime;

    @ExcelProperty("业务单付款完成时间")
    private String paymentTime;

    @ExcelProperty("产品名称")
    private String productCode;

    @ExcelProperty("产品数量")
    private Integer productCount;

    @ExcelProperty("业务单金额（元）")
    private BigDecimal amount;

    @ExcelProperty("应开票金额（元）")
    private BigDecimal expectedAmount;

    @ExcelProperty("开票中金额（元）")
    private BigDecimal ongoingAmount;

    @ExcelProperty("已开票金额（元）")
    private BigDecimal completedAmount;

    @ExcelProperty("待开票金额（元）")
    private BigDecimal pendingAmount;

    // 转换后的时间戳字段
    private Long bizCreateTimestamp;
    private Long paymentTimestamp;

    // 转换后的金额字段（分为单位）
    private Long totalAmountCent;
    private Long expectedInvoiceAmountCent;
    private Long ongoingInvoiceAmountCent;
    private Long completedInvoiceAmountCent;
    private Long pendingInvoiceAmountCent;
    private Long amountCent;
    private Long expectedAmountCent;
    private Long ongoingAmountCent;
    private Long completedAmountCent;
    private Long pendingAmountCent;
}
