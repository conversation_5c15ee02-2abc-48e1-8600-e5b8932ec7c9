package so.dian.invoice.pojo.dto.invoice.manage.agg;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import so.dian.invoice.converter.InvoiceManageConverter;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDTO;
import so.dian.invoice.pojo.entity.InvoiceManageDO;
import so.dian.invoice.pojo.entity.InvoiceManageDetailDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 14:49
 */
@Slf4j
@Data
public class InvoiceManage {

    /**
     * 主键
     */
    private Long manageId;

    private InvoiceManageDTO invoiceManageDTO;

    private List<InvoiceManageDetail> manageDetails;

    private List<InvoiceRequestRecordDTO> invoiceRequestRecords;

    public static InvoiceManage build(InvoiceManageDO invoiceManageDO, List<InvoiceManageDetailDO> detailDOS, List<InvoiceRequestRecordDO> requestRecordDOS, List<InvoiceRequestRecordDetailDO> requestRecordDetailDOS) {
        InvoiceManage invoiceManage = new InvoiceManage();
        invoiceManage.setManageId(invoiceManageDO.getId());
        // 构建单据DTO
        InvoiceManageDTO manageDTO = InvoiceManageDTO.fromDO(invoiceManageDO);
        invoiceManage.setInvoiceManageDTO(manageDTO);
        // 构建明细聚合
        List<InvoiceManageDetail> details = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailDOS)) {
            details = InvoiceManageConverter.build(detailDOS, requestRecordDetailDOS);
        }else {
            log.warn("未查询到开票管理详情 | manageId:{}", invoiceManageDO.getId());
        }
        invoiceManage.setManageDetails(details);
        // 构建, 并计算开票金额单据详情
        List<InvoiceManageDetailDTO> manageDetailDTOS = details.stream().map(e -> e.getManageDetailDTO()).collect(Collectors.toList());
        manageDTO.setManageDetailDTOS(manageDetailDTOS);
        //构建请求记录DTO
        if (CollectionUtils.isNotEmpty(requestRecordDOS)) {
            List<InvoiceRequestRecordDTO> records = requestRecordDOS.stream().map(InvoiceRequestRecordDTO::fromDO).collect(Collectors.toList());
            invoiceManage.setInvoiceRequestRecords(records);
        }
        //计算开票金额
        invoiceManage.cal();
        return invoiceManage;
    }

    public Long getPendingInvoiceAmount(){
        return this.invoiceManageDTO.getPendingInvoiceAmount();
    }

    /**
     * 是否开票完成状态
     *
     * @return
     */
    @JsonIgnore
    public boolean isCompletedInvoiceStatus() {
        return this.invoiceManageDTO.isCompletedInvoiceStatus();
    }

    /**
     * 初始化开票金额
     */
    private void cal(){
        // 计算应开票金额
        this.calculateExpectedInvoice();
        //计算已开票金额
        this.calculateCompletedInvoiceAmount();
        //计算开票中金额
        this.calculateOngoingInvoiceAmount();
        //计算待开票金额
        this.invoiceManageDTO.setPendingInvoiceAmount(this.invoiceManageDTO.getExpectedInvoiceAmount() - this.invoiceManageDTO.getCompletedInvoiceAmount() - this.invoiceManageDTO.getOngoingInvoiceAmount());

    }

    /**
     * 计算应开票金额
     */
    private void calculateExpectedInvoice(){
        // 计算应开票金额
        Long expectedInvoiceAmount = manageDetails.stream()
                .mapToLong(e -> e.getExpectedInvoiceAmount())
                .sum();

        this.invoiceManageDTO.setExpectedInvoiceAmount(expectedInvoiceAmount);
    }

    /**
     * 计算开票中金额
     * request_record 中的 status 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    private void calculateOngoingInvoiceAmount(){
        Long ongoingInvoiceAmount = 0L;
        if (CollectionUtils.isNotEmpty(invoiceRequestRecords) ) {
            // 计算开票中金额
            ongoingInvoiceAmount = invoiceRequestRecords.stream()
                    .filter(e -> e.isOngoingInvoice())
                    .mapToLong(e -> e.getAmount())
                    .sum();
        }
        this.invoiceManageDTO.setOngoingInvoiceAmount(ongoingInvoiceAmount);
    }

    /**
     * 计算已开票金额
     * request_record 中的 status 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    private void calculateCompletedInvoiceAmount(){
        Long completedInvoiceAmount = 0L;
        if (CollectionUtils.isNotEmpty(invoiceRequestRecords) ) {
            // 计算开票中金额
            completedInvoiceAmount = invoiceRequestRecords.stream()
                    .filter(e -> e.isCompletedInvoice())
                    .mapToLong(e -> e.getAmount())
                    .sum();
        }
        this.invoiceManageDTO.setCompletedInvoiceAmount(completedInvoiceAmount);
    }
}
