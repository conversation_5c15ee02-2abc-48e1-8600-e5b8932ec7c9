package so.dian.invoice.pojo.dto.invoice.manage;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceRequestDO;
import so.dian.invoice.pojo.vo.invoice.manage.InvoiceRequestVO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:54
 */
@Data
public class InvoiceRequestDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 购买方id
     */
    private Long subjectId;

    /**
     * 购买方类型
     */
    private Integer subjectType;

    /**
     * 购买方名称
     */
    private String subjectName;

    /**
     * 发票抬头类型：
     */
    private Integer titleType;

    /**
     * 发票抬头
     */
    private String title;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票类型 1: 增值税专用发票 2: 普通发票
     */
    private Integer type;

    /**
     * 发票金额
     */
    private Long amount;

    /**
     * 邮件地址
     */
    private String email;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 开票状态： 0 待开票，1 已开票， 2 已拒绝, 3 部分开票
     */
    private Integer status;

    /**
     * 审批意见
     */
    private String financeFeedback;

    /**
     * 逻辑删除，0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 开票完成时间
     */
    private Long invoiceCompletedTime;

    private List<InvoiceRequestRecordDTO> requestRecords;


    public static InvoiceRequestDTO fromDO(InvoiceRequestDO model) {
        InvoiceRequestDTO dto = new InvoiceRequestDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }

    public InvoiceRequestDO toDO(){
        InvoiceRequestDO model = new InvoiceRequestDO();
        BeanUtils.copyProperties(this, model);
        return model;
    }

    public InvoiceRequestVO toVO(){
        InvoiceRequestVO model = new InvoiceRequestVO();
        BeanUtils.copyProperties(this, model);
        return model;
    }

    public List<String> getSpuCodes(){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(requestRecords)) {
            return null;
        }
        return requestRecords.stream().map(e -> e.getSpuCodes()).flatMap(List::stream).collect(Collectors.toList());
    }

    public void fillProductName(Map<String, String> spuNameMap){
        if (CollectionUtils.isEmpty(requestRecords)) {
            return;
        }
        requestRecords.forEach(e -> e.fillProductName(spuNameMap));
    }

}
