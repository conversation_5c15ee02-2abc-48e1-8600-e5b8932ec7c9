package so.dian.invoice.pojo.dto.invoice.manage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceManageDO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 14:49
 */
@Data
public class InvoiceManageDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 购买方业务类型 0 代理商， 5 合资公司
     */
    private Integer subjectType;

    /**
     * 购买方id
     */
    private Long subjectId;

    /**
     * 购买方名称
     */
    private String subjectName;

    /**
     * 业务单总金额
     */
    private Long totalAmount;

    /**
     * 应开票金额
     */
    private Long expectedInvoiceAmount;

    /**
     * 开票中金额
     */
    private Long ongoingInvoiceAmount;

    /**
     * 已开票金额
     */
    private Long completedInvoiceAmount;

    /**
     * 待开票金额
     */
    private Long pendingInvoiceAmount;

    /**
     * 业务单付款完成时间
     */
    private Long bizCreateTime;

    /**
     * 业务单付款完成时间
     */
    private Long paymentTime;

    /**
     * 状态，0 开票中, 1 开票完成（业务单金额=已开票金额）, 2 未开票
     */
    private Integer status;

    @JsonIgnore
    private List<InvoiceManageDetailDTO> manageDetailDTOS;


    /**
     * 是否开票完成状态
     *
     * @return
     */
    @JsonIgnore
    public boolean isCompletedInvoiceStatus() {
        if (Objects.isNull(totalAmount)) {
            return false;
        }
        return this.totalAmount.equals(this.completedInvoiceAmount);
    }

    /**
     * 盘点是否存在应开票金额 < 0 的详情记录
     * @return
     */
    public boolean existPendingInvoiceAmountLessThenZero(){
        if (CollectionUtils.isEmpty(manageDetailDTOS)) {
            return false;
        }
        return manageDetailDTOS.stream().anyMatch(e -> NumberUtils.LONG_ZERO.compareTo(e.getPendingInvoiceAmount()) > 0);
    }

//    /**
//     * 获取应开票金额
//     * @return
//     */
//    public Long calExpectedInvoiceAmount(){
//        if (CollectionUtils.isEmpty(manageDetailDTOS)) {
//            return null;
//        }
//        return manageDetailDTOS.stream().mapToLong(InvoiceManageDetailDTO::getExpectedInvoiceAmount).sum();
//    }

    public List<String> getSpuCodes(){
        if (CollectionUtils.isEmpty(manageDetailDTOS)) {
            return null;
        }
        return manageDetailDTOS.stream().map(InvoiceManageDetailDTO::getProductCode).collect(Collectors.toList());
    }

    public void fillProductName(Map<String, String> spuNameMap){
        if (CollectionUtils.isEmpty(manageDetailDTOS)) {
            return;
        }
        manageDetailDTOS.forEach(e -> e.setProductName(spuNameMap.get(e.getProductCode())));
    }

    public static InvoiceManageDTO fromDO(InvoiceManageDO invoiceManageDO) {
        InvoiceManageDTO invoiceManageDTO = new InvoiceManageDTO();
        BeanUtils.copyProperties(invoiceManageDO, invoiceManageDTO);
        return invoiceManageDTO;
    }

    public InvoiceManageDO toDO() {
        InvoiceManageDO model = new InvoiceManageDO();
        BeanUtils.copyProperties(this, model);
        return model;
    }
}

