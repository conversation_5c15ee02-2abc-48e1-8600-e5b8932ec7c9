package so.dian.invoice.pojo.dto.invoice.manage;

import lombok.Data;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO;

/**
 * 发票系统消息凭证表
 * @TableName invoice_news_voucher
 */
@Data
public class InvoiceChangeRecordDetailDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理ID
     */
    private Long changeRecordId;

    /**
     * 开票管理ID
     */
    private Long manageId;

    /**
     * 开票管理详情ID
     */
    private Long manageDetailId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品数量
     */
    private Integer productCount;

    /**
     * 变更类型： increase 增加 ，decrease 减少
     */
    private String type;

    /**
     * 变更金额
     */
    private Long amount;


    public static InvoiceChangeRecordDetailDTO fromDO(InvoiceChangeRecordDetailDO model) {
        InvoiceChangeRecordDetailDTO dto = new InvoiceChangeRecordDetailDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }

    public InvoiceChangeRecordDetailDO toDO() {
        InvoiceChangeRecordDetailDO model = new InvoiceChangeRecordDetailDO();
        BeanUtils.copyProperties(this, model);
        return model;
    }
}