package so.dian.invoice.pojo.dto.invoice.manage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;

import java.util.Objects;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:55
 */
@Data
public class InvoiceRequestRecordDetailDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 开票管理详情id
     */
    private Long manageDetailId;

    /**
     * 开票申请记录id
     */
    private Long requestRecordId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品数量
     */
    private Integer productCount;

    /**
     * 开票金额
     */
    private Long amount;

    /**
     * 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    private Integer status;

    /**
     * 是否开票中
     * @return
     */
    @JsonIgnore
    public boolean isOngoingInvoice(){
	return Objects.nonNull(status) && status == 0;
    }

    /**
     * 是否已开票
     * @return
     */
    @JsonIgnore
    public boolean isCompletedInvoice(){
	return Objects.nonNull(status) && status == 1;
    }

    public static InvoiceRequestRecordDetailDTO fromDO(InvoiceRequestRecordDetailDO model) {
	InvoiceRequestRecordDetailDTO dto = new InvoiceRequestRecordDetailDTO();
	BeanUtils.copyProperties(model, dto);
	return dto;
    }

    public InvoiceRequestRecordDetailDO toDO(){
	InvoiceRequestRecordDetailDO model = new InvoiceRequestRecordDetailDO();
	BeanUtils.copyProperties(this, model);
	return model;
    }
}
