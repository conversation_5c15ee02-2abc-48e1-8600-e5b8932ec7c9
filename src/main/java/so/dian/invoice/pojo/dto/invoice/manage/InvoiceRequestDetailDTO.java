package so.dian.invoice.pojo.dto.invoice.manage;

import lombok.Data;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceRequestDO;

import java.util.List;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:54
 */
@Data
public class InvoiceRequestDetailDTO {

    /**
     * 申请Id
     */
    private Long requestId;

    /**
     * 申请记录Id
     */
    private Long recordId;

    /**
     * 申请记录Id
     */
    private Long recordDetailId;

    /**
     * 购买方id
     */
    private Long subjectId;

    /**
     * 购买方类型
     */
    private Integer subjectType;

    /**
     * 购买方名称
     */
    private String subjectName;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 发票金额
     */
    private Long amount;

    private List<InvoiceRequestRecordDetailDTO> requestRecordDetails;



    public static InvoiceRequestDetailDTO fromDO(InvoiceRequestDO model) {
        InvoiceRequestDetailDTO dto = new InvoiceRequestDetailDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }

    public InvoiceRequestDO toDO(){
        InvoiceRequestDO model = new InvoiceRequestDO();
        BeanUtils.copyProperties(this, model);
        return model;
    }

}
