package so.dian.invoice.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @Author: chenan
 * @Date: 2020/9/21 16:57
 */
@Data
public class SupplierDTO {
    private Integer id;

    private String supplierNo;

    private String name;

    private String shortName;

    private String type;

    private String supplyProxy;

    private String address;

    private String bankName;

    private String bankNo;

    private String contact;

    private String contactMobile;

    private String mail;

    private String memo;

    private Date gmtCreate;

    private Date gmtModified;

    private Integer isDelete;

    private Integer manager;
    private String nickName;

    @JSONField(format = "yyyy-MM-dd")
    private Date expireTime;

    private Integer templateId;

    private List<String> addressList;

    private BigDecimal deposit;

    private BigDecimal requestDepositDeduction;

//    /**
//     * 仓库信息
//     */
//    private List<SCMSUpplierWarehouseVO> warehouseList;
//
//    /**
//     * 操作员信息
//     */
//    private List<SCMSupplierOperatorVO> operators;
}
