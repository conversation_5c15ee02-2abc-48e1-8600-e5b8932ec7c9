package so.dian.invoice.pojo.dto.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.enums.NewsVoucherEnum;
import so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO;

/**
 * 发票系统消息凭证表
 * @TableName invoice_news_voucher
 */
@Data
public class InvoiceNewsVoucherDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 外部业务ID
     */
    private String outBizId;

    /**
     * 外部业务类型，1 采购付款 2 维修完成 3 采购取消 4 授信还款退款 5 授信还款
     */
    private Integer outBizType;

    /**
     * 状态（1-新建，2-处理成功，3-处理失败）
     */
    private Integer status;

    /**
     * 失败重试次数（一次成功时为0）
     */
    private Integer retryTime;

    /**
     * 凭证内容（JSON格式）
     */
    private String eventVoucher;

    /**
     * 业务发生时间（13位时间戳）
     */
    private Long receiveTime;

    @JsonIgnore
    public boolean isSuccess(){
        return NewsVoucherEnum.SUCCESS.code().equals(this.status);
    }

    public void process(){
        this.status = NewsVoucherEnum.PROCESS.code();
    }
    public void success(){
        this.status = NewsVoucherEnum.SUCCESS.code();
    }
    public void fail(){
        this.status = NewsVoucherEnum.FAIL.code();
    }

    public static InvoiceNewsVoucherDTO fromDO(InvoiceNewsVoucherDO model) {
        InvoiceNewsVoucherDTO dto = new InvoiceNewsVoucherDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }

    public InvoiceNewsVoucherDO toDO() {
        InvoiceNewsVoucherDO model = new InvoiceNewsVoucherDO();
        BeanUtils.copyProperties(this, model);

        return model;
    }
}