package so.dian.invoice.pojo.dto.invoice.manage;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import so.dian.invoice.enums.InvoiceRequestStatusEnum;
import so.dian.invoice.service.invoice.manage.impl.InvoiceRequestServiceImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 14:49
 */
@Slf4j
@Data
public class InvoiceImportListener extends AnalysisEventListener<InvoiceImportDTO> {

    /**
     * 导入的数据
     */
    public List<InvoiceImportDTO> data = new ArrayList<>();

    public List<InvoiceImportDTO> failedData = new ArrayList<>();

    /**
     * 审批意见
     */
    private String financeFeedback;


    private InvoiceRequestServiceImpl invoiceRequestService;
    /**
     * 状态
     */
    private int status;

    public int allDataSize;


    public InvoiceImportListener(InvoiceRequestServiceImpl invoiceRequestService, int status, String financeFeedback) {
	this.invoiceRequestService = invoiceRequestService;
	this.status = status;
	this.financeFeedback = financeFeedback;
    }

    @Override
    public void invoke(InvoiceImportDTO rowData, AnalysisContext context) {
	allDataSize ++;
	String validateResult = this.validate(rowData);
	if(StringUtils.isNotBlank(validateResult)){
	    rowData.setMessage(validateResult);
	    failedData.add(rowData);
	    return;
	}
	data.add(rowData);

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
	// 处理剩余数据
	if (!data.isEmpty()) {
	    invoiceRequestService.batchAudit(data, status, financeFeedback);
	}
	log.info("插入数据成功,共插入{}条数据", allDataSize - failedData.size());
	log.info("插入数据失败,共失败{}条数据", failedData.size());

    }

    private String validate(InvoiceImportDTO rowData) {
	StringBuffer sb = new StringBuffer();
	if (Objects.isNull(rowData.getRequestId())){
	    sb.append("开票申请ID不能为空").append(";\r\n");;
	}
	InvoiceRequestDTO invoiceRequestDTO = invoiceRequestService.getInvoiceRequestById(rowData.getRequestId());
	if (Objects.isNull(invoiceRequestDTO)){
	    sb.append("开票申请不存在").append(";\r\n");;
	}else {
	    if (!InvoiceRequestStatusEnum.PENDING_INVOICE.getCode().equals(invoiceRequestDTO.getStatus())){
		sb.append("申请状态非待开票，无法更新").append(";\r\n");;
	    }
	}
	return sb.toString();
    }
}
