package so.dian.invoice.pojo.dto.invoice.manage;

import lombok.Data;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceManageDetailDO;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 14:49
 */
@Data
public class InvoiceManageDetailDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 业务单金额
     */
    private Long amount;

    /**
     * 原始开票金额
     */
    private Long originAmount;

    /**
     * 应开票金额
     */
    private Long expectedInvoiceAmount;

    /**
     * 开票中金额
     */
    private Long ongoingInvoiceAmount;

    /**
     * 已票中金额
     */
    private Long completedInvoiceAmount;

    /**
     * 待开票金额
     */
    private Long pendingInvoiceAmount;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品数量
     */
    private Integer productCount;

    public static InvoiceManageDetailDTO fromDO(InvoiceManageDetailDO invoiceManageDetailDO) {
        InvoiceManageDetailDTO invoiceManageDTO = new InvoiceManageDetailDTO();
        BeanUtils.copyProperties(invoiceManageDetailDO, invoiceManageDTO);
        return invoiceManageDTO;
    }

    public InvoiceManageDetailDO toDO(){
        InvoiceManageDetailDO model = new InvoiceManageDetailDO();
        BeanUtils.copyProperties(this, model);
        return model;
    }
}
