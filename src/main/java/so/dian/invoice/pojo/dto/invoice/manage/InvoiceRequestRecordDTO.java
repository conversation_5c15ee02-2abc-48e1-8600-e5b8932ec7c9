package so.dian.invoice.pojo.dto.invoice.manage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.enums.InvoiceRequestStatusEnum;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:55
 */
@Data
public class InvoiceRequestRecordDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 开票申请id
     */
    private Long requestId;

    /**
     * 开票金额
     */
    private Long amount;

    /**
     * 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    private Integer status;

    /**
     * 审批意见
     */
    private String financeFeedback;

    private List<InvoiceRequestRecordDetailDTO> details;

    public static InvoiceRequestRecordDTO fromDO(InvoiceRequestRecordDO model) {
        InvoiceRequestRecordDTO dto = new InvoiceRequestRecordDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }

    public InvoiceRequestRecordDO toDO(){
        InvoiceRequestRecordDO model = new InvoiceRequestRecordDO();
        BeanUtils.copyProperties(this, model);
        return model;
    }

    /**
     * 是否开票中
     * @return
     */
    @JsonIgnore
    public boolean isOngoingInvoice(){
        return Objects.nonNull(status) && status == InvoiceRequestStatusEnum.PENDING_INVOICE.getCode();
    }

    /**
     * 是否已开票
     * @return
     */
    @JsonIgnore
    public boolean isCompletedInvoice(){
        return Objects.nonNull(status) && status == InvoiceRequestStatusEnum.COMPLETED_INVOICE.getCode();
    }

    @JsonIgnore
    public List<String> getSpuCodes(){
        if (CollectionUtils.isEmpty(details)) {
            return null;
        }
        return details.stream().map(InvoiceRequestRecordDetailDTO::getProductCode).collect(Collectors.toList());
    }

    public void fillProductName(Map<String, String> spuNameMap){
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        details.forEach(e -> e.setProductName(spuNameMap.get(e.getProductCode())));
    }
}
