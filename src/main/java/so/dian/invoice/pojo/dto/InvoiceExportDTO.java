package so.dian.invoice.pojo.dto;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Auther: liangfang
 * @Date: 2020-04-13 11:36
 * @Since feature_20200410_liangfang_发票导出新增字段和发票质检优化
 * @Description: 发票台账导出信息
 */
@Data
@ToString
public class InvoiceExportDTO {
    /**
     * 发票ID
     */
    private Integer id;
    /**
     * 发票号
     */
    private String invoiceNo;
    /**
     * 供应商编号
     */
    private String supplierNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 业务类型 1-供应链供应商 2-分成商户 3代理商分成 6 运营型服务商分成
     */
    private Integer subjectType;
    /**
     * 主体名称
     */
    private String subjectName;
    /**
     * 关联的请款单编号
     */
    private String billNo;
    /**
     * 发票类型 1: 增值税专用发票 2: 普通发票
     */
    private Integer type;
    /**
     * 税前金额，不包括税额
     */
    private BigDecimal rawPrice;
    /**
     * 价税合计（总金额包括税额）
     */
    private BigDecimal price;
    /**
     * 接受时间
     */
    private Date receiveTime;
    /**
     * 发票备注
     */
    private String memo;
    /**
     * 状态 1:未核销 2:部分核销 3:全部核销
     */
    private Integer status;
    /**
     * 已核销金额
     */
    private BigDecimal usedAmount;
    /**
     * 发票日期
     */
    private Date gmtCreate;
    /**
     *
     */
    private Date gmtModified;
    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 创建人
     */
    private Integer creator;
    /**
     * 发票记录创建时间
     */
    private Date createTime;
    /**
     * 来源 1-手动录入,2-模板导入,3-自动录入
     */
    private Integer source;
    /**
     * 行业类型（发票消费类型）
     */
    private String kind;
    /**
     * 校验码
     */
    private String checkCode;
    /**
     * 税额
     */
    private BigDecimal tax;
    /**
     * 销售方名称
     */
    private String seller;
    /**
     * 销售方纳税人识别号
     */
    private String sellerTaxId;
    /**
     * 购买方名称
     */
    private String buyer;
    /**
     * 销售方纳税人识别号
     */
    private String buyerTaxId;
    /**
     * 图片URL
     */
    private String url;
    /**
     * 验证状态 0待验证 1验证通过 2验证不通过
     */
    private Integer isReal;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 从属主体类型（参考枚举：BelongSubjectTypeEnum）
     */
    private Integer belongSubjectType;
    /**
     * 从属主体ID
     */
    private Integer belongSubjectId;
    /**
     * 发票流转状态
     */
    private Integer processStatus;
    /**
     * 发票复核人
     */
    private Long reviewer;

    /**
     * 发票复核时间
     */
    private Date reviewTime;
    /**
     * 发票录入人
     */
    private String createName;
    /**
     * 发票复核时间
     */
    private String reviewRemark;
    /**
     * 是否需要质检
     */
    private Integer needCheck;
    /**
     * 是否进入质检池标记
     */
    private Integer inCheckPool;
    /**
     * 验真失败错误码
     */
    private String validateCode;
    /**
     * 核销金额
     */
    private BigDecimal amount;
    /**
     * 核销创建时间
     */
    private Date deductionCreateTime;
    /**
     * 业务单号
     */
    private String businessNo;
    /**
     * 业务类型 1-供应链 2-分成
     */
    private String businessType;
}
