package so.dian.invoice.pojo.dto.invoice.manage.agg;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDetailDTO;
import so.dian.invoice.pojo.entity.InvoiceManageDetailDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 14:49
 */
@Data
public class InvoiceManageDetail {

    /**
     * 主键
     */
    private Long detailId;

    /**
     * 开票管理详情
     */
    private InvoiceManageDetailDTO manageDetailDTO;

    /**
     * 产品维度开票记录
     */
    private List<InvoiceRequestRecordDetailDTO> recordDetails;

    public static InvoiceManageDetail build(InvoiceManageDetailDO invoiceManageDetailDO, List<InvoiceRequestRecordDetailDO> invoiceRequestRecordDetailDOS) {
        InvoiceManageDetail invoiceManageDetail = new InvoiceManageDetail();
        InvoiceManageDetailDTO dto = InvoiceManageDetailDTO.fromDO(invoiceManageDetailDO);
        invoiceManageDetail.setDetailId(dto.getId());
        invoiceManageDetail.setManageDetailDTO(dto);
        if (CollectionUtils.isNotEmpty(invoiceRequestRecordDetailDOS)) {
            // 构建明细DTO
            List<InvoiceRequestRecordDetailDTO> details = invoiceRequestRecordDetailDOS.stream().map(InvoiceRequestRecordDetailDTO::fromDO).collect(Collectors.toList());
            invoiceManageDetail.setRecordDetails(details);
        }
        //计算金额
        invoiceManageDetail.init();
        return invoiceManageDetail;
    }

    /**
     * 初始化开票状态
     */
    public void init(){
        //计算已开票金额
        this.calculateCompletedInvoiceAmount();
        //计算开票中金额
        this.calculateOngoingInvoiceAmount();
        //计算待开票金额
        this.manageDetailDTO.setPendingInvoiceAmount(this.manageDetailDTO.getExpectedInvoiceAmount() - this.manageDetailDTO.getCompletedInvoiceAmount() - this.manageDetailDTO.getOngoingInvoiceAmount());
    }

    /**
     * 计算开票中金额
     * request_record 中的 status 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    public void calculateOngoingInvoiceAmount(){
        Long ongoingInvoiceAmount = 0L;
        if (CollectionUtils.isNotEmpty(recordDetails)) {
            // 计算开票中金额
            ongoingInvoiceAmount = recordDetails.stream()
                    .filter(e -> e.isOngoingInvoice())
                    .mapToLong(e -> e.getAmount())
                    .sum();
        }
        this.manageDetailDTO.setOngoingInvoiceAmount(ongoingInvoiceAmount);
    }

    /**
     * 计算已开票金额
     * request_record 中的 status 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    public void calculateCompletedInvoiceAmount(){
        Long completedInvoiceAmount = 0L;
        if (CollectionUtils.isNotEmpty(recordDetails)) {
            // 计算已开票中金额
            completedInvoiceAmount = recordDetails.stream()
                    .filter(e -> e.isCompletedInvoice())
                    .mapToLong(e -> e.getAmount())
                    .sum();
        }
        this.manageDetailDTO.setCompletedInvoiceAmount(completedInvoiceAmount);
    }

    public long getExpectedInvoiceAmount() {
        return this.manageDetailDTO.getExpectedInvoiceAmount();
    }
}
