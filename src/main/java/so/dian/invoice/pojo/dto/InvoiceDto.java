package so.dian.invoice.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class InvoiceDto {

    private Integer id;

    private String invoiceCode;

    private String invoiceNo;

    private String supplierNo;

    private String supplierName;

    private Integer subjectType;

    private String subjectName;

    private String subjectTypeStr;

    private Integer type;

    private String typeStr;

    private String billNo;

    private BigDecimal rawPrice;

    private BigDecimal taxPrice;

    private BigDecimal price;

    private BigDecimal taxRate;

    private String memo;

    private Integer status;

    private String statusStr;

    private BigDecimal usedAmount;

    private BigDecimal remainderAmount;

    @JSONField(format = "yyyy-MM-dd")
    private Date receiveTime;

    /**
     * 发票日期
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date gmtCreate;

    @JSONField(format = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 录入时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 复核时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    private Integer isDelete;

    private Integer creator;

    private String creatorNick;

    private Integer source;

    private String sourceStr;

    // 发票验真状态
    private String isRealStr;

    private String businessNo;
    private String kind;

    private String checkCode;

    private BigDecimal tax;

    private String seller;

    private String sellerTaxId;

    private String buyer;

    private String buyerTaxId;

    private String url;

    private Integer isReal;

    private String batchNo;

    /**
     * 从属主体类型
     */
    private Integer belongSubjectType;

    /**
     * 从属主体ID
     */
    private Integer belongSubjectId;

    private List<InvoiceDetailDto> invoiceDetailDtoList;

    /**
     * 购买方英译
     */
    private String companySubject;

    // 购买方中文名
    private String companySubjectStr;

    private String details;

    /**
     * 验真错误码
     */
    private String validateCode;

    /**
     * 验真错误码文本
     */
    private String validateCodeStr;

    // 流转状态code
    private Integer processStatus;

    // 流转状态描述
    private String processStatusStr;

    // 是否在质检池
    private Integer inCheckPool;

    private String isInCheckPoolStr;

    // 复核意见
    private String reviewSuggestion;

    // 审核人
    private String reviewerName;



    private ExpressInfo expressInfo;

    private Boolean canRollBack;

    @Data
    public static class ExpressInfo {
        /**
         * 快递名称
         */
        private String expressName;

        /**
         * 快递单号
         */
        private String expressNo;
    }

}
