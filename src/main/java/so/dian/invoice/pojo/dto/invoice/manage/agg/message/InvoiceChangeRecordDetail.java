package so.dian.invoice.pojo.dto.invoice.manage.agg.message;

import lombok.Data;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.common.NumberScaleConstant;
import so.dian.invoice.enums.ChangeTypeEnum;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDetailDTO;
import so.dian.invoice.util.FractionUtils;
import so.dian.invoice.util.LongUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 发票系统消息凭证表
 * @TableName invoice_news_voucher
 */
@Data
public class InvoiceChangeRecordDetail {

    private String productCode;

    private Integer productCount;

    /**
     * 此产品的业务单金额
     */
    private Long amount;

    /**
     * 原始应开票金额
     */
    private Long originExpectedInvoiceAmount;

    /**
     * 此产品本次变更的金额，计算结果
     */
    private Long spuChangeAmount;

    private String changeType;

    /**
     *
     * 变更金额精确到两位小数，根据SPU产品维度汇总
     * 核心逻辑为还款金额平摊在各产品上，需判断当前产品的业务单金额-该产品已有的应开票金额是否>=还款金额*（该产品类型的业务单金额/业务单总金额）
     * 若是，则产品变更金额计算=还款金额*（该产品类型的业务单金额/业务单总金额）
     * 若否，则产品变更金额计算=业务单金额-当前已有的应开票金额
     * 取余说明
     * 设备交易订单若存在多种产品信息时，每期还款计算最后一个产品类型的变更金额需用还款金额-其他产品已计算好的变更金额之和
     * 最后一个产品类型的挑选规则：如果还款金额-其他产品已计算好的变更金额之和＞当前产品的业务单金额-当前已有的应开票金额，则该产品无法作为最后一个产品类型进行取余，需替换成其他产品类型
     * 小数点说明：所有计算四舍五入保留两位小数
     * 变更总金额为所有SPU的加总
     * @param totalAmount  业务单总金额
     * @param changeAmount 本次变动金额
     */
    public Long calProductChangeAmount(Long totalAmount, Long changeAmount){
        spuChangeAmount = calculate(totalAmount, changeAmount);
        return spuChangeAmount;
    }

    private Long calculate(Long totalAmount, Long changeAmount){
        long calChangeAmountResult = this.calculateChangeAmount(totalAmount, changeAmount);

        ChangeTypeEnum changeTypeEnum = ChangeTypeEnum.getByCode(changeType);
        if (Objects.isNull(changeTypeEnum)) {
            throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "业务变更类型不正确:changeType:"+changeType);
        }
        // 如果是 增加 类型，需判断当前产品的业务单金额-该产品已有的应开票金额是否>=变动金额*（该产品类型的业务单金额/业务单总金额）
        if (ChangeTypeEnum.INCREASE == changeTypeEnum) {
            //需判断当前产品的业务单金额-该产品已有的应开票金额是否>=变动金额*（该产品类型的业务单金额/业务单总金额）
//            long result = this.amount - originExpectedInvoiceAmount;
            long result = LongUtils.subtract(this.amount, originExpectedInvoiceAmount);
            if (result >= calChangeAmountResult) {
                return calChangeAmountResult;
            }
            return result;
        }
        return calChangeAmountResult;
    }

    /**
     * 计算产品变更金额
     * 还款金额*（该产品类型的业务单金额/业务单总金额）
     * @param totalAmount  业务单总金额
     * @param changeAmount 本次变动金额
     * @return
     */
    private Long calculateChangeAmount(Long totalAmount, Long changeAmount){
        //此产品的业务单金额
        BigDecimal amount = FractionUtils.conventAmountInFen(this.amount);
        //业务单总金额
        BigDecimal totalAmountN = FractionUtils.conventAmountInFen(totalAmount);
        //还款或退款金额
        BigDecimal changeAmountN = FractionUtils.conventAmountInFen(changeAmount);
        ///（该产品类型的业务单金额/业务单总金额） 比例值
        BigDecimal bigDecimal = FractionUtils.divide(amount, totalAmountN, NumberScaleConstant.TEN_SCALE);
        //本次变动金额
        BigDecimal decimal = FractionUtils.multiply(changeAmountN, bigDecimal);
        long t = FractionUtils.conventAmountToFen(decimal);
        return t;
    }

    /**
     * 待还款金额
     * @return
     */
    public Long calPendingRepayAmount(){
//        return amount - originExpectedInvoiceAmount;
        return LongUtils.subtract(amount, originExpectedInvoiceAmount);
    }

    public InvoiceChangeRecordDetailDTO toDTO(){
        InvoiceChangeRecordDetailDTO dto = new InvoiceChangeRecordDetailDTO();
        dto.setProductCode(this.productCode);
        dto.setProductCount(this.productCount);
        return dto;
    }
}