package so.dian.invoice.pojo.dto.invoice.manage.agg;

import lombok.Data;
import so.dian.invoice.enums.InvoiceRequestStatusEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDetailDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:54
 */
@Data
public class InvoiceRequestRecord {

    /**
     * 申请开票记录ID
     */
    private Long requestRecordId;
    /**
     * 申请开票金额
     */
    private Long amount;
    /**
     * 开票申请记录id，单次申请 记录和发票管理id是一对一关系
     */
    private InvoiceRequestRecordDTO requestRecordDTO;
    /**
     * 开票申请记录详情，单次申请 记录详情和发票管理详情是一对一关系
     */
    private List<InvoiceRequestRecordDetailDTO> requestRecordDetailDTOS;
    /**
     * 开票申请勾选的业务单
     */
    private InvoiceManage invoiceManage;


    public static InvoiceRequestRecord build(Long amount,InvoiceManage invoiceManage)
    {
        InvoiceRequestRecord record = new InvoiceRequestRecord();
        record.setAmount(amount);

        record.setInvoiceManage(invoiceManage);
        // 初始化申请开票记录DTO
        record.initInvoiceRequestRecordDTO();
        // 初始化申请开票记录详情
        record.initInvoiceRequestRecordDetailDTO();

        record.setRequestRecordId(record.getRequestRecordDTO().getId());
        return record;
    }

    /**
     * 校验开票申请是否合法
     * 校验开票金额是否一致
     */
    public boolean check() {
	// 校验开票金额是否正确
	// 勾选的单据，待开票金额
	Long pendingInvoiceAmount = invoiceManage.getPendingInvoiceAmount();
	// 待开票金额和申请开票金额是否一致
	if (!pendingInvoiceAmount.equals(amount)) {
            return false;
	}
        return true;
    }

    private void initInvoiceRequestRecordDTO() {
        InvoiceManageDTO manageDTO = invoiceManage.getInvoiceManageDTO();
        InvoiceRequestRecordDTO requestRecordDTO = new InvoiceRequestRecordDTO();
        requestRecordDTO.setManageId(manageDTO.getId());
        requestRecordDTO.setAmount(manageDTO.getPendingInvoiceAmount());
        requestRecordDTO.setBizNo(manageDTO.getBizNo());
        requestRecordDTO.setStatus(InvoiceRequestStatusEnum.PENDING_INVOICE.getCode());

        this.requestRecordDTO = requestRecordDTO;
    }


    private void initInvoiceRequestRecordDetailDTO() {

        requestRecordDetailDTOS = new ArrayList<>();
        for(InvoiceManageDetail manageDetail : invoiceManage.getManageDetails()){
            InvoiceManageDetailDTO manageDetailDTO = manageDetail.getManageDetailDTO();
            InvoiceRequestRecordDetailDTO requestRecordDetailDTO = new InvoiceRequestRecordDetailDTO();
            requestRecordDetailDTO.setManageId(invoiceManage.getManageId());
            requestRecordDetailDTO.setManageDetailId(manageDetailDTO.getId());
//            requestRecordDetailDTO.setRequestRecordId(recordId);
            requestRecordDetailDTO.setProductCode(manageDetailDTO.getProductCode());
            requestRecordDetailDTO.setProductCount(manageDetailDTO.getProductCount());
            requestRecordDetailDTO.setAmount(manageDetailDTO.getPendingInvoiceAmount());
            requestRecordDetailDTO.setStatus(InvoiceRequestStatusEnum.PENDING_INVOICE.getCode());

            requestRecordDetailDTOS.add(requestRecordDetailDTO);
        }
    }

}
