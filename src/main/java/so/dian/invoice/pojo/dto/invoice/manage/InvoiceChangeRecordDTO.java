package so.dian.invoice.pojo.dto.invoice.manage;

import lombok.Data;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDO;

import java.util.List;

/**
 * 开票变更记录
 */
@Data
public class InvoiceChangeRecordDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 外部业务ID
     */
    private String outBizId;

    /**
     * 外部业务类型，1 采购付款 2 维修完成 3 采购取消 4 授信还款退款 5 授信还款
     */
    private Integer outBizType;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 变更类型： increase 增加 ，decrease 减少
     */
    private String type;

    /**
     * 变更金额
     */
    private Long amount;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    private List<InvoiceChangeRecordDetailDTO> changeRecordDetailDTOList;


//    public void initType(Integer bizType, String outBizId) {
//        this.setOutBizId(outBizId);
//        if(bizType.equals(1)){
//            this.setOutBizType(OutBizTypeEnum.CREDIT_REPAYMENT_SUCCESS.getCode());
//            this.setType(ChangeTypeEnum.INCREASE.getCode());
//        }else {
//            this.setOutBizType(OutBizTypeEnum.CREDIT_REPAYMENT_REFUND.getCode());
//            this.setType(ChangeTypeEnum.DECREASE.getCode());
//
//        }
//        for (InvoiceChangeRecordDetailDTO detailDTO : changeRecordDetailDTOList) {
//            detailDTO.setType(type);
//        }
//    }

//    public void initType(String type){
//        this.type = type;
//        if(CollectionUtils.isNotEmpty(changeRecordDetailDTOList)){
//            for (InvoiceChangeRecordDetailDTO detailDTO : changeRecordDetailDTOList) {
//                detailDTO.setType(type);
//            }
//        }
//    }
    public static InvoiceChangeRecordDTO fromDO(InvoiceChangeRecordDO model) {
        InvoiceChangeRecordDTO dto = new InvoiceChangeRecordDTO();
        BeanUtils.copyProperties(model, dto);

        return dto;
    }

    public InvoiceChangeRecordDO toDO() {
        InvoiceChangeRecordDO model = new InvoiceChangeRecordDO();
        BeanUtils.copyProperties(this, model);

        return model;
    }
}