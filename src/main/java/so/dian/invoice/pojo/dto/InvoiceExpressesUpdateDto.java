package so.dian.invoice.pojo.dto;

import lombok.Data;

import java.util.List;
@Data
public class InvoiceExpressesUpdateDto {

        private List<Long> ids;

        /**
         * '物流id',
         */
        private Long        expressId;
        /**
         *  '物流名称（冗余）',
         */
        private String         expressName ;
        /**
         * '快递单号',
         */
        private String          expressTrackingNo ;
        /**
         * '流转状态（0-已录入；1-已寄出；2-财务复合通过；3-财务复合不通过）'
         */
        private Integer           processStatus ;
        //'是否删除；0否，1是',
        private Integer            deleted;
        /**
         * 序列化
         */
        private String serialNo;
        /**
         * 操作人id
         */
        private Long operatorId;
        /**
         * 操作人name
         */
        private String operatorName;
        /**
         * 快递图片url
         */
        private String expressImg;


}
