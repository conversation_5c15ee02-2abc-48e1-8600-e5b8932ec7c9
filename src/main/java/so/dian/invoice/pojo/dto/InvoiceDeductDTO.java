package so.dian.invoice.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * InovoiceCanRollbackDTO
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 10:28
 */
@ApiModel(value = "InvoiceDeductDTO", description = "InvoiceDeductDTO")
@Data
public class InvoiceDeductDTO implements Serializable {

    @ApiModelProperty(value = "主键", required = true)
    private Integer id;

    @ApiModelProperty(value = "业务单号", required = true)
    private String businessNo;

    @ApiModelProperty(value = "核销人", required = true)
    private String deductUser;

    @ApiModelProperty(value = "核销时间", required = true)
    private String deductDate;

    @ApiModelProperty(value = "核销金额", required = true)
    private BigDecimal deductAmount;

}
