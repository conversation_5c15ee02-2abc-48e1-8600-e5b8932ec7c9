package so.dian.invoice.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class ScmInvoiceInfoDTO {
    @ApiModelProperty("发票ID")
    private Long id;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    private String invoiceNo;

    @ApiModelProperty("销售方名称")
    private String sellerName;

    @ApiModelProperty("购买方名称")
    private String buyerName;

    @ApiModelProperty("开票日期")
    private Long invoiceDate;

    @ApiModelProperty("发票图片")
    private String invoiceImg;

    @ApiModelProperty("业务类型")
    private Integer businessType;
    private String businessTypeStr;

    /**
     * 发票类型 增值税专用发票、增值税电子专用发票、增值税普通发票、增值税电子普通发票
     */
    @ApiModelProperty("发票类型")
    private Integer invoiceType;

    @ApiModelProperty("发票状态")
    private String statusStr;

    @ApiModelProperty("价税合计（总金额包括税额）")
    private BigDecimal price;

    @ApiModelProperty("已核销金额")
    private BigDecimal usedAmount;

    @ApiModelProperty("未核销金额")
    private BigDecimal remainAmount;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("对账单号列表")
    private List<String> verifyBillNoList;
}
