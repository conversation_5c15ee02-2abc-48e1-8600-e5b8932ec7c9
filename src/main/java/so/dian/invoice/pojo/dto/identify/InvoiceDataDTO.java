package so.dian.invoice.pojo.dto.identify;

import lombok.Data;

import java.util.List;

@Data
public class InvoiceDataDTO {
    private String version;
    private Integer result;
    private Long timestamp;
    private String message;
    private String id;//识别结果标识id,用于结果反馈
    private String sha1;//识别图片唯一标识
    private String time_cost;//识别花费的时长，单位毫秒
    private List<DetailAndExtraDTO> identify_results;//识别信息
}
