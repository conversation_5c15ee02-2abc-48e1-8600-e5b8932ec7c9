package so.dian.invoice.pojo.dto.invoice.manage.agg.message;

import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;
import so.dian.invoice.enums.ChangeTypeEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDetailDTO;
import so.dian.invoice.util.LongUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 发票系统消息凭证表
 * @TableName invoice_news_voucher
 */
@Data
public class InvoiceChangeRecord {

    public Long manageId;

    /**
     * 外部业务ID
     */
    private String outBizId;

    /**
     * 外部业务类型，1 采购付款 2 维修完成 3 采购取消 4 授信还款退款 5 授信还款
     */
    private Integer outBizType;

    /**
     * 业务单总金额
     */
    private Long totalAmount;

    /**
     * 本次首付、还款或退款金额
     */
    private Long changeAmount;

    private String changeType;

    private List<InvoiceChangeRecordDetail> changeDetails;

//    public static InvoiceChangeRecord build(Long manageId, Long totalAmount, Long changeAmount, String changeType) {
//        InvoiceChangeRecord invoiceChangeRecord = new InvoiceChangeRecord();
//        invoiceChangeRecord.setManageId(manageId);
//        invoiceChangeRecord.setTotalAmount(totalAmount);
//        invoiceChangeRecord.setChangeAmount(changeAmount);
//        invoiceChangeRecord.setChangeType(changeType);
//        return invoiceChangeRecord;
//    }

    /**
     * 1.计算采购单的 待还款金额 = sum(各产品的业务单金额 - 该产品已有的应开票金额)，记为  pendingPayAmount。
     * 2.判断 pendingPayAmount == 本次还款金额
     * 3.如果是：则各产品的 还款金额 = 各产品的业务单金额 - 该产品已有的应开票金额
     * 4.如果否：需判断 当前产品的业务单金额-该产品已有的应开票金额是否 >= 还款金额*（该产品类型的业务单金额/业务单总金额）
     * 4.1 如果是，则返回 还款金额*（该产品类型的业务单金额/业务单总金额）
     * 4.2 否则返回：业务单金额-该产品已有的应开票金额
     * 5.最后一个产品类型的变更金额需用还款金额-其他产品已计算好的变更金额之和
     */
    public void calSpuChangeAmount(){
        //还款金额 = 采购单的剩余待还款金额
        long totalPendingRepayAmount = changeDetails.stream().mapToLong(e -> e.calPendingRepayAmount()).sum();
        if (ChangeTypeEnum.INCREASE.getCode().equals(changeType)
                && Objects.nonNull(changeAmount)
                && NumberUtils.compare(totalPendingRepayAmount, changeAmount) == 0) {
            for(InvoiceChangeRecordDetail detail : changeDetails){
                //计算每类产品的变更金额， 最后一个产品类型的变更金额需用还款金额-其他产品已计算好的变更金额之和
                // 防止出现 产品已计算好的变更金额之和 和 变更金额 对不上的问题
                detail.setSpuChangeAmount(detail.calPendingRepayAmount());
            }
        }else {
            int i = 1;
            long spuChangeAmountTotal = 0l;
            for(InvoiceChangeRecordDetail detail : changeDetails){
                //计算每类产品的变更金额， 最后一个产品类型的变更金额需用还款金额-其他产品已计算好的变更金额之和
                // 防止出现 产品已计算好的变更金额之和 和 变更金额 对不上的问题
                long spuChangeAmount = 0l;
                if(i == changeDetails.size()){
//                    spuChangeAmount = changeAmount - spuChangeAmountTotal;
                    spuChangeAmount = LongUtils.subtract(changeAmount, spuChangeAmountTotal);
                    detail.setSpuChangeAmount(spuChangeAmount);
                }else {
                    spuChangeAmount = detail.calProductChangeAmount(totalAmount, changeAmount);
                    spuChangeAmountTotal += spuChangeAmount;
                }
                i++;
            }
        }
    }

    public InvoiceChangeRecordDTO toDTO(){
        InvoiceChangeRecordDTO recordDTO = new InvoiceChangeRecordDTO();
        recordDTO.setManageId(manageId);
        recordDTO.setOutBizId(outBizId);
        recordDTO.setOutBizType(outBizType);
        recordDTO.setAmount(changeAmount);
        recordDTO.setType(changeType);
        List<InvoiceChangeRecordDetailDTO> list = new ArrayList<>();
        for(InvoiceChangeRecordDetail detail : changeDetails){
            InvoiceChangeRecordDetailDTO detailDTO = detail.toDTO();
            detailDTO.setManageId(manageId);
            detailDTO.setType(changeType);
            if (Objects.isNull(detail.getSpuChangeAmount())) {
                detailDTO.setAmount(NumberUtils.LONG_ZERO);
            }else {
                detailDTO.setAmount(detail.getSpuChangeAmount());
            }
            list.add(detailDTO);
        }
        recordDTO.setChangeRecordDetailDTOList(list);
        return recordDTO;
    }


}