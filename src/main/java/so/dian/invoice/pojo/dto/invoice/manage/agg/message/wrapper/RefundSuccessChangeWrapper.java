package so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import so.dian.fis.credit.dto.response.RefundSuccessMessageDetailDTO;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.ChangeTypeEnum;
import so.dian.invoice.enums.OutBizTypeEnum;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecord;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecordDetail;
import so.dian.invoice.util.LongUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-28 11:09
 */
@Slf4j
@Setter
public class RefundSuccessChangeWrapper extends AbstractChangeWrapper<RefundSuccessMessageDetailDTO>{


    private InvoiceManageDTO originInvoiceManageDTO;

    public RefundSuccessChangeWrapper(RefundSuccessMessageDetailDTO refundSuccessMessageDTO, InvoiceManageDTO originInvoiceManageDTO) {
        this.messageDTO = refundSuccessMessageDTO;
	this.originInvoiceManageDTO = originInvoiceManageDTO;
    }

    @Override
    public InvoiceManageDTO buildInvoiceManageDTO() {
	return buildManageDTO();
    }

    @Override
    public InvoiceChangeRecord buildInvoiceChangeRecord() {
	InvoiceChangeRecord changeRecord = new InvoiceChangeRecord();
	changeRecord.setOutBizId(String.valueOf(messageDTO.getRecordId()));
	changeRecord.setOutBizType(OutBizTypeEnum.CREDIT_REPAYMENT_REFUND.getCode());
	changeRecord.setChangeType(ChangeTypeEnum.DECREASE.getCode());

	changeRecord.setTotalAmount(originInvoiceManageDTO.getTotalAmount());
	changeRecord.setChangeAmount(messageDTO.getAmount());

	List<InvoiceChangeRecordDetail> changeRecordDetails = new ArrayList<>();
	for(InvoiceManageDetailDTO manageDetailDTO : originInvoiceManageDTO.getManageDetailDTOS()){
	    InvoiceChangeRecordDetail changeRecordDetail = new InvoiceChangeRecordDetail();
	    changeRecordDetail.setProductCode(manageDetailDTO.getProductCode());
	    changeRecordDetail.setProductCount(manageDetailDTO.getProductCount());
	    changeRecordDetail.setAmount(manageDetailDTO.getAmount());
	    changeRecordDetail.setOriginExpectedInvoiceAmount(manageDetailDTO.getExpectedInvoiceAmount());
	    changeRecordDetail.setChangeType(ChangeTypeEnum.DECREASE.getCode());

	    changeRecordDetails.add(changeRecordDetail);
	}
	changeRecord.setChangeDetails(changeRecordDetails);
	return changeRecord;
    }


    public InvoiceManageDTO buildManageDTO() {
        InvoiceManageDTO dto = new InvoiceManageDTO();
	BeanUtils.copyProperties(originInvoiceManageDTO, dto);

        List<InvoiceManageDetailDTO> manageDetailDTOS = new ArrayList<>();

	Map<String, InvoiceChangeRecordDetail> map = invoiceChangeRecord.getChangeDetails().stream()
		.collect(Collectors.toMap(InvoiceChangeRecordDetail::getProductCode, Function.identity(),(v1, v2) -> v1));
        for(InvoiceManageDetailDTO invoiceManageDetailDTO : originInvoiceManageDTO.getManageDetailDTOS()){

	    InvoiceManageDetailDTO manageDetailDTO = new InvoiceManageDetailDTO();
	    BeanUtils.copyProperties(invoiceManageDetailDTO, manageDetailDTO);

	    InvoiceChangeRecordDetail recordDetail = map.get(invoiceManageDetailDTO.getProductCode());
	    if (recordDetail == null){
		log.info("记录不存在，记录：{}", invoiceManageDetailDTO.getProductCode());
		throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "变更记录详情不存在:productCode:"+invoiceManageDetailDTO.getProductCode());
	    }
//	    Long temp = invoiceManageDetailDTO.getExpectedInvoiceAmount() - recordDetail.getSpuChangeAmount();
	    Long temp = LongUtils.subtract(invoiceManageDetailDTO.getExpectedInvoiceAmount() , recordDetail.getSpuChangeAmount());
	    manageDetailDTO.setExpectedInvoiceAmount(temp);

            manageDetailDTOS.add(manageDetailDTO);
        }
        dto.setManageDetailDTOS(manageDetailDTOS);
        return dto;
    }
}
