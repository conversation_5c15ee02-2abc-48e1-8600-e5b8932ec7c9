package so.dian.invoice.pojo.dto.identify;

import lombok.Data;

import java.util.List;

@Data
public class DetailsDTO {
    private String code;//发票代码
    private String number;//发票号码
    private String date;//开票日期
    private String pretax_amount;//税前金额
    private String total;//总金额
    private String tax;//税额
    private String check_code;//校验码
    private String seller;//销售方名称
    private String seller_tax_id;//销售方纳税人识别号
    private String buyer;//购买方方名称
    private String buyer_tax_id;//购买方纳税人识别号
    private String company_seal;//是否有公司印章（0：没有； 1： 有）
    private String form_type;//发票是第几联
    private String form_name;//发票联次
    private String kind;//发票消费类型
    private String ciphertext;//密码区,四行密码,每行以逗号隔开
    private String remark; //备注
    private List<DetailItemsDTO> items; //发票详细信息
    private String service_name; //发票条目
    /**
     * 条目名称
     */
    private String item_names;
}
