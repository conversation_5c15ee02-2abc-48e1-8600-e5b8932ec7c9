package so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.BizTypeEnum;
import so.dian.invoice.enums.InvoiceManageStatusEnum;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecord;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecordDetail;
import so.dian.invoice.util.FractionUtils;
import so.dian.invoice.util.LongUtils;
import so.dian.taishan.client.pojo.mq.TradeOrderChangeDTO;
import so.dian.taishan.client.pojo.mq.TradeOrderDetailDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-28 11:09
 */
@Slf4j
public class TradeOrderChangeWrapper extends AbstractChangeWrapper<TradeOrderChangeDTO>{

    private InvoiceManageDTO originInvoiceManageDTO;

    // 首付金额，当采购单是授信支付的时候
    // 当采购单非授信支付的时候，此字段值为null。
    private Long changeAmount;

    private Integer outBizType;

    private String changeType;


    public TradeOrderChangeWrapper(TradeOrderChangeDTO tradeOrderChangeDTO) {
        this.messageDTO = tradeOrderChangeDTO;
    }

    public TradeOrderChangeWrapper originManage(InvoiceManageDTO originInvoiceManageDTO){
	this.originInvoiceManageDTO = originInvoiceManageDTO;
	return this;
    }

    public TradeOrderChangeWrapper outBizType(Integer outBizType){
	this.outBizType = outBizType;
	return this;
    }

    public TradeOrderChangeWrapper changeType(String changeType){
	this.changeType = changeType;
	return this;
    }

    public TradeOrderChangeWrapper changeAmount(Long changeAmount){
	this.changeAmount = changeAmount;
	return this;
    }

    @Override
    public InvoiceManageDTO buildInvoiceManageDTO() {
	//新增采购单，直接生成开票管理
	if (Objects.isNull(originInvoiceManageDTO)) {
	    return buildManageDTO();
	}
	//采购单退款，基于已有的开票管理更新产品的应开金额
	return buildManageDTOForOrigin();
    }

//    public TradeOrderChangeWrapper buildInvoiceChangeRecord() {
//	invoiceChangeRecord = this.doInvoiceChangeRecord();
//	invoiceChangeRecord.initSpuChangeAmount();
//	return this;
//    }

    /**
     * 生成开票记录
     * 基于采购单、采购退款消息生成 变更记录
     * 在采购单存在拆单的情况，也就是存在多个商品，但商品的SPU相同，CSPU不同，所以在处理的时候，需要先对商品按照SPU维度进行分组合并
     *
     * 后续的开票管理 InvoiceManageDTO及InvoiceChangeRecordDTO都是基于invoiceChangeRecord生成
     * @return
     */
    @Override
    public InvoiceChangeRecord buildInvoiceChangeRecord() {
	InvoiceChangeRecord changeRecord = new InvoiceChangeRecord();
	changeRecord.setOutBizId(messageDTO.getOrderNo());
	changeRecord.setOutBizType(outBizType);
	changeRecord.setChangeType(changeType);
	//实收金额，扣除运费
	Long amountFen = this.getTotalAmount();
	changeRecord.setTotalAmount(amountFen);
	//
	Long tempChangeAmount = changeAmount;
	if (Objects.isNull(changeAmount)) {
	    tempChangeAmount = this.getTotalAmount();
	}
	changeRecord.setChangeAmount(tempChangeAmount);
	List<InvoiceChangeRecordDetail> changeRecordDetails = new ArrayList<>();
	if (CollectionUtils.isEmpty(messageDTO.getOrderDetailDTOList())) {
	    throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "交易单消息体不正确，无商品详情");
	}
	for(TradeOrderDetailDTO tradeOrderDetailDTO : messageDTO.getOrderDetailDTOList()){
	    //异常情况处理
	    if (Objects.isNull(tradeOrderDetailDTO)
		    || StringUtils.isBlank(tradeOrderDetailDTO.getSpuCode())
		    || Objects.isNull(tradeOrderDetailDTO.getSettleTotalAmount())) {
		throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "交易单消息体不正确，无商品详情或无SPUCODE或结算金额为null");
	    }
	    Long settleTotalAmount = FractionUtils.conventAmountToFen(tradeOrderDetailDTO.getSettleTotalAmount());

	    InvoiceChangeRecordDetail changeRecordDetail = new InvoiceChangeRecordDetail();
	    changeRecordDetail.setProductCode(tradeOrderDetailDTO.getSpuCode());
	    changeRecordDetail.setProductCount(tradeOrderDetailDTO.getQuantity());
	    changeRecordDetail.setAmount(settleTotalAmount);
	    changeRecordDetail.setOriginExpectedInvoiceAmount(0l);
	    changeRecordDetail.setChangeType(changeType);

	    changeRecordDetails.add(changeRecordDetail);
	}
	List<InvoiceChangeRecordDetail> margedDetails = changeRecordDetails;
	//存在多个商品，但商品的SPU相同，CSPU不同，所以在处理的时候，需要先对商品按照SPU维度进行分组合并
	if (CollectionUtils.isNotEmpty(changeRecordDetails)) {
	    margedDetails = mergeInvoiceDetails(margedDetails);
	}
	changeRecord.setChangeDetails(margedDetails);
	return changeRecord;
    }

    /**
     * 基于invoiceChangeRecord 生成开票管理
     * @return
     */
    public InvoiceManageDTO buildManageDTO() {
        InvoiceManageDTO dto = new InvoiceManageDTO();
        dto.setBizNo(messageDTO.getOrderNo());
        dto.setBizType(BizTypeEnum.PURCHASE_ORDER.code());
        dto.setSubjectId(Long.valueOf(messageDTO.getBuyerId()));
        dto.setStatus(InvoiceManageStatusEnum.PENDING_INVOICE.getCode());

        dto.setTotalAmount(this.getTotalAmount());
        if (Objects.nonNull(messageDTO.getCreateTime())) {
            dto.setBizCreateTime(messageDTO.getCreateTime().getTime());
        }else {
            log.info("订单创建时间为空:bizNo:{}, bizType:{}", dto.getBizNo(), dto.getBizType());
        }
        if (Objects.nonNull(messageDTO.getPayTime())) {
            dto.setPaymentTime(messageDTO.getPayTime().getTime());
        }else {
            log.info("订单支付时间为空:bizNo:{}, bizType:{}", dto.getBizNo(), dto.getBizType());
        }

	List<InvoiceManageDetailDTO> manageDetailDTOS = new ArrayList<>();
	for(InvoiceChangeRecordDetail invoiceChangeRecordDetail : invoiceChangeRecord.getChangeDetails()){
	    InvoiceManageDetailDTO manageDetailDTO = new InvoiceManageDetailDTO();
	    manageDetailDTO.setProductCode(invoiceChangeRecordDetail.getProductCode());
	    manageDetailDTO.setProductCount(invoiceChangeRecordDetail.getProductCount());
	    manageDetailDTO.setAmount(invoiceChangeRecordDetail.getAmount());
	    manageDetailDTO.setOriginAmount(invoiceChangeRecordDetail.getAmount());
	    manageDetailDTO.setExpectedInvoiceAmount(invoiceChangeRecordDetail.getSpuChangeAmount());

	    manageDetailDTOS.add(manageDetailDTO);
	}
	dto.setManageDetailDTOS(manageDetailDTOS);
        return dto;
    }

    /**
     * 采购单发生退款的时候，需要基于已有的开票管理和invoiceChangeRecord
     * 并更新产品的应开金额
     * @return
     */
    public InvoiceManageDTO buildManageDTOForOrigin() {
	InvoiceManageDTO dto = new InvoiceManageDTO();
	BeanUtils.copyProperties(originInvoiceManageDTO, dto);
	List<InvoiceManageDetailDTO> manageDetailDTOS = new ArrayList<>();

	Map<String, InvoiceChangeRecordDetail> map = invoiceChangeRecord.getChangeDetails().stream()
		.collect(Collectors.toMap(InvoiceChangeRecordDetail::getProductCode, Function.identity(),(v1, v2) -> v1));
	for(InvoiceManageDetailDTO invoiceManageDetailDTO : originInvoiceManageDTO.getManageDetailDTOS()){

	    InvoiceManageDetailDTO manageDetailDTO = new InvoiceManageDetailDTO();
	    BeanUtils.copyProperties(invoiceManageDetailDTO, manageDetailDTO);

	    InvoiceChangeRecordDetail recordDetail = map.get(invoiceManageDetailDTO.getProductCode());
	    if (recordDetail == null){
		log.info("变更记录详情不存在，记录：{}", invoiceManageDetailDTO.getProductCode());
		throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "变更记录详情不存在:productCode:"+invoiceManageDetailDTO.getProductCode());
	    }
	    //采购单发生退款的时候，需要基于已有的开票管理更新产品的应开金额
//	    Long temp = invoiceManageDetailDTO.getExpectedInvoiceAmount() - recordDetail.getSpuChangeAmount();
	    Long temp = LongUtils.subtract(invoiceManageDetailDTO.getExpectedInvoiceAmount(), recordDetail.getSpuChangeAmount());
	    manageDetailDTO.setExpectedInvoiceAmount(temp);

	    manageDetailDTOS.add(manageDetailDTO);
	}
	dto.setManageDetailDTOS(manageDetailDTOS);
	return dto;
    }

    /**
     * 计算订单总金额
     * @return
     */
    private Long getTotalAmount() {
	//实收金额，扣除运费
	BigDecimal logisticsFee = Objects.isNull(messageDTO.getLogisticsFee()) ? BigDecimal.ZERO : messageDTO.getLogisticsFee();
	BigDecimal amount = FractionUtils.subtract(messageDTO.getRealPayAmount(), logisticsFee);
	Long amountFen = FractionUtils.conventAmountToFen(amount);
	return amountFen;
    }


    /**
     * 合并商品维度
     * @param details
     * @return
     */
    private static List<InvoiceChangeRecordDetail> mergeInvoiceDetails(List<InvoiceChangeRecordDetail> details) {
	return details.stream()
		.collect(Collectors.groupingBy(
			InvoiceChangeRecordDetail::getProductCode,
			Collectors.reducing((d1, d2) -> {
			    InvoiceChangeRecordDetail merged = new InvoiceChangeRecordDetail();
			    BeanUtils.copyProperties(d1, merged);
			    merged.setProductCount(d1.getProductCount() + d2.getProductCount());
			    merged.setAmount(d1.getAmount() + d2.getAmount());
			    return merged;
			})
		))
		.values().stream()
		.filter(e -> e.isPresent())
		.map(e -> e.get())
		.collect(Collectors.toList());
    }
}
