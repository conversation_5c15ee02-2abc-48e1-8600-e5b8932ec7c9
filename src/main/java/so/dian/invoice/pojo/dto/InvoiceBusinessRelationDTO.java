package so.dian.invoice.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;
import lombok.Data;
import so.dian.invoice.enums.BusinessTypeEnum;
/**
 * 发票关联业务信息 <br/>
 * 
 * <AUTHOR>
 * @date 2019-11-04 15:17
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
public class InvoiceBusinessRelationDTO {

    /**
     * 业务单号
     */
    private String businessNo;
    /**
     * 业务类型
     *
     * @see BusinessTypeEnum
     */
    private Integer businessType;
    /**
     * 付款单号列表
     */
    private Set<String> payNoList;
    /**
     * 核销金额
     */
    private BigDecimal amount;
    /**
     * 创建人
     */
    private Long creator;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
