package so.dian.invoice.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class InvoiceExpressesDto {

    private Long id;

    private String invoiceCode;

    private String invoiceNo;

    private String billNo;

    private BigDecimal price;

    private BigDecimal usedAmount;

    private Integer status;


    private Date gmtCreate;

    private Date gmtModified;

    private Long creator;

    private String createName;

    private Date createTime;
    private  String subjectName;
    /**
     * 销售方
     */
    private String seller;
    private String batchNo;
    /**
     * 发票物流id
     */
    private Long expressesId;
    /**
     * 物流快递单号
     */
    private String expressTrackingNo;
    /**
     * 物流名称
     */
    private String expressName;
    /**
     * 物流状态
     */
    private Integer processStatus;


   }
