package so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecord;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-28 11:09
 */
@Slf4j
@Data
public abstract class AbstractChangeWrapper<T> {

    protected T messageDTO;

    protected InvoiceChangeRecord invoiceChangeRecord;

    public abstract InvoiceManageDTO buildInvoiceManageDTO();

    public abstract InvoiceChangeRecord buildInvoiceChangeRecord();

    public AbstractChangeWrapper calSpuChangeAmount() {
        invoiceChangeRecord = this.buildInvoiceChangeRecord();
        invoiceChangeRecord.calSpuChangeAmount();
        return this;
    }
}
