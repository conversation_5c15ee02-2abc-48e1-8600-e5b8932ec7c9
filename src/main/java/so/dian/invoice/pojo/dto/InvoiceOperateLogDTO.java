package so.dian.invoice.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

/**
 * 发票操作记录 <br/>
 *
 * <AUTHOR>
 * @date 2019-11-04 15:02
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
public class InvoiceOperateLogDTO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 发票id
     */
    private Integer invoiceId;
    /**
     * 日志类型
     */
    private String type;
    /**
     * 备注
     */
    private String comment;
    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
