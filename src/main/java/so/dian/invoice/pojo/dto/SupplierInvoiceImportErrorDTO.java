package so.dian.invoice.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import so.dian.invoice.annotation.ExcelEntity;
import so.dian.invoice.annotation.ExcelField;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/3/22 2:43 下午
 */
@Data
@ExcelEntity(tip = "供应商发票导入失败列表")
public class SupplierInvoiceImportErrorDTO {

    @ExcelField(headerAlias = "发票代码")
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;

    @ExcelField(headerAlias = "发票号码")
    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;

    @ExcelField(headerAlias = "提示信息")
    private String tip;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SupplierInvoiceImportErrorDTO)) return false;
        SupplierInvoiceImportErrorDTO that = (SupplierInvoiceImportErrorDTO) o;
        return Objects.equals(invoiceCode, that.invoiceCode) && Objects.equals(invoiceNo, that.invoiceNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(invoiceCode, invoiceNo);
    }

}
