package so.dian.invoice.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;
import java.util.Date;

public class InvoiceDetailDto {

    private Integer id;

    private String invoiceCode;

    private String invoiceNo;

    private Integer subjectType;

    private String subjectName;

    private String materialName;

    private String materialSpec;

    private BigDecimal unitPrice;

    private Integer quantity;

    private String unit;

    private BigDecimal rawPrice;

    private BigDecimal taxRate;

    private BigDecimal taxPrice;

    @JSONField(format = "yyyy-MM-dd")
    private Date gmtCreate;

    @JSONField(format = "yyyy-MM-dd")
    private Date gmtModified;

    private Integer isDelete;

    private String billNo;

    private BigDecimal price;

    private String supplierNo;

    private String supplierName;

    private Integer type;

    private String typeStr;

    private String memo;

    @JSONField(format = "yyyy-MM-dd")
    private Date receiveTime;

    private Integer source;

    /**
     * 从属主体类型（参考枚举：BelongSubjectTypeEnum）
     */
    private Integer belongSubjectType;

    /**
     * 从属主体ID
     */
    private Integer belongSubjectId;


    private String verifyBillNo;

    public String getVerifyBillNo() {
        return verifyBillNo;
    }

    public void setVerifyBillNo(String verifyBillNo) {
        this.verifyBillNo = verifyBillNo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo == null ? null : invoiceNo.trim();
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName == null ? null : materialName.trim();
    }

    public String getMaterialSpec() {
        return materialSpec;
    }

    public void setMaterialSpec(String materialSpec) {
        this.materialSpec = materialSpec == null ? null : materialSpec.trim();
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getRawPrice() {
        return rawPrice;
    }

    public void setRawPrice(BigDecimal rawPrice) {
        this.rawPrice = rawPrice;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxPrice() {
        return taxPrice;
    }

    public void setTaxPrice(BigDecimal taxPrice) {
        this.taxPrice = taxPrice;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeStr() {
        return typeStr;
    }

    public void setTypeStr(String typeStr) {
        this.typeStr = typeStr;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(Integer subjectType) {
        this.subjectType = subjectType;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public Integer getBelongSubjectType() {
        return belongSubjectType;
    }

    public void setBelongSubjectType(Integer belongSubjectType) {
        this.belongSubjectType = belongSubjectType;
    }

    public Integer getBelongSubjectId() {
        return belongSubjectId;
    }

    public void setBelongSubjectId(Integer belongSubjectId) {
        this.belongSubjectId = belongSubjectId;
    }
}
