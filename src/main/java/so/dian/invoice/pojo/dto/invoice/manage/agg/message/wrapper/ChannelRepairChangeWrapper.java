package so.dian.invoice.pojo.dto.invoice.manage.agg.message.wrapper;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.*;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecord;
import so.dian.invoice.pojo.dto.invoice.manage.agg.message.InvoiceChangeRecordDetail;
import so.dian.songshan.client.pojo.dto.ChannelRepairChangeDTO;
import so.dian.songshan.client.pojo.dto.ChannelRepairDetailDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-28 11:09
 */
@Slf4j
@Data
public class ChannelRepairChangeWrapper extends AbstractChangeWrapper<ChannelRepairChangeDTO>{


    public ChannelRepairChangeWrapper(ChannelRepairChangeDTO channelRepairChangeDTO) {
        this.messageDTO = channelRepairChangeDTO;
    }

    @Override
    public InvoiceManageDTO buildInvoiceManageDTO() {
	return buildManageDTO();
    }

    @Override
    public InvoiceChangeRecord buildInvoiceChangeRecord() {
	InvoiceChangeRecord record = new InvoiceChangeRecord();
	record.setOutBizId(messageDTO.getRepairNo());
	record.setOutBizType(OutBizTypeEnum.REPAIR_ORDER_COMPLETE.getCode());
	record.setChangeType(ChangeTypeEnum.INCREASE.getCode());
	record.setTotalAmount(Long.valueOf(messageDTO.getPayAmount()));
	record.setChangeAmount(record.getTotalAmount());
	List<InvoiceChangeRecordDetail> details = new ArrayList<>();
	if (CollectionUtils.isEmpty(messageDTO.getRepairDetailDTOList())) {
	    throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "维修单消息体不正确，无商品详情");
	}
	for(ChannelRepairDetailDTO repairDetailDTO : messageDTO.getRepairDetailDTOList()){
	    //异常情况处理
	    if (Objects.isNull(repairDetailDTO)
		    || StringUtils.isBlank(repairDetailDTO.getSpuCode())
		    || Objects.isNull(repairDetailDTO.getPayRepairFee())) {
		throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "维修单消息体不正确，无商品详情或无SPUCODE或维修金额为null");
	    }
	    InvoiceChangeRecordDetail changeRecordDetail = new InvoiceChangeRecordDetail();
	    changeRecordDetail.setProductCode(repairDetailDTO.getSpuCode());
	    changeRecordDetail.setProductCount(repairDetailDTO.getPayProductNum());
	    changeRecordDetail.setAmount(repairDetailDTO.getPayRepairFee().longValue());
	    changeRecordDetail.setOriginExpectedInvoiceAmount(0l);
	    changeRecordDetail.setChangeType(ChangeTypeEnum.INCREASE.getCode());

	    details.add(changeRecordDetail);
	}
	record.setChangeDetails(details);
	return record;
    }


    public InvoiceManageDTO buildManageDTO() {
	InvoiceManageDTO dto = new InvoiceManageDTO();
	dto.setBizNo(messageDTO.getRepairNo());
	dto.setBizType(BizTypeEnum.REPAIR_ORDER.getCode());
	dto.setSubjectId(Long.valueOf(messageDTO.getAgentId()));

	dto.setStatus(InvoiceManageStatusEnum.PENDING_INVOICE.getCode());
	dto.setTotalAmount(invoiceChangeRecord.getTotalAmount());
	if (Objects.nonNull(messageDTO.getCreateTime())) {
	    dto.setBizCreateTime(messageDTO.getCreateTime().getTime());
	}else {
	    log.warn("订单创建时间为空:bizNo:{}, bizType:{}", dto.getBizNo(), dto.getBizType());
	}
	if (Objects.nonNull(messageDTO.getPayTime())) {
	    dto.setPaymentTime(messageDTO.getPayTime().getTime());
	}else {
	    log.warn("订单支付时间为空:bizNo:{}, bizType:{}", dto.getBizNo(), dto.getBizType());
	}

	List<InvoiceManageDetailDTO> manageDetailDTOS = new ArrayList<>();
	for(InvoiceChangeRecordDetail changeRecordDetail : invoiceChangeRecord.getChangeDetails()){
	    InvoiceManageDetailDTO manageDetailDTO = new InvoiceManageDetailDTO();
	    manageDetailDTO.setProductCode(changeRecordDetail.getProductCode());
	    manageDetailDTO.setProductCount(changeRecordDetail.getProductCount());
	    manageDetailDTO.setAmount(changeRecordDetail.getAmount());
	    manageDetailDTO.setOriginAmount(changeRecordDetail.getAmount());
	    manageDetailDTO.setExpectedInvoiceAmount(changeRecordDetail.getSpuChangeAmount());

	    manageDetailDTOS.add(manageDetailDTO);
	}
	dto.setManageDetailDTOS(manageDetailDTOS);
	return dto;
    }
}
