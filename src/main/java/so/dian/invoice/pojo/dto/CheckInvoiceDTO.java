package so.dian.invoice.pojo.dto;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Auther: liangfang
 * @Date: 2020-04-21 19:44
 * @Since feature_20200416_liangfang_发票质检优化
 * @Description:
 */
@Data
@ToString
public class CheckInvoiceDTO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 结论ID
     */
    private Long conclusionId;
    /**
     * 质检备注
     */
    private String remark;

    /**
     * 质检状态
     */
    private Integer status;
    /**
     * 质检人
     */
    private Long checker;
    /**
     * 质检时间
     */
    private Date checkTime;
    /**
     * 发票ID
     */
    private Long invoiceId;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 关联物流单号
     */
    private String expressNo;
    /**
     * 录入人
     */
    private Long operator;

    private Integer beforeStatus;
    /**
     * 创建时间
     */
    private Long gmtCreate;
    /**
     * 更新时间
     */
    private Long gmtUpdate;
    /**
     * @see so.dian.commons.eden.enums.DeletedEnum
     */
    private Integer deleted;
    /**
     * 发票所属城市code
     */
    private Integer cityCode;
}
