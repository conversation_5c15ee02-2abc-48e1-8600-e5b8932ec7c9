package so.dian.invoice.pojo.dto.invoice.manage;

import lombok.Data;
import org.springframework.beans.BeanUtils;
import so.dian.invoice.pojo.entity.InvoiceOperateRecordDO;

/**
 * @program: invoice
 * @description: 发票操作记录DTO
 * @author: yuechuan
 * @create: 2025-09-04 14:30
 */
@Data
public class InvoiceOperateRecordDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 开票申请ID
     */
    private Long requestId;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 备注
     */
    private String remark;

    public static InvoiceOperateRecordDTO fromDO(InvoiceOperateRecordDO model) {
        InvoiceOperateRecordDTO dto = new InvoiceOperateRecordDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }

    public InvoiceOperateRecordDO toDO() {
        InvoiceOperateRecordDO model = new InvoiceOperateRecordDO();
        BeanUtils.copyProperties(this, model);
        return model;
    }
}
