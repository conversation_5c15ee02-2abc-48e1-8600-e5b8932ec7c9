package so.dian.invoice.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.Getter;

/**
 * InvoiceRollBackResultDTO
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 10:47
 */
@ApiModel(value = "InvoiceRollBackResultDTO", description = "InvoiceRollBackResultDTO")
@Data
public class InvoiceRollBackResultDTO implements Serializable {

    @ApiModelProperty(value = "发票核销id", required = true)
    private Long id;

    @ApiModelProperty(value = "发票回滚结果", required = true)
    private String retVal;

    @ApiModelProperty(value = "回滚状态", required = true)
    private Integer rollbackStatus;


    @Getter
    public static enum RollBackStatus {
        SUCCESS(1, "回滚成功"),
        FAILED(2, "回滚失败");
        private Integer code;

        private String desc;

        RollBackStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }


    }

}
