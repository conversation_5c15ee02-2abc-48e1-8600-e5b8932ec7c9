package so.dian.invoice.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckInvoiceBuyerDTO  {

    /**
     * 税号
     */
    private String buyerTaxId;

    /**
     * 购买方名称
     */
    private String buyer;
    /**
     * 类型
     */
    private Integer belongSubjectType;

    /**
     * agentId
     */
    private Long belongSubjectId;
}
