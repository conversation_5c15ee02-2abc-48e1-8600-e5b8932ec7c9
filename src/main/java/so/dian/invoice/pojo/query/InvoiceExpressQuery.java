package so.dian.invoice.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class InvoiceExpressQuery {
    private String batchNo;
    @NotNull(message = "录入人不能为空")
    private String creatorName;

    @NotNull(message = "录入人id")
    private Long creator;

    @NotNull(message = "发票录入开始日期不能为空")
    private String startCreateTime;
    @NotNull(message = "发票录入结束日期不能为空")
    private String endCreateTime;

    private String startGmtCreate;
    private String endGmtCreate;
    /**
     * 流程状态
     * @see InvoiceProcessStatusEnum
     */
    private Integer processStatus;
    private  Integer deleted;


}
