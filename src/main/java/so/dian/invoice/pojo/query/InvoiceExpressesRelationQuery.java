package so.dian.invoice.pojo.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class InvoiceExpressesRelationQuery {
    /**
     * '快递单号'
     */
    private String expressTrackingNo;
    /**
     * 查询开始时间
     */
    private Date startTime;
    /**
     * 查询结束时间
     */
    private Date endTime;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 操作人id
     *
     */
    private Long operatorId;
    /**
     * 快递图片
     */
    private String expressImg;
    /**
     * 操作流水号
     */
    private String serialNo;
    /**
     * 分页大小
     */
    private Integer pageSize;
    /**
     * 页数
     */
    private Integer pageNo;

    @JsonIgnore
    public Long getStart() {
        return Long.parseLong(this.pageSize.toString()) * (pageNo - 1);
    }
}
