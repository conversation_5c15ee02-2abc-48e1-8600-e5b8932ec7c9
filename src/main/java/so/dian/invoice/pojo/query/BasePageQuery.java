package so.dian.invoice.pojo.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jiaoge
 * @Date: 2019/8/28 5:14 PM
 * @Description:
 */
@Data
public class BasePageQuery implements Serializable{

	private static final long serialVersionUID = -3954606962542956613L;
	/**
	 * 分页大小
	 */
	private Integer pageSize;
	/**
	 * 页数
	 */
	private Integer pageNo;

	@JsonIgnore
	public Long getStart() {
		return Long.parseLong(this.pageSize.toString()) * (pageNo - 1);
	}
}
