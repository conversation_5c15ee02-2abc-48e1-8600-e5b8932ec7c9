package so.dian.invoice.pojo.vo;

import java.util.Date;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/9/27 11:04 AM
 * @Description:
 */
@Data
public class InvoiceIdentifyRecordVO {

	private Integer invoiceId;

	private String invoiceNo;

	private String invoiceCode;

	private Integer type;

	private String checkCode;

	private String rawPrice;

	private String price;

	private String tax;

	private String seller;

	private String sellerTaxId;

	private String buyer;

	private String buyerTaxId;

	private String kind;

	private Integer isReal;

	private Integer isRelated;

	private String url;

	private Date createTime;

	private Integer creator;

	private Date updateTime;

	private Integer updater;

	private Integer deleted;

	private String typeStr;

	private String isRealStr;

	private String batchNo;

	private String reason;

	private Integer isIdentify;

	private String isIdentifyStr;

	private String details;

	/**
	 * 提供给前端，开票时间
	 */
	private Date gmtCreate;
}
