package so.dian.invoice.pojo.vo;

import lombok.Data;

/**
 * @Author: chenan
 * @Date: 2020/9/23 16:28
 */
@Data
public class InvoiceIdentifyRecordShopVO {

//    private Integer id;

    private String invoiceNo;

    private String invoiceCode;

    private Integer invoiceType;

    private String invoiceDate;

    private String checkCode;

    private String pretaxAmount;

    private String total;

    private String tax;

    private String seller;

    private String sellerTaxId;

    private String buyer;

    private String buyerTaxId;

    private String kind;

//    private Integer isReal;

//    private Integer isRelated;

//    private String url;

//    private Date createTime;

//    private Integer creator;

//    private Date updateTime;

//    private Integer updater;

//    private Integer deleted;

    private String invoiceTypeStr;

//    private String isRealStr;

//    private String batchNo;

//    private String reason;

//    private Integer isIdentify;

//    private String isIdentifyStr;

    private String details;

    /**
     * 提供给前端，开票时间
     */
    private Long gmtCreate;

    /**
     * OCR识别结果
     */
    private String result;

//    /**
//     * 税率
//     */
//    private BigDecimal taxRate;

    /**
     * 发票地址
     */
    private String invoiceImg;

}
