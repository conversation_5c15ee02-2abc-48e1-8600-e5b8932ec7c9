package so.dian.invoice.pojo.vo.invoice.manage;

import lombok.Data;
import so.dian.invoice.annotation.ExcelEntity;
import so.dian.invoice.annotation.ExcelField;

/**
 * 导出
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/14 10:32
 */
@Data
@ExcelEntity(tip = "开票申请明细列表")
public class ExportInvoiceRequestRecordDetailExcel {

    /**
     * 开票申请ID
     */
    @ExcelField(headerAlias = "开票申请ID")
    private Long requestId;

    /**
     * 渠道商类型
     */
    @ExcelField(headerAlias = "渠道商类型")
    private String subjectType;

    /**
     * 渠道商ID
     */
    @ExcelField(headerAlias = "渠道商ID")
    private Long subjectId;

    /**
     * 渠道商名称
     */
    @ExcelField(headerAlias = "渠道商名称")
    private String subjectName;

    /**
     * 业务类型
     */
    @ExcelField(headerAlias = "业务类型")
    private String bizType;

    /**
     * 业务单据
     */
    @ExcelField(headerAlias = "业务单据")
    private String bizNo;

    /**
     * 本单开票金额
     */
    @ExcelField(headerAlias = "本单开票金额（元）")
    private String amount;

    /**
     * 小电开票主体
     */
    @ExcelField(headerAlias = "小电开票主体")
    private String invoiceSubjectName;

    /**
     * 抬头类型
     */
    @ExcelField(headerAlias = "抬头类型")
    private String titleType;

    /**
     * 发票抬头
     */
    @ExcelField(headerAlias = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ExcelField(headerAlias = "税号")
    private String invoiceNo;

    /**
     * 发票类型
     */
    @ExcelField(headerAlias = "发票类型")
    private String type;

    /**
     * 收票邮箱
     */
    @ExcelField(headerAlias = "收票邮箱")
    private String email;

    /**
     * 产品名称
     */
    @ExcelField(headerAlias = "产品名称")
    private String productName;

    /**
     * 产品数量
     */
    @ExcelField(headerAlias = "产品数量")
    private Integer productCount;

    /**
     * 产品开票金额
     */
    @ExcelField(headerAlias = "产品开票金额")
    private String productInvoiceAmount;

    /**
     * 申请人
     */
    @ExcelField(headerAlias = "申请人")
    private String applicantName;

    /**
     * 申请时间
     */
    @ExcelField(headerAlias = "申请时间")
    private String gmtCreate;

    /**
     * 开票完成时间
     */
    @ExcelField(headerAlias = "开票完成时间")
    private String invoiceCompleteTime;

    /**
     * 审批意见
     */
    @ExcelField(headerAlias = "审批意见")
    private String financeFeedback;

    /**
     * 状态
     */
    @ExcelField(headerAlias = "状态")
    private String status;

}
