package so.dian.invoice.pojo.vo;

import lombok.Builder;
import lombok.Data;
import so.dian.agent.api.dto.AgentDTO;

/**
 * 公司信息
 */
@Data
@Builder
public class AgentVO {
    /**
     * 代理商id
     */
    private Long agentId;
    /**
     * 代理商名称
     */
    private String agentName;
    /**
     * 主体名称
     */
    private String subjectName;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 代理商类型
     */
    private Integer type;


    public static AgentVO fromDTO(AgentDTO agentDTO){
        return AgentVO.builder()
                .agentId(agentDTO.getAgentId())
                .agentName(agentDTO.getAgentName())
                .subjectName(agentDTO.getSubjectName())
                .status(agentDTO.getStatus())
                .type(agentDTO.getType())
                .build();
    }
}