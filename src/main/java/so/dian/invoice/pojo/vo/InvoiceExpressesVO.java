package so.dian.invoice.pojo.vo;

import lombok.Data;

/**
 * 发票物流列表
 */
@Data
public class InvoiceExpressesVO implements java.io.Serializable {


    private Long id;


    private String batchNo;
    private Long createTime;

    private String creatorName;



    private Long gmtCreate;


    private String invoiceCode;

    private String invoiceNo;
    /**
     * 单位分
     */
    private Long price;
    /**
     * 未核销金额单位分
     */
    private Long unusedAmount;

    /**
     * 物流状态
     */
    private Integer processStatus;

    /**
     * 物流状态
     */
    private String processStatusName;

    private String seller;


    /**
     * 发票物流id
     */
    private Long expressesId;
    /**
     * 物流名称
     */
    private String expressName;
    /**
     * 物流单号
     */
    private String  expressTrackingNo;




}
