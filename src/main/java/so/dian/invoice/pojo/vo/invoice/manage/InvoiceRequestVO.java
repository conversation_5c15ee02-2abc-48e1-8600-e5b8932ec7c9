package so.dian.invoice.pojo.vo.invoice.manage;//package so.dian.invoice.pojo.vo.invoice.manage;

import lombok.Data;
import so.dian.invoice.pojo.dto.MaskingDataDTO;

/**
 * @program: invoice
 * @description:
 * @author: y<PERSON>chuan
 * @create: 2025-03-12 17:17
 */
@Data
public class InvoiceRequestVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 购买方id
     */
    private Long subjectId;

    /**
     * 购买方类型
     */
    private Integer subjectType;

    /**
     * 购买方名称
     */
    private String subjectName;

    /**
     * 发票抬头类型：
     */
    private Integer titleType;

    /**
     * 发票抬头
     */
    private String title;

    /**
     * 发票号
     */
    private MaskingDataDTO invoiceNo;

    /**
     * 发票类型 1: 增值税专用发票 2: 普通发票
     */
    private Integer type;

    /**
     * 发票金额
     */
    private Long amount;

    /**
     * 邮件地址
     */
    private String email;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 开票状态： 0 待开票，1 已开票， 2 已拒绝, 3 部分开票
     */
    private Integer status;

    /**
     * 审批意见
     */
    private String financeFeedback;

    /**
     * 逻辑删除，0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 开票完成时间
     */
    private Long invoiceCompletedTime;

}
