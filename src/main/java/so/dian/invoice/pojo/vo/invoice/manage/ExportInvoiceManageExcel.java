package so.dian.invoice.pojo.vo.invoice.manage;

import lombok.Data;
import so.dian.invoice.annotation.ExcelEntity;
import so.dian.invoice.annotation.ExcelField;

/**
 * 导出
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/14 10:32
 */
@Data
@ExcelEntity(tip = "发票管理列表")
public class ExportInvoiceManageExcel {

    /**
     * 业务单号
     */
    @ExcelField(headerAlias = "业务单号")
    private String bizNo;

    /**
     * 业务类型
     */
     @ExcelField(headerAlias = "业务类型")
    private String bizType;

    /**
     * 购买方类型(4-代理商，11-合资公司)
     */
     @ExcelField(headerAlias = "渠道商类型")
    private String subjectType;

    /**
     * 购买方id
     */
     @ExcelField(headerAlias = "渠道商ID")
    private Long subjectId;

    /**
     * 购买方名称
     */
     @ExcelField(headerAlias = "渠道商名称")
    private String subjectName;

    /**
     * 业务单总金额
     */
     @ExcelField(headerAlias = "业务单总金额（元）")
    private String totalAmount;

    /**
     * 应开票总金额
     */
     @ExcelField(headerAlias = "应开票总金额（元）")
    private String expectedInvoiceTotalAmount;

    /**
     * 开票中金额
     */
     @ExcelField(headerAlias = "开票中总金额（元）")
    private String ongoingInvoiceTotalAmount;

    /**
     * 已开票总金额
     */
     @ExcelField(headerAlias = "已开票总金额（元）")
    private String completedInvoiceTotalAmount;

    /**
     * 待开票总金额
     */
     @ExcelField(headerAlias = "待开票总金额（元）")
    private String pendingInvoiceTotalAmount;

    /**
     * 状态，0 部分开票, 1 开票完成（业务单金额=已开票金额）, 2 未开票
     */
     @ExcelField(headerAlias = "状态")
    private String status;

    /**
     * 业务单创建时间
     */
     @ExcelField(headerAlias = "业务单创建时间")
    private String bizCreateTime;

    /**
     * 业务单付款完成时间
     */
     @ExcelField(headerAlias = "业务单付款完成时间")
    private String paymentTime;

}
