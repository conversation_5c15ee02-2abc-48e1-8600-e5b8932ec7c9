package so.dian.invoice.pojo.vo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 发票详情VO
 *
 * <AUTHOR>
 * @date 2019/7/7
 */
@Data
public class InvoiceInfoVO {
    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 销售方名称
     */
    private String invoiceSellerName;

    /**
     * 业务类型编码
     */
    private Integer businessTypeCode;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 发票创建时间
     */
    private String invoiceDate;

    /**
     * 价税合计金额
     */
    private BigDecimal totalAmount;

    /**
     * 一核销金额
     */
    private BigDecimal unUseAmount;

    /**
     * 发票核销状态编码
     */
    private Integer invoiceStatus;

    /**
     * 发票核销状态名称
     */
    private String invoiceStatusName;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 发票创建时间
     */
    private Date createTime;

    /**
     * 购买方名称
     */
    private String buyer;

    /**
     * 前端是否展示发票(先款后票的发票核销页面使用)
     * 如果 needShow == true ，展示此发票；
     * 如果 needShow == false ，不展示此发票；
     */
    private boolean needShow;

}
