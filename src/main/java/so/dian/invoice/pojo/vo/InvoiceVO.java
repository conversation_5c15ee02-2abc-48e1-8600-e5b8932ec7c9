package so.dian.invoice.pojo.vo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/9/27 10:53 AM
 * @Description:
 */
@Data
public class InvoiceVO {

	/**
	 * 发票录入时间
	 */
	private Date createTime;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 发票id
	 */
	private Integer invoiceId;

	/**
	 * 发票销售方
	 */
	private String seller;

	/**
	 * 发票核销状态Str
	 */
	private String statusStr;

	/**
	 * 价税合计
	 */
	private BigDecimal price;
}
