package so.dian.invoice.pojo.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 供应商发票主表
 *
 * <AUTHOR>
 * @date 2021-03-17 14:03:28
 */
@ApiModel("供应商发票主表列表")
@Data
public class SupplierInvoicePageVO {

    @ApiModelProperty(value = "供应商发票主体id")
    private Integer id;

    @ApiModelProperty(value = "发票类型")
    private String invoiceTypeStr;

    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;

    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;

    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty(value = "发票日期")
    private String invoiceDate;

    @ApiModelProperty(value = "开票方名称")
    private String sellerName;

    @ApiModelProperty(value = "开票方税号")
    private String sellerTaxId;

    @ApiModelProperty(value = "校验码")
    private String checkCode;

    @ApiModelProperty(value = "购买方名称")
    private String buyer;

    @ApiModelProperty(value = "购买方税号")
    private String buyerTaxId;

    @ApiModelProperty(value = "税前金额(单位 分),保留2为小数")
    private String rawPriceStr;

    @ApiModelProperty(value = "税率,正数,不超过100")
    private String taxRateStr;

    @ApiModelProperty(value = "税额(单位 分),保留2为小数")
    private String taxStr;

    @ApiModelProperty(value = "价税合计（总金额包括税额）")
    private String priceStr;

    @ApiModelProperty(value = "状态。0已录入 1已转发票台账")
    private String statusStr;

    @ApiModelProperty(value = "状态。0已录入 1已转发票台账")
    private Integer status;

    @ApiModelProperty(value = "发票图片url")
    private String invoiceUrl;

    @ApiModelProperty(value = "发票图片id")
    private Integer attachmentId;
}
