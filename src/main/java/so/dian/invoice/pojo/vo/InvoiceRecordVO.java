package so.dian.invoice.pojo.vo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/9/24 3:23 PM
 * @Description:
 */
@Data
public class InvoiceRecordVO {

	/**
	 * 购买方名称
	 */
	private String buyerName;

	/**
	 * 开票明细
	 */
	private String details;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 发票开票时间
	 */
	private String invoiceCreateTime;

	/**
	 * 发票类型
	 */
	private Integer invoiceType;

	/**
	 * 发票类型Str
	 */
	private String invoiceTypeStr;

	/**
	 * 备注
	 */
	private String memo;

	/**
	 * 税前金额
	 */
	private String rawPrice;

	/**
	 * 发票销售方名称
	 */
	private String sellerName;

	/**
	 * 税额
	 */
	private String tax;




}
