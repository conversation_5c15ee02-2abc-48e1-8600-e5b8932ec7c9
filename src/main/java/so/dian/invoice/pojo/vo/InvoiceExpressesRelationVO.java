package so.dian.invoice.pojo.vo;

import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class InvoiceExpressesRelationVO {
    /**
     * '物流名称（冗余）',
     */
    private String expressName;
    /**
     * '快递单号',
     */
    private String expressTrackingNo;
    private Date updateTime;
    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 快递图片
     */
    private String expressImg;
    /**
     * 操作流水号
     */
    private String serialNo;
    /**
     * 发票数量
     * 数据库无此字段
     */
    private Integer invoiceNumbers;
}
