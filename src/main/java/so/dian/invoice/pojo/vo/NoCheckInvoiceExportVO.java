package so.dian.invoice.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jiaoge
 * @Date: 2020/1/8 3:22 PM
 * @Description:
 */
@Data
public class NoCheckInvoiceExportVO {
	/**
	 * 发票ID
	 */
	private Long invoiceId;
	/**
	 * 发票号码
	 */
	private String invoiceNo;
	/**
	 * 发票代码
	 */
	private String invoiceCode;
	/**
	 * 状态 （核销状态Str）
	 */
	private String deductStatusStr;
	/**
	 * 快递单号
	 */
	private String expressInfoStr;
	/**
	 * 发票录入时间
	 */
	private String invoiceCreateTimeStr;
	/**
	 * 销售方名称
	 */
	private String supplierName;
	/**
	 * 发票类型Str
	 */
	private String typeStr;
	/**
	 * 价税合计
	 */
	private BigDecimal price;
	/**
	 * 质检人名称
	 */
	private String checkerName;
	/**
	 * 城市行政
	 */
	private String creatorNick;
	/**
	 * 大区
	 */
	private String region;
}
