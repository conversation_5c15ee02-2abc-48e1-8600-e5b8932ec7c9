package so.dian.invoice.pojo.vo;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:12 PM
 * @Description:
 */
@Data
public class CheckInvoiceVO {

	/**
	 * 质检ID
	 */
	private Long id;

	/**
	 * 发票ID
	 */
	private Long invoiceId;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 发票日期
	 */
	private Long invoiceTime;

	/**
	 * 发票录入时间
	 */
	private Long invoiceCreateTime;

	/**
	 * 质检发票创建时间
	 */
	private Long createTime;

	/**
	 * 发票真伪Str
	 */
	private String isRealStr;

	/**
	 * 价税合计
	 */
	private BigDecimal price;

	/**
	 * 流转状态Str
	 */
	private String processStatusStr;

	/**
	 * 未核销金额
	 */
	private BigDecimal remainderAmount;

	/**
	 * 销售方名称
	 */
	private String supplierName;

	/**
	 * 发票类型Str
	 */
	private String typeStr;

	/**
	 * 批次号
	 */
	private String batchNo;

	/**
	 * 业务单号
	 */
	private List<String> billNoList;

	/**
	 * 质检结论
	 */
	private String checkResultStr;

	/**
	 * 质检时间
	 */
	private Long checkTime;

	/**
	 * 质检人名称
	 */
	private String checkerName;

	/**
	 * 创建人名称
	 */
	private String creatorNick;

	/**
	 * 状态 （核销状态Str）
	 */
	private String deductStatusStr;

	/**
	 * 发票快递单号信息
	 */
	private ExpressInfo expressInfo;

	private String expressInfoStr;

	@Data
	public static class ExpressInfo {

		/**
		 * 快递名称
		 */
		private String expressName;

		/**
		 * 快递单号
		 */
		private String expressNo;
	}


	/**
	 * 发票日期
	 */
	private String invoiceTimeStr;

	/**
	 * 发票录入时间
	 */
	private String invoiceCreateTimeStr;

	/**
	 * 质检发票创建时间
	 */
	private String createTimeStr;

	/**
	 * 质检时间
	 */
	private String checkTimeStr;

	/**
	 * 大区名
	 */
	private String region;
}
