package so.dian.invoice.pojo.vo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * @Author: jiaoge
 * @Date: 2019/9/13 9:43 PM
 * @Description:
 */
@Data
@ToString
public class InvoiceExportInfoVO implements Cloneable{

	/**
	 * 发票id
	 */
	private Integer invoiceId;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 发票内容
	 */
	private String details;

	/**
	 * 开票公司
	 */
	private String subjectName;

	/**
	 * 业务类型Str
	 */
	private String subjectTypeStr;

	/**
	 * 发票类型
	 */
	private String invoiceType;

	/**
	 * 验真状态str
	 */
	private String isRealStr;

	/**
	 * 不含税金额
	 */
	private BigDecimal rawPrice;

	/**
	 * 税额
	 */
	private BigDecimal taxPrice;

	/**
	 * 价税合计
	 */
	private BigDecimal price;

	/**
	 * 开票日期
	 */
	private Date gmtCreate;

	/**
	 * 录入人名称
	 */
	private String creatorName;

	/**
	 * 发票销售方
	 */
	private String seller;

	/**
	 * 发票购买方
	 */
	private String buyer;

	/**
	 * 验真错误码文本
	 */
	private String validateCodeStr;

	/**
	 * <AUTHOR>
	 * @Since feature_20200410_liangfang_发票导出新增字段和发票质检优化
	 * Description 以下字段为本期新增字段
	 */
	/**
	 * 核销状态
	 */
	private String status;
	/**
	 * 发票核销金额
	 */
	private BigDecimal usedAmount;
	/**
	 * 单据核销金额
	 */
	private BigDecimal amount;
	/**
	 * 核销时间
	 */
	private Date deductionCreateTime;
	/**
	 * 付款单号
	 */
	private String billNo;
	/**
	 * 付款状态
	 */
	private String tradeStatus;
	/**
	 * 付款时间
	 */
	private Date payTime;
	/**
	 * 业务单号
	 */
	private String businessNo;
	/**
	 * 录入时间
	 */
	private Date invoiceCreateTime;
	/**
	 * 流转状态
	 */
	private String processStatus;
	/**
	 * 所属大区及城市
	 * 优先取对应业务单据（付款申请单/提现单）对应大区与城市，如没有取对应付款单对应大区与城市；如付款单还没有产生为空值
	 */
	private String regionCity;
	/**
	 * 商户ID(发票核销对应业务单据商户ID)
	 */
	private Long merchantId;
	/**
	 * 商户名称(发票核销对应业务单据商户名称)
	 */
	private String merchantName;
	/**
	 * 发票URL地址
	 */
	private String url;

	/**
	 * 税率
	 */
	private String taxRate;

	public InvoiceExportInfoVO clone()
	{
		InvoiceExportInfoVO o = null;
		try
		{
			o = (InvoiceExportInfoVO)super.clone();
		}
		catch(CloneNotSupportedException e)
		{
			System.out.println(e.toString());
		}
		return o;
	}


}
