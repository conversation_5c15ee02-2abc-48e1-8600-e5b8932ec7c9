package so.dian.invoice.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import so.dian.customer.dto.request.AuthenticationSubjectDTO;
import so.dian.customer.enums.AuthenticationTypeEnum;

@Data
@Builder
@ApiModel("认证主体类")
public class AuthenticationSubjectVO {

    private Long id;
    /***
     *认证主体类型
     *     UNKNOWN(0, "未知"),
     *     COMPANY(1,"企业"),
     *     PERSON(2,"个人");
     */
    @ApiModelProperty("认证主体类型")
    private Integer type;
    /***
     * 主体名
     */
    @ApiModelProperty("主体名称，企业名或者个人姓名")
    private String name;

    @ApiModelProperty("证件类型")
    private AuthenticationTypeEnum certificateType;
    /***
     * 开票税号
     * 主体类型为企业时为企业信用代码;
     * 主体类型为个人时为个人身份证号;
     */
    @ApiModelProperty("证件号码")
    private String invoiceNo;


    public static AuthenticationSubjectVO fromDTO(AuthenticationSubjectDTO model){
        return AuthenticationSubjectVO.builder()
                .type(model.getType().getCode())
                .name(model.getName())
                .certificateType(model.getCertificateType())
                .invoiceNo(model.getCertificateCode())
                .build();
    }
}
