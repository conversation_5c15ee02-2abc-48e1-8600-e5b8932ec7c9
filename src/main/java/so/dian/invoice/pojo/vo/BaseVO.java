package so.dian.invoice.pojo.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class BaseVO<T> implements Serializable{

    private static final long serialVersionUID = 2717545930643665328L;
    private String msg = "成功";
    private Object code = 1;
    private boolean success = true;
    private T data;

    public BaseVO() {
    }

    public BaseVO(T data) {
        this.data = data;
    }

    public BaseVO(String msg, Object  code, boolean success, T data) {
        this.msg = msg;
        this.code = code;
        this.success = success;
        this.data = data;
    }
}
