package so.dian.invoice.pojo.vo;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2020/1/8 3:22 PM
 * @Description:
 */
@Data
public class CheckInvoiceExportVO {
	/**
	 * 质检ID
	 */
	private Long id;

	/**
	 * 发票ID
	 */
	private Long invoiceId;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 发票真伪Str
	 */
	private String isRealStr;

	/**
	 * 价税合计
	 */
	private BigDecimal price;

	/**
	 * 流转状态Str
	 */
	private String processStatusStr;

	/**
	 * 未核销金额
	 */
	private BigDecimal remainderAmount;

	/**
	 * 销售方名称
	 */
	private String supplierName;

	/**
	 * 发票类型Str
	 */
	private String typeStr;

	/**
	 * 批次号
	 */
	private String batchNo;

	/**
	 * 业务单号
	 */
	private List<String> billNoList;

	/**
	 * 质检结论
	 */
	private String checkResultStr;

	/**
	 * 质检人名称
	 */
	private String checkerName;

	/**
	 * 创建人名称
	 */
	private String creatorNick;

	/**
	 * 状态 （核销状态Str）
	 */
	private String deductStatusStr;



	private String expressInfoStr;

	/**
	 * 发票日期
	 */
	private String invoiceTimeStr;

	/**
	 * 发票录入时间
	 */
	private String invoiceCreateTimeStr;

	/**
	 * 质检发票创建时间
	 */
	private String createTimeStr;
	/**
	 * 质检时间
	 */
	private String checkTimeStr;
	/**
	 * 大区
	 */
	private String region;
}
