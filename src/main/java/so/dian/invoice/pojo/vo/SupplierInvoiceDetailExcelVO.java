package so.dian.invoice.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import so.dian.invoice.annotation.ExcelEntity;
import so.dian.invoice.annotation.ExcelField;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/3/17 7:38 下午
 */
@Data
@ExcelEntity(tip = "供应商发票明细列表")
public class SupplierInvoiceDetailExcelVO {

    /**
     * 发票类型
     */
    @ExcelField(headerAlias = "发票类型")
    private String invoiceTypeStr;

    @ExcelField(headerAlias = "发票号码")
    private String invoiceNo;

    @ExcelField(headerAlias = "发票代码")
    private String invoiceCode;

    @ExcelField(headerAlias = "发票日期")
    private String invoiceDateStr;

    @ExcelField(headerAlias = "开票方名称")
    private String sellerName;

    @ExcelField(headerAlias = "开票方税号")
    private String sellerTaxId;

    @ExcelField(headerAlias = "校验码")
    private String checkCode;

    @ExcelField(headerAlias = "购买方名称")
    private String buyer;

    @ExcelField(headerAlias = "购买方税号")
    private String buyerTaxId;

    @ExcelField(headerAlias = "物料名称")
    private String materialName;

    @ExcelField(headerAlias = "规格型号")
    private String materialSpec;

    @ExcelField(headerAlias = "设备单位")
    private String unit;

    @ExcelField(headerAlias = "数量")
    private Integer quantity;

    @ExcelField(headerAlias = "不含税单价")
    private String unitPriceStr;

    @ExcelField(headerAlias = "不含税金额")
    private String rawPriceStr;

    @ExcelField(headerAlias = "税率")
    private String taxRateStr;

    @ExcelField(headerAlias = "税额")
    private String taxStr;

    @ExcelField(headerAlias = "价税合计")
    private String priceStr;

    @ExcelField(headerAlias = "采购批次")
    private String purchaseBatchBillNo;

    @ExcelField(headerAlias = "采购订单")
    private String purchaseOrderBillNo;

    @ExcelField(headerAlias = "对账单号")
    private String verifyBillNo;

}
