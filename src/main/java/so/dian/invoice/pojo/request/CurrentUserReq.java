package so.dian.invoice.pojo.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/24 下午2:29
 */
@Data
public class CurrentUserReq {

    // userId=22466;
    // nickName=%E5%AF%B8%E9%95%BF;
    // current_role=affairsManager;

    /*******从cookie中拿的*********/
    private Integer userId;

    private String currentRole;

    private String nickName;
    /*******从cookie中拿的*********/


    /*******从hr远程中拿的*********/
    private String userName;

    private Integer roleId;

    private String email;

    private String mobile;

    /*******从hr远程中拿的*********/

    /**
     * 登录用户的所属类型
     */
    private Integer belongSubjectType;
    /**
     * 登录用户的所属公司id
     */
    private Integer belongSubjectId;
}
