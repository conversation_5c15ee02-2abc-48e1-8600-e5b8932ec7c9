package so.dian.invoice.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * BaseEmpRoleReq
 *
 * <AUTHOR>
 * @desc 基本员工角色入参
 * @date 2019-09-16
 */
@ApiModel("基本员工角色参数")
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Data
public class BaseEmpRoleReq extends BaseEmpReq {

    @ApiModelProperty(value = "员工角色", example = "warehouseKeeper")
    private String role;
}
