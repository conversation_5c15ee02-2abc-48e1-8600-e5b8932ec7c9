package so.dian.invoice.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * BaseEmpReq
 *
 * <AUTHOR>
 * @desc 基本员工入参
 * @date 2019-09-16
 */
@ApiModel("基本员工参数")
@Data
public class BaseEmpReq {

    @ApiModelProperty(value = "员工ID", example = "12391")
    private Long employeeId;


}
