package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 操作人 <br/>
 *
 * <AUTHOR>
 * @date 2019-10-21 16:09
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
public class OperatorParam {

    @ApiModelProperty("操作人ID")
    protected Long operatorId;

    @ApiModelProperty("操作人名称")
    protected String operatorName;

    @ApiModelProperty("备注")
    protected String remark;
}