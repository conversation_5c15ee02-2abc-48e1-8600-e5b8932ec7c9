package so.dian.invoice.pojo.param;

import java.math.BigDecimal;
import lombok.Data;
import so.dian.invoice.enums.BusinessTypeEnum;

/**
 * 发票回滚 <br/>
 *
 * <AUTHOR>
 * @date 2019-10-30 16:44
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
public class InvoiceRecoverParam {
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票单号
     */
    private String invoiceNo;
    /**
     * 业务单号
     */
    private String businessNo;
    /**
     * 业务类型
     *
     * @see BusinessTypeEnum
     */
    private Integer businessType;
    /**
     * 核销的金额
     */
    private BigDecimal amount;

}
