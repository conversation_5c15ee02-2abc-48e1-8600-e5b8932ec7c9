package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p> 发票回滚 </p>
 *
 * <AUTHOR>
 * @date 2021/11/1 11:23 上午
 * @Copyright 杭州小电科技股份有限公司 2016-2020 © 版权所有 京ICP备17000101号
 */
@Data
public class ScmInvoiceRecoverParam {

    @ApiModelProperty("发票核销明细ID")
    private Long deductionId;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票单号")
    private String invoiceNo;

    @ApiModelProperty("业务单号")
    private String businessNo;

    @ApiModelProperty("核销的金额")
    private BigDecimal amount;
}
