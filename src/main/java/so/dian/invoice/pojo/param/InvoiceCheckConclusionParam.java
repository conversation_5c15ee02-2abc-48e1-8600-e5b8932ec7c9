package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 4:19 PM
 * @Description:
 */
@ApiModel("发票质检结论配置请求参数")
@Data
public class InvoiceCheckConclusionParam extends BaseEmpRoleReq {

	@ApiModelProperty("质检ID")
	private Long id;

	@ApiModelProperty("质检结论")
	private String code;
}
