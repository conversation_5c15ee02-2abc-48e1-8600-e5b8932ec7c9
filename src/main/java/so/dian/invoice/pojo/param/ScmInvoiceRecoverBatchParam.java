package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p> 发票批量回滚 </p>
 *
 * <AUTHOR>
 * @date 2021/11/1 11:21 上午
 * @Copyright 杭州小电科技股份有限公司 2016-2020 © 版权所有 京ICP备17000101号
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScmInvoiceRecoverBatchParam extends OperatorParam {

    @ApiModelProperty("发票列表")
    private List<ScmInvoiceRecoverParam> list;
}
