package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:53 PM
 * @Description:
 */
@ApiModel("发票质检请求参数")
@Data
public class CheckInvoiceParam extends BaseEmpRoleReq {

	@ApiModelProperty("质检id")
	private Long id;

	@ApiModelProperty("质检结论")
	private Long checkResult;

	@ApiModelProperty("质检备注")
	private String remark;
}
