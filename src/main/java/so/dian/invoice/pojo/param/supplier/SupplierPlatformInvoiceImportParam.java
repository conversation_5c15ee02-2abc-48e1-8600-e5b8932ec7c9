package so.dian.invoice.pojo.param.supplier;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 4:03 PM
 * @Description:
 */
@ApiModel("供应商平台发票excel导入参数")
@Data
public class SupplierPlatformInvoiceImportParam {

    @ApiModelProperty(value = "发票类型")
    private String invoiceTypeStr;
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;
    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;
    @ApiModelProperty(value = "发票日期")
    private String invoiceDateStr;
    @ApiModelProperty(value = "开票方名称")
    private String sellerName;

    @ApiModelProperty(value = "开票方税号")
    private String sellerTaxId;
    @ApiModelProperty(value = "校验码")
    private String checkCode;
    @ApiModelProperty(value = "购买方名称")
    private String buyer;
    @ApiModelProperty(value = "购买方税号")
    private String buyerTaxId;
    @ApiModelProperty(value = "物料(商品)名称")
    private String materialName;

    @ApiModelProperty(value = "物料(商品)规格")
    private String materialSpec;
    @ApiModelProperty(value = "设备单位")
    private String unit;
    @ApiModelProperty(value = "数量")
    private String quantity;
    @ApiModelProperty(value = "不含税单价")
    private String unitPriceStr;
    @ApiModelProperty(value = "税前总金额(单位 分),保留2为小数")
    private String rawPriceStr;

    @ApiModelProperty(value = "税额(单位 分),保留2为小数")
    private String taxStr;
    @ApiModelProperty(value = "税率,正数,不超过100")
    private String taxRateStr;
    @ApiModelProperty(value = "价税合计（总金额包括税额）")
    private String priceStr;
    @ApiModelProperty(value = "采购批次")
    private String purchaseBatchBillNo;
    @ApiModelProperty(value = "采购订单")
    private String purchaseOrderBillNo;
    @ApiModelProperty(value = "对账单号")
    private String verifyBillNo;
}
