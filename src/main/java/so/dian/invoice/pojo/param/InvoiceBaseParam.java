package so.dian.invoice.pojo.param;

import lombok.Data;

/**
 * 参数类基类
 *
 * <AUTHOR>
 * @date 2019/7/8
 */
@Data
public class InvoiceBaseParam {

    /**
     * 从属主体类型（参考枚举：BelongSubjectTypeEnum）
     */
    private Integer belongSubjectType;

    /**
     * 从属主体ID
     */
    private Integer belongSubjectId;

    /**
     * 分页偏移量
     */
    private Integer offset = 0;

    /**
     * 每页数量
     */
    private Integer pageSize = 500;
}
