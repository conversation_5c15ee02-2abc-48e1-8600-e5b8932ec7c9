package so.dian.invoice.pojo.param;

import lombok.Data;
import so.dian.invoice.enums.InvoiceSubjectRelationStatusEnum;
import so.dian.invoice.util.StringUtil;

/**
 * @Author: jiaoge
 * @Date: 2019/8/27 2:24 PM
 * @Description:
 */
@Data
public class InvoiceSubjectRelationPageParam extends PageParam {

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户签约主体名称
     */
    private String signSubjectName;

    /**
     * 开票主体名称
     */
    private String subjectName;

    /**
     * 状态 1-生效中 0-已失效
     *
     * @see InvoiceSubjectRelationStatusEnum
     */
    private Integer status;

    public String getSignSubjectName() {
        return StringUtil.trimFull2Half(signSubjectName);
    }

    public String getSubjectName() {
        return StringUtil.trimFull2Half(subjectName);
    }
}
