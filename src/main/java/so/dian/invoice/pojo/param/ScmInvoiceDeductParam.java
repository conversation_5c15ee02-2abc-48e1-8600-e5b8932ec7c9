package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p> 供应链发票核销 </p>
 *
 * <AUTHOR>
 * @date 2021/11/1 11:14 上午
 * @Copyright 杭州小电科技股份有限公司 2016-2020 © 版权所有 京ICP备17000101号
 */
@Data
public class ScmInvoiceDeductParam {

    @ApiModelProperty("核销金额")
    private BigDecimal amount;

    @ApiModelProperty("发票ID")
    private Long invoiceId;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    private String invoiceNo;

    @ApiModelProperty("业务单号")
    private String businessNo;

    @ApiModelProperty("业务类型")
    private Integer businessType;

    @ApiModelProperty("销售方名称")
    private String sellerName;

    @ApiModelProperty("购买方名称")
    private String buyerName;
}
