package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 3:11 PM
 * @Description:
 */
@ApiModel("发票质检详情请求参数")
@Data
public class InvoiceCheckDetailsParam extends BaseEmpRoleReq {

	@ApiModelProperty("质检ID")
	private Long id;
}
