package so.dian.invoice.pojo.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class InvoiceDeductOperationParam extends InvoiceDeductParam {

    /**
     * 操作人ID
     */
    protected Long operatorId;
    /**
     * 操作人名称
     */
    protected String operatorName;
    /**
     * 备注
     */
    protected String remark;
}
