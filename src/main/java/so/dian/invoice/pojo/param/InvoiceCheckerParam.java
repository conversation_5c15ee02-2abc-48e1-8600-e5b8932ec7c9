package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 4:03 PM
 * @Description:
 */
@ApiModel("发票质检员工配置参数")
@Data
public class InvoiceCheckerParam extends BaseEmpRoleReq {

	@ApiModelProperty("质检员工")
	private Long checker;

	@ApiModelProperty("大区列表")
	private List<String> regionList;
}
