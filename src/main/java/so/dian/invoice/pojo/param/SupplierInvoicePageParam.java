package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 供应商发票列表、明细列表、明细excel导出都用的这个
 */
@ApiModel("供应商发票列表参数")
@Data
public class SupplierInvoicePageParam {

    @ApiModelProperty(value = "购买方名称")
    private String buyer;

    /**
     * @see so.dian.invoice.enums.SupplierInvoiceTypeEnum
     */
    @ApiModelProperty(value = "发票类型")
    private Integer invoiceType;

    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;

    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;

    @ApiModelProperty(value = "采购批次")
    private String purchaseBatchBillNo;

    @ApiModelProperty(value = "采购订单")
    private String purchaseOrderBillNo;

    @ApiModelProperty(value = "对账单号")
    private String verifyBillNo;

    @ApiModelProperty(value = "发票日期-起始")
    private LocalDate invoiceDateStart;

    @ApiModelProperty(value = "发票日期-结束")
    private LocalDate invoiceDateEnd;

    @ApiModelProperty(value = "状态。0已录入 1已转发票台账")
    private Integer status;

    @ApiModelProperty(value = "起始页", notes = "默认: 1")
    private Integer pageNo = 1;

    @ApiModelProperty(value = "页面大小", notes = "默认: 20")
    private Integer pageSize = 20;


}
