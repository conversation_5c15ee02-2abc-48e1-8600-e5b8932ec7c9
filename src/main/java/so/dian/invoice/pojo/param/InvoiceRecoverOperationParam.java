package so.dian.invoice.pojo.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 发票回滚 <br/>
 *
 * <AUTHOR>
 * @date 2019-10-30 16:44
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class InvoiceRecoverOperationParam extends InvoiceRecoverParam {
    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 备注
     */
    private String remark;
}
