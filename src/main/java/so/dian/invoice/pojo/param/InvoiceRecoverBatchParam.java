package so.dian.invoice.pojo.param;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 批量发票回滚 <br/>
 *
 * <AUTHOR>
 * @date 2019-10-30 16:44
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class InvoiceRecoverBatchParam extends OperatorParam {
    /**
     * 发票列表
     */
    private List<InvoiceRecoverParam> list;
}
