package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 7:45 PM
 * @Description:
 */
@ApiModel("删除发票质检员工大区配置参数")
@Data
public class DeletedInvoiceCheckerParam extends BaseEmpRoleReq {

	@NotNull(message = "质检员工不能为空")
	@ApiModelProperty("质检员工")
	private Long checker;
}
