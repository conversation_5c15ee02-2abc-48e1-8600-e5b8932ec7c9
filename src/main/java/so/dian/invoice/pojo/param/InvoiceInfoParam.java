package so.dian.invoice.pojo.param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import so.dian.invoice.pojo.dto.InvoiceDetailDto;
import so.dian.invoice.pojo.request.CurrentUserReq;

/**
 * @Author: jiaoge
 * @Date: 2019/9/27 10:11 AM
 * @Description:
 */
@Data
public class InvoiceInfoParam {

	private Integer id;

	private String invoiceCode;

	private String invoiceNo;

	private String supplierNo;

	private String supplierName;

	private Integer subjectType;

	private String subjectName;

	private String subjectTypeStr;

	private Integer type;

	private String typeStr;

	private String billNo;

	private BigDecimal rawPrice;

	private BigDecimal taxPrice;

	private BigDecimal price;

	private Date receiveTime;

	private String memo;

	private Integer status;

	private String statusStr;

	private BigDecimal usedAmount;

	private BigDecimal remainderAmount;

	private Date gmtCreate;

	private Date gmtModified;

	private Integer isDelete;

	private Integer creator;

	private String creatorNick;

	private Date createTime;

	private Integer source;

	private String sourceStr;

	private String businessNo;

	private String kind;

	private String checkCode;

	private BigDecimal tax;

	private String seller;

	private String sellerTaxId;

	private String buyer;

	private String buyerTaxId;

	private String url;

	private Integer isReal;

	private String batchNo;

	/**
	 * 从属主体类型（参考枚举：BelongSubjectTypeEnum）
	 */
	private Integer belongSubjectType;

	/**
	 * 从属主体ID
	 */
	private Integer belongSubjectId;

	private List<InvoiceDetailDto> invoiceDetailDtoList;

	/**
	 * 登陆人信息
	 */
	private CurrentUserReq currentUserReq;

	/**
	 * 购买方英译
	 */
	private String companySubject;

	private String details;
}
