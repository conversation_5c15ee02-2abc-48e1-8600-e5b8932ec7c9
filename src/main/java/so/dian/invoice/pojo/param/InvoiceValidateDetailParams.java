package so.dian.invoice.pojo.param;

import java.math.BigDecimal;
import lombok.Data;
import so.dian.invoice.pojo.request.CurrentUserReq;


@Data
public class InvoiceValidateDetailParams {
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 校验码
     */
    private String checkCode;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 税前金额
     */
    private String pretaxAmount;
    /**
     * 发票类型
     */
    private Integer invoiceType;
    /**
     * 发票类型
     */
    private String invoiceTypeStr;
    /**
     * 发票日期 年/月/日
     */
    private String invoiceDate;
    private Integer id;
    private String total;
    private String seller;
    /**
     * 0识别验证, 1发票验证
     */
    private Integer type;

    /**
     * 购买方
     */
    private String buyer;

    /**
     * 购买方税号
     */
    private String buyerTaxId;

    /**
     * 发票内容
     */
    private String details;

    /**
     * 税额
     */
    private String tax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    private CurrentUserReq currentUserReq;
}
