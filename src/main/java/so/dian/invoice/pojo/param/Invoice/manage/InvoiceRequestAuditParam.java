package so.dian.invoice.pojo.param.Invoice.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class InvoiceRequestAuditParam {

    @ApiModelProperty(value = "开票申请ID", required = true)
    @NotNull(message = "开票申请ID不能为空")
    private Long requestId;

    @ApiModelProperty(value = "审核结果", required = true)
    @NotNull(message = "审核结果不能为空")
    private Integer auditResult;

    @ApiModelProperty(value = "数电发票号码", required = true)
    @NotBlank(message = "数电发票号码不能为空")
    private String invoiceCode;

    @ApiModelProperty(value = "审批意见")
    private String auditReason;
}
