package so.dian.invoice.pojo.param;

import java.util.List;
import lombok.Data;

@Data
public class InvoiceParam {

    private Integer id;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    private String supplierNo;

    private Integer subjectType;

    private String subjectName;

    private String supplierName;

    private Integer creator;

    // 发票录入时间 - start
    private String startCreateTime;

    // 发票录入时间 - end
    private String endCreateTime;

    // 发票日期 - start
    private String startGmtCreate;

    // 发票日期 - end
    private String endGmtCreate;

    // 发票录入时间 - start
    private String startTime;

    // 发票录入时间 - end
    private String endTime;

    private Integer status;

    private Integer offset = 0;

    private Integer pageSize = 10;

    private Integer pageNo;

    // 总条数
    private Integer count;

    private Boolean needFilter;

    /**
     * 商家id
     */
    private Integer merchantId;

    /**
     * 提现单id
     */
    private Long billId;

//    private List<String> supplierNoFilterList;

    /**
     * 从属主体类型（参考枚举：BelongSubjectTypeEnum）
     */
    private Integer belongSubjectType;

    /**
     * 从属主体ID
     */
    private Integer belongSubjectId;

    /**
     * 删除标识
     */
    private Integer isDelete = 0;

    private List<String> invoiceCodeList;

    private List<String> invoiceNoList;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 发票来源
     */
    private Integer source;

    /**
     * 销售方名称
     */
    private String sellerName;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 发票类型
     */
    private Integer type;

    /**
     * 发票流转状态
     */
    private Integer invoiceProcessStatus;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 发票真伪
     */
    private Integer isReal;

    /**
     * 快递单号
     */
    private String expressNo;

    private List<Long> invoiceIdList;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 录入人
     */
    private String nickName;

    /**
     * 流转状态
     */
    private Integer processStatus;

    /**
     * 是否在质检池
     */
    private Integer isInCheckPool;

    /**
     * 验真错误码
     */
    private String validateCode;

}
