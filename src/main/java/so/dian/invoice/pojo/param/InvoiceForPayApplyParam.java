package so.dian.invoice.pojo.param;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Date 2019-03-18
 * @Copyright 北京伊电园网络科技有限公司 2016-2018 © 版权所有 京ICP备17000101号
 */
public class InvoiceForPayApplyParam {

    //商家id
    private Integer merchantId;

    private BigDecimal amount;

    private Integer businessType;


    public Integer getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Integer merchantId) {
        this.merchantId = merchantId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
}
