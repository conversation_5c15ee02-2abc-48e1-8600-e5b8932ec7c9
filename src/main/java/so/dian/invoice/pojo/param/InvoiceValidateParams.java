package so.dian.invoice.pojo.param;

import lombok.Data;
import so.dian.invoice.pojo.request.CurrentUserReq;

import java.util.List;


/**
 * 发票识别参数类
 */
@Data
public class InvoiceValidateParams {
    private Integer subjectType;//业务类型
    private Integer loginUserId;
    private Integer loginRoleId;
    private Integer offset;
    private Integer pageSize;
    private Integer id;
    private List<Integer> ids;
    private List<InvoiceValidateDetailParams> list;
    private String batchNo;
    private Integer isReal;
    private Integer isIdentify;
    private String loginUserName;

    private Long creator;

    private CurrentUserReq currentUserReq;
}
