package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScmInvoiceDeductBatchParam extends OperatorParam {

    @ApiModelProperty("发票列表")
    @Size(min = 1,message = "发票不能为空")
    private List<ScmInvoiceDeductParam> list;
}
