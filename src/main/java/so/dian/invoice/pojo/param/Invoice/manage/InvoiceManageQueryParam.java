package so.dian.invoice.pojo.param.Invoice.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import so.dian.invoice.pojo.param.PageParam;

import java.util.List;

/**
 * @program: invoice
 * @description:
 * @author: y<PERSON><PERSON><PERSON>
 * @create: 2025-03-12 14:51
 */
@Data
public class InvoiceManageQueryParam extends PageParam {

    //业务单号 精准匹配查询
    @ApiModelProperty(value = "业务单号 精准匹配查询")
    private String bizNo;
    //业务类型 下拉单选（枚举值：设备采购/设备维修）
    @ApiModelProperty(value = "业务类型 下拉单选（枚举值：设备采购/设备维修）")
    private Integer bizType;
    //下拉单选（合资公司/代理商）
    //- 仅小电员工可见
    @ApiModelProperty(value = "下拉单选（合资公司/代理商）")
    private Integer subjectType;
    //渠道商id - 下拉单选（合资公司/代理商）
    //- 仅小电员工可见
    @ApiModelProperty(value = "下拉搜索框（支持ID/名称多选）")
    private List<Long> subjectIds;
    //渠道商名称 - 下拉搜索框（支持ID/名称多选）
    //- 数据权限控制
    //- 仅小电员工可见
//    //- 非小电员工默认登录人所属公司
//    @ApiModelProperty(value = "下拉搜索框（支持ID/名称多选）")
//    private String subjectName;
    //状态 - 下拉单选（待开票/已开票/开票失败）
    @ApiModelProperty(value = "下拉单选（0 开票中/1 开票完成/2 开票失败）")
    private Integer status;
    //业务单创建开始时间 - 日期范围筛选
    @ApiModelProperty(value = "业务单创建开始时间")
    private Long bizCreateTimeStart;
    //业务单创建结束时间 - 日期范围筛选
    @ApiModelProperty(value = "业务单创建结束时间")
    private Long bizCreateTimeEnd;

}
