package so.dian.invoice.pojo.param;

import java.math.BigDecimal;
import lombok.Data;

/**
 * InvoiceDeductParam
 *
 * <AUTHOR>
 * @date 2018/4/9
 */
@Data
public class InvoiceDeductParam {

    /**
     * check amount,unit:元
     */
    private BigDecimal amount;
    /**
     * invoice code
     */
    private String invoiceCode;
    /**
     * invoice no
     */
    private String invoiceNo;
    /**
     * 业务单号
     */
    private String businessNo;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 主体名称
     */
    private String subjectName;
    /**
     * 购买方名称
     */
    private String buyerName;
    /**
     * 是否需要发票主体关联
     */
    private Boolean needSubjectRelation;

}
