package so.dian.invoice.pojo.param;

import lombok.Data;
import so.dian.invoice.pojo.request.CurrentUserReq;

import java.util.Date;

/**
 * @Author: jiaoge
 * @Date: 2019/9/26 10:16 AM
 * @Description:
 */
@Data
public class AddSingleInvoiceParam {

	/**
	 * 购买方
	 */
	private String buyer;

	/**
	 * 开票明细
	 */
	private String details;

	/**
	 * 发票类型
	 */
	private Integer type;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 开票时间
	 */
	private Date gmtCreate;

	/**
	 * 备注
	 */
	private String memo;

	/**
	 * 价税合计
	 */
	private String price;

	/**
	 * 不含税金额
	 */
	private String rawPrice;

	/**
	 * 发票销售方
	 */
	private String seller;

	/**
	 * 业务类型
	 */
	private Integer subjectType;

	/**
	 * 税额
	 */
	private String tax;

	/**
	 * 发票图片地址
	 */
	private String url;

	/**
	 * 当前登陆人id
	 */
	private Integer loginUserId;

	/**
	 * 校验码
	 */
	private String checkCode;

	/**
	 * 购买方纳税人识别号
	 */
	private String buyerTaxId;

	/**
	 * 销售方纳税人识别号
	 */
	private String sellerTaxId;

	/**
	 * 发票消费类型
	 */
	private String kind;


	private CurrentUserReq currentUserReq;
}
