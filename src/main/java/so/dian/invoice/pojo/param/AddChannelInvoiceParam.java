package so.dian.invoice.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/10/28 14:10
 * @description:
 */
@Data
public class AddChannelInvoiceParam {

    private Long id;

    @NotNull(message = "发票抬头不能为空")
    @Size(max = 128, message = "发票抬头长度不能超过128个字符")
    private String title;

    @NotNull(message = "税号不能为空")
    @Size(max = 64, message = "税号长度不能超过64个字符")
    private String taxNo;

    @NotNull(message = "收件人不能为空")
    @Size(max = 64, message = "收件人长度不能超过64个字符")
    private String receiver;

    @NotNull(message = "收件人号码不能为空")
    private String receiverPhone;

    @NotNull(message = "省份不能为空")
    private String provinceCode;

    @NotNull(message = "城市不能为空")
    private String cityCode;

    @NotNull(message = "区域不能为空")
    private String areaCode;

    @NotNull(message = "详细地址不能为空")
    @Size(max = 128, message = "详细地址长度不能超过128个字符")
    private String addressDetail;
}
