package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 4:03 PM
 * @Description:
 */
@ApiModel("供应商发票excel导入参数")
@Data
public class SupplierInvoiceImportParam {

    @ApiModelProperty(value = "发票类型")
    private String invoiceTypeStr;
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;
    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;
    @ApiModelProperty(value = "发票日期")
    private String invoiceDateStr;
    @ApiModelProperty(value = "开票方名称")
    private String sellerName;

    @ApiModelProperty(value = "开票方税号")
    private String sellerTaxId;
    @ApiModelProperty(value = "校验码")
    private String checkCode;
    @ApiModelProperty(value = "购买方名称")
    private String buyer;
    @ApiModelProperty(value = "购买方税号")
    private String buyerTaxId;
    @ApiModelProperty(value = "物料(商品)名称")
    private String materialName;

    @ApiModelProperty(value = "物料(商品)规格")
    private String materialSpec;
    @ApiModelProperty(value = "设备单位")
    private String unit;
    @ApiModelProperty(value = "数量")
    private String quantity;
    @ApiModelProperty(value = "不含税单价")
    private String unitPriceStr;
    @ApiModelProperty(value = "税前总金额(单位 分),保留2为小数")
    private String rawPriceStr;

    @ApiModelProperty(value = "税额(单位 分),保留2为小数")
    private String taxStr;
    @ApiModelProperty(value = "税率,正数,不超过100")
    private String taxRateStr;
    @ApiModelProperty(value = "价税合计（总金额包括税额）")
    private String priceStr;
    @ApiModelProperty(value = "采购批次")
    private String purchaseBatchBillNo;
    @ApiModelProperty(value = "采购订单")
    private String purchaseOrderBillNo;
    @ApiModelProperty(value = "对账单号")
    private String verifyBillNo;


    public void trim() {
        if(StringUtils.isNotBlank(this.invoiceTypeStr)){
            this.invoiceTypeStr = this.invoiceTypeStr.trim();
        }
        if(StringUtils.isNotBlank(this.invoiceCode)){
            this.invoiceCode = this.invoiceCode.trim();
        }
        if(StringUtils.isNotBlank(this.invoiceNo)){
            this.invoiceNo = this.invoiceNo.trim();
        }
        if(StringUtils.isNotBlank(this.invoiceDateStr)){
            this.invoiceDateStr = this.invoiceDateStr.trim();
        }
        if(StringUtils.isNotBlank(this.sellerName)){
            this.sellerName = this.sellerName.trim();
        }

        if(StringUtils.isNotBlank(this.sellerTaxId)){
            this.sellerTaxId = this.sellerTaxId.trim();
        }
        if(StringUtils.isNotBlank(this.checkCode)){
            this.checkCode = this.checkCode.trim();
        }
        if(StringUtils.isNotBlank(this.buyer)){
            this.buyer = this.buyer.trim();
        }
        if(StringUtils.isNotBlank(this.buyerTaxId)){
            this.buyerTaxId = this.buyerTaxId.trim();
        }
        if(StringUtils.isNotBlank(this.materialName)){
            this.materialName = this.materialName.trim();
        }

        if(StringUtils.isNotBlank(this.materialSpec)){
            this.materialSpec = this.materialSpec.trim();
        }
        if(StringUtils.isNotBlank(this.unit)){
            this.unit = this.unit.trim();
        }
        if(StringUtils.isNotBlank(this.quantity)){
            this.quantity = this.quantity.trim();
        }
        if(StringUtils.isNotBlank(this.unitPriceStr)){
            this.unitPriceStr = this.unitPriceStr.trim();
        }
        if(StringUtils.isNotBlank(this.rawPriceStr)){
            this.rawPriceStr = this.rawPriceStr.trim();
        }

        if(StringUtils.isNotBlank(this.taxStr)){
            this.taxStr = this.taxStr.trim();
        }
        if(StringUtils.isNotBlank(this.taxRateStr)){
            this.taxRateStr = this.taxRateStr.trim();
        }
        if(StringUtils.isNotBlank(this.priceStr)){
            this.priceStr = this.priceStr.trim();
        }
        if(StringUtils.isNotBlank(this.purchaseBatchBillNo)){
            this.purchaseBatchBillNo = this.purchaseBatchBillNo.trim();
        }
        if(StringUtils.isNotBlank(this.purchaseOrderBillNo)){
            this.purchaseOrderBillNo = this.purchaseOrderBillNo.trim();
        }
        if(StringUtils.isNotBlank(this.verifyBillNo)){
            this.verifyBillNo = this.verifyBillNo.trim();
        }
    }
}
