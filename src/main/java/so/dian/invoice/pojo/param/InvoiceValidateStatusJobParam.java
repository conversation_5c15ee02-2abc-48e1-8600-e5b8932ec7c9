package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;

import java.util.List;

/**
 * 发票验真定时任务参数
 * <AUTHOR>
 */
@ApiModel("发票验真任务参数")
@Data
public class InvoiceValidateStatusJobParam {
    @ApiModelProperty("开始时间，yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty("结束时间，yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty("强制执行，手动执行必传true")
    private Boolean enforceFlag;

    @ApiModelProperty("往前n天的待验真发票，请传负整数")
    private Integer stayValidateDay;

    @ApiModelProperty("往前n天的验真不通过发票，请传负整数")
    private Integer failValidateDay;

    @ApiModelProperty("需要校验的code集合")
    private List<InvoiceIdentifyRecordEnum.ValidationCodeEnum> validationCode;

    @ApiModelProperty("发票台账里需要验真的类型")
    private List<InvoiceIdentifyRecordEnum.IsRealEnum> isRealListEnum;

}
