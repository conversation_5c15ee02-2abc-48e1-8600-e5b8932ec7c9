package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 4:00 PM
 * @Description:
 */
@ApiModel("发票质检比例配置参数")
@Data
public class InvoiceCheckRateParam extends BaseEmpRoleReq {

	@ApiModelProperty("质检比例")
	private Integer checkRate;
}
