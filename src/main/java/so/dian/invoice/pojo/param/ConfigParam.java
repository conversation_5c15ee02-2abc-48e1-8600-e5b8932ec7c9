package so.dian.invoice.pojo.param;

import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Author: jiaoge
 * @Date: 2019/12/26 3:07 PM
 * @Description:
 */
@Data
@ToString
public class ConfigParam {

	@NotBlank(message = "key不能为空")
	private String key;

	@NotBlank(message = "value不能为空")
	private String value;

	@NotNull(message = "status不能为空")
	private Integer status;

	@NotNull(message = "version不能为空")
	private Integer version;

}
