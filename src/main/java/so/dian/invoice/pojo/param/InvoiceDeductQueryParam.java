package so.dian.invoice.pojo.param;

import java.util.List;
import lombok.Data;

/**
 * 发票查询相关参数类（核销相关）
 *
 * <AUTHOR>
 * @date 2019/7/7
 */
@Data
public class InvoiceDeductQueryParam {
    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 主体名称
     */
    private String subjectName;
    /**
     * 业务类型编码
     */
    private Integer businessType;
    /**
     * 关联主体名称
     */
    private String relationSubjectName;
    private Boolean needRelate = false;
    /**
     * 0 商户 1渠道
     */
    private Integer relationBizType = 0;
    private List<String> subjectNameList;
    /**
     * 购买方
     */
    private String buyerName;

    /**
     * 归属主体
     */
    private Long belongSubjectId;
}
