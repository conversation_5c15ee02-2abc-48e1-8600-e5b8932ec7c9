package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: jiaoge
 * @Date: 2019/12/24 2:29 PM
 * @Description:
 */
@ApiModel("发票质检列表页请求参数")
@Data
public class InvoiceCheckPageParam extends BaseEmpRoleReq {

	@ApiModelProperty(value = "页数", example = "10")
	private Integer pageSize;

	@ApiModelProperty(value = "页码",example = "1")
	private Integer pageNo;

	@ApiModelProperty(value = "质检开始时间", example = "1577169298000")
	private String startCheckTime;

	@ApiModelProperty(value = "质检结束时间", example = "1577169298000")
	private String endCheckTime;

	@ApiModelProperty(value = "发票质检创建时间", example = "1577169298000")
	private Long startCreateTime;

	@ApiModelProperty(value = "发票质检创建时间", example = "1577169298000")
	private Long endCreateTime;

	@ApiModelProperty(value = "质检结论", example = "2")
	private Integer checkResult;

	@ApiModelProperty(value = "质检人", example = "10716")
	private String checker;

	@ApiModelProperty(value = "快递单号", example = "123123123")
	private String expressNo;

	@ApiModelProperty(value = "发票号码")
	private String invoiceNo;

	@ApiModelProperty(value = "发票代码")
	private String invoiceCode;

	@ApiModelProperty(value = "录入人名称")
	private String creator;

	@NotNull(message = "质检状态不能为空(0:待质检 1:已质检)")
	@ApiModelProperty(value = "质检状态")
	private Integer status;

	@ApiModelProperty(value = "大区名称")
	private String region;

	private Long offset;
	/**
	 * 城市code
	 */
	private List<Integer> cityCodes;
	/**
	 * 发票录入人
	 */
	private List<Long> operatorIdList;

	/**
	 * 录入人ID
	 */
	private Long creatorId;

	/**
	 * 质检人ID
	 */
	private Long checkerId;




}
