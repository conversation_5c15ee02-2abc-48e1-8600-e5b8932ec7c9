package so.dian.invoice.pojo.param;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 发票手动核销参数类
 *
 * <AUTHOR>
 * @date 2019/7/6
 */
@Data
public class InvoiceManualDeductParam {
    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 核销金额
     */
    private BigDecimal amount;

    /**
     * 业务单号 20200331
     */
    private String businessNo;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 操作人
     */
    private Integer creator;

    /**
     * 操作类型
     */
    private Integer operateType;
}
