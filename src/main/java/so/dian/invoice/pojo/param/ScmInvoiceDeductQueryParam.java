package so.dian.invoice.pojo.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p> 供应链可核销发票查询参数 </p>
 *
 * <AUTHOR>
 * @date 2021/10/28 10:43 上午
 * @Copyright 杭州小电科技股份有限公司 2016-2020 © 版权所有 京ICP备17000101号
 */
@Data
public class ScmInvoiceDeductQueryParam {

    /**
     * 业务类型编码
     */
    private Integer businessType;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 销售方名称
     */
    @NotNull(message = "销售方必填")
    private String sellerName;

    /**
     * 购买方
     */
    @NotNull(message = "购买方必填")
    private String buyerName;

    private List<String> buyerNames;


    private List<String> sellerNames;

    /**
     * 对账单号
     */
    private String verifyBillNo;

    @JsonIgnore
    private List<Map<String, String>> invoiceMapList;

}
