package so.dian.invoice.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 供应商发票明细表
 *
 * <AUTHOR>
 * @date 2021-03-17 17:03:12
 */
@ApiModel("供应商发票明细表查询入参")
@Data
public class SupplierInvoiceDetailPageParam {

    @NotNull(message = "供应商发票主体id不能为空")
    @ApiModelProperty(value = "供应商发票主体id")
    private Integer invoiceId;

}
