package so.dian.invoice.pojo.param;

import java.util.List;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/9/13 1:15 PM
 * @Description:
 */
@Data
public class InvoiceExportParam {

	/**
	 * 发票id
	 */
	private Integer id;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 业务类型
	 */
	private Integer subjectType;

	/**
	 * 创建人昵称
	 */
	private String nickName;

	private String startCreateTime;

	private String endCreateTime;

	private String startGmtCreate;

	private String endGmtCreate;

	private Integer status;

	/**
	 * 发票类型
	 */
	private Integer type;

	/**
	 * 用户id
	 */
	private Integer userId;

	/**
	 * 发票真伪
	 */
	private Integer isReal;

	/**
	 * 批次号
	 */
	private String batchNo;

	/**
	 * 发票来源
	 */
	private Integer source;

	/**
	 * 销售方名称
	 */
	private String subjectName;

	/**
	 * 发票流转状态
	 */
	private Integer processStatus;

	/**
	 * 快递单号
 	 */
	private String expressNo;

	private List<Long> invoiceIdList;

	private Long creator;

	private String validateCode;
	/**
	 * 单据编号
	 */
	private String billNo;
	/**
	 * 是否在质检池
	 */
	private Integer isInCheckPool;
	/**
	 * 批量导出的开始偏量
	 */
	private Integer offset;
	/**
	 * 批量导出页大小
	 */
	private Integer pageSize;

	/**
	 * 从属主体类型（参考枚举：BelongSubjectTypeEnum）
	 */
	private Integer belongSubjectType;

	/**
	 * 从属主体ID
	 */
	private Integer belongSubjectId;

}
