package so.dian.invoice.pojo.param.Invoice.manage;

import lombok.Data;

import java.util.List;

@Data
public class InvoiceRequestParam {

    /**
     * 发票抬头类型
     */
    private String titleType;
    /**
     * 发票抬头
     */
    private String title;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 发票类型 1: 专用发票 2: 普通发票
     */
    private Integer type;
    /**
     * 金额
     */
    private Long amount;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 勾选的业务单id
     */
    private List<Long> invoiceManageIds;

}
