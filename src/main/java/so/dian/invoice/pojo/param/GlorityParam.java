package so.dian.invoice.pojo.param;

import cn.hutool.core.date.DateUtil;
import javax.validation.constraints.NotNull;
import lombok.*;
import okhttp3.FormBody;
import org.apache.shiro.crypto.hash.Md5Hash;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.volidator.InvoiceValidator;

/**
 * 睿琪请求参数
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class GlorityParam {
    /**
     * appKey
     */
    @NotNull
    private String app_key;

    /**
     * token
     */
    @NotNull
    private String token;

    /**
     * 时间戳
     * timestamp 为January 1 1970 00:00:00 GMT 到现在的秒数
     */
    @NotNull
    private long timestamp;

    /**
     * 发票代码
     */
    private String code;

    /**
     * 发票号码
     */
    @NotNull
    private String number;

    /**
     * 金额
     * 专票(税前金额)、机动车票(税前金额)、二手车票(总价)不为空，普票可为空
     */
    private String pretax_amount;

    /**
     * 金额
     * 金额 价税合计 必填
     */
    private String total;

    /**
     * 开票日期
     * 格式为: 年/月/日
     */
    @NotNull
    private String date;

    /**
     * 发票类型
     */
    @NotNull
    private String type;

    public static GlorityParam of(InvoiceBO invoiceBO, String appKey, String appSecret){
        long timestamp = System.currentTimeMillis() / 1000;
        String token = new Md5Hash(appKey + "+" + timestamp + "+" + appSecret).toString();
        GlorityParam param = GlorityParam.builder().app_key(appKey).token(token)
                    .code(invoiceBO.getInvoiceCode())
                    .number(invoiceBO.getInvoiceNo())
                    .pretax_amount(String.valueOf(invoiceBO.getRawPrice()))
                    .date(DateUtil.format(invoiceBO.getGmtCreate(), "yyyy-MM-dd"))
                    .type(InvoiceTypeEnum.getBizTypeByType(invoiceBO.getInvoiceType()))
                    .timestamp(timestamp).build();
        return param;
    }
}
