package so.dian.invoice.pojo.param;

import java.util.List;
import lombok.*;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;

import java.util.Date;

/**
 * 发票验真查询参数
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class InvoiceValidateStatusQueryParam {

    private Integer isReal;

    private List<InvoiceIdentifyRecordEnum.ValidationCodeEnum> validateCodeList;

    private Date startCreateTime;

    private Date endCreateTime;

}
