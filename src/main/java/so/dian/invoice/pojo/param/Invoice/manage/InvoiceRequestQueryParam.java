package so.dian.invoice.pojo.param.Invoice.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import so.dian.invoice.pojo.param.PageParam;

import java.util.List;

@Data
public class InvoiceRequestQueryParam extends PageParam {

    @ApiModelProperty(value = "开票申请id")
    private Long requestId;

    /**
     * 主体ids
     */
    @ApiModelProperty(value = "主体ids")
    private List<Long> subjectIds;
    /**
     * 主体类型
     */
    @ApiModelProperty(value = "主体类型")
    private Integer subjectType;
    /**
     * 业务单号
     */
    @ApiModelProperty(value = "业务单号")
    private String bizNo;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 状态多选
     */
    @ApiModelProperty(value = "状态多选")
    private List<Integer> statusList;
    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    private Long applyStartTime;
    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    private Long applyEndTime;

}
