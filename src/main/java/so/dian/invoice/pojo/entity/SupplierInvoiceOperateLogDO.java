package so.dian.invoice.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.himalaya.common.entity.BaseDO;

import java.time.LocalDateTime;

/**
 * 操作记录表
 *
 * <AUTHOR>
 * @date 2021-03-23 14:03:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierInvoiceOperateLogDO extends BaseDO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 供应商发票id
     */
    private Integer supplierInvoiceId;
    /**
     * 操作类型（导入发票、删除发票、转台账、上传附件、删除附件）
     */
    private Integer operateType;
    /**
     * 操作内容，存json
     */
    private String content;
    /**
     * 操作人
     */
    private String operatorName;
    /**
     * 创建人
     */
    private Integer operatorId;
    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
}