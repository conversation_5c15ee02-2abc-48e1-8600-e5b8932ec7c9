package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 开票管理详情表
 * @TableName invoice_manage_detail
 */
@Data
public class InvoiceManageDetailDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 业务单金额
     */
    private Long amount;

    /**
     * 原始开票金额
     */
    private Long originAmount;

    /**
     * 应开票金额
     */
    private Long expectedInvoiceAmount;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品数量
     */
    private Integer productCount;

}