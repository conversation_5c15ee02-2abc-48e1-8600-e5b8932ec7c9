package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 开票申请
 * @TableName invoice_request
 */
@Data
public class InvoiceRequestDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 购买方id
     */
    private Long subjectId;

    /**
     * 购买方类型
     */
    private Integer subjectType;

    /**
     * 购买方名称
     */
    private String subjectName;

    /**
     * 发票抬头类型： 
     */
    private String titleType;

    /**
     * 发票抬头
     */
    private String title;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票类型 1: 增值税专用发票 2: 普通发票
     */
    private Integer type;

    /**
     * 发票金额
     */
    private Long amount;

    /**
     * 邮件地址
     */
    private String email;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 开票状态： 0 待开票，1 已开票， 2 已拒绝, 3 部分开票
     */
    private Integer status;

    /**
     * 审批意见
     */
    private String financeFeedback;

    /**
     * 开票完成时间
     */
    private Long invoiceCompletedTime;
}