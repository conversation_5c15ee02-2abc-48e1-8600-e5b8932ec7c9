package so.dian.invoice.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.himalaya.common.entity.BaseDO;

import java.time.LocalDate;
import java.util.Date;
import java.math.BigDecimal;
/**
 * 供应商发票主表
 *
 * <AUTHOR>
 * @date 2021-03-16 19:03:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierInvoiceDO extends BaseDO {

    /**
     * 供应商发票id
     */
    private Integer id;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 校验码
     */
    private String checkCode;
    /**
     * 发票类型 增值税专用发票、增值税电子专用发票、增值税普通发票、增值税电子普通发票
     */
    private Integer invoiceType;
    /**
     * 发票日期
     */
    private LocalDate invoiceDate;
    /**
     * 开票方名称
     */
    private String sellerName;
    /**
     * 开票方税号
     */
    private String sellerTaxId;
    /**
     * 购买方名称
     */
    private String buyer;
    /**
     * 购买方税号
     */
    private String buyerTaxId;
    /**
     * 税前金额(单位 分),保留2为小数
     */
    private BigDecimal rawPrice;
    /**
     * 税额(单位 分),保留2为小数
     */
    private BigDecimal tax;
    /**
     * 税率,正数,不超过100
     */
    private BigDecimal taxRate;
    /**
     * 价税合计（总金额包括税额）
     */
    private BigDecimal price;
    /**
     * 状态。0已录入 1已转发票台账
     */
    private Integer status;
    /**
     * 供应商编号
     */
    private String supplierNo;
    /**
     * 创建人
     */
    private Integer creator;

}