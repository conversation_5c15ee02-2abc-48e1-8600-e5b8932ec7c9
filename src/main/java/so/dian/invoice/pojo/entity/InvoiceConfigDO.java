package so.dian.invoice.pojo.entity;


import java.util.Date;
import lombok.Data;

@Data
public class InvoiceConfigDO {

    private Long id;
    private String key;
    private String value;
    private Integer status;
    private Integer version;
    private Date createTime;
    private Date updateTime;

    public static InvoiceConfigDO of(InvoiceConfigDO config, String value) {
        InvoiceConfigDO invoiceConfigDO = new InvoiceConfigDO();
        invoiceConfigDO.setId(config.getId());
        invoiceConfigDO.setValue(value);
        invoiceConfigDO.setStatus(config.getStatus());
        invoiceConfigDO.setVersion(config.getVersion());
        invoiceConfigDO.setUpdateTime(config.getUpdateTime());
        return invoiceConfigDO;

    }
}
