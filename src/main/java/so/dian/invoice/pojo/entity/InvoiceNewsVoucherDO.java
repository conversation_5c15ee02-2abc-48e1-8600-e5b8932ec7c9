package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 发票系统消息凭证表
 * @TableName invoice_news_voucher
 */
@Data
public class InvoiceNewsVoucherDO extends BaseDO{
    /**
     * 主键
     */
    private Long id;

    /**
     * 外部业务ID
     */
    private String outBizId;

    /**
     * 外部业务类型，1 采购付款 2 维修完成 3 采购取消 4 授信还款退款 5 授信还款
     */
    private Integer outBizType;

    /**
     * 状态（1-新建，2-处理成功，3-处理失败）
     */
    private Integer status;

    /**
     * 失败重试次数（一次成功时为0）
     */
    private Integer retryTime;

    /**
     * 凭证内容（JSON格式）
     */
    private String eventVoucher;

    /**
     * 业务发生时间（13位时间戳）
     */
    private Long receiveTime;

}