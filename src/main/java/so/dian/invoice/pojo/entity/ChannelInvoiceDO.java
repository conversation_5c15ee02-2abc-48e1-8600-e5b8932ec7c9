package so.dian.invoice.pojo.entity;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

/**
 * channel_invoice
 * <AUTHOR>
@Data
public class ChannelInvoiceDO extends BaseDO {
    /**
     * ID
     */
    private Long id;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 发票抬头
     */
    private String title;

    /**
     * 税号
     */
    private String taxNo;

    /**
     * 结算主体类型
     */
    private Integer settleSubjectType;

    /**
     * 结算主体ID
     */
    private Long settleSubjectId;

    /**
     * 省份编码
     */
    private Integer provinceCode;

    /**
     * 城市编码
     */
    private Integer cityCode;

    /**
     * 区域编码
     */
    private Integer areaCode;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 收件人名称
     */
    private String receiver;

    /**
     * 收件人号码
     */
    private String receiverPhone;

    /**
     * 创建人
     */
    private Long creator;

}