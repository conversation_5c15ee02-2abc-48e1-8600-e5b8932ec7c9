package so.dian.invoice.pojo.entity;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.commons.eden.entity.BaseDO;
import so.dian.commons.eden.enums.DeletedEnum;
import so.dian.invoice.enums.InvoiceValidateTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.param.InvoiceValidateDetailParams;

import javax.validation.constraints.NotNull;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 3:42 PM
 * @Description: 发票验真状态DO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceValidateStatusDO extends BaseDO {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 发票ID
	 */
	@NotNull(message = "发票id不能为null")
	private Long invoiceId;

	/**
	 * 发票号码
	 */
	@NotNull(message = "发票号码不能为null")
	private String invoiceNo;

	/**
	 * 发票代码
	 */
	@NotNull(message = "发票代码不能为null")
	private String invoiceCode;

	/**
	 * 发票录入时间
	 */
	@NotNull(message = "发票录入时间不能为null")
	private Date invoiceTime;

	/**
	 * 验真时间
	 */
	@NotNull(message = "验真时间不能为null")
	private Date validateTime;

	/**
	 * 验真状态
	 */
	@NotNull(message = "验真状态不能为null")
	private Integer isReal;

	/**
	 * 验真类型
	 * @see InvoiceValidateTypeEnum
	 */
	@NotNull(message = "验真类型不能为null")
	private Integer validateType;

	/**
	 * 验真失败错误码，成功为空或1
	 */
	private String validateCode;

	/**
	 * 验真结果
	 */
	@NotNull(message = "验真结果不能为null")
	private String validateResult;

	public static InvoiceValidateStatusDO of(InvoiceBO validateInvoiceBO, Date now, String validateResult, InvoiceValidateTypeEnum validateTypeEnum){
		InvoiceValidateStatusDO ivsDO = InvoiceValidateStatusDO.builder()
				.invoiceId(validateInvoiceBO.getId().longValue())
				.invoiceNo(validateInvoiceBO.getInvoiceNo())
				.invoiceCode(validateInvoiceBO.getInvoiceCode())
				.invoiceTime(validateInvoiceBO.getCreateTime())
				.validateTime(now)
				.validateType(validateTypeEnum.getCode())
				.validateResult(validateResult)
				.build();
		ivsDO.setCreateTime(now);
		ivsDO.setUpdateTime(now);
		ivsDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
		return ivsDO;
	}

	// public static InvoiceValidateStatusDO of(InvoiceValidateDetailParams param, Date now, String validateResult){
	// 	InvoiceValidateStatusDO ivsDO = InvoiceValidateStatusDO.builder()
	// 			.invoiceId(param.getId().longValue())
	// 			.invoiceNo(param.getInvoiceNo())
	// 			.invoiceCode(param.getInvoiceCode())
	// 			.invoiceTime(param.getCreateTime())
	// 			.validateTime(now)
	// 			.validateType(InvoiceValidateTypeEnum.MANUAL_VERIFICATION.getCode())
	// 			.validateResult(validateResult)
	// 			.build();
	// 	ivsDO.setCreateTime(now);
	// 	ivsDO.setUpdateTime(now);
	// 	ivsDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
	// 	return ivsDO;
	// }


}
