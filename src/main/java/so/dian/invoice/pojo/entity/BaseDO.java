package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * @Author: ji<PERSON>ge
 * @Date: 2019/12/24 7:00 PM
 * @Description:
 */
@Data
public class BaseDO {

	/**
	 * 创建时间
	 */
	private Long gmtCreate;

	/**
	 * 更新时间
	 */
	private Long gmtUpdate;

	/**
	 * @see so.dian.commons.eden.enums.DeletedEnum
	 */
	private Integer deleted;


    public void init() {

	this.deleted = 0;

	this.gmtCreate = System.currentTimeMillis();

	this.gmtUpdate = System.currentTimeMillis();
    }
}
