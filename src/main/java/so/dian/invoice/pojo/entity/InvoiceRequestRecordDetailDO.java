package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 开票申请记录明细
 * @TableName invoice_request_record_detail
 */
@Data
public class InvoiceRequestRecordDetailDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 开票管理详情id
     */
    private Long manageDetailId;

    /**
     * 开票申请记录id
     */
    private Long requestRecordId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品数量
     */
    private Integer productCount;

    /**
     * 开票金额
     */
    private Long amount;

    /**
     * 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    private Integer status;

}