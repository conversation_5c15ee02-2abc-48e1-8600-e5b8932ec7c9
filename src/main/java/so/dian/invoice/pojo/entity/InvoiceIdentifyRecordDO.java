package so.dian.invoice.pojo.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InvoiceIdentifyRecordDO {
    private Integer id;

    private String invoiceNo;

    private String invoiceCode;

    private Integer invoiceType;

    private String invoiceDate;

    private String checkCode;

    private String pretaxAmount;

    private String total;

    private String tax;

    private String seller;

    private String sellerTaxId;

    private String buyer;

    private String buyerTaxId;

    private String kind;

    private Integer isReal;

    private Integer isRelated;

    private String url;

    private Date createTime;

    private Integer creator;

    private Date updateTime;

    private Integer updater;

    private Integer deleted;

    private String details;

    private String batchNo;

    private String reason;

    private Integer isIdentify;
    /**
     * 识别结果
     */
    private String result;

}