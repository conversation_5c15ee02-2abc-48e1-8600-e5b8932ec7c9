package so.dian.invoice.pojo.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import so.dian.invoice.util.StringUtil;

@Data
public class InvoiceDO {
    private Integer id;

    private String invoiceNo;

    private String supplierNo;

    private String invoiceCode;

    /**
     * 业务类型 1-供应链供应商 2-分成商户 3代理商分成 6 运营型服务商分成
     */
    private Integer subjectType;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 关联的请款单编号
     */
    private String billNo;

    /**
     * 发票类型 1: 增值税专用发票 2: 普通发票
     */
    private Integer type;

    /**
     * 税前金额，不包括税额
     */
    private BigDecimal rawPrice;

    /**
     *
     */
    private BigDecimal taxPrice;

    /**
     * 发票税率，有多个就取第一个
     */
    private BigDecimal taxRate;

    /**
     * 价税合计（总金额包括税额）
     */
    private BigDecimal price;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 备注
     */
    private String memo;

    /**
     * 状态 1:未核销 2:部分核销 3:全部核销
     */
    private Integer status;

    /**
     * 已核销金额
     */
    private BigDecimal usedAmount;

    private Date gmtCreate;

    private Date gmtModified;

    private Integer isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    private Date createTime;

    /**
     * 来源 1-手动录入，2-模版导入 3自动录入
     */
    private Integer source;

    /**
     * 行业类型（发票消费类型）
     */
    private String kind;

    /**
     * 校验码
     */
    private String checkCode;

    /**
     * 税额
     */
    private BigDecimal tax;

    /**
     * 销售方名称
     */
    private String seller;

    /**
     * 销售方纳税人识别号
     */
    private String sellerTaxId;

    /**
     *购买方方名称
     */
    private String buyer;
    /**
     *购买方纳税人识别号
     */
    private String buyerTaxId;
    /**
     *图片url
     */
    private String url;
    /**
     *验证状态0待验证 1验证通过 2验证不通过
     */
    private Integer isReal;
    /**
     *批次号
     */
    private String batchNo;

    /**
     * 从属主体类型
     */
    private Integer belongSubjectType;

    /**
     * 从属主体ID
     */
    private Integer belongSubjectId;

    /**
     * 发票流转状态
     */
    private Integer processStatus;

    /**
     * 发票复核人
     */
    private Long reviewer;

    /**
     * 发票复核时间
     */
    private Date reviewTime;

    /**
     * 发票录入人，不能直接设置为当前登录用户
     */
    private String createName;

    /**
     * 发票复核时间
     */
    private String reviewRemark;

    /**
     * 是否需要质检：0-不需要；1-需要
     */
    private Integer needCheck;

    /**
     * 是否进入质检池标记：0-未进入；1-已进入
     */
    private Integer inCheckPool;

    /**
     * 验真失败错误码
     */
    private String validateCode;

    /**
     * 发票录入时,城市行政所属的城市code(目前只有小二端录入城市code才有值)
     */
    private Integer cityCode;



    public String getBuyer() {
        return StringUtil.trimFull2Half(buyer);
    }

    public void setBuyer(String buyer) {
        this.buyer = StringUtil.trimFull2Half(buyer);
    }

    public String getSubjectName() {
        return StringUtil.trimFull2Half(subjectName);
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = StringUtil.trimFull2Half(subjectName);
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo.trim();
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = StringUtils.isNotBlank(invoiceCode) ? invoiceCode.trim() : "";
    }
}