package so.dian.invoice.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.himalaya.common.entity.BaseDO;

/**
 * 供应商发票明细单据号
 *
 * <AUTHOR>
 * @date 2021-03-17 16:03:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierInvoiceAttachmentDO extends BaseDO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 供应商发票id
     */
    private Integer supplierInvoiceId;
    /**
     * 附件类型（发票图片、发票对账单附件等）
     */
    private Integer type;
    /**
     * 附件地址
     */
    private String url;
}