package so.dian.invoice.pojo.entity;

import java.math.BigDecimal;
import lombok.Data;
import so.dian.invoice.enums.BusinessTypeEnum;
@Data
public class InvoiceBizRelationDO extends BaseDO{

    private Long id;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 业务编码
     */
    private String businessNo;

    /**
     * 业务类型
     * @see BusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 核销金额
     */
    private BigDecimal amount;
}