package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 应开变更记录详情表
 * @TableName invoice_change_record_detail
 */
@Data
public class InvoiceChangeRecordDetailDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理ID
     */
    private Long changeRecordId;

    /**
     * 开票管理ID
     */
    private Long manageId;

    /**
     * 开票管理详情ID
     */
    private Long manageDetailId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品数量
     */
    private Integer productCount;

    /**
     * 变更类型： increase 增加 ，decrease 减少
     */
    private String type;

    /**
     * 变更金额
     */
    private Long amount;

}