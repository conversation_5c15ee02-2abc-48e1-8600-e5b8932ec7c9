package so.dian.invoice.pojo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.himalaya.common.entity.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商发票明细表
 *
 * <AUTHOR>
 * @date 2021-03-17 17:03:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierInvoiceDetailDO extends BaseDO {
    /**
     * 供应商发票明细id
     */
    private Integer id;
    /**
     * 供应商发票id
     */
    private Integer supplierInvoiceId;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 校验码
     */
    private String checkCode;
    /**
     * 发票类型 增值税专用发票、增值税电子专用发票、增值税普通发票、增值税电子普通发票
     */
    private Integer invoiceType;
    /**
     * 发票日期
     */
    private LocalDate invoiceDate;
    /**
     * 开票方名称
     */
    private String sellerName;
    /**
     * 开票方税号
     */
    private String sellerTaxId;
    /**
     * 购买方名称
     */
    private String buyer;
    /**
     * 购买方税号
     */
    private String buyerTaxId;
    /**
     * 税前总金额(单位 分),保留2为小数
     */
    private BigDecimal rawPrice;
    /**
     * 税额(单位 分),保留2为小数
     */
    private BigDecimal tax;
    /**
     * 税率,正数,不超过100
     */
    private BigDecimal taxRate;
    /**
     * 价税合计（总金额包括税额）
     */
    private BigDecimal price;
    /**
     * 状态。0已录入 1已转发票台账
     */
    private Integer status;
    /**
     * 物料(商品)名称
     */
    private String materialName;
    /**
     * 物料(商品)规格
     */
    private String materialSpec;
    /**
     * 单位
     */
    private String unit;
    /**
     * 不含税单价
     */
    private BigDecimal unitPrice;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 供应商编号
     */
    private String supplierNo;


    /**************下面都不是数据库里面的字段***************/

    /**
     * 业务号
     */
    private List<SupplierInvoiceDetailBillNoDO> detailBillNoDOList;

    /**
     * 采购批次
     */
    private String purchaseBatchBillNo;

    /**
     * 采购订单
     */
    private String purchaseOrderBillNo;

    /**
     * 对账单号
     */
    private String verifyBillNo;
}