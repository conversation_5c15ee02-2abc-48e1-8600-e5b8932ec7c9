package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 开票申请记录
 * @TableName invoice_request_record
 */
@Data
public class InvoiceRequestRecordDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 开票申请id
     */
    private Long requestId;

    /**
     * 开票金额
     */
    private Long amount;

    /**
     * 开票状态： 0 待开票，1 已开票， 2 开票失败
     */
    private Integer status;

    /**
     * 审批意见
     */
    private String financeFeedback;

}