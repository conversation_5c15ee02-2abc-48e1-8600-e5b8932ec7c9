package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 应开变更记录表
 * @TableName invoice_change_record
 */
@Data
public class InvoiceChangeRecordDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 外部业务ID
     */
    private String outBizId;

    /**
     * 外部业务类型，1 采购付款 2 维修完成 3 采购取消 4 授信还款退款 5 授信还款
     */
    private Integer outBizType;

    /**
     * 开票管理id
     */
    private Long manageId;

    /**
     * 变更类型： increase 增加 ，decrease 减少
     */
    private String type;

    /**
     * 变更金额
     */
    private Long amount;

}