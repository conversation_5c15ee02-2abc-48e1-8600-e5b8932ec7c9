package so.dian.invoice.pojo.entity;

import lombok.Data;

import javax.persistence.Transient;
import java.util.Date;

@Data
public class InvoiceExpressesRelationDO {

    private Long id;
    /**
     * '发票id',
     */
    private Long invoiceId;
    /**
     * '物流id',
     */
    private Long expressId;
    /**
     * '物流名称（冗余）',
     */
    private String expressName;
    /**
     * '快递单号',
     */
    private String expressTrackingNo;
    private Date createTime;
    private Date updateTime;
    //'是否删除；0否，1是',
    private Integer deleted;
    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 快递图片
     */
    private String expressImg;
    /**
     * 操作流水号
     */
    private String serialNo;
    /**
     * 发票数量
     * 数据库无此字段
     */
    @Transient
    private Integer invoiceNumbers;
}
