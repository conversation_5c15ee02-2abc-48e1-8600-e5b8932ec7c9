package so.dian.invoice.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.himalaya.common.entity.BaseDO;

/**
 * 供应商发票明细单据号
 *
 * <AUTHOR>
 * @date 2021-03-17 20:03:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierInvoiceDetailBillNoDO extends BaseDO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 供应商发票明细id
     */
    private Integer supplierInvoiceDetailId;
    /**
     * 供应商发票id
     */
    private Integer supplierInvoiceId;
    /**
     * 单据号类型
     */
    private Integer billType;
    /**
     * 单据号
     */
    private String billNo;
}