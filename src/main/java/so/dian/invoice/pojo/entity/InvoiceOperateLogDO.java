package so.dian.invoice.pojo.entity;

import lombok.Data;
import so.dian.commons.eden.entity.BaseDO;

/**
 * @Author: jiaoge
 * @Date: 2019/9/12 11:24 AM
 * @Description:
 */
@Data
public class InvoiceOperateLogDO extends BaseDO{

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 发票id
	 */
	private Integer invoiceId;

	/**
	 * 日志类型
	 */
	private Integer type;

	/**
	 * 日志内容
	 */
	private String content;

	/**
	 * 操作人ID
	 */
	private Long operatorId;

	/**
	 * 操作人名称
	 */
	private String operatorName;


}
