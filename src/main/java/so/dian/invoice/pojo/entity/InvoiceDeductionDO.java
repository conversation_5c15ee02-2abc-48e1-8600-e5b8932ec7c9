package so.dian.invoice.pojo.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import so.dian.invoice.enums.BusinessTypeEnum;

@Data
public class InvoiceDeductionDO {

    private Integer id;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票号
     */
    private String invoiceNo;
    /**
     * 业务单号
     */
    private String businessNo;
    /**
     * 业务类型
     * @see BusinessTypeEnum
     */
    private Integer businessType;
    /**
     * 操作类型
     */
    private Integer operateType;
    /**
     * 创建人ID
     */
    private Long creator;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除：0未删除，1删除
     */
    private Integer deleted;
    /**
     * 核销金额
     */
    private BigDecimal amount;
    /**
     * 原因
     */
    private String reason;

}