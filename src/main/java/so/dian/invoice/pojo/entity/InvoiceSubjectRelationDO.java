package so.dian.invoice.pojo.entity;

import java.util.Date;
import lombok.Data;
import so.dian.invoice.util.StringUtil;

@Data
public class InvoiceSubjectRelationDO {

    private Integer id;

    private Integer bizId;

    private Integer bizType;

    private String subjectName;

    private String relationSubjectName;

    private String remark;

    private Date createTime;

    private Integer creator;

    private Date updateTime;

    private Integer updater;

    private Integer deleted;

    private Integer status;

    private Integer beforeStatus;

    public String getSubjectName() {
        return StringUtil.trimFull2Half(subjectName);
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = StringUtil.trimFull2Half(subjectName);
    }

    public String getRelationSubjectName() {
        return StringUtil.trimFull2Half(relationSubjectName);
    }

    public void setRelationSubjectName(String relationSubjectName) {
        this.relationSubjectName = StringUtil.trimFull2Half(relationSubjectName);
    }
}