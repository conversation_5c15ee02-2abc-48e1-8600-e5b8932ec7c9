package so.dian.invoice.pojo.entity;

import lombok.Data;

/**
 * 开票管理表
 * @TableName invoice_manage
 */
@Data
public class InvoiceManageDO  extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 业务类型 BizTypeEnum 1 采购单，2 维修单
     */
    private Integer bizType;

    /**
     * 购买方id
     */
    private Long subjectId;

    /**
     * 购买方业务类型 0 代理商， 1 合资公司
     */
    private Integer subjectType;

    /**
     * 业务单总金额
     */
    private Long totalAmount;

    /**
     * 业务单创建时间
     */
    private Long bizCreateTime;

    /**
     * 业务单付款完成时间
     */
    private Long paymentTime;

    /**
     * 状态，0 部分开票, 1 开票完成（业务单金额=已开票金额）, 2 未开票
     */
    private Integer status;

}