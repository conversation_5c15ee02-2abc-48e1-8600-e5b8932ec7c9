package so.dian.invoice.pojo.bo;

import java.util.Date;
import java.util.Map;
import lombok.Data;
import so.dian.invoice.enums.InvoiceOperateLogTypeEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/9/12 11:45 AM
 * @Description:
 */
@Data
public class InvoiceOperateLogBO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 发票id
     */
    private Integer invoiceId;
    /**
     * 日志类型
     */
    private InvoiceOperateLogTypeEnum type;
    /**
     * 日志内容
     */
    private Map<String, Object> content;
    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除
     */
    private boolean deleted;
}
