package so.dian.invoice.pojo.bo;

import lombok.Data;
import so.dian.invoice.enums.InvoiceSubjectRelationStatusEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/8/27 3:49 PM
 * @Description:
 */
@Data
public class InvoiceSubjectRelationBO {

	private Integer id;

	private Integer bizId;

	private Integer bizType;

	private String subjectName;

	private String relationSubjectName;

	private String remark;

	private Long createTime;

	private Integer creator;

	private Long updateTime;

	private Integer updater;

	private Integer deleted;

	private InvoiceSubjectRelationStatusEnum statusEnum;
}
