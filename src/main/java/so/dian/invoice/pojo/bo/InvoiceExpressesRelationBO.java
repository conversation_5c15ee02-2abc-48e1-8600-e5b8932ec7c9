package so.dian.invoice.pojo.bo;

import lombok.*;
import so.dian.commons.eden.enums.DeletedEnum;

import javax.persistence.Transient;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class InvoiceExpressesRelationBO {
    private Long id;
    /**
     * '发票id',
     */
    private Long invoiceId;
    /**
     * '物流id',
     */
    private Long expressId;
    /**
     * '物流名称（冗余）',
     */
    private String expressName;
    /**
     * '快递单号',
     */
    private String expressTrackingNo;
    private Date createTime;
    private Date updateTime;
    //'是否删除；0否，1是',
    private DeletedEnum deleted;
    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 快递图片
     */
    private String expressImg;
    /**
     * 操作流水号
     */
    private String serialNo;
    /**
     * 发票数量
     * 数据库无此字段
     */
    private Integer invoiceNumbers;
}
