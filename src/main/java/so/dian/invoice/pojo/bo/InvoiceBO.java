package so.dian.invoice.pojo.bo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 9:29 PM
 * @Description:
 */
@Data
public class InvoiceBO {

	private Integer id;

	private String invoiceNo;

	private String supplierNo;

	private String invoiceCode;

	private Integer subjectType;

	private String subjectName;

	private String billNo;

	private Integer invoiceType;

	private BigDecimal rawPrice;

	private BigDecimal taxPrice;

	private BigDecimal price;

	private Date receiveTime;

	private String memo;

	private Integer invoiceStatus;

	private BigDecimal usedAmount;

	private Date gmtCreate;

	private Date gmtModified;

	private Integer isDelete;

	private Integer creator;

	private Date createTime;

	private Integer invoiceSource;

	private String kind;

	private String checkCode;

	/**
	 * 税额
	 */
	private BigDecimal tax;

	private String seller;

	private String sellerTaxId;

	private String buyer;

	private String buyerTaxId;

	private String url;

	private Integer isReal;

	private String batchNo;

	/**
	 * 从属主体类型（参考枚举：BelongSubjectTypeEnum）
	 */
	private Integer belongSubjectType;

	/**
	 * 从属主体ID
	 */
	private Integer belongSubjectId;

	/**
	 * 发票流转状态
	 */
	private InvoiceProcessStatusEnum invoiceProcessStatusEnum;

	/**
	 * 发票复核人
	 */
	private Long reviewer;

	/**
	 * 发票复核时间
	 */
	private Date reviewTime;

	/**
	 * 发票复核时间
	 */
	private String reviewRemark;

	/**
	 * 验真失败错误码
	 * @see so.dian.invoice.enums.InvoiceIdentifyRecordEnum.ValidationCodeEnum
	 */
	private String validateCode;

	/**
	 * 税率
	 */
	private BigDecimal taxRate;

}
