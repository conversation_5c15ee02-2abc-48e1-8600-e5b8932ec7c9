package so.dian.invoice.pojo.bo;

import java.util.Date;
import lombok.Data;
import so.dian.invoice.enums.CheckInvoiceStatusEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:15 AM
 * @Description:
 */
@Data
public class CheckInvoiceBO extends BaseBO {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 结论ID
	 */
	private Long conclusionId;

	/**
	 * 质检备注
	 */
	private String remark;

	/**
	 * 质检状态
	 */
	private CheckInvoiceStatusEnum checkInvoiceStatusEnum;

	/**
	 * 质检人
	 */
	private Long checker;

	/**
	 * 质检时间
	 */
	private Date checkTime;

	/**
	 * 发票ID
	 */
	private Long invoiceId;

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 关联物流单号
	 */
	private String expressNo;

	/**
	 * 录入人
	 */
	private Long operator;

	/**
	 * 发票所属城市code
	 */
	private Integer cityCode;
}
