package so.dian.invoice.pojo.bo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/9/16 11:40 AM
 * @Description:
 */
@Data
public class InvoiceDetailBO {

	private Integer id;

	private String invoiceCode;

	private String invoiceNo;

	private String materialName;

	private String materialSpec;

	private String unit;

	private BigDecimal unitPrice;

	private Integer quantity;

	private BigDecimal rawPrice;

	private BigDecimal taxRate;

	private BigDecimal taxPrice;

	private Date gmtCreate;

	private Date gmtModified;

	private Integer isDelete;

	private BigDecimal price;
}
