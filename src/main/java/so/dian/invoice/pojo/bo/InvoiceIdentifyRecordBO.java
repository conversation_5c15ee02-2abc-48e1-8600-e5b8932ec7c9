package so.dian.invoice.pojo.bo;

import lombok.Data;

/**
 * @Author: jiaoge
 * @Date: 2019/12/27 2:46 PM
 * @Description:
 */
@Data
public class InvoiceIdentifyRecordBO {

	/**
	 * 发票号码
	 */
	private String invoiceNo;

	/**
	 * 发票代码
	 */
	private String invoiceCode;

	/**
	 * 开票时间
	 */
	private String invoiceDate;

	/**
	 * 发票检验码
	 */
	private String checkCode;

	/**
	 * 税前金额
	 */
	private String pretaxAmount;

	/**
	 * 总金额
	 */
	private String total;

	/**
	 * 税额
	 */
	private String tax;

	/**
	 * 开票明细
	 */
	private String details;

	/**
	 * 发票销售方名称
	 */
	private String seller;

	/**
	 * 发票购买方名称
	 */
	private String buyer;

	/**
	 * 发票识别结果集
	 */
	private String result;

}
