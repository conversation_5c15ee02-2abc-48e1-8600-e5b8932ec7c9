package so.dian.invoice.volidator;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalObjectUtils;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;
import so.dian.invoice.enums.InvoiceSourceEnum;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.enums.SubjectTypeEnum;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.pojo.param.AddSingleInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceFilterTimeParam;
import so.dian.invoice.pojo.param.InvoiceExportParam;
import so.dian.invoice.pojo.param.InvoiceInfoParam;
import so.dian.invoice.pojo.param.InvoiceReviewParam;
import so.dian.lvy.pojo.enums.MainBizTypeEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 9:37 PM
 * @Description:
 */
@Slf4j
public class InvoiceValidator {

	public static void checkInvoiceReviewParam(InvoiceReviewParam param) {
		if (LocalObjectUtils.allNull(param.getId(), param.getStatus(), param.getSuggestion(), param.getUserId())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}

		//枚举判断
		if (!InvoiceProcessStatusEnum.REVIEW_STATUS.contains(param.getStatus())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}
	}

	public static void checkInvoiceExportParam(InvoiceExportParam param) {
		//业务类型判断
		if (Objects.nonNull(param.getSubjectType())) {
			if (Objects.isNull(SubjectTypeEnum.getBizType(param.getSubjectType()))) {
				throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
			}
		}

		//发票验真状态判断
		if (Objects.nonNull(param.getIsReal())) {
			if (Objects.isNull(InvoiceIdentifyRecordEnum.IsRealEnum.getByCode(param.getIsReal()))) {
				throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
			}
		}

		//发票类型判断
		if (Objects.nonNull(param.getType())) {
			if (Objects.isNull(InvoiceTypeEnum.getField(param.getType()))) {
				throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
			}
		}

		//发票流转状态判断
		if (Objects.nonNull(param.getProcessStatus())) {
			InvoiceProcessStatusEnum processStatusEnum =
					LocalEnumUtils.findByCodeWithoutDefault(InvoiceProcessStatusEnum.class,
							param.getProcessStatus());
			if (Objects.isNull(processStatusEnum)) {
				throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
			}
		}

		//发票来源判断
		if (Objects.nonNull(param.getSource())) {
			if (Objects.isNull(InvoiceSourceEnum.getField(param.getSource()))) {
				throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
			}
		}
	}

	public static void checkInvoiceCreateTimeParam(InvoiceFilterTimeParam param) {

		if (Objects.isNull(param.getFilterTime()) || Objects.isNull(param.getPageNo())
				|| Objects.isNull(param.getPageSize())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}
	}

	public static void checkAddSingleInvoiceParam(AddSingleInvoiceParam param) {

		if(!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(param.getType())) && StringUtils.isBlank(param.getInvoiceCode())){
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}

		if(InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(param.getType())) && StringUtils.isNotBlank(param.getInvoiceCode())){
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}

		if (StringUtils.isBlank(param.getBuyer()) || Objects.isNull(param.getType()) || Objects.isNull(param.getGmtCreate()) || StringUtils.isBlank(param
				.getInvoiceNo()) || StringUtils.isBlank(param.getSeller()) || Objects.isNull(param.getSubjectType())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}

		if (StringUtils.isBlank(param.getDetails())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_DETAILS_MUST_NOT_BE_NULL);
		}

		if (Objects.isNull(param.getTax()) || Objects.isNull(param.getPrice()) || Objects.isNull(param.getRawPrice())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_PRICE_SHOULD_BE_NOT_NULL);
		}

		if (StringUtils.isBlank(param.getTax())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_TAX_MUST_NOT_BE_NULL);
		}
		if (StringUtils.isBlank(param.getPrice())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_PRICE_MUST_NOT_BE_NULL);
		}
		if (StringUtils.isBlank(param.getRawPrice())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_RAW_PRICE_MUST_NOT_BE_NULL);
		}

		BigDecimal tax = new BigDecimal(param.getTax());
		BigDecimal price = new BigDecimal(param.getPrice());
		BigDecimal rawPrice = new BigDecimal(param.getRawPrice());
		if (tax.add(rawPrice).compareTo(price) != 0) {
			log.info("税额,tax:{}",param.getTax(),",不含税金额,rawPrice:{}",param.getRawPrice()
					+",总金额,price:{}"+param.getPrice());
			throw BizException.create(InvoiceErrorCodeEnum.TOTAL_PRICE_TAX_MUST_EQUALS_TOTAL_AMOUNT);
		}

		if (Objects.nonNull(param.getSubjectType())) {
			String subjectTypeName = SubjectTypeEnum.getField(param.getSubjectType());
			if (StringUtils.isBlank(subjectTypeName)) {
				throw BizException.create(InvoiceErrorCodeEnum.INVOICE_SUBJECT_TYPE_ERROR);
			}
		}

		if (!checkInvoice(InvoiceTypeEnum.getByType(param.getType()))) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_TYPE_ERROR);
		}
	}

	public static void checkUpdateParam(InvoiceInfoParam param) {
		if(!InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(param.getType())) && StringUtils.isBlank(param.getInvoiceCode())){
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}

		if(InvoiceValidator.checkFullPowerInvoice(InvoiceTypeEnum.getByType(param.getType())) && StringUtils.isNotBlank(param.getInvoiceCode())){
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}
		if (StringUtils.isBlank(param.getBuyer()) || Objects.isNull(param.getType()) || Objects.isNull(param.getGmtCreate()) || StringUtils.isBlank(param
				.getInvoiceNo()) || StringUtils.isBlank(param.getSeller()) || Objects.isNull(param.getSubjectType())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}

		if (Objects.isNull(param.getTax()) || Objects.isNull(param.getPrice()) || Objects.isNull(param.getRawPrice())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_PRICE_SHOULD_BE_NOT_NULL);
		}

		if (StringUtils.isBlank(param.getDetails())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_DETAILS_MUST_NOT_BE_NULL);
		}

		if (param.getTax().add(param.getRawPrice()).compareTo(param.getPrice()) != 0) {
			log.info("税额,tax:{}",param.getTax(),",不含税金额,rawPrice:{}",param.getRawPrice()
					+",总金额,price:{}"+param.getPrice());
			throw BizException.create(InvoiceErrorCodeEnum.TOTAL_PRICE_TAX_MUST_EQUALS_TOTAL_AMOUNT);
		}

		if (!checkInvoice(InvoiceTypeEnum.getByType(param.getType()))) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_TYPE_ERROR);
		}

		String subjectTypeName = SubjectTypeEnum.getField(param.getSubjectType());
		if (StringUtils.isBlank(subjectTypeName)) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_SUBJECT_TYPE_ERROR);
		}
	}

	public static Boolean checkFullPowerInvoice(InvoiceTypeEnum invoiceTypeEnum){
		if(invoiceTypeEnum == null){
			return false;
		}
		List<InvoiceTypeEnum> InvoiceTypeEnums = Lists.newArrayList(
				InvoiceTypeEnum.FULL_POWER_MAJOR,//电子发票(专用发票)
				InvoiceTypeEnum.FULL_POWER_GENERAL//电子发票(普通发票)
		);
		if(InvoiceTypeEnums.contains(invoiceTypeEnum)){
			return true;
		}
		return false;
	}

	public static Boolean checkInvoice(InvoiceTypeEnum invoiceTypeEnum){
		if(invoiceTypeEnum == null){
			return false;
		}
		List<InvoiceTypeEnum> InvoiceTypeEnums = Lists.newArrayList(
				InvoiceTypeEnum.VAT,
				InvoiceTypeEnum.ORD,
				InvoiceTypeEnum.ORD_ELECTR,
				InvoiceTypeEnum.ORD_ROLL,
				InvoiceTypeEnum.MACHINE,
				InvoiceTypeEnum.FULL_POWER_MAJOR,//电子发票(专用发票)
				InvoiceTypeEnum.FULL_POWER_GENERAL,//电子发票(普通发票)
				InvoiceTypeEnum.NONE
		);
		if(InvoiceTypeEnums.contains(invoiceTypeEnum)){
			return true;
		}
		return false;
	}


}
