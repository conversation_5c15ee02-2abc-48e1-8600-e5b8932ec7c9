package so.dian.invoice.volidator;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;

import java.util.List;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 9:37 PM
 * @Description:
 */
@Slf4j
public class InvoiceTypeValidator {
    public static List<InvoiceTypeEnum> getInvoiceTypeForPlatform(){
        List<InvoiceTypeEnum> invoiceTypeEnums = Lists.newArrayList(
                InvoiceTypeEnum.VAT,//增值税专用发票
                InvoiceTypeEnum.ORD,//增值税普通发票
                InvoiceTypeEnum.ORD_ELECTR,//增值税电子普通发票
                InvoiceTypeEnum.ORD_ROLL,//增值税普通发票(卷票)
                InvoiceTypeEnum.MACHINE,//机打发票
                InvoiceTypeEnum.FULL_POWER_MAJOR,//电子发票(专用发票)
                InvoiceTypeEnum.FULL_POWER_GENERAL//电子发票(普通发票)
        );
        return invoiceTypeEnums;
    }
}
