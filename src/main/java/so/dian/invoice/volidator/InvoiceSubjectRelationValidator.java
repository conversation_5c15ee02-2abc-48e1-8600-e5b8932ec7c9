package so.dian.invoice.volidator;

import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.util.LocalObjectUtils;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.enums.error.InvoiceSubjectRelationErrorCodeEnum;
import so.dian.invoice.pojo.param.AddInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvalidInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.param.InvoiceSubjectRelationPageParam;

/**
 * @Author: jiaoge
 * @Date: 2019/8/27 2:48 PM
 * @Description:
 */
public class InvoiceSubjectRelationValidator {

	public static void checkInvoiceSubjectRelationPageParam(InvoiceSubjectRelationPageParam param) {
		if (Objects.isNull(param) || LocalObjectUtils.allNull(param.getPageNo(), param.getPageSize())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}
		if (param.getPageNo()<1) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}
	}

	public static void checkAddInvoiceSubjectRelationParam(AddInvoiceSubjectRelationParam param) {
		if (Objects.isNull(param) || LocalObjectUtils.allNull(param.getUserId(), param.getMerchantId())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}
		if (CollectionUtils.isEmpty(param.getSubjectNameList())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}

		long distinctCount = param.getSubjectNameList().stream().distinct().count();
		int size = param.getSubjectNameList().size();
		if (distinctCount < size) {
			throw BizException.create(InvoiceSubjectRelationErrorCodeEnum.INVOICE_SUBJECT_REPEAT);
		}
	}

	public static void checkInvalidInvoiceSubjectRelationParam(InvalidInvoiceSubjectRelationParam param) {
		if (Objects.isNull(param) || LocalObjectUtils.allNull(param.getId(), param.getUserId())) {
			throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL);
		}
	}


}
