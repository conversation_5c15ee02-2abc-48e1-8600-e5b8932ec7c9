package so.dian.invoice.volidator;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.exception.BizException;
import so.dian.invoice.enums.BelongSubjectTypeEnum;
import so.dian.invoice.enums.BuyerTaxIdEnum;
import so.dian.invoice.enums.CompanySubjectEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * InvoiceBuyerValidator
 *
 * <AUTHOR>
 */
@Slf4j
public class InvoiceBuyerValidator {

    public static final String errorMsg = "抬头信息校验失败";

    private static final Date BJ_YDY_2020_03_25 = DateUtil.parse("2020-03-25 00:00:00",
            DatePattern.NORM_DATETIME_FORMAT);

    /**
     * 【杭州小电科技股份有限公司】发票的开票日期>=2020-6-29
     */
    private static final Date XIAO_DIAN_2020_06_29 = DateUtil.parse("2020-06-29 00:00:00",
            DatePattern.NORM_DATETIME_FORMAT);

    /**
     * 2020-03-25<=【杭州伊电园网络科技有限公司】发票开票日期<=2020-07-31
     */
    private static final Date YIDIANYUAN_2020_07_31 = DateUtil.parse("2020-07-31 00:00:00",
            DatePattern.NORM_DATETIME_FORMAT);

    private static final List<String> NO_CHECK_INVOICE_BUYER_LIST = Arrays.asList(BuyerTaxIdEnum.HZ_YD1_INVOICE.getBuyer(),
            BuyerTaxIdEnum.XM_XD_INVOICE.getBuyer(),
            BuyerTaxIdEnum.YOUDIAN_INVOICE.getBuyer(),BuyerTaxIdEnum.HZ_MG_ZNSBYXGS.getBuyer(),
            BuyerTaxIdEnum.HZ_XD_ZNSBYXGS.getBuyer(),BuyerTaxIdEnum.HZ_YW_ZNSBYXGS.getBuyer());
    /**
     * 小电校验，非小电不校验
     *
     * @param belongSubjectType
     * @param buyerTaxId
     * @return true正确，false错误
     */
    public static boolean checkTaxId(Integer belongSubjectType, String buyerTaxId) {
        if (!BelongSubjectTypeEnum.isXD(belongSubjectType)) {
            return true;
        } else {
            return BuyerTaxIdEnum.checkTaxId(buyerTaxId);
        }
    }

    /**
     * 小电校验，非小电不校验
     *
     * @param belongSubjectType
     * @param buyer
     * @return true正确，false错误
     */
    public static boolean checkBuyer(Integer belongSubjectType, String buyer) {
        if (!BelongSubjectTypeEnum.isXD(belongSubjectType)) {
            return true;
        } else {
            return BuyerTaxIdEnum.checkBuyer(buyer);
        }
    }

    /**
     * 小电校验，非小电不校验
     *
     * @param belongSubjectType
     * @param buyer
     * @param buyerTaxId
     * @return true正确，false错误
     */
    public static boolean checkInvoiceBuyerAndTaxId(Integer belongSubjectType, String buyer, String buyerTaxId) {
        if (!BelongSubjectTypeEnum.isXD(belongSubjectType)) {
            return true;
        } else {
            return BuyerTaxIdEnum.checkInvoiceBuyerAndTaxId(buyer, buyerTaxId);
        }
    }

    /**
     * 发票日期校验，有几个发票的购买方日期，必须在规定日期中
     * <p>
     * 【北京伊电园网络科技有限公司】开票日期 <= 2020-03-25 00:00:00
     * 2020-03-25<=【杭州伊电园网络科技有限公司】发票开票日期<=2020-07-31
     * 【杭州小电科技股份有限公司】> 2020-06-29 00:00:00
     * 去除时间校验
     * </p>
     *
     * @param buyer
     * @param invoiceDate
     * @param onlyCheckOwnerSubject
     * @return
     */
    public static String validInvoiceBuyer(Integer belongSubjectType, String buyer, Date invoiceDate, Boolean onlyCheckOwnerSubject) {
        if(!BelongSubjectTypeEnum.isXD(belongSubjectType)){
            return null;
        }
        boolean checkBuyer = BuyerTaxIdEnum.checkBuyer(buyer);
        if (checkBuyer) {
            return null;
        }
        if (!onlyCheckOwnerSubject) {
            return null;
        }
        return errorMsg;
       // if(InvoiceConstants.DIAN_INVOICE_BUYER.equals(buyer)) {
       //     if(Objects.isNull(invoiceDate)) {
       //         return "北京伊电园开票日期有误";
       //     }
       //     if(BJ_YDY_DEADLINE_DATE.compareTo(invoiceDate) <= 0) {
       //         return "北京伊电园开票日期有误";
       //     }
       //     return null;
       // }
       // if(InvoiceConstants.HZ_DIAN_INVOICE_BUYER.equals(buyer)) {
       //     if(Objects.isNull(invoiceDate)) {
       //         return "杭州伊电园开票日期有误";
       //     }
       //     if(BJ_YDY_DEADLINE_DATE.compareTo(invoiceDate) > 0) {
       //         return "杭州伊电园开票日期有误";
       //     }
       //     if (YIDIANYUAN_INVOICE_DATE_END.compareTo(invoiceDate) < 0) {
       //         return "杭州伊电园开票日期有误";
       //     }
       //     return null;
       // }
       // if(InvoiceConstants.YOUDIAN_INVOICE_BUYER.equals(buyer)) {
       //     return null;
       // }
       // if(InvoiceConstants.XIAODIAN_INVOICE_BUYER.equals(buyer)) {
       //     if(Objects.isNull(invoiceDate)) {
       //         return "杭州小电科技股份有限公司开票日期有误";
       //     }
       //     if(XIAO_DIAN_INVOICE_DATE.compareTo(invoiceDate) > 0) {
       //         return "杭州小电科技股份有限公司开票日期有误";
       //     }
       //     return null;
       // }
       // if (!onlyCheckOwnerSubject) {
       //     return null;
       // }
       // // 以下不需要校验
       // if(Objects.equals(InvoiceConstants.HZ_YD1_INVOICE_BUYER, buyer)
       //       || Objects.equals(InvoiceConstants.XM_XD_INVOICE_BUYER, buyer)
       //       || Objects.equals(BuyerTaxIdEnum.HZ_XD_ZHENGZHOU_INVOICE.getBuyer(), buyer)
       //       || Objects.equals(BuyerTaxIdEnum.HZ_YD_CIXIAN_INVOICE.getBuyer(), buyer)) {
       //     return null;
       // }
       //return "抬头信息校验失败";
    }

    /**
     * 非小电不校验
     * @param belongSubjectType
     * @param buyer
     * @param invoiceDate
     * @param onlyCheckOwnerSubject
     * @return
     */
    public static boolean validDianInvoiceBuyer(Integer belongSubjectType,String buyer, Date invoiceDate, Boolean onlyCheckOwnerSubject) {
        if(!BelongSubjectTypeEnum.isXD(belongSubjectType)){
            return true;
        }
        if(BuyerTaxIdEnum.DIAN_INVOICE.getBuyer().equals(buyer)) {
            return !Objects.isNull(invoiceDate) && BJ_YDY_2020_03_25.compareTo(invoiceDate) > 0;
        }
        if(BuyerTaxIdEnum.HZ_DIAN_INVOICE.getBuyer().equals(buyer)) {
            return !Objects.isNull(invoiceDate)
                    && BJ_YDY_2020_03_25.compareTo(invoiceDate) <= 0
                    && YIDIANYUAN_2020_07_31.compareTo(invoiceDate) >= 0;
        }

        if(BuyerTaxIdEnum.XIAODIAN_INVOICE.getBuyer().equals(buyer)) {
            return !Objects.isNull(invoiceDate) && XIAO_DIAN_2020_06_29.compareTo(invoiceDate) <= 0;
        }
        // 杭州有电 & 厦门小电
        if(NO_CHECK_INVOICE_BUYER_LIST.contains(buyer)) {
            return true;
        }
        return !onlyCheckOwnerSubject;
    }

    /**
     * 非小电不校验
     * @param invoiceDate
     * @param subjectName
     */
    public static void checkInvoiceDateRange(Integer belongSubjectType,Date invoiceDate, String subjectName) {
        if(!BelongSubjectTypeEnum.isXD(belongSubjectType)){
            return ;
        }
        if (Objects.isNull(invoiceDate)) {
            throw BizException.create(InvoiceErrorCodeEnum.PARAM_RESOLVER_FAIL);
        }

        CompanySubjectEnum subjectEnum = CompanySubjectEnum.getEnumByDesc(subjectName);
        if (Objects.isNull(subjectEnum)) {
            log.warn("购买方非我司发票,不校验日期,subjectName:{}", subjectName);
            return;
        }

        switch (subjectEnum) {
            case HZYIDIANYUAN:
                boolean containRangeForYiDianYuan = BJ_YDY_2020_03_25.getTime() <= invoiceDate.getTime()
                        && invoiceDate.getTime() <= YIDIANYUAN_2020_07_31.getTime();
                if (!containRangeForYiDianYuan) {
                    throw BizException.create(InvoiceErrorCodeEnum.INVOICE_DATE_ERROR_YIDIANYUAN);
                }
                break;
            case XIAODIAN:
                boolean containRangeForXiaoDian = XIAO_DIAN_2020_06_29.getTime() <= invoiceDate.getTime();
                if (!containRangeForXiaoDian) {
                    throw BizException.create(InvoiceErrorCodeEnum.INVOICE_DATE_ERROR);
                }
                break;
            default:
                break;
        }
    }
}
