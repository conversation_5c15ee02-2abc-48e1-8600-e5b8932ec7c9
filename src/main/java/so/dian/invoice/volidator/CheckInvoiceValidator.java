package so.dian.invoice.volidator;

import java.util.Objects;
import so.dian.commons.eden.exception.BizException;
import so.dian.invoice.pojo.param.CheckInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceCheckConclusionParam;
import so.dian.invoice.pojo.param.InvoiceCheckDetailsParam;
import so.dian.invoice.pojo.param.InvoiceCheckPageParam;
import so.dian.invoice.pojo.param.InvoiceCheckRateParam;
import so.dian.invoice.pojo.param.InvoiceCheckerParam;
import so.dian.invoice.util.biz.AssertUtils;

import static so.dian.invoice.enums.error.CheckInvoiceErrorCodeEnum.*;
import static so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum.*;

/**
 * @Author: jiaoge
 * @Date: 2020/1/2 4:57 PM
 * @Description:
 */
public class CheckInvoiceValidator {

	public static void checkInvoiceCheckPageParam(InvoiceCheckPageParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
		if (Objects.isNull(param.getPageNo()) || Objects.isNull(param.getPageSize()) || param.getPageNo()<=0 ||
				Objects.isNull(param.getStatus())) {
			throw BizException.create(PARAM_RESOLVER_FAIL);
		}
	}

	public static void checkCheckInvoiceParam(CheckInvoiceParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
		AssertUtils.notEmptyWithBizExp(param.getId(), PARAM_RESOLVER_FAIL);
		AssertUtils.notEmptyWithBizExp(param.getCheckResult(), PARAM_RESOLVER_FAIL);
	}

	public static void checkInvoiceCheckDetailsParam(InvoiceCheckDetailsParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
	}

	public static void checkInvoiceCheckRateParam(InvoiceCheckRateParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
		AssertUtils.notEmptyWithBizExp(param.getCheckRate(), PARAM_RESOLVER_FAIL);
		if (param.getCheckRate() < 0) {
			throw BizException.create(CHECK_RATE_MUST_NOT_LESS_THAN_0);
		}
	}

	public static void checkInvoiceCheckerParam(InvoiceCheckerParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
		AssertUtils.notEmptyWithBizExp(param.getChecker(), CHECKER_MUST_NOT_BE_NULL);
		AssertUtils.collectionIsEmptyWithBizExp(param.getRegionList(), CHECK_REGION_MUST_NOT_BE_NULL);
	}

	public static void checkInvoiceCheckConclusionParam(InvoiceCheckConclusionParam param) {
		AssertUtils.notEmptyWithBizExp(param, PARAM_RESOLVER_FAIL);
		AssertUtils.notEmptyWithBizExp(param.getCode(), CHECK_CONCLUSION_MUST_NOT_BE_NULL);
	}

}
