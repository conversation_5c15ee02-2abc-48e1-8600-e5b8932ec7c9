package so.dian.invoice.enums;

import lombok.Getter;

/**
 * 开票申请状态枚举
 */
@Getter
public enum InvoiceRequestStatusEnum {
    UNKNOWN(-1, "未知状态"),
    PENDING_INVOICE(0, "待开票"),
    COMPLETED_INVOICE(1, "已开票"),
    REFUSED_INVOICE(2, "拒绝开票")
    ;

    InvoiceRequestStatusEnum(Integer code, String desc) {
	this.code = code;
	this.desc = desc;
    }

    private Integer code;
    private String desc;

    public static InvoiceRequestStatusEnum getByCode(Integer code) {
	for (InvoiceRequestStatusEnum invoiceRequestStatusEnum : values()) {
	    if (invoiceRequestStatusEnum.getCode().equals(code)) {
		return invoiceRequestStatusEnum;
	    }
	}
	return null;
    }
}
