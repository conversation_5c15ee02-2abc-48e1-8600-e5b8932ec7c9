package so.dian.invoice.enums;

public enum InvoiceSourceEnum {

    MANUAL(1, "手动录入"),
    BATCH(2, "模版导入"),
    AUTO(3,"自动录入"),
    SUPPLIER_TRANSMIT(4,"供应商发票转入")
    ;


    InvoiceSourceEnum(int type, String field) {

        this.type = type;
        this.field = field;

    }

    private int type;

    private String field;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public static String getField(int type) {

        for(InvoiceSourceEnum source : InvoiceSourceEnum.values()) {
            if(source.getType() == type) {
                return source.getField();
            }
        }

        return null;
    }
}
