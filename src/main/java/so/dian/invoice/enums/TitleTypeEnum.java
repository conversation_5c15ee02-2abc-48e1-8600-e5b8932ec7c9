package so.dian.invoice.enums;

public enum TitleTypeEnum {
    UNKNOWN(0, "未知"),
    COMPANY(1,"企业"),
    PERSON(2,"个人");

    public int code;

    public String name;

    TitleTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static TitleTypeEnum getByCode(Integer code){
        if(code==null){
            return UNKNOWN;
        }
        for(TitleTypeEnum e : values()){
            if(e.getCode() == code){
                return e;
            }
        }
        return UNKNOWN;
    }
}