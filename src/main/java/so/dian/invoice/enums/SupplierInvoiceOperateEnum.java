package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 4:46 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SupplierInvoiceOperateEnum implements EnumInterface<SupplierInvoiceOperateEnum> {

    导入发票(1, "导入发票"),
    删除发票(2, "删除发票"),
    转发票台账(3, "转发票台账"),
    上传附件(4, "上传附件"),
    删除附件(5, "删除附件"),

    ;

    private Integer code;
    private String desc;

    @Override
    public SupplierInvoiceOperateEnum getDefault() {
        return null;
    }

}
