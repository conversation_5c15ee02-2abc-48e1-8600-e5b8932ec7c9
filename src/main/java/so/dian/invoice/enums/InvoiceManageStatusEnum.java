package so.dian.invoice.enums;

import lombok.Getter;

/**
 * 开票申请状态枚举
 */
@Getter
public enum InvoiceManageStatusEnum {

    PENDING_INVOICE(0, "开票中"),
    COMPLETED_INVOICE(1, "开票完成"),
//    NOT_INVOICE(2, "未开票")
    ;

    InvoiceManageStatusEnum(Integer code, String desc) {
	this.code = code;
	this.desc = desc;
    }

    private Integer code;
    private String desc;

    public static InvoiceManageStatusEnum from(Integer code) {
	for (InvoiceManageStatusEnum statusEnum : values()) {
	    if (statusEnum.code.equals(code)) {
		return statusEnum;
	    }
	}
	return null;
    }
}
