package so.dian.invoice.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @author: yuechuan
 * @create: 2025/03/21 14:18
 * @description:
 */
@Getter
public enum BizTypeEnum {
    PURCHASE_ORDER(1, "设备采购"),
    REPAIR_ORDER(2, "设备维修")
    ;
    private final Integer code;
    private final String desc;

    BizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer code() {
        return this.code;
    }

    public String desc() {
        return this.desc;
    }

    public static BizTypeEnum getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(r -> r.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
