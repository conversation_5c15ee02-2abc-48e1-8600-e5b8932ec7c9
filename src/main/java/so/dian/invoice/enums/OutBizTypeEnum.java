package so.dian.invoice.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @author: yuechuan
 * @create: 2025/03/15 14:18
 * @description:
 */
@Getter
public enum OutBizTypeEnum {
    PURCHASE_PAYMENT(1, "采购付款"),

    REPAIR_ORDER_COMPLETE(2, "维修完成"),

    PURCHASE_CANCEL(3, "采购取消"),

    CREDIT_REPAYMENT_REFUND(4, "授信还款退款"),

    CREDIT_REPAYMENT_SUCCESS(5, "授信还款");

    private final Integer code;
    private final String desc;

    OutBizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OutBizTypeEnum from(Integer code) {
        return Arrays.stream(values())
                .filter(r -> r.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
