package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 4:46 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SupplierInvoiceBillNoEnum implements EnumInterface<SupplierInvoiceBillNoEnum> {

	PURCHASE_BATCH(1,"采购批次"),
	PURCHASE_ORDER(2,"采购订单"),
	VERIFY(3,"对账单号"),

	;

	private Integer code;
	private String desc;

	@Override
	public SupplierInvoiceBillNoEnum getDefault() {
		return null;
	}

}
