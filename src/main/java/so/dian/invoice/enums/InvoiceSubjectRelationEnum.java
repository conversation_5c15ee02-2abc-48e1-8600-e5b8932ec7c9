package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public enum InvoiceSubjectRelationEnum {
    ;

    @Getter
    @AllArgsConstructor
    public enum BizTypeEnum {

        MERCHANT(0, "商户"),
        CHANNEL(1, "渠道"),;

        Integer code;
        String desc;

        public static BizTypeEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (BizTypeEnum temp : BizTypeEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum DeletedEnum {

        UN_DELETED(0, "未删除"),
        DELETED(-1, "删除");

        Integer code;
        String desc;

        public static  DeletedEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (DeletedEnum temp : DeletedEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
    }
}
