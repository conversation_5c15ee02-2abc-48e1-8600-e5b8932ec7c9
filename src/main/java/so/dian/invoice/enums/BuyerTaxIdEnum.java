package so.dian.invoice.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 4:46 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum BuyerTaxIdEnum {

    DIAN_INVOICE("北京伊电园网络科技有限公司", "91110105MA00A4QR97"),
    HZ_DIAN_INVOICE("杭州伊电园网络科技有限公司", "91110105MA00A4QR97"),
    XIAODIAN_INVOICE("杭州小电科技股份有限公司", "91110105MA00A4QR97"),
    YOUDIAN_INVOICE("杭州友电科技有限公司", "91330110MA2B0UY84N"),
    HZ_YD1_INVOICE("杭州有电科技有限公司", "91330110MA2AXBAH2J"),
    XM_XD_INVOICE("厦门小电科技有限公司", "91350203MA341C3Y1K"),
    HZ_YD_CIXIAN_INVOICE("杭州友电科技有限公司磁县分公司", "91130427MA0GKFGP8N"),
    HZ_XD_ZHENGZHOU_INVOICE("杭州小电科技股份有限公司郑州分公司", "91410122MA9K0MK33F"),
    HZ_MG_WLKJYXGS("杭州满格网络科技有限公司", "91330110MA2H3B4P2F"),
    HZ_MG_ZNSBYXGS("杭州满格智能设备有限公司","91330110MA2H3B4P2F"),
    HZ_XD_ZNSBYXGS( "杭州小电智能设备有限公司", "91330110MAC152UA51"),
    HZ_YW_ZNSBYXGS("杭州易维智能设备有限公司","91330110MACECPLJ4X");

    /**
     * 购买方
     * 发票抬头
     */
    private String buyer;

    /**
     * 购买方税号
     */
    private String taxId;

    /**
     * k:购买方——————v:购买方税号
     */
    private static Map<String, String> buyerTaxIdMap = new HashMap<>();
    private static List<String> buyers = new ArrayList<>();
    private static List<String> taxIds = new ArrayList<>();
    /**
     * 伊电园购票方
     */
    private static List<String> YDY_BUYER_LIST;

    static {
        BuyerTaxIdEnum[] buyerTaxIdEnums = BuyerTaxIdEnum.values();
        for (BuyerTaxIdEnum buyerTaxIdEnum : buyerTaxIdEnums) {
            buyerTaxIdMap.put(buyerTaxIdEnum.buyer, buyerTaxIdEnum.taxId);
            buyers.add(buyerTaxIdEnum.buyer);
            taxIds.add(buyerTaxIdEnum.taxId);
        }

        buyers = buyers.stream().distinct().collect(Collectors.toList());
        taxIds = taxIds.stream().distinct().collect(Collectors.toList());

        YDY_BUYER_LIST = Lists.newArrayList(DIAN_INVOICE.buyer, HZ_DIAN_INVOICE.buyer,
                XIAODIAN_INVOICE.buyer);
    }

    public static String getTaxIdByBuyer(String buyer) {
        if(buyerTaxIdMap.containsKey(buyer)){
            return buyerTaxIdMap.get(buyer);
        }
        return "";
    }

    /**
     * 校验小电进项发票抬头
     * @param buyer
     * @param buyerTaxId
     *@return true正确，false错误
     */
    public static boolean checkInvoiceBuyerAndTaxId(String buyer, String buyerTaxId) {
        String existTaxId = buyerTaxIdMap.get(buyer);
        if (Objects.isNull(existTaxId)) {
            return false;
        }
        return Objects.equals(existTaxId, buyerTaxId);
    }

    /**
     * @param buyer
     * @return true正确，false错误
     */
    public static boolean checkBuyer( String buyer) {
        return buyers.contains(buyer);
    }

    /**
     * 小电税号是否正确
     * @param taxId
     * @return true正确，false错误
     */
    public static boolean checkTaxId(String taxId) {
        return taxIds.contains(taxId);
    }

    /**
     * 伊电园购票方
     * @param taxId
     * @return
     */
    public static boolean checkYDYbuyer(String taxId) {
        return YDY_BUYER_LIST.contains(taxId);
    }

    public static List<String> getBuyers() {
        return buyers;
    }

    public static List<String> getTaxIds() {
        return taxIds;
    }

}
