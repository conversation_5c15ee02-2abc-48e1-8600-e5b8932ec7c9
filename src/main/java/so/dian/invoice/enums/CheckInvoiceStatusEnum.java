package so.dian.invoice.enums;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:20 AM
 * @Description:
 */
@Getter
public enum CheckInvoiceStatusEnum implements EnumInterface<CheckInvoiceStatusEnum> {

	NOT_CHECK(0, "未质检"),
	ALREADY_CHECK(1, "已质检"),
	;
	private Integer code;
	private String desc;

	CheckInvoiceStatusEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 默认为null
	 *
	 * @return 默认枚举对象
	 */
	@Override
	public CheckInvoiceStatusEnum getDefault() {
		return null;
	}
}
