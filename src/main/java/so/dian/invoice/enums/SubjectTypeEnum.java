package so.dian.invoice.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum SubjectTypeEnum {

    SCM(1, "供应链", -1),
    DEVIDE(2, "商家分成", 1),
    CHANNEL(3, "渠道商分成", 4),
//    AGENT(3, "代理商分成", 4),
//    OP_SERVICE(6, "运营型服务商分成", 7),
//    ENTRANCE_FEE(7, "进场费", 1),
//    NO_DEVIDE(9, "非直营商家分成", -1),
    NORMAL_ENTRY_FEE(10, "普通商户进场费", -1),
    BRAND_ENTRY_FEE(11, "品牌商户进场费", -1),
    JOIN_ENTRY_FEE(12, "加盟商户进场费", -1),
    RESOURCE_ENTRY_FEE(13, "资源型服务商进场费", -1),
//    RESOURCE_KP_ENTRY_FEE(14, "运营型服务商进场费", -1),
    KP_SERVICE_ENTRY_FEE(15, "KP服务商进场费", -1),
    NORMAL_ENTRANCE_OTHER_FEE(16,"普通商户其他进场相关费用",-1),
    BRAND_ENTRANCE_OTHER_FEE(17,"品牌商户其他进场相关费用",-1),
    JOIN_ENTRANCE_OTHER_FEE(18,"加盟商户其他进场相关费用",-1),
    RESOURCE_ENTRANCE_OTHER_FEE(19,"资源型服务商其他进场相关费用",-1),
    KP_ENTRANCE_OTHER_FEE(20,"KP服务商其他进场相关费用",-1),
    /**
     * @since 20200409-liangfang-addInvoiceBizType
     * <AUTHOR>
     * 只有财务经理才能看到此业务类型
     */
    EQUIPMENT_PROCUREMENT(21,"设备采购发票",-1),

    /**
     * @since feature_20200525-liangfang-增加费用发票业务类型
     * <AUTHOR>
     * 只有财务经理才能看到此业务类型
     */
    FEE(22,"费用发票",-1),
    ADVANCE_PAYMENT(23,"分成预付",-1),
    SHOU_JU(24,"收据",-1),
    JOINT_VENTURE_COMPANY(25,"合资公司分成",-1),
    ;

    private int type;

    private String field;

    private int bizType;


    public static String getField(int type) {

        for(SubjectTypeEnum status : SubjectTypeEnum.values()) {
            if(status.getType() == type) {
                return status.getField();
            }
        }

        return null;
    }

    public static Integer getBizType(int type) {

        for(SubjectTypeEnum status : SubjectTypeEnum.values()) {
            if(status.getType() == type) {
                return status.getBizType();
            }
        }

        return null;
    }

    public static SubjectTypeEnum findByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (SubjectTypeEnum en : SubjectTypeEnum.values()) {
            if (Objects.equals(en.getType(), type)) {
                return en;
            }
        }
        return null;
    }
}
