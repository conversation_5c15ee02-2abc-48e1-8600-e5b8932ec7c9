package so.dian.invoice.enums.error;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 3:49 PM
 * @Description: 15001-18000
 */
@Getter
@AllArgsConstructor
public enum CheckInvoiceErrorCodeEnum implements EnumInterface<CheckInvoiceErrorCodeEnum> {

	USER_ALREADY_CONFIG(15001, "员工已经配置"),
	USER_IS_NOT_AFFAIRS_MANAGER_CANNOT_ADD(15002, "该员工不是财务经理，不能添加"),
	ADD_USER_REGION_RELATION_CONFIG_FAILED(15003, "新增员工大区配置失败"),
	USER_REGION_CONFIG_NOT_EXIST(15004, "员工大区配置不存在"),
	BATCH_DELETED_USER_REGION_CONFIG_FAILED(15005, "批量删除大区员工配置失败"),
	CONCLUSION_MUST_NOT_EXCEED_10(15006, "质检结论长度不能超过10个字"),
	CONCLUSION_IS_USED_CAN_NOT_MODIFY(15007, "质检结论已被使用，不能修改"),
	CONCLUSION_IS_NOT_EXIST(15008, "质检结论不存在"),
	CONCLUSION_UPDATE_FAILED(15009, "质检结论更新失败"),
	INVOICE_CHECK_INFO_NOT_EXIST(15010, "发票质检信息不存在"),
	GET_REGION_RELATION_CITY_USER_FAILED(15011, "获取大区关联城市行政失败"),
	CHECK_RATE_UPDATE_FAILED(15012, "质检比例更新失败"),
	ADD_CHECK_CONFIG_FAILED(15013, "添加质检配置失败"),
	BATCH_ADD_CHECK_INVOICE_FAILED(15014, "批量新增质检发票失败"),
	BATCH_UPDATE_INVOICE_IN_CHECK_POOL_FAILED(15015, "批量更新发票进入质检池失败"),
	EXPORT_DATA_MUST_NOT_MORE_THAN_10000(15016, "导出的数据不能超过10000条"),
	CHECK_RATE_MUST_NOT_LESS_THAN_0(15017, "质检比例不能小于0"),
	CHECKER_MUST_NOT_BE_NULL(15018, "质检员工不能为空"),
	CHECK_REGION_MUST_NOT_BE_NULL(15019, "质检大区不能为空"),
	CHECK_CONCLUSION_MUST_NOT_BE_NULL(15020, "质检结论不能为空"),
	DELETED_CHECK_CONCLUSION_FAILED(15021, "删除质检结论失败"),
	CHECK_INVOICE_UPDATE_FAILED(15022,"质检发票更新失败"),
	INVOICE_IS_ALREADY_CHECK(15023, "发票已经质检"),
	CHECK_CONCLUSION_IS_ALREADY_EXIST(15024, "质检结论已存在"),
	REGION_ALREADY_CONFIG(15025, "大区已经配置"),
	CHECK_INVOICE_TIME_MUST_NUT_BE_NULL(15026, "发票质检时间不能为空"),
	;
	private Integer code;
	private String desc;

	@Override
	public CheckInvoiceErrorCodeEnum getDefault() {
		return null;
	}
}
