package so.dian.invoice.enums.error;

import lombok.AllArgsConstructor;
import so.dian.himalaya.able.ErrorCodeInterface;
import so.dian.himalaya.common.enums.ServiceLineEnum;

/**
 * @Author: chenan
 * @Date: 2020/9/21 11:23
 */
@AllArgsConstructor
public enum InvoiceCodeEnum implements ErrorCodeInterface<InvoiceCodeEnum> {

    REMOTE_INTERNAL_ERROR(20001, "远程服务调用"),
    /**
     * 开票管理相关
     */
    INVOICE_APPLY(80000, "已选择的单据待开票金额存在更新，请重新确认"),
    INVOICE_MANAGE_NOT_EXISTS(80001, "申请开票单据不存在"),

    EVENT_EXECUTE_ERROR(80002, "事件执行失败"),

    TRADE_ORDER_EXIST_ERROR(80003, "交易单已存在"),

    TRADE_ORDER_STATUS_ERROR(80004, "交易单状态不正确"),

    TRADE_ORDER_NOT_EXIST_ERROR(80005, "交易单不存在"),

    PARAM_ERROR(80006, "参数错误"),

    BATCH_AUDIT_ERROR(80007, "非白名单用户不允许批量审核"),

    BIZ_ERROR(80009, "业务处理错误"),

    ;

    ;


    private Integer code;
    private String desc;


    @Override
    public String getCode() {
        return ServiceLineEnum.FINANCE.getCode() + code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
