package so.dian.invoice.enums.error;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/8/28 2:50 PM
 * @Description: 12001-13000
 */
@Getter
@AllArgsConstructor
public enum  InvoiceSubjectRelationErrorCodeEnum implements EnumInterface<InvoiceSubjectRelationErrorCodeEnum>{

	MERCHANT_INVOICING_SUBJECT_OF_THE_MERCHANT_ALREADY_EXISTS(12001,"该商户对应的开票主体已存在"),
	INVOICE_SUBJECT_RECORD_DOSE_NOT_EXIST(12002,"该条记录不存在"),
	INVOICE_SUBJECT_RECORD_ALREADY_INVALID(12003,"该条记录已失效"),
	INSERT_MERCHANT_SUBJECT_FAILED(12004,"新增商户主体信息失败"),
	INVOICE_SUBJECT_REPEAT(12005, "开票主体重复"),
	MERCHANT_SUBJECT_NAME_NOT_NULL(12006, "商户主体名称不存在"),
	MERCHANT_NOT_EXISTED(12007, "商家不存在"),

	;
	private Integer code;
	private String desc;

	@Override
	public InvoiceSubjectRelationErrorCodeEnum getDefault() {
		return null;
	}
}
