package so.dian.invoice.enums.error;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/8/28 2:45 PM
 * @Description: 11001-12000
 */
@Getter
@AllArgsConstructor
public enum InvoiceCommentErrorCodeEnum implements EnumInterface<InvoiceCommentErrorCodeEnum> {

	PARAM_RESOLVER_FAIL(11001, "参数校验不通过"),
	USER_NOT_EXIST(11002, "用户不存在"),
	USER_EMAIL_NOT_EXIST(11003, "用户邮箱不存在，请先联系HR添加邮箱！"),
	USER_UNAUTHORIZED(11004, "用户无权限访问"),
	USER_LOGIN_IS_INVALID_MAST_REGISTER(11005, "当前登陆无效，请重新登录"),
	USER_NO_PERMISSION_OPERATE(11006, "用户无权限操作"),
	FILE_UPLOAD_ERROR(12001, "文件上传失败"),
	PARAMS_ERROR(13001, "参数错误"),
	EMPLOYEE_NOT_NULL(40000, "员工不可为空"),
	;
	private Integer code;
	private String desc;

	@Override
	public InvoiceCommentErrorCodeEnum getDefault() {
		return null;
	}
}
