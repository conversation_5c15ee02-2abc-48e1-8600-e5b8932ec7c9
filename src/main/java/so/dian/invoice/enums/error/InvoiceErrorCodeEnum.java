package so.dian.invoice.enums.error;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/9/12 10:27 AM
 * @Description: 13001~15000
 */
@Getter
@AllArgsConstructor
public enum InvoiceErrorCodeEnum implements EnumInterface<InvoiceErrorCodeEnum> {

    PARAM_RESOLVER_FAIL(2000, "参数校验不通过"),
    INVOICE_DATE_ERROR(2001, "不能使用开票方的开票日期为2020-06-29之前的【杭州小电科技股份有限公司】的发票"),
    INVOICE_DATE_ERROR_YIDIANYUAN(2002, "开票方为【杭州小电科技股份有限公司】只能使用发票日期在2020-03-25～2020-07-31之间的发票"),
    INVOICE_SELLER_ERROR(2003, "发票销售方有误"),

    INVOICE_NOT_EXIST(13001, "发票不存在"),
    INVOICE_NOT_SEND_OUT_DO_NOT_REVIEW(13002, "发票未寄出，无法复核"),
    INVOICE_ALREADY_REVIEW(13003, "该发票已复核"),
    INVOICE_REVIEW_FAILED(13004, "发票复核失败"),
    INVOICE_EXPORT_DATA_TO_BIG_PLEASE_FILTER_AND_TRY_AGAIN(13005, "发票台账导出数据超过10000条，请筛选后重试"),
    INVOICE_IDENTIFICATION_FAILED(13006, "发票识别失败"),
    INVOICE_TYPE_MATCH_FAILED(13007, "发票类型匹配失败"),
    INVOICE_SUBJECT_TYPE_ERROR(13008, "发票业务类型错误"),
    INVOICE_TYPE_ERROR(13009, "发票类型错误"),
    INVOICE_PRICE_ERROR(13010, "发票价格有误"),
    INVOICE_IS_ALREADY_EXIST(13011, "发票已存在"),
    ADD_INVOICE_ERROR(13012, "新增发票异常"),
    INVOICE_UPDATE_DATA_EXCEEDS_PERMISSIONS(13013, "发票更新数据越权"),
    INVOICE_IS_ALREADY_USED(13014, "发票已被使用"),
    INVOICE_PRICE_SHOULD_BE_NOT_NULL(13015, "发票金额不能为空"),
    TOTAL_PRICE_TAX_MUST_EQUALS_TOTAL_AMOUNT(13016, "价税合计必须等于总金额"),
    CAN_ONLY_QUERY_INVOICE_FOR_THIS_MONTH_AND_LAST_MONTH(13017, "只能查询本月和上个月的发票"),
    MULTIPLE_UPLOADS_ARE_NOT_ALLOWED_ON_THE_SAME_INVOICE(13018, "同一张发票不允许多次上传"),
    CHECK_CODE_MUST_NOT_BE_NULL(13019, "校验码不能为空"),
    INVOICE_CANNOT_BE_IDENTIFIED_PLEASE_ENTER_BY_HAND(13020, "发票无法识别"),
    INVOICE_TAX_MUST_NOT_BE_NULL(13021, "发票税额不能为空"),
    INVOICE_PRICE_MUST_NOT_BE_NULL(13022, "发票金额不能为空"),
    INVOICE_RAW_PRICE_MUST_NOT_BE_NULL(13023, "发票不含税金额不能为空"),
    INVOICE_DETAILS_MUST_NOT_BE_NULL(13024, "开票明细不能为空"),
    DEDUCT_ERROR(13025, "发票核销失败"),
    INVOICE_IDENTIFY_CACHE_NOT_EXIST(13026, "发票识别缓存不存在"),
    INVOICE_IMAGE_MUST_NOT_NULL(13027, "发票图片信息不能为空"),
    INVOICE_IDENTIFY_RECORD_NOT_EXIST(13028, "发票识别记录不存在"),
    INVOICE_IDENTIFY_FAILED(13029, "发票识别错误"),
    INVOICE_BUYER_INVALID(13030, "发票抬头不正确"),
    INVOICE_DEDUCTION_NOT_EXIST(13031, "发票核销记录不存在"),
    RECOVER_ERROR(13032, "发票回滚失败"),
    INVOICE_DEDUCT_PERMISSIONS(13033, "发票核销越权"),

    TO_NO_RECORD(10300, "根据搜索条件未查询到记录"),
    TO_TOO_MUCH(10301, "根据搜索条件查询到记录超过50000条"),

    // 供应商发票
    NO_PERMISSION_AFFAIRS_MANAGER(10400, "[无权限],只有财务经理可操作"),
    NO_PERMISSION_PO_OPERATOR(10401, "[无权限],只有采购单操作人可操作"),
    NOT_OPERATE_DEAL_WITH(10402, "已转发票台账状态不能操作"),
    EXIST_INVOICE(10403, "发票记录已存在"),
    NULL_INVOICE_CODE(10404, "全电发票发票代码和校验码必须为空"),
    BUYER_NOT_EXIST(10405,"未查询到该用户对应的购买方"),
    NO_PERMISSION_BUYER(10406, "无权录入该购买方发票")
    ;
    private Integer code;
    private String desc;

    @Override
    public InvoiceErrorCodeEnum getDefault() {
        return null;
    }
}
