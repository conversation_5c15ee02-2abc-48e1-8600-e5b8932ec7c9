package so.dian.invoice.enums.error;
import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;
/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/01/18 17:07
 * @description:
 */
@Getter
@AllArgsConstructor
public enum SupplierInvoiceErrorCode implements EnumInterface<SupplierInvoiceErrorCode>{
    COOKIE_USER_NOT_EXIST(16000, "登录态中取不到当前用户信息"),
    USER_BELONG_SUPPLIER_NOT_EXIST(16001, "用户所属的供应商查询不到"),
    USER_EMAIL_NOT_EXIST(16002, "用户邮箱不存在，请联系管理员添加邮箱"),
    ;
    private final Integer code;
    private final String desc;

    @Override
    public SupplierInvoiceErrorCode getDefault() {
        return null;
    }
}
