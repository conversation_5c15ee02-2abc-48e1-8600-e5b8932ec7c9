package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 4:46 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SupplierInvoiceTypeEnum implements EnumInterface<SupplierInvoiceTypeEnum> {

    // 1, "增值税专用发票"
    VAT(InvoiceTypeEnum.VAT.getType(), InvoiceTypeEnum.VAT.getField()),
    /**
     * 增值税电子专票没有单独分类。会分类在在10100增值税专用发票中，然后通过'electronic_mark': '1',
     * 是否为电子增票，你们来做一下二次分类
     */
    // 14, "增值税电子专用发票"
    VAT_ELECTR(InvoiceTypeEnum.VAT_ELECTR.getType(), InvoiceTypeEnum.VAT_ELECTR.getField()),
	// 2, "增值税普通发票"
    ORD(InvoiceTypeEnum.ORD.getType(), InvoiceTypeEnum.ORD.getField()),
	// 3, "增值税电子普通发票"
    ORD_ELECTR(InvoiceTypeEnum.ORD_ELECTR.getType(), InvoiceTypeEnum.ORD_ELECTR.getField()),
    FULL_POWER_ELECTR(InvoiceTypeEnum.FULL_POWER_MAJOR.getType(), InvoiceTypeEnum.FULL_POWER_MAJOR.getField()),
    FULL_POWER_ORD(InvoiceTypeEnum.FULL_POWER_GENERAL.getType(), InvoiceTypeEnum.FULL_POWER_GENERAL.getField()),
    ;

    private Integer code;
    private String desc;

    @Override
    public SupplierInvoiceTypeEnum getDefault() {
        return null;
    }

}
