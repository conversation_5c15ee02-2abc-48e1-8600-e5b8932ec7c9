package so.dian.invoice.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.invoice.common.CommonConstants;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CacheEnum {
    VALIDETA_JOB_FLAG("valideta_job_flag", "发票验真定时任务", 120L, TimeUnit.DAYS),
    INVOICE_IDENTIFY("invoice.identify", "发票识别", 30L, TimeUnit.MINUTES),
    INVOICE_IDENTIFY_RATE_LIMITER("invoice_identify_rate_limiter", "发票识别限流", 1L, TimeUnit.SECONDS),
    INVOICE_OCR_LOCK("invoice_ocr_lock", "批量发票OCR识别锁", 600L, TimeUnit.SECONDS),

    INVOICE_MANAGE_LOCK("invoice_manage_lock", "开票管理锁", 60L, TimeUnit.SECONDS),

    CREATE_INVOICE_REQUEST_LOCK("create_invoice_request_lock", "开票申请锁", 10L, TimeUnit.SECONDS),
    ;

    private String key;
    private String desc;
    private Long expire;
    private TimeUnit unit;

    public String getKey() {
        return CommonConstants.APPLICATION_NAME + CommonConstants.REDIS_SEPARATOR + this.key;
    }
}
