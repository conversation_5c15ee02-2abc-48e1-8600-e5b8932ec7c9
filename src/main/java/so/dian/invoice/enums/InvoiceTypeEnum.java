package so.dian.invoice.enums;

public enum InvoiceTypeEnum {
    UNKNOWN(0, "未知"),
    VAT_SPECIAL_INVOICE(1,"专用发票"),
    REGULAR_INVOICE(2,"普通发票");

    public int code;

    public String name;

    InvoiceTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static InvoiceTypeEnum getByCode(Integer code){
        if(code==null){
            return UNKNOWN;
        }
        for(InvoiceTypeEnum e : values()){
            if(e.getCode() == code){
                return e;
            }
        }
        return UNKNOWN;
    }
}