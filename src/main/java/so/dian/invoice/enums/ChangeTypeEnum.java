package so.dian.invoice.enums;

import lombok.Getter;

/**
 * @author: yuechuan
 * @create: 2025/03/19 14:18
 * @description:
 */
@Getter
public enum ChangeTypeEnum {
    INCREASE("increase", "新增"),
    DECREASE("decrease", "减少");
    private final String code;
    private final String desc;

    ChangeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChangeTypeEnum getByCode(String code) {
        for (ChangeTypeEnum value : ChangeTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
