package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 4:46 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SupplierInvoiceStatusEnum implements EnumInterface<SupplierInvoiceStatusEnum> {

	INITIALIZE(0,"已录入"),
	DEAL_WITH(1,"已转发票台账"),
	;

	private Integer code;
	private String desc;

	@Override
	public SupplierInvoiceStatusEnum getDefault() {
		return null;
	}

}
