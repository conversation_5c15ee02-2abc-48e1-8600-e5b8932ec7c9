package so.dian.invoice.enums;
import lombok.AllArgsConstructor;
import so.dian.commons.eden.enums.EnumInterface;
/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/10/28 14:33
 * @description:
 */
@AllArgsConstructor
public enum ChannelInvoiceStatus implements EnumInterface<ChannelInvoiceStatus>{
    DISABLE(0, "禁用"),
    ENABLE(1, "启用"),
    ;

    private final Integer code;
    private final String desc;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public ChannelInvoiceStatus getDefault() {
        return null;
    }
}
