//package so.dian.invoice.enums;
//
//import lombok.Getter;
//import so.dian.fis.credit.dto.response.CreditMessageDTO;
//import so.dian.invoice.constant.InvoiceConstants;
//import so.dian.songshan.client.pojo.enums.ChannelRepairStatusEnum;
//
///**
// * @program: invoice
// * @description:
// * @author: yuechuan
// * @create: 2025-05-29 17:47
// */
//@Getter
//public class OutStatusToOutBizType {
//
//
//    /**
//     * 信用相关
//     */
//    @Getter
//    public enum CreditEnum {
//        REPAY_SUCCESS_STATUS(CreditMessageDTO.Type.REPAY_SUCCESS.getCode(), OutBizTypeEnum.CREDIT_REPAYMENT_SUCCESS),
//
//        REFUND_SUCCESS_STATUS(CreditMessageDTO.Type.REFUND_SUCCESS.getCode(), OutBizTypeEnum.CREDIT_REPAYMENT_REFUND);
//
//
//        private final Integer code;
//        private final OutBizTypeEnum outBizTypeEnum;
//
//        CreditEnum(Integer code, OutBizTypeEnum desc) {
//            this.code = code;
//            this.outBizTypeEnum = desc;
//        }
//
//        public static CreditEnum getByCode(Integer code) {
//            for (CreditEnum value : CreditEnum.values()) {
//                if (value.getCode().equals(code)) {
//                    return value;
//                }
//            }
//            return null;
//        }
//    }
//
//    /**
//     * 采购相关
//     */
//    @Getter
//    public enum TradeEnum {
//
//        TRADE_ORDER_STATUS(InvoiceConstants.WAIT_APPROVAL, OutBizTypeEnum.PURCHASE_PAYMENT),
//
//        TRADE_CANCEL_ORDER_STATUS(InvoiceConstants.CLOSED, OutBizTypeEnum.PURCHASE_CANCEL);
//
//
//        private final Integer code;
//        private final OutBizTypeEnum outBizTypeEnum;
//
//        TradeEnum(Integer code, OutBizTypeEnum desc) {
//            this.code = code;
//            this.outBizTypeEnum = desc;
//        }
//
//        public static TradeEnum getByCode(Integer code) {
//            for (TradeEnum value : TradeEnum.values()) {
//                if (value.getCode().equals(code)) {
//                    return value;
//                }
//            }
//            return null;
//        }
//    }
//
//    /**
//     * 维修相关
//     */
//    @Getter
//    public enum RepairEnum {
//        REPAIR_PAY_STATUS(ChannelRepairStatusEnum.WAIT_DIS.getCode(), OutBizTypeEnum.REPAIR_ORDER_COMPLETE);
//
//        private final Integer code;
//        private final OutBizTypeEnum outBizTypeEnum;
//
//        RepairEnum(Integer code, OutBizTypeEnum desc) {
//            this.code = code;
//            this.outBizTypeEnum = desc;
//        }
//
//        public static RepairEnum getByCode(Integer code) {
//            for (RepairEnum value : RepairEnum.values()) {
//                if (value.getCode().equals(code)) {
//                    return value;
//                }
//            }
//            return null;
//        }
//    }
//}
