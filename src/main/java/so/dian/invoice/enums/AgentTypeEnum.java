package so.dian.invoice.enums;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * AgentType，supplier暂时没用到。
 *
 * <AUTHOR> 2017/12/27 下午2:39
 * @see [相关类/方法]
 * @since [版本号]
 */
public enum AgentTypeEnum {
	AGENT_TYPE(0, "代理商"),
	BD_AGENT_TYPE(1, "开户型服务商"),
	RP_AGENT_TYPE(2, "资源型服务商"),// resource provider
	OP_AGENT_TYPE(3, "运营型服务商"),// operation provider
	KP_SERVICE(4, "KP服务商"),
	JV_COMPANY_TYPE(5, "合资公司");// operation provider
	;

	AgentTypeEnum(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	public static AgentTypeEnum getAgentType(Integer id) {
	    for (AgentTypeEnum agentType : AgentTypeEnum.values()){
		if (Objects.equals(agentType.getId(), id)){
			return agentType;
		}
	    }
	    return null;
	}

	public static List<JSONObject> getAll(){
		List<JSONObject> list = Lists.newArrayList();
		for (AgentTypeEnum agentType : AgentTypeEnum.values()) {
			JSONObject obj = new JSONObject();
			obj.put("id", agentType.getId());
			obj.put("name", agentType.getName());
			list.add(obj);
		}
		return list;
	}

	private Integer id;
	private String name;

	public Integer getId() {
		return id;
	}

	public String getName() {
		return name;
	}
}
