package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 4:46 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum InvoiceValidateTypeEnum implements EnumInterface<InvoiceValidateTypeEnum> {

	TASK_VERIFICATION(0,"定时任务验真"),
	MANUAL_VERIFICATION(1,"手动验真"),
	;

	private Integer code;
	private String desc;

	@Override
	public InvoiceValidateTypeEnum getDefault() {
		return null;
	}


}
