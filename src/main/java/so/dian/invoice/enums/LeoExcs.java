package so.dian.invoice.enums;
import com.meidalife.common.exception.ExcsEnum;
public enum LeoExcs implements ExcsEnum {

    SERVER_INTERNAL_ERROR(1, "Server Internal Error", "服务器开了小差请稍后重试"),
    PARAM_RESOLVER_FAIL(9, "参数校验不通过"),
    CHECK_NULL(10, "入参为空"),
    CHECK_ID(11, "入参不是ID"),
    CHECK_NULL_OR_EMPTY(12, "入参为空字符串"),
    CHECK_POSITIVE(13, "入参不是正整数"),
    CHECK_PHONE(14, "手机号格式不正确"),
    CHECK_WORD_ERROE(15, "CHECK_WORD_ERROE", "包含非法关键词"),
    CHECK_BLANK(16, "CHECK_BLANK", "入参为空字符串"),
    CHECK_EQUALS(17, "CHECK_EQUALS", "入参不是期望的值"),
    CHECK_RANGE(18, "CHECK_RANGE", "入参长度不在规定范围内"),
    CHECK_NUM_LIMIT(19, "CHECK_NUM_LIMIT", "入参数值大小不在规定范围内"),
    CHECK_NOT_NEGATIVE(20, "CHECK_NOT_NEGTIVE", "入参不是非负数"),
    CHECK_NO_ROLE_CONFIGED(21, "CHECK_NO_ROLE_CONFIGED", "该接口未配置合适权限"),
    CHECK_INVALID(22, "CHECK_INVALID", "入参非法"),
    CHECK_POI(23, "CHECK_POI", "定位失败"),
    CHECK_UNIMPLEMENTS(24, "CHECK_UNIMPLEMENTS", "功能暂未实现"),
    CHECK_VERSION_NEED_UPDATE(25, "CHECK_VERSION_NEED_UPDATE", "请使用更新版本接口"),
    CHECK_OPERATION_INVALID(26, "CHECK_OPERATION_INVALID", "无效的操作"),
    CHECK_PARAM_RESULT_NULL(27, "CHECK_PARAM_RESULT_NULL", "查询结果为空"),
    SAVE_FAIL(28, "SAVE_FAIL", "保存失败"),
    DB_INSERT_ERROR(31, "DB_INSERT_ERROR", "数据库插入失败"),
    DB_SELECT_ERROR(32, "DB_SELECT_ERROR", "数据库查询失败"),
    OSS_IMAGE_UPLOAD_FAILED(90, "IMAGE_UPLOAD_FAILED", "图片上传失败"),
    IMAGE_SIZE_TOO_LARGE(91, "IMAGE_SIZE_TOO_LARGE", "图片太大了，换张小点儿的吧"),
    OSS_FILE_UPLOAD_FAILED(92, "FILE_UPLOAD_FAILED", "文件上传失败"),
    OSS_FILE_UPLOAD_UNSUPPORTED(93, "FILE_UPLOAD_UNSUPPORTED", "不支持的文件类型"),
    IMAGES_URL_INVALID(94, "IMAGES_URL_INVALID", "图片链接格式不符合规范"),
    IMAGE_SIZE_TOO_MUCH(95, "IMAGE_SIZE_TOO_LARGE", "图片太多了，换少点吧"),
    NEED_LOGIN(206, "NEED_LOGIN", "需要登录"),
    USER_NOT_EXIST(206, "USER_NOT_EXIST", "用户不存在"),
    LOGIN_ID_PARSE(207, "USER ID PARSE", "用户id解析出错"),
    EMPLOYEE_NOT_EXIST(208, "EMPLOYEE_NOT_EXIST", "用户不存在"),
    EMPLOYEE_FREEZE(208, "EMPLOYEE_FREEZE", "用户被冻结"),
    EMPLOYEE_ERROR_COUNT_OUT(209,"EMPLOYEE_ERROR_COUNT_OUT","登陆失败次数过多被锁定"),
    EMPLOYEE_LOGIN_FREQUENTLY(210,"EMPLOYEE_LOGIN_FREQUENTLY","登陆过于频繁"),
    EMPLOYEE_BLACKLIST(210,"上周盒子设备运营数据较差，本周无法申领或借入盒子设备，无法发起配送订单。要了解如何解除限制，请咨询城市运营部"),
    USER_LOG_OUT_FAILED(215, "USER_LOG_OUT_FAILED", "用户登出失败"),
    USER_MOBILE_INVALID(211, "USER_MOBILE_INVALID", "手机号码有误"),
    SMS_TOKEN_SEND_TOO_FREQUENT(212, "SMS_TOKEN_SEND_TOO_FREQUENT", "验证码发送太频繁"),
    SMS_TOKEN_LOGIN_FAILED(213, "SMS_TOKEN_LOGIN_FAILED", "验证码不正确"),
    SMS_TOKEN_SEND_FAILED(214, "SMS_TOKEN_SEND_FAILED", "验证码发送失败"),
    SMS_SIGN_NOT_EXIST(215, "SMS_SIGN_NOT_EXIST", "签名验证不正确"),
    EMPLOYEE_NOT_COMMIT(216, "EMPLOYEE_NOT_COMMIT", "用户待激活"),
    USER_SIGN_NOT_EXIST(217, "USER_SIGN_NOT_EXIST", "用户不存在"),
    USER_EXIST(251, "USER_EXIST", "用户已存在"),
    USER_NO_ROLE(252, "USER_NO_ROLE", "用户没有该权限"),
    USER_ROLE_NOT_EXIST(253, "USER_ROLE_NOT_EXIST", "用户没有选择角色"),
    DEVICE_KEEPER_NEED_ADD_BEFORE_LOGIN(260, "DEVICE_KEEPER_NEED_ADD_BEFORE_LOGIN", "设备管理员需先由对应商户添加入库"),
    MERCHANT_NOT_DEFINE_TO_DEVICE_KEEPER(261, "MERCHANT_NOT_DEFINE_TO_DEVICE_KEEPER", "不能添加商户为设备管理员"),
    MERCHANT_TEXT_TO_LONG(262, "MERCHANT_TEXT_TO_LONG", "商户信息过长"),
    MERCHANT_COUNT_OUT_OF_RANGE(263, "MERCHANT_COUNT_OUT_OF_RANGE", "最多只能添加三个设备管理员"),
    NOT_ALLOW_CHANGE_MERCHANT_IN_SIGNING(264,"NOT_ALLOW_CHANGE_MERCHANT_IN_SIGNING","不允许变更审批中的商户"),
    DEVICE_NOT_EXIST(301, "DEVICE_NOT_EXIST", "设备不存在"),
    DEVICE_OPEN_FAILED(302, "DEVICE_OPEN_FAILED", "设备打开失败"),
    DEVICE_CLOSE_FAILED(303, "DEVICE_CLOSE_FAILED", "设备关闭失败"),
    DEVICE_GET_FAILED(304, "DEVICE_GET_FAILED", "获取设备信息失败"),
    DEVICE_REPEAT(305, "DEVICE_REPEAT", "设备重复"),
    DEVICE_NUMBER_ERROR(306, "DEVICE_NUMBER_ERROR", "设备数量错误"),
    DEVICE_ALREADY_BIND(307, "DEVICE_ALREADY_BIND", "设备已绑定到该店铺"),
    DEVICE_OTHER_BIND(308, "DEVICE_OTHER_BIND", "设备已绑定到其他门店"),
    DEVICE_UNKNOWN_PRODUCT(309, "DEVICE_UNKNOWN_PRODUCT", "未知设备类型"),
    DEVICE_MAC_ALREADY_BINDED(310, "DEVICE_MAC_ALREADY_BINDED", "该设备已被安装"),
    DEVICE_NUMBER_ALREADY_BINDED(311, "DEVICE_NUMBER_ALREADY_BINDED", "该设备编号已被绑定"),
    DEVICE_USER_ALREADY_BINDED(7710, "DEVICE_USER_ALREADRY_BINDED", "该用户已经进行过设备绑定，无需进行二次绑定"),
    DEVICE_UNAVAILABLE(315, "DEVICE_UNAVAILABLE", "该设备暂时无法使用"),
    DEVICE_INVALID_QRCODE(317, "错误的二维码"),
    DEVICE_INVALID_TYPE(317, "目前只支持座充 二代/三代 二维码更换"),
    DEVICE_INVALID_QRCODE_EXIST(317, "DEVICE_INVALID_QRCODE_EXIST", "二维码已使用"),
    DEVICE_BOX_MUST_TEST_SLOT(318, "DEVICE_BOX_MUST_TEST_SLOT", "确保全部槽位测试通过"),
    DEVICE_BOX_SLOT_CLOSE_FAIL(318,"DEVICE_BOX_SLOT_CLOSE_FAIL","存在未取出的充电宝"),
    DEVICE_BOX_MUST_HAVE_6_BANK(319, "DEVICE_BOX_MUST_HAVE_6_BANK", "确保拥有6个充电宝"),
    DEVICE_BOX_NOT_BIND(320, "DEVICE_BOX_NOT_BIND", "设备没有绑定"),
    DEVICE_OLD_BOX_NOT_BIND(320, "DEVICE_OLD_BOX_NOT_BIND", "原设备未安装，无法替换"),
    DEVICE_BOX_NOT_ACTIVE(321, "DEVICE_BOX_NOT_ACTIVE", "盒子未激活，请等待盒子联网或重新打开盒子电源后再扫码"),
    DEVICE_BOX_HAS_NOT_FACTORY_TEST(322, "DEVICE_BOX_HAS_NOT_FACTORY_TEST", "设备未进行产测或产测未通过"),
    DEVOCE_HAS_BIND_DEMO_MACHINE(323, "DEVOCE_HAS_BIND_DEMO_MACHINE", "单台设备不能重复安装演示机"),
    DEVICE_DAMAGED(324, "DEVICE_DAMAGED", "检测到损坏设备，请将设备退回仓库"),
    DEVICE_OWNER_INVALID(325, "DEVOCE_OWNER_INVALID", "设备所有者不匹配"),
    DEVICE_LOST_EXISTS(326, "DEVICE_LOST_EXISTS", "该设备已经申请遗失"),
    DEVICE_LOST_NOT_AUDITED(799, "DEVICE_LOST_NOT_AUDITED", "存在未审核的设备遗失申请"),
    DEVICE_NOT_ACTIVE(327, "DEVICE_NOT_ACTIVE", "设备未激活"),
    DEVICE_OFFLINE(328, "DEVICE_OFFLINE", "该设备已离线"),
    DEVICE_BOX_DONT_NEED_ADD_POWERBANK(329, "DEVICE_BOX_DONT_NEED_ADD_POWERBANK", "此设备不需要补充充电宝"),
    DEVICE_FIND_BACK_COUNT_INVALID(330, "DEVICE_FIND_BACK_COUNT_INVALID", "找回数量不正确"),
    DEVICE_IS_DIFFERENT(331, "DEVICE_NOT_DIFFERENT", "不是相同的设备"),
    DEVICE_NOT_DIFFERENT(332, "DEVICE_NOT_DIFFERENT", "不是相同的设备"),
    DEVICE_SECRET_UNINIT(333, "DEVICE_SECRET_UNINIT", "密码版设备未入库"),
    DEVICE_SECRET_CANT_REPAIR(334, "DEVICE_SECRET_CANT_REPAIR", "不能检修未安装的设备或不是你安装的设备"),
    DEVICE_SECRET_RESOLVE_FAIL(335, "DEVICE_SECRET_RESOLVE_FAIL", "设备重置密码解析出错"),
    DEVICE_NOT_BLUETOOTH(336, "DEVICE_NOT_BLUETOOTH", "设备不是蓝牙版"),
    DEVICE_STATUS_ERROR(337,"DEVICE_STATUS_ERROR","设备申领状态错误"),
    DEVICE_APPLY_NOTEXISTS(338,"DEVICE_APPLY_NOTEXISTS","设备申请单不存在"),
    DEVICE_CANCEL_BIND(339,"DEVICE_CANCEL_BIND","确保设备解除绑定"),
    DEVICE_NOT_INSTALL(343,"DEVICE_NOT_INSTALL","设备未安装"),
    DEVICE_SLOT_OPEN_FAILED(344,"DEVICE_SLOT_OPEN_FAILED","槽位打开失败，请刷新页面后重试"),
    DEVICE_SORTING_STATE_FAILED(345,"DEVICE_SORTING_STATE_FAILED","分拣单状态错误"),
    DEVICE_NOT_ALLOW_BIND(346,"DEVICE_NOT_ALLOW_BIND","旧盒子不允许绑定"),
    AMS_NOT_FIND_DEVICE(347,"AMS_NOT_FIND_DEVICE","AMS未发现该设备注册信息"),
    DEVICE_NOT_EXIST_OR_TEST(348,"DEVICE_NOT_EXIST_OR_TEST","该设备不存在或未厂测"),
    DEVICE_NOT_IN_SHOP(349, "DEVICE_NOT_IN_SHOP", "设备不在门店"),



    DEVICE_BIND_POWERBANK_NOT_BOX(339, "DEVICE_BIND_POWERBANK_NOT_BOX", "安装充电宝必须先安装盒子"),
    DEVICE_BOX_MUST_HAVE_2_BANK(340, "DEVICE_BOX_MUST_HAVE_2_BANK", "确保拥有2个充电宝"),
    DEVICE_NOT_BELONG_TO_YOU(341,"DEVICE_NOT_BELONG_TO_YOU","设备不属于你"),
    DEVICE_QRCODE_REPEAT(342, "DEVICE_QRCODE_REPEAT", "二维码重复"),
    NOT_SCAN_CODE(345,"NOT_SCAN_CODE","未开通扫码出库功能"),

    SHOP_NOT_EXIST(401, "SHOP_NOT_EXIST", "店铺不存在"),
    SHOP_CITY_CODE_ERROR(402, "SHOP_CITY_CODE_ERROR", "获取店铺城市信息失败，请重试"),
    SHOP_NO_MERCHANT(403, "SHOP_NO_MERCHANT", "请关联商家"),
    SHOP_NOT_BELONG_YOU(404, "SHOP_NOT_BELONG_YOU", "您没有权限修改该店铺"),
    SHOP_LEADS_NOT_EXIST(405, "SHOP_LEADS_NOT_EXIST", "该门店例子不存在"),
    SHOP_LEADS_BINDED_SHOP(406, "SHOP_LEADS_BINDED_SHOP", "该门店例子已经绑定过了"),
    SHOP_IS_INSATLLED(407, "SHOP_IS_INSATLLED", "门店已安装"),
    SHOP_LEADS_DELETE_ERROR(408, "SHOP_LEADS_DELETE_ERROR", "删除shopleads失败"),
    SHOP_LEADS_CITY_DISTRICT_ERROR(409, "SHOP_LEADS_CITY_DISTRICT_ERROR", "城市和地区绑定不匹配"),
    SHOP_INFO_MISS(410, "SHOP_INFO_MISS", "工作对象丢失"),
    SHOP_SELLER_NOT_BELONG_YOU(404,"SHOP_SELLER_NOT_BELONG_YOU","该门店不属于你"),
    SHOP_NOT_BIND_PERMISSIONS(411, "SHOP_NOT_BIND_PERMISSIONS", "店铺没有绑定该设备的权限"),

    CONTRACT_NOT_EXIST(451, "CONTRACT_NOT_EXIST", "签约信息不存在或已失效"),
    CONTRACT_PASS_STATUS_DATA_ERROR(452, "CONTRACT_PASS_STATUS_DATA_ERROR", "针对签约对象的一个品牌存在多条审核通过的合同记录"),
    MERCHANT_EXIST(501, "MERCHANT_EXIST", "商家已存在或已绑定其他微信号"),
    DEVICE_KEEPER_EXIST(502, "DEVICE_KEEPER_EXIST", "设备管理员已存在"),
    MERCHANT_NOT_EXIST(503, "MERCHANT_NOT_EXIST", "商家不存在"),
    NOT_ALLOW_BIND_KA_MERCHANT(504, "NOT_ALLOW_BIND_KA_MERCHANT", "不允许绑定到大客户商户"),
    NOT_ALLOW_UNBIND_KA_MERCHANT(505, "NOT_ALLOW_UNBIND_KA_MERCHANT", "不允许解绑大客户商户"),
    NOT_ALLOW_RELEASE_KASHOP_OR_RPSHOP(506, "NOT_ALLOW_RELEASE_KASHOP_OR_RPSHOP", "不允许释放大客户门店或资源型服务商门店"),
    AGENT_SELLER_NOT_EXIST(601, "AGENT_SELLER_NOT_EXIST", "没有该小二"),
    AGENT_DEPARTMENT_NOT_EXIST(602, "AGENT_DEPARTMENT_NOT_EXIST", "部门不存在"),
    AGENT_EMPLOYEE_SCHEDULE_CONFLICT(603, "AGENT_EMPLOYEE_SCHEDULE_CONFLICT", "员工档期冲突"),
    AGENT_DEPARTMENT_LEADER_NOT_EXIST(604, "AGENT_DEPARTMENT_LEADER_NOT_EXIST", "部门leader不存在"),
    AGENT_EMPLOYEE_MOBILE_EXIST(605, "AGENT_EMPLOYEE_MOBILE_EXIST", "手机号码已存在"),
    AGENT_EMPLOYEE_NICKNAME_EXIST(606, "AGENT_EMPLOYEE_NICKNAME_EXIST", "花名已存在"),


    AGENT_DEPARTMENT_NAME_EXIST(607, "AGENT_DEPARTMENT_NAME_EXIST", "部门名称已存在"),
    AGENT_DEPARTMENT_CHILD_EXIST(608, "AGENT_DEPARTMENT_CHILD_EXIST", "有在用的下属部门"),
    AGENT_DEPARTMENT_EMPLOYEE_EXIST(609, "AGENT_DEPARTMENT_EMPLOYEE_EXIST", "部门下面有在职员工"),
    AGENT_EMPLOYEE_EMPLOYEENO_EXIST(610, "AGENT_EMPLOYEE_EMPLOYEENO_EXIST", "工号已存在"),
    AGENT_EMPLOYEE_EMAIL_EXIST(611, "AGENT_EMPLOYEE_EMAIL_EXIST", "邮箱已存在"),
    AGENT_DEPARTMENT_HRBP_NO_ROLE(612, "AGENT_DEPARTMENT_HRBP_NO_ROLE", "该HRBP没有操作权限"),
    AGENT_BLACKLIST(210,"上周盒子设备运营数据较差，本周无法申领盒子设备，无法发起配送订单。要了解如何解除限制，请咨询渠道经理"),
    AGENT_EMPLOYEE_CREATE_ERROR(613,"远程调用新增员工接口失败"),
    AGENT_EMPLOYEE_SET_ROLE_ERROR(614,"远程调用修改权限接口失败"),
    AGENT_EMPLOYEE_SET_STATUS_ERROR(615,"远程调用修改员工状态接口失败"),
    AGENT_EMPLOYEE_SET_WAREHOUSE_ERROR(616,"远程调用修改员工仓库接口失败"),
    AGENT_EMPLOYEE_INFO_ERROR(617,"远程调用查询员工信息失败"),

    INSTALLER_NOT_EXIST(701, "INSTALLER_NOT_EXIST", "对应的安装工不存在"),
    INSTALL_INSTALLATION_NOT_EXIST(702, "INSTALL_INSTALLATION_NOT_EXIST", "安装单不存在"),
    INSTALL_NOT_WAIT_INSTALL(703, "INSTALL_NOT_WAIT_INSTALL", "工单不在待安装状态"),
    INSTALL_NO_ADLE(704, "INSTALL_NO_ADLE", "该时间段无空闲安装工"),
    INSTALLER_NO_INSTALLER(705, "INSTALLER_NO_INSTALLER", "未分配安装工"),
    INSTALLER_ALREADY_INSTALLED(706, "INSTALLER_ALREADY_INSTALLED", "已安装完成"),
    INSTALLER_CITY_ERROR(707, "INSTALLER_CITY_ERROR", "安装工城市不匹配"),
    INSTALL_INSTALLATION_ALREADY_FINISHED(709, "INSTALL_INSTALLATION_ALREADY_FINISHED", "安装单已完成"),
    INSTALL_APPROVAL_NOT_EXIST(751, "INSTALL_APPROVAL_NOT_EXIST", "审批申请不存在"),
    INSTALL_APPROVAL_STATUS_ERROR(752, "INSTALL_APPROVAL_STATUS_ERROR", "审批申请状态错误"),
    INSTALL_APPROVAL_HAS_AUDITED(753, "INSTALL_APPROVAL_HAS_AUDITED", "签约已审批不能修改"),
    INSTALL_APPROVAL_OPT_BY_CS(754, "INSTALL_APPROVAL_OPT_BY_CS", "签约审批已转由客服进行"),
    INSTALL_APPROVAL_AUDITOR_INVALID(755, "INSTALL_APPROVAL_AUDITOR_INVALID", "非当前客服工单"),
    INSTALL_APPROVAL_SIGNED(756, "INSTALL_APPROVAL_SIGNED", "工单已被领取"),
    INSTALL_APPROVAL_NOT_SIGNED(757, "INSTALL_APPROVAL_NOT_SIGNED", "工单未被领取"),
    INSTALL_APPROVAL_AUDITING(758, "INSTALL_APPROVAL_AUDITING", "当前有签约单正在审核"),
    PAYMENT_REFUND_FAIL(800, "退款失败"),
    SHOP_HAS_CONTRACT(801, "SHOP_HAS_CONTRACT", "店铺无须重复签约"),
    SHOP_NAME_DUPLICATE(802, "SHOP_NAME_DUPLICATE", "店铺已存在"),
    SHOP_HAS_CONTRACT_CAN_NOT_EDIT(803, "SHOP_HAS_CONTRACT_CAN_NOT_EDIT", "不能编辑已签约门店"),
    SHOP_NAME_TOO_LONG(804, "SHOP_NAME_TOO_LONG", "门店名称太长"),
    SHOP_CONTACT_NAME_TOO_LONG(805, "SHOP_CONTACT_NAME_TOO_LONG", "门店联系人名称太长"),
    SHOP_CONTACT_MOBILE_TOO_LONG(806, "SHOP_CONTACT_MOBILE_TOO_LONG", "门店联系人电话太长"),
    SHOP_MOBILE_TOO_LONG(807, "SHOP_MOBILE_TOO_LONG", "门店电话太长"),
    SHOP_CANT_EDIT(808, "SHOP_CANT_EDIT", "门店信息不能修改"),
    SHOP_TOO_MANY(809, "SHOP_TOO_MANY", "符合条件的店铺太多，请缩小查询范围"),
    SHOP_NAME_TOO_SHORT(810, "SHOP_NAME_TOO_SHORT", "店铺名称太短"),
    SHOP_TYPE_NOT_EXIST(901, "SHOP_TYPE_NOT_EXIST", "店铺类型不存在"),
    STOCK_NO_STOCK(1001, "NO_STOCK", "仓库没有库存"),
    STOCK_LOW_STOCK(1002, "NO_STOCK", "库存不足"),
    STOCK_PRODUCT_NOT_EXIST(1003, "STOCK_PRODUCT_NOT_EXIST", "产品不存在"),
    STOCK_NOT_RESPONSIBLE(1004, "STOCK_NOT_RESPONSIBLE", "用户不是仓管员"),
    STOCK_INVALID_INSTALLATION(1005, "STOCK_INVALID_INSTALLATION", "非待配货的安装单"),
    STOCK_PRODUCT_SN_EXIST(1006, "STOCK_PRODUCT_SN_EXIST", "产品序列号已存在"),
    STOCK_PURCHASER_NOT_EXIST(1007, "STOCK_PURCHASER_NOT_EXIST", "采购员不存在"),
    STOCK_RESPONSIBLE_NOT_EXIST(1008, "STOCK_RESPONSIBLE_NOT_EXIST", "仓管员不存在"),
    STOCK_APPLIER_NOT_EXIST(1009, "STOCK_APPLIER_NOT_EXIST", "安装工不存在"),
    STOCK_WAREHOUSE_NOT_EXIST(1010, "STOCK_WAREHOUSE_NOT_EXIST", "对应的仓库不存在"),
    USER_WAREHOUSE_NOT_RELATION(1010, "STOCK_WAREHOUSE_NOT_RELATION", "用户未关联仓库"),
    STOCK_OUTGOING_NOT_EXIST(1011, "STOCK_OUTGOING_NOT_EXIST", "出库工单不存在"),
    STOCK_PURCHASE_HAS_EXIST(1012, "STOCK_PURCHASE_HAS_EXIST", "采购单已存在"),
    STOCK_PRODUCT_QUERY_INCONSISTENT(1013, "STOCK_PRODUCT_QUERY_INCONSISTENT", "产品条码与产品ID对应产品不一致"),
    STOCK_WAREHOUSE_ON_FREEZE(1014, "STOCK_WAREHOUSE_ON_FREEZE", "仓库处于冻结状态"),
    STOCK_INCOMING_EXIST(1015, "STOCK_INCOMING_EXIST", "入库工单已经存在"),
    STOCK_OUTGOING_EXIST(1016, "STOCK_OUTGOING_EXIST", "出库工单已经存在"),
    STOCK_ZJS_MAIL_NO_NOT_ENOUGH(1018, "STOCK_ZJS_MAIL_NO_NOT_ENOUGH", "宅急送配送面单不足"),
    STOCK_ZJS_NOT_EXIST(1019, "STOCK_ZJS_NOT_EXIST", "当前店铺所在地区未找到宅急送仓库"),
    STOCK_REEZE_STOCK_LESS(1020, "STOCK_REEZE_STOCK_LESS", "激活库存小于冻结库存"),
    STOCK_CHECK_FAIL(1021, "STOCK_CHECK_FAIL", "盘库失败"),
    SPARE_APPLY_NOT_EXIST(1051, "SPARE_APPLY_NOT_EXIST", "备件申领记录不存在"),
    SPARE_APPLY_STATUS_ERROR(1052, "SPARE_APPLY_STATUS_ERROR", "备件申领记录状态错误"),
    SPARE_APPLY_INFO_ERROR(1053, "SPARE_APPLY_INFO_ERROR", "备件申领记录信息错误"),
    SPARE_COUNT_MORE_THAN_MAX(1054, "SPARE_COUNT_MORE_THAN_MAX", "申领备件数大于最大数"),
    SPARE_APPLY_CANT_REVOKE(1055, "SPARE_APPLY_CANT_REVOKE", "该申领不能撤销"),
    SPARE_APPLY_NOT_BELONG_YOU(1056, "SPARE_APPLY_NOT_BELONG_YOU", "该申领不属于你"),
    SPARE_NUMBER_NOT_ENOUGH(1057, "SPARE_NUMBER_NOT_ENOUGH", "备件数量不足，请至仓库补货"),
    SPARE_BORROW_COMPELTED(1058, "SPARE_BORROW_COMPELTED", "借出工单已完成"),
    SPARE_APPLY_EXIST(1058, "SPARE_APPLY_EXIST", "还有申领/互借工单未处理无法申领新设备"),
    SPARE_INFO_NOT_EXIST(1059, "SPARE_INFO_NOT_EXIST", "备件信息未初始化，请先进入《备件库设备》刷新后再进行操作。"),
    SPARE_NUMBER_OUT(1060,"SPARE_NUMBER_OUT","无法申领更多的设备"),
    SPARE_PART_NUMBER_OUT(1061,"SPARE_PART_NUMBER_OUT","配件数量超过设备配比"),
    SPARE_NUMBER_TAKE_OUT(1062,"SPARE_NUMBER_TAKE_OUT","无法携带更多的设备"),
    ACTIVITI_START_ERROR(1101, "ACTIVITI_START_ERROR", "activiti流程引擎启动失败，请联系管理员"),
    ACTIVITI_SUBPROCESS_START_ERROR(1102, "ACTIVITI_SUBPROCESS_START_ERROR", "activiti子流程启动失败，请联系管理员"),
    ACTIVITI_TASK_NOT_EXIST(1103, "ACTIVITI_TASK_NOT_EXIST", "任务查询失败"),
    WORK_ORDER_NOT_EXIST(1201, "WORK_ORDER_NOT_EXIST", "工单不存在"),
    WORK_ORDER_STEP_ERROR(1202, "WORK_ORDER_STEP_ERROR", "工单环节异常"),
    WORK_ORDER_CLAIM_ERROR(1203, "WORK_ORDER_CLAIM_ERROR", "工单认领失败"),
    WORK_STEP_NOT_EXIST(1204, "WORK_STEP_NOT_EXIST", "工单环节不存在"),
    WORK_STEP_HANDLE_ERROR(1205, "WORK_STEP_HANDLE_ERROR", "工单环节操作失败"),
    WORK_STEP_PERMISSION_DENIED(1206, "WORK_STEP_PERMISSION_DENIED", "没有该工单环节操作权限"),
    CITY_NOT_EXIST(1301, "CITY_NOT_EXIST", "城市信息不存在"),
    DISTRICT_NOT_BELONE_CITY(1302, "DISTRICT_NOT_BELONE_CITY", "该区域不属于城市"),
    DISTRICT_NOT_BELONE_YOU(1303, "DISTRICT_NOT_BELONE_YOU", "你不属于该城市"),
    WX_CHECK_NULL(1401, "WX_CHECK_NULL", "微信出现异常，请稍后再试"),
    WX_AUTH_FAILURE(1402, "WX_AUTH_FAILURE", "微信授权失败，请重新授权登录"),
    CRM_NICK_NOT_EXISTS(1501, "CRM_NICK_NOT_EXISTS", "花名不存在"),
    CRM_DEPARTMENT_NOT_EMPTY(1502, "CRM_DEPARTMENT_NOT_EMPTY", "部门非空，不能删除"),
    DEVICE_BACK_ERROR(1600, "DEVICE_BACK_ERROR", "设备退回故障中，请等待修复"),
    DEVICE_BACK_NOT_EXIST(1601, "DEVICE_BACK_NOT_EXIST", "退回设备工单不存在"),
    DEVICE_BACK_PRODUCT_NOT_EXIST(1602, "DEVICE_BACK_PRODUCT_NOT_EXIST", "设备信息丢失"),
    DEVICE_BACK_NEED_CHECK_PRODUCT_NUM(1603, "DEVICE_BACK_NEED_CHECK_PRODUCT_NUM", "需要校准设备数量"),
    DEVICE_NOT_BELONG_YOU(1604, "DEVICE_NOT_BELONG_YOU", "该退回工单不属于你"),
    DEVICE_NOT_THIS_SHOP(1605, "DEVICE_NOT_THIS_SHOP", "设备不属于这个门店"),
    DEVICE_BACK_STATUS_ERROR(1606, "DEVICE_BACK_STATUS_ERROR", "退回工单状态错误"),
    DEVICE_POSITION_ERROR(1607, "DEVICE_POSITION_ERROR", "设备归属异常"),

    RECYCLE_NOT_EXIST(1651, "RECYCLE_NOT_EXIST", "回收工单不存在"),
    RECYCLE_SHOP_NO_DEVICE(1652, "RECYCLE_SHOP_NO_DEVICE", "店铺没有设备可以回收"),
    RECYCLE_DEVICE_TOO_MORE(1653, "RECYCLE_DEVICE_TOO_MORE", "回收的设备大于店铺设备"),
    RECYCLE_TOO_MORE(1653, "RECYCLE_TOO_MORE", "回收的数量大于可回收数量"),
    RECYCLE_STATUS_ERROR(1654, "RECYCLE_STATUS_ERROR", "回收工单状态错误"),
    RECYCLE_NOT_BELONG_YOU(1655, "RECYCLE_NOT_BELONG_YOU", "回收工单不属于你"),
    RECYCLE_NOT_HAVE_DEIVCE(1656, "RECYCLE_NOT_HAVE_DEIVCE", "回收店铺设备数错误"),
    RECYCLE_DEVICE_NOT_THIS_SHOP(1657, "RECYCLE_DEVICE_NOT_THIS_SHOP", "设备不属于该门店"),
    RECYCLE_DEVICE_NUMBER_ERROR(1658, "RECYCLE_DEVICE_NUMBER_ERROR", "回收设备数量错误"),

    RECYCLE_SHOP_EXIST_POWERBANK(1660, "RECYCLE_SHOP_EXIST_POWERBANK", "店铺还有未回收的充电宝"),
    RECYCLE_SHOP_POWERBANK_STATUS_ERROT(1661, "RECYCLE_SHOP_POWERBANK_STATUS_ERROT", "回收目标充电宝状态异常或不存在"),

    DELIVERY_NOT_DELIVERYER(1701, "DELIVERY_NOT_DELIVERYER", "没有配送员"),
    DELIVERY_DELIVER_NOT_EXIST(1702, "DELIVERY_DELIVER_NOT_EXIST", "找不到该配送员"),
    DELIVERY_NOT_EXIST(1703, "DELIVERY_NOT_EXIST", "没有对应的配送单"),
    DELIVERY_CANT_ALLOCATE_DELIVERYER(1704, "DELIVERY_CANT_ALLOCATE_DELIVERYER", "工单不能分配配送员"),
    DELIVERY_STATUS_ERROR(1705, "DELIVERY_STATUS_ERROR", "工单状态错误"),
    DELIVERY_EXIST(1706, "DELIVERY_EXIST", "配送单已存在"),
    DELIVERY_CANNOT_EDIT_WAREHOUSE(1707, "DELIVERY_CANNOT_EDIT_WAREHOUSE", "不允许修改仓库"),
    DELIVERY_STATUS_HAS_FINISHED(1708, "DELIVERY_STATUS_HAS_FINISHED", "已配送完成不能修改"),
    DELIVERY_ZJS_CREATE_ORDER_FAIL(1709, "DELIVERY_ZJS_CREATE_ORDER_FAIL", "宅急送配送订单创建失败"),
    DELIVERY_ZJS_VERIFY_DATA_NOT_EQUALS(1710, "DELIVERY_ZJS_VERIFY_DATA_NOT_EQUALS", "宅急送签名验证失败"),
    DELIVERY_ZJS_REQUEST_XML_ERROR(1711, "DELIVERY_ZJS_REQUEST_XML_ERROR", "宅急送状态上报参数错误"),
    DELIVERY_ZJS_REPEAT_REQUEST(1712, "DELIVERY_ZJS_REPEAT_REQUEST", "您申请安装的盒子尚未安装配送，请勿重复提交"),
    DELIVERY_ZJS_REQUEST_NUM_ERROR(1713, "DELIVERY_ZJS_REQUEST_NUM_ERROR", "申请安装设备数量超标或少于一台"),
    DELIVERY_ZJS_DEVICE_NUM_MORE_THEN_ONE(1713, "DELIVERY_ZJS_DEVICE_NUM_MORE_THEN_ONE", "申请配送数量暂时不能大于1，请等待后续开发"),
    REPAIR_WORK_EXIST(1751, "REPAIR_WORK_EXIST", "维修工单已存在"),
    REPAIR_DEVICE_NOT_EXIST(1752, "REPAIR_DEVICE_NOT_EXIST", "需要维修的设备不存在"),
    REPAIR_WORK_NOT_EXIST(1753, "REPAIR_WORK_NOT_EXIST", "维修工单不存在"),
    REPAIR_WORK_NOT_OVER(1753, "REPAIR_WORK_NOT_OVER", "该设备检修工作没有结束"),
    REPAIR_HANDLE_ERROR(1754, "REPAIR_HANDLE_ERROR", "维修操作错误"),
    REPAIR_WORK_NOT_BELONG_TO_YOU(1756, "REPAIR_WORK_NOT_BELONG_TO_YOU", "维修工单不属于你"),
    REPAIR_WORK_STATUS_ERROR(1757, "REPAIR_WORK_STATUS_ERROR", "维修工单状态错误"),
    REPAIR_RECORD_NOT_EXIST(1758, "REPAIR_RECORD_NOT_EXIST", "维修记录不存在"),
    REPAIR_WORK_ORIGIN_ERROR(1759, "REPAIR_WORK_ORIGIN_ERROR", "维修工单不存在"),
    REPAIR_SHOP_UNINSTALLED(1760, "REPAIR_SHOP_UNINSTALLED", "店铺未安装完成"),
    EXTRA_OUTGOING_UNEQUAL_QUANTITY(9801, "EXTRA_OUTGOING_UNEQUAL_QUANTITY", "杂项出库工单不存在"),
    EXTRA_OUTGOING_NOT_EXIST(1801, "EXTRA_OUTGOING_NOT_EXIST", "杂项出库工单不存在"),
    EXTRA_OUTGOING_STATUS_ERROR(1802, "EXTRA_OUTGOING_STATUS_ERROR", "杂项出库工单状态错误"),
    EXTRA_OUTGOING_LEADER_NOT_EXIST(1803, "EXTRA_OUTGOING_LEADER_NOT_EXIST", "分管领导不存在"),
    EXTRA_OUTGOING_FINANCER_NOT_EXIST(1804, "EXTRA_OUTGOING_FINANCER_NOT_EXIST", "对应财务不存在"),
    ADJUST_OUTGOING_NOT_EXIST(1851, "ADJUST_OUTGOING_NOT_EXIST", "调拨出库工单不存在"),
    ADJUST_OUTGOING_STATUS_ERROR(1852, "ADJUST_OUTGOING_STATUS_ERROR", "调拨出库工单状态错误"),
    CHECK_STOCK_UN_COMPLETE(1901, "CHECK_STOCK_UN_COMPLETE", "有未完成的盘库"),
    CHECK_STOCK_NOT_EXIST(1902, "CHECK_STOCK_NOT_EXIST", "盘库工单没有找到"),
    CHECK_STOCK_STATUS_ERROR(1903, "CHECK_STOCK_STATUS_ERROR", "盘库工单状态错误"),
    U8_PURCHASE_SIGN_ERROR(1911, "U8_PURCHASE_SIGN_ERROR", "签名错误"),
    ORDER_NOT_EXISTS(2000, "ORDER_NOT_EXISTS", "订单不存在"),
    ORDER_STATUS_INVALID(2001, "ORDER_STATUS_INVALID", "订单状态错误"),
    ORDER_AMOUNT_INVALID(2002, "ORDER_AMOUNT_INVALID", "订单金额不合法"),
    ORDER_REFUND_FAILURE(2050, "ORDER_REFUND_FAILURE", "退款失败"),
    ORDER_REFUND_NOT_ZERO(2051, "ORDER_REFUND_NOT_ZERO", "退款金额不能为0"),
    ORDER_REFUND_STATUS_NOT_PAY(2052, "ORDER_REFUND_STATUS_NOT_PAY", "该订单未支付不能退款"),
    ORDER_REFUND_AMOUNT_INVALID(2053, "ORDER_REFUND_AMOUNT_INVALID", "退款金额不合法"),
    ORDER_REFUND_NO_PAYMENT(2054, "ORDER_REFUND_NO_PAYMENT", "没有支付记录"),
    ORDER_REFUND_UNSUPPORTED_PAYMENT(2055, "ORDER_REFUND_UNSUPPORTED_PAYMENT", "不支持的支付方式"),
    ORDER_REFUND_HAS_REFUND(2056, "ORDER_REFUND_HAS_REFUND", "订单已退款或待支付"),
    ORDER_HAS_NEED_RETRUN(2057, "ORDER_HAS_NEED_RETRUN", "有需要归还的订单"),
    ORDER_HAS_NEED_PAY(2058, "ORDER_HAS_NEED_PAY", "有需要支付的订单"),
    ORDERS_DEPOSIT_ACCOUNT_ZERO(2059, "ORDERS_DEPOSIT_ACCOUNT_ZERO", "该用户押金账户为0"),
    USER_ACCOUNT_WALLET_CHECK_ERROR(2060, "USER_ACCOUNT_WALLET_CHECK_ERROR", "用户余额校验失败，稍后重试"),
    TUYA_CREATE_USER_FAILURE(2100, "TUYA_CREATE_USER_FAILURE", "涂鸦创建用户失败"),
    TUYA_CREATE_TOKEN_FAILURE(2101, "TUYA_CREATE_TOKEN_FAILURE", "涂鸦创建Token失败"),
    // 代理商异常
    AGENT_NOT_EXISTS(3000, "AGENT_NOT_EXISTS", "用户不存在"),
    AGENT_NOT_AGENCY(3001, "AGENT_NOT_AGENCY", "您不是代理商"),
    AGENT_INVALID(3002, "AGENT_INVALID", "暂无法登陆"),
    AGENT_MEMBER_INVALID(3003, "AGENT_MEMBER_INVALID", "非当前代理商员工"),
    AGENT_BOSS_EMPLOYEETYPE_UPDATE(3004, "AGENT_BOSS_EMPLOYEETYPE_UPDATE", "代理商老板不能修改自己的员工类型"),
    AGENT_BOSS_IS_NOT(3005, "AGENT_BOSS_IS_NOT", "不是代理商员工"),
    AGENT_BALANCE_INSUFFICIENT(3010, "AGENT_BALANCE_INSUFFICIENT", "账户余额不足"),
    AGENT_AGENCY_FEE_INSUFFICIENT(3011, "AGENT_AGENCY_FEE_INSUFFICIENT", "代理费账户余额不足"),
    AGENT_PURCHASE_AMOUNT_TO_LESS(3012, "AGENT_PURCHASE_AMOUNT_TO_LESS", "采购金额过低"),
    AGENT_AGENCY_FEE_RECORD_STATUS_INVALID(3013, "AGENT_AGENCY_FEE_RECORD_STATUS_INVALID", "代理费充值记录状态不正确"),
    AGENT_WITHDRAW_ACCOUNT_INVALID(3014, "AGENT_WITHDRAW_ACCOUNT_INVALID", "提现账户不正确"),
    AGENT_ADD_ACCOUNT_ERROR(3015,"AGENT_ADD_ACCOUNT_ERROR","代理商新增资金账户异常"),
    AGENT_TYPE_INVALID(3016, "AGENT_TYPE_INVALID", "无效的用户类型"),
    AGENT_NEED_LOGIN(3203, "AGENT_NO_ROLE", "电小代：您没有该操作的权限"),
    AGENT_NO_ROLE(3252, "AGENT_NO_ROLE", "电小二：您没有该操作的权限"),
    BD_AGENT_CONTRACT_NOT_EXIST(3301, "BD_AGENT_CONTRACT_NOT_EXIST", "bd代理签约记录不存在"),
    BD_AGENT_SHOP_NOT_ORIGIN_AGENT(3302, "BD_AGENT_SHOP_NOT_ORIGIN_AGENT", "门店不属于bd代理签约"),
    BD_AGENT_CONTRACT_STATUS_ERROR(3303, "BD_AGENT_CONTRACT_STATUS_ERROR", "bd代理签约记录状态错误"),
    BD_AGENT_CONTRACT_RECEIVED(3304, "BD_AGENT_CONTRACT_RECEIVED", "签约工单已被领取"),
    BD_AGENT_CONTRACT_NOT_BELONE_TO_YOU(3305, "BD_AGENT_CONTRACT_NOT_BELONE_TO_YOU", "签约工单已被其他人领取"),
    FEEDBACK_EXCEPTION_PROCESS_STATUS_INVALID(3400, "FEEDBACK_EXCEPTION_PROCESS_STATUS_INVALID", "状态不正确"),
    SUPPLIER_NOT_EXIST(3306, "SUPPLIER_NOT_EXIST", " 代理工厂不存在"),
    SHIPORDER_NOT_EXIST(3307, "SHIPORDER_NOT_EXIST", " 订单不存在"),
    SHIPORDER_NOT_DELETE(3308, "SHIPORDER_NOT_DELETE", " 订单不可删除"),
    SHIPORDER_NOT_NULL(3309, " SHIPORDER_NOT_NULL", "设备不能为空"),
    REMOTE_SERVICE_ERROR(3400, "REMOTE_SERVICE_ERROR", "远程服务器异常"),
    WITHDRAWAL_AMOUNT_TO_LOWER(3501, " WITHDRAWAL_AMOUNT_TO_LOWER", "提现金额过小"),
    DEVICE_BIND_FAIL(6788,"DEVICE_BIND_FAIL","设备绑定失败"),
    SKU_EXCEPTION(7761, "SKU_EXCEPTION", "调用SKU改价接口失败"),


    /**
     * 代理级别
     **/
    AGENT_PROFIT_LEVEL_NOT_EXIST(4001, "AGENT_PROFIT_LEVEL_NOT_EXIST", "代理级别不存在"),
    AGENT_PROFIT_AMOUNT_NOT_VALIDATE(4002, " AGENT_PROFIT_AMOUNT_NOT_VALIDATE", "输入的累积充值金额不合理，请重新输入！"),
    /**
     * 代理费管理
     **/
    AGENT_AGENCY_FEE_NOT_EXIST(4501, "AGENT_AGENCY_FEE_NOT_EXIST", "代理费充值记录不存在"),
    AGENT_AGENCY_FEE_AMOUNT_IS_NOT_VALIDATE(4502, "AGENT_AGENCY_FEE_AMOUNT_IS_NOT_VALIDATE", "充值代理费必须大于0"),
    /**
     * 渠道采购
     **/
    AGENT_PURCHASE_ORDER_NO_IS_NULL(5000, "AGENT_PURCHASE_ORDER_NO_IS_NULL", "采购单号为空"),
    AGENT_PURCHASE_ORDER_NOT_EXIST(5001, "AGENT_PURCHASE_ORDER_NOT_EXIST", "采购单不存在"),


    ACTIVITY_NOEXIST(5500, "活动不存在"),
    ACTIVITY_ERROR_DATE(5501, "当前日期不在活动时间内"),
    ACTIVITY_STATUS_LOSE_EFFECT(5502, "活动已失效"),
    ACTIVITY_USER_TAG_NEW_ERROR(5503, "不符合领取身份(仅限新用户领取)"),
    ACTIVITY_COUPON_MAX(5504, "优惠券已经领完"),
    ACTIVITY_LIMIT(5505, "已到该活动的领取上限"),
    ACTIVITY_NOCODE(5506, "缺少兑换码参数"),
    COUPON_USER_ERROR(5507, "优惠券不是当前用户所有！"),
    COUPON_USED(5508, "优惠券已经被使用"),
    MARKING_CENTER_FAIL(5509, "调用营销中心借口失败"),
    ACTIVITY_USER_TAG_EMPLOYEE_ERROR(5510, "不符合领取身份(仅限内部员工领取)"),
    COUPON_CODE_ERROR(5511, "无效的兑换码"),
    ALILOG_API_EXCEPTION(6001, "ALILOG_API_EXCEPTION", "阿里云日志服务接口响应异常"),



    /**
     * 门店管理相关
     */
    FEERATE_INVALID(7001,"FEERATE_INVALID","分成比例不合法"),
    DATEFORMAT_INVALID(7002,"DATEFORMAT_INVALID","时间格式错误"),
    DATERANGE_INVALID(7003,"DATERANGE_INVALID","时间范围错误"),
    RECEIVE_SHOP_NUM_OUT_RANGE(7004,"RECEIVE_SHOP_NUM_OUT_RANGE","本次操作超出当前bd能认领的公海门店总数"),
    ALLOCATE_HEAD_SHOP_NUM_OUT_RANGE(7005,"ALLOCATE_HEAD_SHOP_NUM_OUT_RANGE","本次操作超出当前bd能认领的头部门店总数"),
    INVALID_IMPORT_ACTION(7006,"INVALID_IMPORT_ACTION","该门店已经分配过了，请勿重复分配公海门店"),

    REMOTE_SERVICE_EXCEPTION(8001, "MARKETING_EXCEPTION", "远程服务调SKU服务用失败"),
    VISIT_ADDRESS_ERROR(8002,"VISIT_ADDRESS_ERROR","拜访位置获取失败"),
    REMIND_SELLER_ERROR(8003,"REMIND_SELLER_ERROR","提醒失败"),
    ARGUMENT_ILLEGAL_ERROR(8004,"ARGUMENT_ILLEGAL_ERROR","参数含有非法攻击字符,已禁止继续访问！"),


    /**
     * BD 操作控制 配置出错
     */
    AGENT_OPERATION_RESULT_ERROR(9001,"AGENT_OPERATION_RESULT_ERROR","操作控制配置条件错误"),

    /**
     * 订单中心
     */
    HERMES_FAIL(40000, "HERMES_FAIL", "订单中心远程调用失败"),
    REFUND_ERROR(40001, "REFUND_ERROR", "订单中心退款失败"),
    // 资产相关
    SUBJECTTYPE_NOT_CORRECT(10000,"SUBJECTTYPE_NOT_NULL","主体类型不符合要求"),
    ASSETS_STATUS_ERROR(10001,"ASSETS_STATUS_ERROR","当前资产状态不可进行该操作"),
    ASSETS_STATUS_LOST_ERROR(10002,"ASSETS_STATUS_LOST_ERROR","设备已申报遗失无法操作"),
    ASSETS_ABNORMAL_ID_NOT_EXISTS(10003,"ABNORMAL_ID_NOT_EXISTS","设备异动记录未找到"),
    ASSETS_DEVICE_POSITION_CHANGE(10004, "DEVICE_POSITION_CHANGE", "工单待审批期间，设备资产位置存在变化，无法通过审批，请拒绝后由BD重新上报。"),
    ASSETS_DEVICE_POSITION_CHANGE_FAIL(10004, "DEVICE_POSITION_CHANGE_FAIL", "资产位置更新失败"),

    MERCHANT_ADD_ERROR(20001, "批量商户确认异常"),
    MERCHANT_UPDATE_ERROR(20002, "批量商户变更确认异常"),
    MERCHANT_PROFIT_STATUS_SYNC_ERROR(20003,"MERCHANT_PROFIT_STATUS_SYNC_ERROR","商户分成合同状态同步出错，或在流程中暂未同步"),
    MERCHANT_WITHDRAW_FINANCIAL_ERROR(12001,"MERCHANT_WITHDRAW_FINANCIAL_ERROR","财务审核出错"),

    TARGET_ADD_ERROR(20001, "新增计费对象异常"),

    /**
     * 调用远程服务
     */
    REMOTE_SERVICE_ZUUL_AUTHORITY(30001, "REMOTE_SERVICE_ZUUL_AUTHORITY", "鉴权中心授权异常"),
    /**
     * 银行卡变更
     */
     BANK_SORCE_EDIT_ERROR(12000,"BANK_SORCE_EDIT_ERROR","编辑银行卡出错"),

    PAYMENT_REFUND(40001, "退款接口反馈失败，请稍后重试"),

    /**
     * 网格化运营
     */
    FOUND_DUPLICATE_EFFECT_GRID(50001,"FOUND_DUPLICATE_EFFECT_GRID","同一组织架构出现同时生效的网格方案"),
    UNDEFINE_SHOP_GRID_RELATION(50002,"UNDEFINE_SHOP_GRID_RELATION","门店网格归属待确认...次日生效"),

    EXPRESS_NO_NOTEXSIT(60000,"EXPRESS_NO_NOTNULL","快递单号不能为空"),
    FILE_TYPE_NOXLS(60001,"FILE_TYPE_NOXLS","文件格式非xls"),
    FILE_FORMAT_NOEXCEL(60002,"FILE_FORMAT_NOEXCEL","文件非excel文件"),
    EXPRESS_SECRETKEY(60003,"EXPRESS_SECRETKEY","快递secret异常"),
    EXPRESS_CONFIG_NULL(60004,"EXPRESS_CONFIG_NULL","快递配置错误"),



    REPAIR_DEVICE_IS_OVER(44444, "REPAIR_DEVICE_IS_OVER", "设备维修已结束"),
    REPAIR_DEVICE_IS_OFFLINE(22222, "REPAIR_DEVICE_IS_OFFLINE", "设备已离线"),
    APOLLO_REMOTE_ERROR(22222, "APOLLO REMOTE ERROR", "APOLLO 调用失败"),

    /**
     * 数据风控中心
     */
    POSEIDON_FAIL(400000, "POSEIDON_FAIL", "数据风控中心远程调用失败"),

    FOCUS_ERROR(70000,"FOCUS_ERROR","所选门店包含已关注门店"),
    NOT_FOCUS_ERROR(70000,"NOT_FOCUS_ERROR","所选门店包含未关注门店"),

    CREDIT_ORDERS_CHANGE_AMOUNT_ERROR(10001,"CREDIT_ORDERS_CHANGE_AMOUNT_ERROR","微信支付分订单不允许改价。"),

    /**
     * 供应商相关
     */
    SUPPLIER_CREATE_WAREHOUSE_ERROR(8000,"生成仓库失败"),

    /**
     * 供应链相关
     */
    HAS_NOT_FINISH_PURCHASE(11001,"存在未完成的采购入库单，请先进行处理！"),
    HAS_NOT_FINISH_TRANSFERS(11002,"存在未完成的调拨出库单，请先进行处理！"),
    HAS_NOT_FINISH_SUNDRY(11003,"存在未完成的杂项出库单，请先进行处理！"),
    QUERY_NOT_FINISH_SUNDRY_ERROR(11007,"查询未完成的杂项出库单失败"),
    HAS_NOT_FINISH_CHECK_STOCK(11004,"存在未完成的盘点单，请先进行处理！"),
    HAS_NOT_FINISH_DEVICE_BACK(11005,"存在未完成的设备回收入库单，请先进行处理！"),
    HAS_NOT_FINISH_DEVICE_APPLY(11006,"存在未完成的设备申领出库单，请先进行处理！"),
    HAS_STOCK(11007,"仓库存在库存，请先进行处理！"),

    /**
     * 会员相关
     */
    MEMBER_REMOTE_ERROR(90000, "MEMBER REMOTE ERROR", "SELENE 调用失败"),
    MEMBER_NOT_EXISTS(90001, "MEMBER NOT EXISTS", "用户不是月卡会员"),
    MEMBER_REFUND_ZERO(90002, "MEMBER REFUND ZERO", "退款金额为0，不能退款"),
    MEMBER_HAS_PROCESSING_ORDER(90003, "MEMBER HAS PROCESSING ORDER", "用户有处理中的订单，不能退会员"),
    MEMBER_REFUND_ERROR(90004, "MEMBER REFUND ERROR", "退款失败"),
    MEMBER_HAS_RENEWAL(90005, "MEMBER HAS RENEWAL", "用户已开通自动扣款 不能退款"),
    MEMBER_OVER_ONE_MONTH(90006, "MEMBER OVER ONE MONTH", "会员有期超过一个月，暂不支持退款"),
    MEMBER_ORDER_NOT_EXISTS(90001, "MEMBER ORDER NOT EXISTS", "用户订单不存在"),
    SHOP_NO_BOX(700,"SHOP_NO_BOX","门店暂无盒子设备"),
    DELIVERY_QTY_EXIST(701,"DELIVERY_QTY_EXIST","发货数量异常"),
    REPLACE_NOT_POWERS(702,"REPLACE_NOT_POWERS","没权限替换"),
    REPLACE_RECEVIE_NUM(703,"REPLACE_RECEVIE_NUM","签收数量不能小于0"),
    REPLACE_EXPRESS_ALREADY_SIGIN(704,"REPLACE_EXPRESS_ALREADY_SIGIN","该快递已经被其他人签收"),
    SLOTPOWERBANK_NOT_BAD(705,"SLOTPOWERBANK_NOT_BAD","该槽位充电宝正常无须替换"),
    TAKE_OUT_FAIL(706,"TAKE_OUT_FAIL","取出充电宝失败"),
    REPLACE_NOT_COMPLETE(707,"REPLACE_NOT_COMPLETE","替换未完成"),
    REPLACE_TASK_SIGN_ERROR(708,"REPLACE_TASK_SIGN_ERROR","快递员签收异常"),
    EXPRESS_NOT_SIGN(709,"EXPRESS_NOT_SIGN","快递未被签收或该快递员没有权限"),
    NEED_NOT_REPLACE(710,"NEED_NOT_REPLACE","由于该门店充电宝都正常无须替换"),
    BOX_NOT_SHOP(711,"BOX_NOT_SHOP","该设备不属于此门店"),
    REPLACE_EXPRESS_COMPLETE(712,"REPLACE_EXPRESS_COMPLETE","该替换任务已经结束"),
    REPLACE_TIME(713,"REPLACE_TIME","替换充电宝大于收货数量"),
    REPLACE_ERROR(714,"REPLACE_ERROR","替换操作异常"),

    MANUAL_DEDUCT_ERROR(715, "MANUAL_DEDUCT_ERROR", "发票手动核销失败"),
    INVOICE_NON_ERROR(716, "INVOICE_NON_ERROR", "指定的发票不存在"),
    DEDUCT_AMOUNT_ERROR(717, "DEDUCT_AMOUNT_ERROR", "发票可核销余额不足"),
    INVOICE_PERMISSION_ERROR(718, "INVOICE_PERMISSION_ERROR", "没有操作该发票的权限"),

    ;

    private int code;
    private String desc;
    private String message;

    LeoExcs(int code, String desc, String message) {
        this.code = code;
        this.desc = desc;
        this.message = message;
    }

    LeoExcs(int code, String message) {
        this(code, null, message);
    }

    public String toString() {
        return code + ":" + desc + ":" + message;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String getTitle() {
        return null;
    }
}