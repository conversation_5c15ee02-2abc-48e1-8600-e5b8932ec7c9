package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/10 4:46 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SupplierAttachmentTypeEnum implements EnumInterface<SupplierAttachmentTypeEnum> {

	INVOICE_PIC(1,"发票图片"),
	INVOICE_VERIFY(2,"发票对账单附件"),
	;

	private Integer code;
	private String desc;

	@Override
	public SupplierAttachmentTypeEnum getDefault() {
		return null;
	}

}
