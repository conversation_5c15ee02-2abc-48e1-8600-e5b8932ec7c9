package so.dian.invoice.enums;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/26 6:49 PM
 * @Description:
 */
@Getter
public enum InCheckPoolStatusEnum implements EnumInterface<InCheckPoolStatusEnum> {

	NOT_ENTERED(0, "未进入"),
	ENTERED(1, "已进入"),
	;
	private Integer code;
	private String desc;

	InCheckPoolStatusEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 默认为null
	 *
	 * @return 默认枚举对象
	 */
	@Override
	public InCheckPoolStatusEnum getDefault() {
		return null;
	}
}
