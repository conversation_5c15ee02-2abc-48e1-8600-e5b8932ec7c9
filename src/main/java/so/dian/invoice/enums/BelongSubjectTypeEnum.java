package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.EnumInterface;

/**
 * 发票系统所属类型
 * <AUTHOR>
 * @date 2022/3/9 10:19 AM
 */
@Getter
@AllArgsConstructor
public enum BelongSubjectTypeEnum implements EnumInterface<BelongSubjectTypeEnum> {

    XIAODIAN(0, "小电集团"),

    JV_CORP(5, "合资公司");

    private Integer code;
    private String desc;

    /**
     * 默认为null
     *
     * @return 默认枚举对象
     */
    @Override
    public BelongSubjectTypeEnum getDefault() {
        return null;
    }

    /**
     * 是否是小电
     * @param belongSubjectType
     * @return true是，false不是
     */
    public static boolean isXD(Integer belongSubjectType){
        return XIAODIAN.code.equals(belongSubjectType);
    }
}

