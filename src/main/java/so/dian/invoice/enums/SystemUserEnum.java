package so.dian.invoice.enums;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/8/29 3:28 PM
 * @Description:
 */
@Getter
public enum SystemUserEnum implements EnumInterface<SystemUserEnum>{

	SYSTEM_USER(0,"系统用户"),
	;
	private Integer code;
	private String desc;

	SystemUserEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 默认为null
	 *
	 * @return 默认枚举对象
	 */
	@Override
	public SystemUserEnum getDefault() {
		return null;
	}
}
