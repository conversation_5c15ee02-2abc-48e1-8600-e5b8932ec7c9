package so.dian.invoice.enums;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/01/25 14:18
 * @description:
 */
public enum NewsVoucherEnum {
    CREATE(1, "新建"),
    SUCCESS(2, "处理成功"),
    FAIL(3, "处理失败"),
    PROCESS(4, "处理中");
    private final Integer code;
    private final String desc;

    NewsVoucherEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer code() {
        return this.code;
    }

    public String desc() {
        return this.desc;
    }

    public static NewsVoucherEnum from(Integer code) {
        return Arrays.stream(values())
                .filter(r -> r.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
