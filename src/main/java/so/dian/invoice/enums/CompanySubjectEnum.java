package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: jiaoge
 * @Date: 2019/9/18 10:32 AM
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum CompanySubjectEnum {

	YIDIANYUAN(1, "YIDIANYUAN","北京伊电园网络科技有限公司"),
	HZYIDIANYUAN(1, "HZYIDIANYUAN","杭州伊电园网络科技有限公司"),
	XIAODIAN(1, "XIAODIAN","杭州小电科技股份有限公司"),
	YOUDIAN(0, "YOUDIAN","杭州友电科技有限公司"),
	YOUDIAN1(4, "YOUDIAN1","杭州有电科技有限公司"),
	XM_XD(5, "XM_XD","厦门小电科技有限公司"),
	HZ_YD_CIXIAN(0, "HZ_YD_CIXIAN","杭州友电科技有限公司磁县分公司"),
	HZ_XD_ZHENGZHOU(1, "HZ_XD_ZHENGZHOU","杭州小电科技股份有限公司郑州分公司"),
	;
	private int code;

	private String name;

	private String desc;

	public static CompanySubjectEnum getEnumByName(String name) {
		for(CompanySubjectEnum subject : CompanySubjectEnum.values()) {
			if(subject.getName().equals(name)) {
				return subject;
			}
		}
		return null;
	}

	public static CompanySubjectEnum getEnumByDesc(String desc) {
		for(CompanySubjectEnum subject : CompanySubjectEnum.values()) {
			if(subject.getDesc().equals(desc)) {
				return subject;
			}
		}
		return null;
	}

}
