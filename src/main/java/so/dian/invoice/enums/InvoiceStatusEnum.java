package so.dian.invoice.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvoiceStatusEnum {

    WAIT(1, "未核销"),
    PART(2, "部分核销"),
    ALL(3, "全部核销");


    private int type;

    private String field;



    public static String getField(int type) {

        for(InvoiceStatusEnum status : InvoiceStatusEnum.values()) {
            if(status.getType() == type) {
                return status.getField();
            }
        }

        return null;
    }

    public static InvoiceStatusEnum findByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (InvoiceStatusEnum en : InvoiceStatusEnum.values()) {
            if (Objects.equals(en.getType(), type)) {
                return en;
            }
        }
        return null;
    }
}
