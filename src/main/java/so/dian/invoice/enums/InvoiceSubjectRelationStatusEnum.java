package so.dian.invoice.enums;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * InvoiceSubjectRelationStatusEnum
 *
 * <AUTHOR>
 */
@Getter
public enum InvoiceSubjectRelationStatusEnum implements EnumInterface<InvoiceSubjectRelationStatusEnum> {
    VALID(1, "生效中"),
    INVALID(0, "已失效");

    private Integer code;
    private String desc;

    InvoiceSubjectRelationStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 默认为null
     *
     * @return 默认枚举对象
     */
    @Override
    public InvoiceSubjectRelationStatusEnum getDefault() {
        return null;
    }
}
