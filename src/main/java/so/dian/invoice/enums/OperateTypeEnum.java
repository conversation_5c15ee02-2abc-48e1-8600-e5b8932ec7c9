package so.dian.invoice.enums;

public enum OperateTypeEnum {

    DEDUCT(1, "核销"),
    RECOVER(2, "回滚");

    OperateTypeEnum(int type, String field) {

        this.type = type;
        this.field = field;

    }

    private int type;

    private String field;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public static String getField(int type) {

        for(OperateTypeEnum status : OperateTypeEnum.values()) {
            if(status.getType() == type) {
                return status.getField();
            }
        }

        return null;
    }
}
