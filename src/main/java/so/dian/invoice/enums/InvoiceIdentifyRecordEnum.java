package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public enum InvoiceIdentifyRecordEnum {
    ;


    @Getter
    @AllArgsConstructor
    public enum IsRealEnum {

        STAY(0, "待验真"),
        SUC(1, "已验真"),
        FAIL(2, "未通过"),
        ;

        Integer code;
        String desc;

        public static IsRealEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (IsRealEnum temp : IsRealEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }

        public static String getDescByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (IsRealEnum temp : IsRealEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp.getDesc();
                }
            }
            return null;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum IsRelatedEnum {

        NO(0, "未关联"),
        YES(1, "已关联"),
        DOING(2, "关联中"),
        ;

        Integer code;
        String desc;

        public static IsRelatedEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (IsRelatedEnum temp : IsRelatedEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
    }
    @Getter
    @AllArgsConstructor
    public enum DeletedEnum {

        UN_DELETED(0, "未删除"),
        DELETED(-1, "删除");

        Integer code;
        String desc;

        public static DeletedEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (DeletedEnum temp : DeletedEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
    }

    /**
     * 发票验真code枚举值
     */
    @Getter
    @AllArgsConstructor
    public enum ValidationCodeEnum{

        SUCCESS("10000","查验成功"),
        NO("10001", "查无此票"),
        DIFFER("10002","查验信息不一致"),
        OVER_LIMIT("10003", "验真次数超过限制"),
        NO_SUPPORT("10004","不支持验真发票类型"),
        INVALID_PARAM("10005", "无效参数"),
        OTHER("10006","国税接口不稳定"),
        BUYER_TAX_ID_ERROR("10007", "纳税人识别号不正确"),
        ;

        String code;
        String desc;

        public static ValidationCodeEnum getByCode(String code) {
            if (code == null) {
                return null;
            }

            for (ValidationCodeEnum temp : ValidationCodeEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
        public static String getDescByCode(String code) {
            if (code == null) {
                return "";
            }

            for (ValidationCodeEnum temp : ValidationCodeEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp.desc;
                }
            }
            return "";
        }
    }
    /**
     * 发票验真code枚举值
     */
    @Getter
    @AllArgsConstructor
    public enum InvoiceResultEnum {

        SUCCESS(1,"成功"),
        FAIL(0, "失败"),
        ;

        Integer code;
        String desc;

        public static InvoiceResultEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (InvoiceResultEnum temp : InvoiceResultEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum IsIdentifyEnum {
        SUC(1, "识别通过"),
        FAIL(2, "识别不通过"),
        ;

        Integer code;
        String desc;

        public static IsIdentifyEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (IsIdentifyEnum temp : IsIdentifyEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
    }
    @Getter
    @AllArgsConstructor
    public enum InvoiceIdentifyTypeEnum {
        增值税专用发票("10100", "增值税专用发票"),
        定额发票("10200", "定额发票"),
        过路费发票("10507", "过路费发票"),
        增值税普通发票("10101", "增值税普通发票"),
        机打发票("10400", "机打发票"),
        可报销其他发票("10900", "可报销其他发票"),
        增值税电子普通发票("10102", "增值税电子普通发票"),
        出租车发票("10500", "出租车发票"),
        其他("00000", "其他"),
        增值税普通发票卷票("10103", "增值税普通发票(卷票)"),
        火车票("10503", "火车票"),
        国际小票("20100", "国际小票"),
        机动车销售统一发票("10104", "机动车销售统一发票"),
        客运汽车("10505", "客运汽车"),
        国际大票("20103", "国际大票"),
        定额二手车销售统一发票发票("10105", "二手车销售统一发票"),
        航空运输电子客票行程单("10506", "航空运输电子客票行程单"),
        电子发票专用发票("10107", "电子发票专用发票"),
        电子发票普通发票("10108", "电子发票普通发票"),
        ;

        String code;
        String desc;

        public static InvoiceIdentifyTypeEnum getByCode(String code) {
            if (code == null) {
                return null;
            }

            for (InvoiceIdentifyTypeEnum temp : InvoiceIdentifyTypeEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }

        public static String getDescByCode(String code) {
            if (code == null) {
                return null;
            }

            for (InvoiceIdentifyTypeEnum temp : InvoiceIdentifyTypeEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp.desc;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum IdentifyTypeEnum {
        RECORD(0, "识别验证"),
        INVOICE(1, "发票验证"),
        ;
        Integer code;
        String desc;

        public static IdentifyTypeEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }

            for (IdentifyTypeEnum temp : IdentifyTypeEnum.values()) {
                if (temp.getCode().equals(code)) {
                    return temp;
                }
            }
            return null;
        }
    }



}
