package so.dian.invoice.enums;

import lombok.Getter;

/**
 * 授信支付的付款渠道
 */
@Getter
public enum PayChannel{
    /**
     * 授信账户
     * 代理商：小电
     */
    CREDIT_PAY_XD(49, "授信账户"),

    /**
     * 授信账户
     * 合资公司：友电
     */
    CREDIT_PAY_YD(50, "授信账户"),
    /**
     * 组合支付-[2,49]-[微信支付,授信账户(小电)]
     */
    WX_GRANT_XD_ASSEMBLE_PAY(1002049, "微信支付、授信账户"),

    /**
     * 组合支付-[2,50]-[微信支付,授信账户(友电)]
     */
    WX_GRANT_YD_ASSEMBLE_PAY(1002050, "微信支付、授信账户"),
    /**
     * 组合支付-[4,49]-[支付宝,授信账户(小电)]
     */
    ALI_GRANT_XD_ASSEMBLE_PAY(1004049, "支付宝、授信账户"),

    /**
     * 组合支付-[4,50]-[支付宝,授信账户(友电)]
     */
    ALI_GRANT_YD_ASSEMBLE_PAY(1004050, "支付宝、授信账户"),
    /**
     * 组合支付-[48,49]-[设备资金账户,授信账户(小电)]
     */
    AGENT_GRANT_XD_ASSEMBLE_PAY(1048049, "设备资金账户、授信账户"),

    /**
     * 组合支付-[48,50]-[设备资金账户,授信账户(友电)]
     */
    AGENT_GRANT_YD_ASSEMBLE_PAY(1048050, "设备资金账户、授信账户");

    private Integer code;
    private String name;

    PayChannel(Integer code, String name) {
	this.code = code;
	this.name = name;
    }

    public static PayChannel getByCode(Integer code) {
	for (PayChannel payChannel : PayChannel.values()) {
	    if (payChannel.getCode().equals(code)) {
		return payChannel;
	    }
	}
	return null;
    }

}