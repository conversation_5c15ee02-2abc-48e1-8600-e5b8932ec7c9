package so.dian.invoice.enums;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 8:57 PM
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum InvoiceProcessStatusEnum implements EnumInterface<InvoiceProcessStatusEnum> {

    ALREADY_ENTRY(0, "已录入"),
    SENDING(1, "已寄出"),
    REVIEW_PASS(2, "财务复核通过"),
    REVIEW_NOT_PASS(3, "财务复核不通过"),
    ;

    private Integer code;
    private String desc;

    /**
     * 默认为null
     *
     * @return 默认枚举对象
     */
    @Override
    public InvoiceProcessStatusEnum getDefault() {
        return null;
    }

    public static InvoiceProcessStatusEnum getByCode(Integer code) {

        for (InvoiceProcessStatusEnum source : InvoiceProcessStatusEnum.values()) {
            if (Objects.equals(code, source.getCode())) {
                return source;
            }
        }

        return null;
    }

    /**
     * 复核状态列表
     */
    public static final List<Integer> REVIEW_STATUS = Lists.newArrayList(
            REVIEW_PASS.getCode(),
            REVIEW_NOT_PASS.getCode()
    );

}
