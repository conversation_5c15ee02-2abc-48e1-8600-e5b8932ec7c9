package so.dian.invoice.enums;

import lombok.Getter;
import so.dian.fis.credit.dto.response.CreditMessageDTO;
import so.dian.songshan.client.pojo.dto.ChannelRepairChangeDTO;
import so.dian.taishan.client.pojo.mq.TradeOrderChangeDTO;

/**
 * @author: yuechuan
 * @create: 2025/03/19 14:18
 * @description:
 */
@Getter
public enum NewsVoucherTypeEnum {
    PURCHASE_PAYMENT(OutBizTypeEnum.PURCHASE_PAYMENT.getCode(), TradeOrderChangeDTO.class),
    REPAIR_ORDER_COMPLETE(OutBizTypeEnum.REPAIR_ORDER_COMPLETE.getCode(), ChannelRepairChangeDTO.class),
    PURCHASE_CANCEL(OutBizTypeEnum.PURCHASE_CANCEL.getCode(), TradeOrderChangeDTO.class),
    CREDIT_REPAYMENT_REFUND(OutBizTypeEnum.CREDIT_REPAYMENT_REFUND.getCode(), CreditMessageDTO.class),
    CREDIT_REPAYMENT_SUCCESS(OutBizTypeEnum.CREDIT_REPAYMENT_SUCCESS.getCode(), CreditMessageDTO.class),

    ;
    private final int code;
    private final Class clazz;

    NewsVoucherTypeEnum(int code, Class aClazz) {
        this.code = code;
        this.clazz = aClazz;
    }

    public static NewsVoucherTypeEnum getByCode(int code) {
        for (NewsVoucherTypeEnum value : NewsVoucherTypeEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
