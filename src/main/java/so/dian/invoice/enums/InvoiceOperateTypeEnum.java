package so.dian.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: invoice
 * @description: 发票操作类型枚举
 * @author: yuechuan
 * @create: 2025-09-04 14:35
 */
@Getter
@AllArgsConstructor
public enum InvoiceOperateTypeEnum {

    INVOICE_UPDATE(1, "发票更新"),
    INVOICE_AUDIT(2, "发票审核"),
    INVOICE_CREATE(3, "发票创建");

    private final Integer code;
    private final String desc;

    public static InvoiceOperateTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InvoiceOperateTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        InvoiceOperateTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getDesc() : null;
    }
}
