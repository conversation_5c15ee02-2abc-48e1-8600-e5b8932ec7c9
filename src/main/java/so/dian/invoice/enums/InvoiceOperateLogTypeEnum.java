package so.dian.invoice.enums;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/9/12 11:46 AM
 * @Description:
 */
@Getter
public enum InvoiceOperateLogTypeEnum implements EnumInterface<InvoiceOperateLogTypeEnum> {

    INVOICE_MANUAL_ENTRY(10, "发票手动录入"),
    INVOICE_OCR_ENTRY(11, "发票OCR录入"),
    INVOICE_XIAOER_ENTRY(12, "发票小二端自动录入"),
    INVOICE_EDIT(20, "发票编辑"),
    INVOICE_DEDUCT(30, "发票核销"),
    INVOICE_REVERT(31, "发票回滚"),
    INVOICE_MANUAL_DEDUCT(32, "发票手动核销"),
    INVOICE_EXPRESS(40, "发票快递"),
    INVOICE_REVIEW(50, "发票复核"),
    CHECK_INVOICE_RATE_CONFIG(60,"发票质检比例配置"),
    CHECK_INVOICE_CONCLUSION_CONFIG(65, "发票质检结论配置"),
    CHECK_INVOICE_USER_REGION_CONFIG(70, "发票质检员工大区配置"),
    CHECK_INVOICE(75, "发票质检"),
    DELETED_CHECK_CONCLUSION(80, "删除质检结论"),
    INVOICE_OBSOLETE(99, "发票作废"),
    ;

    private Integer code;
    private String desc;

    InvoiceOperateLogTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 默认为null
     *
     * @return 默认枚举对象
     */
    @Override
    public InvoiceOperateLogTypeEnum getDefault() {
        return null;
    }
}
