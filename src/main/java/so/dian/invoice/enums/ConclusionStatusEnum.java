package so.dian.invoice.enums;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:20 AM
 * @Description:
 */
@Getter
public enum ConclusionStatusEnum implements EnumInterface<ConclusionStatusEnum> {

	NOT_USED(0, "未使用"),
	ALREADY_USED(1, "已使用"),
	;
	private Integer code;
	private String desc;

	ConclusionStatusEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 默认为null
	 *
	 * @return 默认枚举对象
	 */
	@Override
	public ConclusionStatusEnum getDefault() {
		return null;
	}
}
