package so.dian.invoice.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import so.dian.invoice.configuration.excel.CustomCellWriteHeightConfig;
import so.dian.invoice.configuration.excel.CustomCellWriteWidthConfig;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;


public class EasyExcelUtils {

    /**
     * 创建并配置ExcelWriter对象，用于写入指定的临时文件
     * 该方法通过EasyExcel库实现，允许自适应列宽和行高，并应用预定义的样式策略
     *
     * @param tempFile 用于写入的临时文件对象
     * @return 配置好的ExcelWriter对象
     */
    public static ExcelWriter buildExcelWriteOfFile(File tempFile){
        return EasyExcel.write(tempFile)
                .registerWriteHandler(new CustomCellWriteWidthConfig()) /*自适应列宽*/
                .registerWriteHandler(new CustomCellWriteHeightConfig()) /*自适应行高（根据自己情况选择使用，我这里没用到）*/
                .registerWriteHandler(EasyExcelUtils.getStyleStrategy()) /*引用样式*/
                .build();
    }

    /**
     * 创建并配置ExcelWriter对象，用于写入指定的输出流
     * 与buildExcelWriteOfFile方法类似，但输出目标是一个输出流而不是文件
     *
     * @param outputStream 用于写入的输出流对象
     * @return 配置好的ExcelWriter对象
     */
    public static ExcelWriter buildExcelWriteOfOutputStream(OutputStream outputStream){
        return EasyExcel.write(outputStream)
                .registerWriteHandler(new CustomCellWriteWidthConfig()) /*自适应列宽*/
                .registerWriteHandler(new CustomCellWriteHeightConfig()) /*自适应行高（根据自己情况选择使用，我这里没用到）*/
                .registerWriteHandler(EasyExcelUtils.getStyleStrategy()) /*引用样式*/
                .build();
    }

    /**
     * 导出Excel文件
     *
     * @param response 输出流
     * @param data         数据列表
     * @param clazz        数据类类型
     * @param sheetName    工作表名称
     */
    public static void exportExcel(HttpServletResponse response, List<?> data, Class<?> clazz, String sheetName) throws IOException {
        // 确保文件名包含 .xlsx 后缀
        String fileName = sheetName.endsWith(".xlsx") ? sheetName : sheetName + ".xlsx";
        // 对文件名进行 URL 编码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.addHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
        exportExcel(response.getOutputStream(), data, clazz, sheetName);
    }

    /**
     * 导出Excel文件
     *
     * @param outputStream 输出流
     * @param data         数据列表
     * @param clazz        数据类类型
     * @param sheetName    工作表名称
     */
    public static void exportExcel(OutputStream outputStream, List<?> data, Class<?> clazz, String sheetName) {
        ExcelWriter excelWriter = buildExcelWriteOfOutputStream(outputStream);
        WriteSheet mainSheet = EasyExcel.writerSheet(sheetName).head(clazz).build();
        excelWriter.write(data, mainSheet);
        excelWriter.finish();
    }

    /**
     * 导出Excel文件，默认工作表名称为“数据”
     *
     * @param outputStream 输出流
     * @param data         数据列表
     * @param clazz        数据类类型
     */
    public static void exportExcel(OutputStream outputStream, List<?> data, Class<?> clazz) {
        exportExcel(outputStream, data, clazz, "导出数据");
    }


    /**
     * 设置excel样式
     */
    public static HorizontalCellStyleStrategy getStyleStrategy() {
        // 头的策略  样式调整
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 头背景 浅绿
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        // 头字号
        headWriteFont.setFontHeightInPoints((short) 12);
        // 字体样式
        headWriteFont.setFontName("宋体");
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 自动换行
        headWriteCellStyle.setWrapped(true);
        // 设置细边框
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        // 设置边框颜色 25灰度
        headWriteCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        headWriteCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        headWriteCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        headWriteCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 水平对齐方式
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 垂直对齐方式
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 内容的策略 宋体
        WriteCellStyle contentStyle = new WriteCellStyle();
        // 设置垂直居中
        contentStyle.setWrapped(true);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置 水平居中
//        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteFont contentWriteFont = new WriteFont();
        // 内容字号
        contentWriteFont.setFontHeightInPoints((short) 12);
        // 字体样式
        contentWriteFont.setFontName("宋体");
        contentStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentStyle);
    }
}