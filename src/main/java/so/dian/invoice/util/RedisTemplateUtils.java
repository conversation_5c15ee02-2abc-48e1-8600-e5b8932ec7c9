package so.dian.invoice.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created by xuanbing on 2017/12/6.
 */
@Component
@Slf4j
public class RedisTemplateUtils {

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 单个插入hash
     */
    public void setIntoHash(String key, String field, Object object) {
        try {
            redisTemplate.opsForHash().put(key, field, JSON.toJSONString(object));
        } catch (Exception e) {
            log.error(String.format("RedisUtils setIntoHash. key=%s, field=%s, object=%s", key, field,
                    JSON.toJSONString(object)), e);
        }
    }


    /**
     * 批量插入hash
     */
    public void setAllIntoHash(String key, Map map) {
        this.setAllIntoHash(key, map, null, null);

    }


    /**
     * 批量插入hash
     */
    public void setAllIntoHash(String key, Map map, Long expireTime, TimeUnit timeUnit) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (expireTime != null) {
                redisTemplate.expire(key, expireTime, timeUnit);
            }
        } catch (Exception e) {
            log.error(String.format("RedisUtils setAllIntoHash. key=%s,  object=%s", key, map), e);
        }
    }


    /**
     * 插入list
     */
    public void setIntoList(String key, List list, Long expireTime, TimeUnit timeUnit) {
        try {
            redisTemplate.opsForList().leftPushAll(key, list);
            if (expireTime != null) {
                redisTemplate.expire(key, expireTime, timeUnit);
            }
        } catch (Exception e) {
            log.error(String.format("RedisUtils setIntoList. key=%s, object=%s", key, JSON.toJSONString(list)), e);
        }
    }


    /**
     * 批量插入set 不带过期时间
     */
    public void setIntoSet(String key, Object[] objects) {
        this.setIntoSet(key, objects, null, null);
    }


    /**
     * 批量插入set
     */
    public void setIntoSet(String key, Object[] objects, Long expireTime, TimeUnit timeUnit) {
        try {
            redisTemplate.opsForSet().add(key, objects);
            if (expireTime != null) {
                redisTemplate.expire(key, expireTime, timeUnit);
            }
        } catch (Exception e) {
            log.error(String.format("RedisUtils setIntoList. key=%s, object=%s", key, objects), e);
        }
    }


    /**
     * 获取指定key的set
     */
    public Set getFromSet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtils getFromSet. key=%s", key), e);
        }
        return null;
    }

    /**
     * 获取list
     */
    public List getList(String key) {
        try {
            return redisTemplate.opsForList().range(key, 0, -1);
        } catch (Exception e) {
            log.error(String.format("RedisUtils getList. key=%s, object=%s", key, e));
        }
        return null;
    }


    /**
     * 获取hash的value
     */
    public <T> T getHashValue(String key, String field, Class<T> clazz) {

        try {
            Object obj = redisTemplate.opsForHash().get(key, field);
            if (obj != null) {
                return JSON.parseObject(obj.toString(), clazz);
            }
        } catch (Exception e) {
            log.error(String.format("RedisUtils getList. key=%s, object=%s", key, e));
        }
        return null;
    }

    public <T> List<T> getAllHashValue(String key, Class<T> clazz) {
        List<T> relist = Lists.newArrayList();
        try {
            List list = redisTemplate.opsForHash().values(key);
            if (list != null && !list.isEmpty()) {
                for (Object obj : list) {
                    relist.add(JSON.parseObject(obj.toString(), clazz));
                }
            }
        } catch (Exception e) {
            log.error(String.format("RedisUtils getList. key=%s, object=%s", key, e));
        }
        return relist;
    }

    /**
     * 清除指定缓存
     */
    public void evictCacheByKey(String key) {
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtils evictCacheByKey. key=%s", key, e));
        }
    }

}