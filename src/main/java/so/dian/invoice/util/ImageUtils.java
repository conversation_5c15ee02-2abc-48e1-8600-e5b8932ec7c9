package so.dian.invoice.util;

import com.google.common.base.Strings;
import com.google.common.primitives.Ints;
import com.meidalife.common.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;
import so.dian.invoice.enums.LeoExcs;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 * @since 2015-05-03
 */
public class ImageUtils {


    private static Logger logger = LoggerFactory.getLogger(ImageUtils.class);

    public static final String IMAGE_DOMAIN = "lhc-image.oss-cn-beijing.aliyuncs.com/";
    public static final String IMAGE_DOMAIN_WITH_PROTOCOL = "http://" + IMAGE_DOMAIN;

    public static final String IMAGE_DOMAIN_QCLOUD = "img3.dian.so/";
    public static final String IMAGE_DOMAIN_WITH_PROTOCOL_QCLOUD = "http://" + IMAGE_DOMAIN_QCLOUD;

    private static final Pattern imageWidthAndHeightInUrlPattern = Pattern.compile("[^@/]+/(\\d+)w_(\\d+)h_");

    private static String EMPTY_STR = "";

    public static boolean checkSuffixIsLegal(String fileName) {
        return !Strings.isNullOrEmpty(fileName) &&
                ( (fileName = fileName.trim().toLowerCase()).endsWith(".jpg")
                    || fileName.endsWith(".jpeg")
                    || fileName.endsWith(".png")
                    || fileName.endsWith(".gif")
                    || fileName.endsWith(".pdf"));
    }

    public static String getSuffix(String fileName) {
        if (Strings.isNullOrEmpty(fileName) || !fileName.contains(".")) {
            return EMPTY_STR;
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).trim().toLowerCase();
    }

    public static String getSaveFileName(String originalFilename,
                                         String contentType,
                                         String sizeHex) {
        checkNotNull(originalFilename);
        checkNotNull(contentType);
        checkNotNull(sizeHex);
        String fileToken = DigestUtils.md5DigestAsHex((originalFilename + contentType + sizeHex).getBytes())
                .substring(0, 5).toUpperCase();
        String suffix = getSuffix(originalFilename);
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat("/YYYY/MM/dd/");
        return "leo" + dateFormat.format(now)
                + fileToken
                + now.getTime()
                + "." + suffix;
    }

    public static String getSaveFileName(String rootName,
                                         String originalFilename,
                                         String contentType,
                                         String sizeHex,
                                         int width, int height) {
        checkNotNull(originalFilename);
        checkNotNull(contentType);
        checkNotNull(sizeHex);
        String fileToken = DigestUtils.md5DigestAsHex((originalFilename + contentType + sizeHex).getBytes())
                .substring(0, 5).toUpperCase();
        String suffix = getSuffix(originalFilename);
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat("/YYYY/MM/dd/");
        return rootName + dateFormat.format(now) + width + "w_" + height + "h_"
                + fileToken
                + now.getTime()/1000
                + "." + suffix;
    }

    public static String removeDomain(String url) {
        checkNotNull(url);
        if (url.contains(ImageUtils.IMAGE_DOMAIN)) {
            return url.substring(url.indexOf(ImageUtils.IMAGE_DOMAIN) + ImageUtils.IMAGE_DOMAIN.length());
        }
        return url;
    }

    public static String removeSizeSuffix(String url) {
        checkNotNull(url);
        return url.split("@")[0];
    }


    public static int[] getWidthAndHeightFromUrl(String imageUrl) {
        if (!StringUtil.isNotBlank(imageUrl)) {
            return null;
        }
        Matcher matcher = imageWidthAndHeightInUrlPattern.matcher(imageUrl);
        if (matcher.find()) {
            int[] widthAndHeight = new int[2];
            widthAndHeight[0] = Ints.tryParse(matcher.group(1));
            widthAndHeight[1] = Ints.tryParse(matcher.group(2));
            return widthAndHeight;
        }
        return null;
    }

    public static String affixTextWatermark(String picOriginUrl, String text) {//针对手机端的水印文字size
        return picOriginUrl+"?x-oss-process=image/resize,w_2000/watermark,type_ZmFuZ3poZW5nc2h1c29uZw==,size_64,text_"+Base64Util.encode(text)+",color_FFFFFF,t_60,g_west,x_25,y_195,fill_1,rotate_340";
    }


    // 通过图片byte[] 加文字水印后 返回 新byte[]
    public static byte[] markFontXDKJ(byte[] bytes) {
        try {
            ByteArrayInputStream is = new ByteArrayInputStream(bytes);
            // 读取原图片信息
            Image img = ImageIO.read(is);;
            int imgWidth = img.getWidth(null);
            int imgHeight = img.getHeight(null);
            Font font = new Font("宋体", Font.PLAIN, imgWidth/20);
            // 加水印
            BufferedImage bufImg = new BufferedImage(imgWidth, imgHeight, BufferedImage.TYPE_INT_RGB);
            mark(bufImg, img, "小电科技", font, Color.decode("#FFFFFF"));
            // 输出图片

            ByteArrayOutputStream outImgStream = new ByteArrayOutputStream();
            ImageIO.write(bufImg, "jpg", outImgStream);
            return outImgStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }
    }
    private static void mark(BufferedImage bufImg, Image img, String text, Font font, Color color) {
        Graphics2D g = bufImg.createGraphics();
        g.setColor(color);
        g.setFont(font);

        Color markContentColor = Color.white;
        g.setColor(markContentColor);
        g.setBackground(Color.white);

        //画出图片-----------------------------------
        g.drawImage(img, 0, 0, bufImg.getWidth(), bufImg.getHeight(), null);

        // 设置水印旋转
        g.rotate(Math.toRadians(-30),
                (double) bufImg.getWidth() / 2, (double) bufImg
                        .getHeight() / 2);
        g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP,
                0.4f));

        int waterWidth = bufImg.getWidth()*2;
        int waterHeight = bufImg.getHeight()*2;

        for (int height = -waterHeight/15; height < waterHeight; height = height + waterHeight/15) {
            for (int width = -waterWidth/6; width < waterWidth; width = width + waterWidth/6) {
                g.drawString(text, width - waterWidth/6, height - waterHeight/15);
            }
        }
        //添加水印的文字和设置水印文字出现的内容 ----位置
        g.dispose();//画笔结束
    }

    public static InputStream downloadImageToStream(String imageUrl) throws Exception {
        URL url = new URL(imageUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setConnectTimeout(10_000); // 10秒连接超时
        connection.setReadTimeout(30_000);    // 30秒读取超时
        connection.setRequestMethod("GET");
        connection.connect();

        if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("Failed to download image: " + connection.getResponseMessage());
        }

        return connection.getInputStream();
    }

    public static byte[] downloadImageToByte(String imageUrl) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;
        try {
            inputStream = downloadImageToStream(imageUrl);
            outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } finally {
            // 关闭资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    logger.warn("关闭资源失败", e);
                }
            }
            // 关闭资源
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e) {
                    logger.warn("关闭资源失败", e);
                }
            }
        }
        return outputStream.toByteArray();
    }

}
