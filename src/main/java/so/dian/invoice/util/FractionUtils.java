package so.dian.invoice.util;

import org.apache.commons.lang3.math.NumberUtils;
import so.dian.invoice.common.NumberScaleConstant;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Objects;


public class FractionUtils {

    /**
     * 加法 a + b
     * @param a BigDecimal
     * @param b BigDecimal
     * @return a + b
     */
    public static BigDecimal add(BigDecimal a,BigDecimal b) {
        return setScale(a.add(b));
    }

    /**
     * 加法 a + b
     * @param a BigDecimal
     * @param b BigDecimal
     * @return a + b
     */
    public static BigDecimal add(BigDecimal a,BigDecimal b, int scale) {
        return setScale(a.add(b),scale);
    }

    /**
     * 减法 a - b
     * @param a BigDecimal
     * @param b BigDecimal
     * @return a - b
     */
    public static BigDecimal subtract(BigDecimal a,BigDecimal b) {
        return setScale(a.subtract(b));
    }

    /**
     * 乘法 a x b
     * @param a BigDecimal
     * @param b BigDecimal
     * @return 乘法 a x b
     */
    public static BigDecimal multiply(BigDecimal a,BigDecimal b) {
        return setScale(a.multiply(b));
    }

    /**
     * 乘法 a x b
     * @param a BigDecimal
     * @param b BigDecimal
     * @return 乘法 a x b
     */
    public static BigDecimal multiply(BigDecimal a,BigDecimal b, int scale) {
        return setScale(a.multiply(b),scale);
    }

    /**
     * 除法 a / b
     * @param a BigDecimal
     * @param b BigDecimal
     * @return a / b
     */
    public static BigDecimal divide(BigDecimal a,BigDecimal b) {
        return a.divide(b, NumberScaleConstant.TWO_SCALE, NumberScaleConstant.ROUNDING_MODE);
    }

    /**
     * 除法 a / b
     * @param a BigDecimal
     * @param b BigDecimal
     * @return a / b
     */
    public static BigDecimal divide(BigDecimal a,BigDecimal b, int scale) {
        return a.divide(b, scale, NumberScaleConstant.ROUNDING_MODE);
    }

    public static BigDecimal setScale(BigDecimal a) {
        return setScale(a,NumberScaleConstant.getDefaultScale(), NumberScaleConstant.getDefaultRoundingMode());
    }
    public static BigDecimal setScale(BigDecimal a, int scale, int roundingMode) {
        if (Objects.isNull(a) || BigDecimal.ZERO.compareTo(a) == 0) {
            return BigDecimal.ZERO;
        }
        return a.setScale(scale, roundingMode);
    }
    public static BigDecimal setScale(BigDecimal a, int scale) {
        return setScale(a, scale, NumberScaleConstant.getDefaultRoundingMode());
    }

    /**
     * 金额单位转换，将分转成元
     * @param amount 金额，单位分
     * @return 金额，单位元
     */
    public static BigDecimal conventAmountInFen(Long amount) {
        if (Objects.nonNull(amount)) {
            return  FractionUtils.divide(new BigDecimal(amount), new BigDecimal(100));
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 金额单位转换，将分转成元
     * @param amount 金额，单位分
     * @return 金额，单位元
     */
    public static String conventStrAmountInFen(Long amount) {
        BigDecimal bigDecimal = FractionUtils.conventAmountInFen(amount);
        return bigDecimal.toPlainString();
    }

    public static Long conventAmountToFen(BigDecimal amount) {
        return conventAmountToFen(amount, NumberUtils.LONG_ZERO);
    }

    public static Long conventAmountToFen(BigDecimal amount, Long defaultAmount) {
        if (Objects.nonNull(amount)) {
            return  FractionUtils.multiply(amount, new BigDecimal(100)).longValue();
        } else {
            return defaultAmount;
        }
    }

    /**
     * 将金额转换为千分位格式
     *
     * @param amount 金额数值
     * @return 格式化后的字符串
     */
    public static String formatToThousandSeparator(Long amount) {
        BigDecimal bigDecimal = FractionUtils.conventAmountInFen(amount);
        return formatToThousandSeparator(bigDecimal, NumberScaleConstant.TWO_SCALE);
    }

    /**
     * 将金额转换为千分位格式
     *
     * @param amount 金额数值
     * @return 格式化后的字符串
     */
    public static String formatToThousandSeparator(BigDecimal amount) {
        return formatToThousandSeparator(amount, NumberScaleConstant.TWO_SCALE);
    }

    /**
     * 将金额转换为千分位格式（可选保留小数位）
     *
     * @param amount 金额数值
     * @param decimalPlaces 保留的小数位数
     * @return 格式化后的字符串
     */
    public static String formatToThousandSeparator(BigDecimal amount, int decimalPlaces) {
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(decimalPlaces);
        return numberFormat.format(amount);
    }
}