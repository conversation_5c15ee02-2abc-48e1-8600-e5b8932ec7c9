package so.dian.invoice.util;

import so.dian.commons.eden.enums.EnumInterface;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @Author: chenan
 * @Date: 2020/11/10 17:13
 */
public class InvoiceEnumUtils {

    public static <E extends EnumInterface<?>> String[] getEnumDesc(Class<E> enumClass) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        return Stream.of(enumClass.getEnumConstants()).map(EnumInterface::getDesc).toArray(String[]::new);
    }

}
