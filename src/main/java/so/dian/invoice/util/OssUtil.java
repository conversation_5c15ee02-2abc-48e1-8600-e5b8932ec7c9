package so.dian.invoice.util;

import cn.hutool.core.util.IdUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import so.dian.himalaya.boot.constant.CommonConstants;
import so.dian.himalaya.boot.util.EnvUtils;
import so.dian.invoice.constant.OSSConstants;
import so.dian.invoice.constant.SymbolConstants;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2020/8/20 下午9:24
 */
@Slf4j(topic = "biz")
public class OssUtil {

    public static String BASE_URL_PREFIX = "http://" + OSSConstants.BUCKET_NAME + "." + OSSConstants.END_POINT + "/";

    public static long ONE_HOUR_ACL = 3600 * 1000;

    private static OSS ossClient;

    static {
        ossClient = new OSSClient(OSSConstants.END_POINT, OSSConstants.ACCESS_KEY_ID, OSSConstants.ACCESS_KEY_SECRET);
    }

    /**
     * @param file spring mvc 文件上传实体
     */
    public static String uploadMultipartFile(MultipartFile file) throws IOException {
        // 获取文件的mime type，校验文件类型是否合法
        InputStream is = file.getInputStream();
        String suffix = getSuffix(file.getOriginalFilename());
        String mimeType = file.getContentType();
        // 获取唯一的文件名
        String fileName = getUniqueName(suffix);
        return uploadFile(is, fileName, mimeType);
    }

    /**
     * 文件上传
     *
     * @param is       文件输入流
     * @param fileType 文件类型
     */
    public static String uploadFile(InputStream is, String fileType)
            throws IOException {
        String fileName = getUniqueName(fileType);
        String mimeType = getMimeType(fileType);
        return uploadFile(is, fileName, mimeType);
    }

    /**
     * 文件上传
     *
     * @param file     文件
     * @param mimeType content-type
     */
    public static String uploadFile(File file, String mimeType) throws IOException {
        // 获取文件在oss路径
        String ossPath = getOssPath();
        // 获取唯一的文件名
        String fileName = getUniqueName(getSuffix(file.getName()));

        InputStream is = new FileInputStream(file);

        return uploadFile(is, ossPath, fileName, mimeType);
    }

    /**
     * 文件上传
     *
     * @param is       文件输入流
     * @param fileName 文件名称
     * @param mimeType content-type
     */
    public static String uploadFile(InputStream is, String fileName, String mimeType)
            throws IOException {
        String ossPath = getOssPath();
        return uploadFile(is, ossPath, fileName, mimeType);
    }

    /**
     * @param is       文件内容的输入流
     * @param ossPath  oss路径
     * @param fileName 文件名称
     * @param mimeType mime type
     */
    public static String uploadFile(InputStream is, String ossPath, String fileName, String mimeType)
            throws IOException {
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(is.available());
        objectMeta.setContentType(mimeType);
        objectMeta.setContentDisposition("inline; filename=" + fileName);
        String pathFileName = ossPath + fileName;
        ossClient.putObject(OSSConstants.BUCKET_NAME, pathFileName, is, objectMeta);
        return BASE_URL_PREFIX + pathFileName;
    }

    /**
     * 获得acl url
     *
     * @param unAclUrl 上传文件返回的 url
     * @param time     acl 允许时间
     * @return acl url
     */
    public static String getAclUrl(String unAclUrl, long time) {
        if (StringUtils.isEmpty(unAclUrl)) {
            return null;
        }
        // 设置 acl 过期时间
        Date expiration = new Date(System.currentTimeMillis() + time);
        // 获取文件名称
        String objectName = unAclUrl.replaceAll(BASE_URL_PREFIX, SymbolConstants.EMPTY).trim();
        // 获取url的权限
        URL url = ossClient.generatePresignedUrl(OSSConstants.BUCKET_NAME, objectName, expiration);
        return url.toString();
    }

    public static String getOneHourAclUrl(String unAclUrl) {
        return getAclUrl(unAclUrl, ONE_HOUR_ACL);
    }

    /**
     * @param suffix 后缀名称
     * @return 唯一的文件名称
     */
    public static String getUniqueName(String suffix) {
        String uniqueId = IdUtil.objectId();
        return uniqueId + SymbolConstants.PERIOD + suffix;
    }

    /**
     * 生成文件全路径名
     * fragranthills/local/2020/05/15/
     */
    public static String getOssPath() {
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
        return CommonConstants.APPLICATION_NAME
                + SymbolConstants.SLASH
                + EnvUtils.SYSTEM_ENV
                + dateFormat.format(now);
    }

    /**
     * 获取文件后缀
     * 1.pdf=》pdf
     *
     * @param originalFilename 原文件名称
     * @return 文件后缀
     */
    public static String getSuffix(String originalFilename) {
        if (StringUtils.isEmpty(originalFilename) || !originalFilename.contains(SymbolConstants.PERIOD)) {
            return SymbolConstants.EMPTY;
        }
        return originalFilename.substring(originalFilename.lastIndexOf(SymbolConstants.PERIOD) + 1).trim().toLowerCase();
    }

    /**
     * @param fileType 文件后缀
     * @return 文件mime type
     */
    public static String getMimeType(String fileType) {
        switch (fileType) {
            case "xls":
            case "xlsx":
                return "application/vnd.ms-excel";
            case "pdf":
                return "application/pdf";
            case "doc":
            case "docx":
            case "dot":
                return "application/msword";
            case "gif":
                return "image/gif";
            case "png":
                return "image/png";
            default:
                return "image/jpeg";
        }
    }

}