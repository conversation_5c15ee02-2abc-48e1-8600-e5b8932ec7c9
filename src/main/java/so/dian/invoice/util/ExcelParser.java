package so.dian.invoice.util;

import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import so.dian.invoice.pojo.vo.CheckInvoiceExportVO;
import so.dian.invoice.pojo.vo.InvoiceExportInfoVO;
import so.dian.invoice.pojo.vo.NoCheckInvoiceExportVO;

import java.io.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ExcelParser {

    private static final Logger logger = LoggerFactory.getLogger(ExcelParser.class);

    public static List<String> readFileByLines(String fileName){

        File file = new File(fileName);
        BufferedReader reader = null;
        List<String> result = new ArrayList<String>();

        try{

            reader = new BufferedReader(new FileReader(file));
            String line = null;

            while((line = reader.readLine()) != null){
                result.add(line);
            }

            reader.close();

        }catch(IOException e){
            e.printStackTrace();

        }finally{
            if(reader != null){
                try{
                    reader.close();
                }catch(IOException e1){
                }
            }
        }

        return result;
    }

    public static void appendToFile(String fileName, String line){

        try{
            FileWriter writer = new FileWriter(fileName, true);
            writer.write(line);
            writer.close();

        }catch(IOException e){
            e.printStackTrace();
        }
    }

    /*
     * 创建目录
     * */
    public static void createDir(String filePath){

        File f = new File(filePath);
        if(!f.exists()){
            f.mkdirs();
        }
    }
    /*
     * 创建文件
     * */
    public static void createFile(String filePath, String fileName){

        File f = new File(filePath);
        if(!f.exists()){
            f.mkdirs();
        }

        File file = new File(f,fileName);
        if(!file.exists()){
            try {

                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    /**
     *
     * 删除文件
     *
     * */
    public static void deleteFile(String fileName) {

        File file = new File(fileName);
        if (file.isFile() && file.exists()) {
            file.delete();
        }
    }

    public static boolean readExcel(String fileName, int pageNo, int pageSize, List<List<String> > excelData) {

        Workbook workbook = getWorkbook(fileName);

        if(workbook != null){
            return readExcelData(workbook, pageNo, pageSize, excelData);
        }

        return false;
    }

    public static Workbook getWorkbook(String fileName) {

        Workbook workbook = null;
        try{
            if (fileName != null) {
                String fileType = fileName.substring(fileName.lastIndexOf("."), fileName.length()).trim().toLowerCase();
                FileInputStream fileStream = new FileInputStream(new File(fileName));

                if (".xls".equals(fileType)) {
                    workbook = new HSSFWorkbook(fileStream);
                } else if (".xlsx".equals(fileType)) {
                    workbook = new XSSFWorkbook(fileStream);
                }
            }
        }catch(Exception e){
            logger.error(">>>getWorkbook, error {}", e);
        }

        return workbook;
    }

    public static CellStyle getCellStyle(Workbook workbook) {

        CellStyle style = workbook.createCellStyle();
        // 自动换行
        //style.setWrapText(true);
        // 生成一个字体
        Font font = workbook.createFont();
        font.setColor(HSSFColor.RED.index);
        //font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        font.setFontName("宋体");

        // 把字体 应用到当前样式
        style.setFont(font);

        return style;
    }

    public static boolean readExcelData(Workbook workbook, int pageNo, int pageSize, List<List<String>> rowList) {

        int sheetCount = workbook.getNumberOfSheets();
        boolean hasNextPage = false;

        for(int index = 0; index < sheetCount; index++){

            Sheet sheet = workbook.getSheetAt(index);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            int startIndex = firstRowNum + pageNo*pageSize;
            int endIndex = (startIndex + pageSize) > lastRowNum ? lastRowNum + 1 : (startIndex + pageSize);
            if(endIndex < lastRowNum){
                hasNextPage = true;
            }

            for (int r = startIndex; r < endIndex; r++) {
                Row row = sheet.getRow(r);
                if (row != null) {
                    List<String> rowValues = new ArrayList<>();
                    boolean allBlank = parseRow(row, rowValues);
                    if(!allBlank) {
                        rowList.add(rowValues);
                    }

                }
            }
        }

        return hasNextPage;
    }



    public static boolean readExcelData(Workbook workbook, int sheetIndex, int pageNo, int pageSize, List<List<String> > rowList) {

        boolean hasNextPage = false;
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        int startIndex = firstRowNum + pageNo*pageSize;
        int endIndex = (startIndex + pageSize) > lastRowNum ? lastRowNum + 1 : (startIndex + pageSize);
        if(endIndex < lastRowNum){
            hasNextPage = true;
        }

        for (int r = startIndex; r < endIndex; r++) {

            Row row = sheet.getRow(r);
            if (row != null) {
                List<String> rowValues = new ArrayList<>();
                boolean allBlank = parseRow(row, rowValues);
                if(!allBlank) {
                    rowList.add(rowValues);
                }else {
                    hasNextPage = false;
                    logger.error(">>>>>readExcelData all blank break");
                    break;
                }
            }
        }

        return hasNextPage;
    }

    private static boolean parseRow(Row row, List<String> res) {

        boolean allBlank = true;
        if(row != null){
            int startIndex = row.getFirstCellNum();
            int endIndex = row.getLastCellNum();

            for(int index = startIndex; index < endIndex; index++){
                String value = getCellValue(row.getCell(index));
                if(StringUtil.isNotBlank(value)){
                    allBlank = false;
                }
                res.add(value);
            }
        }

        return allBlank;
    }

    private static String getCellValue(Cell cell) {

        Object result = "";
        if (cell != null) {

            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    result = cell.getStringCellValue();
                    break;
                case Cell.CELL_TYPE_NUMERIC:
                    double numeric = cell.getNumericCellValue();
                    BigDecimal dec = new BigDecimal(numeric);
                    result = dec.toPlainString();
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    result = cell.getBooleanCellValue();
                    break;
                case Cell.CELL_TYPE_FORMULA:
                    result = cell.getCellFormula();
                    break;
                case Cell.CELL_TYPE_ERROR:
                    result = cell.getErrorCellValue();
                    break;
                case Cell.CELL_TYPE_BLANK:
                    break;
                default:
                    break;
            }
        }

        String res = result.toString();

        return res == null ? res : res.trim();
    }

    public static void writeHutoolExcel(List<InvoiceExportInfoVO> rows,BigExcelWriter writer) {
        writer.write(rows);
    }

    public static BigExcelWriter getHutoolExcel(String destFilePath) {
        BigExcelWriter writer = ExcelUtil.getBigWriter(destFilePath);
        writer.addHeaderAlias("invoiceId", "发票ID");
        writer.addHeaderAlias("subjectTypeStr", "业务类型");
        writer.addHeaderAlias("invoiceNo", "发票号码");
        writer.addHeaderAlias("invoiceCode", "发票代码");
        writer.addHeaderAlias("details", "发票内容");
        writer.addHeaderAlias("subjectName", "开票公司");
        writer.addHeaderAlias("invoiceType", "发票类型");
        writer.addHeaderAlias("isRealStr", "验真状态");
        writer.addHeaderAlias("rawPrice", "不含税金额");
        writer.addHeaderAlias("taxPrice", "税额");
        writer.addHeaderAlias("taxRate", "税率");
        writer.addHeaderAlias("price", "价税合计");
        writer.addHeaderAlias("gmtCreate", "开票日期");
        writer.addHeaderAlias("creatorName", "录入人名称");
        writer.addHeaderAlias("seller", "发票销售方");
        writer.addHeaderAlias("buyer", "发票购买方");
        writer.addHeaderAlias("validateCodeStr", "验真结果备注");
        /**
         * <AUTHOR>
         * @Since feature_20200410_liangfang_发票导出新增字段和发票质检优化
         */
        writer.addHeaderAlias("status", "核销状态");
        writer.addHeaderAlias("usedAmount", "发票核销金额");
        writer.addHeaderAlias("amount", "单据核销金额");
        writer.addHeaderAlias("deductionCreateTime", "核销时间");
        writer.addHeaderAlias("billNo", "付款单号");
        writer.addHeaderAlias("tradeStatus", "付款状态");
        writer.addHeaderAlias("payTime", "到账时间");
        writer.addHeaderAlias("businessNo", "业务单号");
        writer.addHeaderAlias("invoiceCreateTime", "录入时间");
        writer.addHeaderAlias("processStatus", "流转状态");
        writer.addHeaderAlias("regionCity", "所属大区及城市");
        writer.addHeaderAlias("merchantId", "商户ID");
        writer.addHeaderAlias("merchantName", "商户名称");
        writer.addHeaderAlias("url", "url地址");
        return writer;
    }

    public static void getHutoolExcelNoCheckInvoice(List<NoCheckInvoiceExportVO> rows, String destFilePath) {
        ExcelWriter writer = ExcelUtil.getWriter(destFilePath);
        writer.addHeaderAlias("invoiceId", "发票ID");
        writer.addHeaderAlias("invoiceNo", "发票号码");
        writer.addHeaderAlias("invoiceCode", "发票代码");
        writer.addHeaderAlias("deductStatusStr", "发票状态");
        writer.addHeaderAlias("expressInfoStr", "快递单号");
        writer.addHeaderAlias("invoiceCreateTimeStr", "发票创建时间");
        writer.addHeaderAlias("supplierName", "发票销售方");
        writer.addHeaderAlias("typeStr", "发票类型");
        writer.addHeaderAlias("price", "发票总金额");
        writer.addHeaderAlias("checkerName", "质检人");
        writer.addHeaderAlias("creatorNick", "城市行政");
        writer.addHeaderAlias("region", "大区");
        writer.write(rows);
        writer.close();
    }
    public static void getHutoolExcelCheckInvoice(List<CheckInvoiceExportVO> rows, String destFilePath) {
        ExcelWriter writer = ExcelUtil.getWriter(destFilePath);
        writer.addHeaderAlias("id", "质检ID");
        writer.addHeaderAlias("invoiceId", "发票ID");
        writer.addHeaderAlias("invoiceCreateTimeStr", "发票录入时间");
        writer.addHeaderAlias("invoiceTimeStr", "发票日期");
        writer.addHeaderAlias("createTimeStr", "质检发票创建时间");
        writer.addHeaderAlias("creatorNick", "录入人");
        writer.addHeaderAlias("invoiceCode", "发票代码");
        writer.addHeaderAlias("invoiceNo", "发票号码");
        writer.addHeaderAlias("supplierName", "销售方名称");
        writer.addHeaderAlias("typeStr", "发票类型");
        writer.addHeaderAlias("deductStatusStr", "状态");
        writer.addHeaderAlias("isRealStr", "发票真伪");
        writer.addHeaderAlias("price", "价税合计");
        writer.addHeaderAlias("remainderAmount", "未核销金额");
        writer.addHeaderAlias("batchNo", "批次");
        writer.addHeaderAlias("processStatusStr", "流转状态");
        writer.addHeaderAlias("expressInfoStr", "快递单号");
        writer.addHeaderAlias("billNoList", "业务单号");
        writer.addHeaderAlias("checkResultStr", "质检结论");
        writer.addHeaderAlias("checkerName", "质检人");
        writer.addHeaderAlias("checkTimeStr", "质检时间");
        writer.addHeaderAlias("region", "大区");
        writer.write(rows);
        writer.close();
    }


}
