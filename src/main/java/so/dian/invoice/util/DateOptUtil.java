package so.dian.invoice.util;

import java.util.Date;

public class DateOptUtil {

    public static String addOneDay(String timeStr) {

        if (timeStr != null && (timeStr.length() == 10 || timeStr.length() == 8 || timeStr.length() == 9)) {

            Date endTime = DateUtil.stringToDate(timeStr, "yyyy-MM-dd");

            return DateUtil.dateToString(DateUtil.addOneDay(endTime), "yyyy-MM-dd");
        }

        return timeStr;
    }
}
