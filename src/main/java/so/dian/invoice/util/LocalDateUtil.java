package so.dian.invoice.util;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

public class LocalDateUtil {

   final static DateTimeFormatter yyyyMMddDateTimeFormatter=DateTimeFormatter.ISO_LOCAL_DATE;

    public final static String yyyy_MM_dd = "yyyy-MM-dd";
    public final static String yyyy_slash_MM_slash_dd = "yyyy/MM/dd";

    public static LocalDate parseToDate(String dateStr){
        return LocalDate.parse(dateStr,yyyyMMddDateTimeFormatter);
    }

    public static LocalDate parseToDate(String dateStr,String pattern){
        return LocalDate.parse(dateStr,DateTimeFormatter.ofPattern(pattern));
    }

    public static String localDateToStr(LocalDate localDate){
        if(Objects.isNull(localDate)){
            return null;
        }
        return localDate.format(yyyyMMddDateTimeFormatter);
    }

    public static String localDateToStr(LocalDate localDate,String pattern){
        if(Objects.isNull(localDate)||Objects.isNull(pattern)){
            return null;
        }

        return localDate.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * Date转LocalDate
     * @param date
     */
    public static LocalDate dateToLocalDate(Date date) {
        if(null == date) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * LocalDate转Date
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        if(null == localDate) {
            return null;
        }
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }

}
