package so.dian.invoice.util;

import java.math.BigDecimal;
import java.util.regex.Pattern;

public class DecimalUtil {

    private static final Pattern PATTERN_DOUBLE_POSITIVE = Pattern.compile("^[\\d]+[.]?[\\d]*$");
    private static final Pattern PATTERN_INT_POSITIVE = Pattern.compile("^[\\+]?[\\d]*$");

    /**
     * @param str      要转换的字符串
     * @param newScale 保留小数位数
     * @return
     */
    public static BigDecimal transBigDecimal(String str, int newScale) {
        if (StringUtil.isBlank(str) || !isDouble(str)) {
            return new BigDecimal("0.0");
        }
        //构造以字符串内容为值的BigDecimal类型的变量bd
        BigDecimal bd = new BigDecimal(str);
        bd = bd.setScale(newScale, BigDecimal.ROUND_HALF_UP);
        return bd;
    }

    /**
     * 是否是正整数
     *
     * @param str
     * @return
     */
    public static Integer transIntegerPositive(String str, Integer defaultValue) {
        if (StringUtil.isBlank(str) || !isIntPositive(str)) {
            return null == defaultValue ? new Integer(0) : defaultValue;
        }
        return new Integer(str);
    }

    /**
     * 判断是否为浮点数，包括double和float
     *
     * @param str 传入的字符串
     * @return 是浮点数返回true, 否则返回false
     */
    public static boolean isDouble(String str) {
        return PATTERN_DOUBLE_POSITIVE.matcher(str).matches();
    }

    public static boolean isIntPositive(String str) {
        return PATTERN_INT_POSITIVE.matcher(str).matches();
    }

    public static void main(String[] args) {
//        System.out.println(transIntegerPositive("1",1));
//        System.out.println(transIntegerPositive("1.1",1));
//        System.out.println(transIntegerPositive("5555555",1));
//        System.out.println(transIntegerPositive("-5555555",1));
//        System.out.println(transIntegerPositive("-1",1));
//        System.out.println(transIntegerPositive("0",1));
//        System.out.println(transIntegerPositive("aaa",1));

        System.out.println(transBigDecimal("1.1", 4));
        System.out.println(transBigDecimal("1.1.1", 4));
        System.out.println(transBigDecimal(".1", 4));
        System.out.println(transBigDecimal(".", 4));
        System.out.println(transBigDecimal("1.", 4));
        System.out.println(transBigDecimal("-1.1", 4));
    }

}
