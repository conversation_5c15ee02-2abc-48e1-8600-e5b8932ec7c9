package so.dian.invoice.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meidalife.common.exception.BizException;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.meta.InsertOnly;
import com.qcloud.cos.request.UploadFileRequest;
import com.qcloud.cos.sign.Credentials;
import lombok.extern.slf4j.Slf4j;
import so.dian.invoice.enums.LeoExcs;


/**
 * 腾讯云 存储工具类
 *
 * <AUTHOR>
 * @since 2015-6-6
 */
@Slf4j
public class QCloudHelper {

    private static long appId = 1255721034;

    private static String secretId = "AKIDljlp1EJqUhwQ0dI62yuATmWLLMwalqaL";

    private static String secretKey = "VfwJdFtGA9JUfq2dHvbetGykXKA3xtqo";

    private static String bucketName = "lhc-image";

    // 初始化秘钥信息
    private static Credentials cred = new Credentials(appId, secretId, secretKey);

    private static ClientConfig clientConfig = new ClientConfig();

    static {
        clientConfig.setRegion("sh");
    }

    // 初始化cosClient
    private static COSClient cosClient = new COSClient(clientConfig, cred);

    //https://cloud.tencent.com/document/product/436/6273 查看参数说明
    //上传文件(将内存数据上传到COS)
    public static void uploadFile(String cosPath, byte[] contentBuffer) {
        //cos路径, 必须从bucket下的根/开始，文件路径不能以/结尾, 例如 /mytest/demo.txt
        UploadFileRequest request = new UploadFileRequest(bucketName, cosPath, contentBuffer);
        // 如果COS上已有文件, 则进行覆盖(默认不覆盖)
        request.setInsertOnly(InsertOnly.OVER_WRITE);
        String fileRet = cosClient.uploadFile(request);
        JSONObject jsonObject = JSON.parseObject(fileRet);
        if (jsonObject == null || !jsonObject.get("code").equals(0)) {
            log.error("上传文件--》失败" + fileRet);
            throw new BizException(LeoExcs.OSS_IMAGE_UPLOAD_FAILED);
        }
    }
}
