package so.dian.invoice.util;

import org.apache.commons.lang3.math.NumberUtils;
import so.dian.invoice.common.NumberScaleConstant;

import java.math.BigDecimal;

public class LongUtils {

    /**
     * 安全加法
     */
    public static Long add(Long a, Long b) {
        if (a == null && b == null) return NumberUtils.LONG_ZERO;
        long aValue = a == null ? 0 : a;
        long bValue = b == null ? 0 : b;
        return aValue + bValue;
    }

    /**
     * 安全减法
     */
    public static Long subtract(Long a, Long b) {
        if (a == null && b == null) return NumberUtils.LONG_ZERO;
        long aValue = a == null ? 0 : a;
        long bValue = b == null ? 0 : b;
        return aValue - bValue;
    }

    /**
     * 安全乘法
     */
    public static Long multiply(Long a, Long b) {
        if (a == null || b == null) return NumberUtils.LONG_ZERO;
        return a * b;
    }

    /**
     * 安全除法（保留小数）
     */
    public static Long divide(Long dividend, Long divisor) {
        if (dividend == null || divisor == null || divisor == 0) {
            return NumberUtils.LONG_ZERO;
        }
        BigDecimal d1 = new BigDecimal(dividend);
        BigDecimal d2 = new BigDecimal(divisor);
        return d1.divide(d2, NumberScaleConstant.TWO_SCALE, BigDecimal.ROUND_HALF_UP).longValueExact();
    }

    /**
     * 安全比较：a >= b ?
     */
    public static boolean ge(Long a, Long b) {
        if (a == null || b == null) return false;
        return a >= b;
    }

    /**
     * 安全比较：a > b ?
     */
    public static boolean gt(Long a, Long b) {
        if (a == null || b == null) return false;
        return a > b;
    }

    /**
     * 安全比较：a <= b ?
     */
    public static boolean le(Long a, Long b) {
        if (a == null || b == null) return false;
        return a <= b;
    }

    /**
     * 安全比较：a < b ?
     */
    public static boolean lt(Long a, Long b) {
        if (a == null || b == null) return false;
        return a < b;
    }

    /**
     * 转换为基本类型 long，null 返回 0
     */
    public static long safeValueOf(Long value) {
        return value == null ? 0L : value;
    }

}
