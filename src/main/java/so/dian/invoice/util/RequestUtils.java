package so.dian.invoice.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import so.dian.commons.eden.exception.BizException;
import so.dian.himalaya.boot.util.EnvUtils;
import so.dian.himalaya.boot.util.SpringBeanUtils;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.zuul.common.util.CookieUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

public class RequestUtils {

    /**
     * cookie 中的 userId
     */
    public static final String COOKIE_NAME_EMPLOYEE_ID = "userId";
    /**
     * cookie 中的 角色
     */
    public static final String COOKIE_NAME_CURRENT_ROLE = "current_role";

    public static final String COOKIE_NAME_NICK_NAME = "nickName";


    /**
     * 获取当前线程绑定的HttpServletRequest
     */
    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        return ((ServletRequestAttributes) Objects.requireNonNull(requestAttributes)).getRequest();
    }

    /**
     * 获取当前登录用户
     */
    public static CurrentUserReq getRemoteUser(HttpServletRequest request) {
        if (EnvUtils.isLocal()) {
            return mockCurrentUser();
        }
        AgentEmployeeService hrService = SpringBeanUtils.getBean(AgentEmployeeService.class);
        CurrentUserReq cookieUser = getCookieUser(request);
        return hrService.getRemoteUser(cookieUser);
    }

    /**
     * 获取当前登录用户
     */
    public static CurrentUserReq getRemoteUser() {
        HttpServletRequest request = getRequest();
        return getRemoteUser(request);
    }

    public static CurrentUserReq getCookieUser() {
        HttpServletRequest request = getRequest();
        return getCookieUser(request);
    }

    public static CurrentUserReq getCookieUser(HttpServletRequest request) {
        CurrentUserReq currentUserReq = new CurrentUserReq();
        String employeeIdStr = CookieUtils.getCookieValue(request, COOKIE_NAME_EMPLOYEE_ID);
        if (StringUtils.isEmpty(employeeIdStr)) {
            throw BizException.create(InvoiceCommentErrorCodeEnum.USER_NOT_EXIST);
        }
        String currentRoleStr = CookieUtils.getCookieValue(request, COOKIE_NAME_CURRENT_ROLE);
        String nickNameStr = CookieUtils.getCookieValue(request, COOKIE_NAME_NICK_NAME);
        currentUserReq.setUserId(Integer.valueOf(employeeIdStr));
        currentUserReq.setCurrentRole(currentRoleStr);
        currentUserReq.setNickName(nickNameStr);
        return currentUserReq;
    }

    /**
     * 本地环境的当前用户
     */
    private static CurrentUserReq mockCurrentUser() {
        CurrentUserReq currentUserReq = new CurrentUserReq();
//        currentUserReq.setUserId(22640);
//        currentUserReq.setUserName("辰安");
//        currentUserReq.setEmail("<EMAIL>");
        currentUserReq.setUserId(22466);
        currentUserReq.setNickName("寸长");
        currentUserReq.setUserName("寸长");
//        currentUserReq.setCurrentRole("affairsManager");
        currentUserReq.setCurrentRole("affairsManager");
        currentUserReq.setEmail("<EMAIL>");
        return currentUserReq;
    }


}
