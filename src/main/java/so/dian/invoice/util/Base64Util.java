package so.dian.invoice.util;

import java.util.Base64;

/**
 * Created by <PERSON><PERSON> on 2017/6/20.
 */
public class Base64Util {

    private Base64Util() {
        // private constructor to hide the implicit public one
    }

    public static String encode(String str) {
        if (!StringUtil.isNotBlank(str)) {
           return "";
        }
        return Base64.getEncoder().encodeToString(str.getBytes());
    }

    public static String encodeByOSS(String str) {
        if (!StringUtil.isNotBlank(str)) {
            return "";
        }
        String base64Str = encode(str);
        base64Str = base64Str.replace("+", "-");
        base64Str = base64Str.replace("/", "_");

        return base64Str;
    }
}
