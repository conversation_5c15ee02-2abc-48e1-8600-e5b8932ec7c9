package so.dian.invoice.util;

import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.entity.PageResult;
import so.dian.invoice.enums.LeoExcs;

/**
 * <AUTHOR>
 * @version 1.0.0 $date
 */
public class ServiceResult<T> {

    private boolean success;
    private String msg;
    private T data;

    public void success() {
        this.success = true;
    }

    public static <T> ServiceResult<T> failResult(String msg) {
        return new ServiceResult<T>().fail(msg);
    }

    public static <T> ServiceResult<T> failResult(LeoExcs excs) {
        return new ServiceResult<T>().fail(excs.getDesc());
    }

    public static <T> ServiceResult<T> failResult(BizResult bizResult) {
        return new ServiceResult<T>().fail(bizResult.getMsg());
    }

    public static <T> ServiceResult<T> failResult(PageResult pageResult) {
        return new ServiceResult<T>().fail(pageResult.getMsg());
    }

    public static <T> ServiceResult<T> successResult(T data) {
        return new ServiceResult<T>().success(data);
    }

    public ServiceResult<T> fail(String msg) {
        success = false;
        this.msg = msg;
        return this;
    }

    public ServiceResult<T> success(T data) {
        this.success = true;
        this.data = data;
        return this;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

