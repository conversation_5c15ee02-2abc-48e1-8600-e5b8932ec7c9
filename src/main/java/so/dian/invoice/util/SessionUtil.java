package so.dian.invoice.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import so.dian.center.common.entity.UserSessionDO;
import so.dian.invoice.properties.InvoiceProperties;
import so.dian.zuul.common.enums.CacheEnum;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Random;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2016-10-09
 */
@Component
public class SessionUtil {

    private static final String ATT_SESSION = "leo–session";
    private static final String ACCESS_TOKEN_SALT = "afj98! #$%#2asdf12&*^&#21413as";

    private static final String APP_NAME = "leo";

    @Resource
    private RedissonClient redissonClient;

    public String getNickName(HttpServletRequest request) {
        String nickName = null;
        if (InvoiceProperties.isDaily()) {
            nickName = request.getParameter("nickName");
        }
        if (StringUtils.isNotBlank(nickName)) {
            return nickName;
        }
        UserSessionDO sessionDO = getSessionDO(request);
        return sessionDO == null ? null : sessionDO.getNickName();
    }


    public UserSessionDO getSessionDO(String skey) {

        if (StringUtil.isBlank(skey)) {
            return null;
        }

        String key = generateKey(CacheEnum.LEO_SESSION_NEW.ns, skey);
        RBucket<UserSessionDO> rBucket = redissonClient.getBucket(key);

        return rBucket.get();
    }

    public UserSessionDO getSessionDO(HttpServletRequest request) {

        if (request == null) {
            return null;
        }

        String sid = getSessionId(request);
        String skey = parseSessionKey(sid);

        return getSessionDO(skey);
    }

    public static String generateKey(String ns, String key) {
        return APP_NAME + "$" + InvoiceProperties.getEnv() + "$" + ns + "$" + key;
    }

    public static <T> void putSessionDO(HttpServletRequest request, T t) {
        if (request != null && t != null) {
            request.setAttribute(ATT_SESSION, t);
        }
    }

    public static Integer getUserId(HttpServletRequest request) {
        String userId = request.getParameter("userId");
        if (StringUtils.isNotBlank(userId)) {
            return Integer.valueOf(userId);
        }
        return null;
    }

    public static String getSessionId(HttpServletRequest request) {
        String dsid = request.getHeader("X-LEO-Token");
        if (StringUtil.isNotBlank(dsid)) {
            return addEqualValues(dsid);
        }
        Cookie[] cookies = request.getCookies();
        if (cookies == null || cookies.length == 0) {
            return null;
        }

        for (Cookie cookie : cookies) {
            if (!"dsid".equals(cookie.getName())) {
                continue;
            }
            try {
                dsid = URLDecoder.decode(cookie.getValue(), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            if (StringUtil.isBlank(dsid)) {
                return null;
            }

            return addEqualValues(dsid);
        }
        return dsid;
    }

    public static String addEqualValues(String dsid) {
        switch (dsid.length() % 4) {
            case 3:
                dsid += "=";
                break;
            case 2:
                dsid += "==";
                break;
            case 1:
                dsid += "===";
                break;
            default:
                break;
        }
        return dsid;
    }

    public static String generateSessionId(Integer userId, String accessToken) {
        String tokenAndUserId = generateSessionKey(userId, accessToken);
        return StringUtils.isBlank(tokenAndUserId) ? null : getBase64(tokenAndUserId);
    }

    public static String generateToken(String accessToken) {
        return StringUtils.isBlank(accessToken) ? null : DigestUtils.sha1Hex(accessToken + ACCESS_TOKEN_SALT).toUpperCase();

    }

    /**
     * 生成sessionKey，即用于缓存中的key
     *
     * @param userId
     * @param accessToken
     * @return
     */
    public static String generateSessionKey(Integer userId, String accessToken) {
        return userId == null || StringUtils.isBlank(accessToken) ? null : generateToken(accessToken) + userId;
    }

    /**
     * 从sessionId中解析出sessionKey
     *
     * @param sid
     * @return
     */
    public static String parseSessionKey(String sid) {
        return getFromBase64(sid);
    }

    /**
     * 从sessionId中解析出userId
     *
     * @param sid
     * @return
     */
    public static Integer parseUserIdFromSessionId(String sid) {
        String tokenAndUserId = getFromBase64(sid);
        if (tokenAndUserId == null) {
            return null;
        }

        if (tokenAndUserId.length() < 41) {
            return null;
        }

        String userId = tokenAndUserId.substring(40, tokenAndUserId.length());
        return Integer.parseInt(userId);
    }

    public static String parseTokenFromSessionId(String sid) {
        String tokenAndUserId = getFromBase64(sid);
        if (tokenAndUserId == null || tokenAndUserId.length() < 41) {
            return null;
        }
        return tokenAndUserId.substring(0, 40);
    }


    public static String getBase64(String str) {
        String s = null;
        try {
            byte[] b = str.getBytes(StandardCharsets.UTF_8);
            if (b != null) {
                s = Base64.getEncoder().encodeToString(b);
            }
        } catch (Exception e) {
            // do nothing
        }
        return s;
    }

    /**
     * Base64解密
     *
     * @param str
     * @return
     */
    public static String getFromBase64(String str) {
        String result = null;
        try {
            if (str != null) {
                byte[] b = Base64.getDecoder().decode(str);
                result = new String(b, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            // do nothing
        }
        return result;
    }

    private static final Random rand = new Random();

    public static String randomAccessToken() {
        long tmpCode = rand.nextInt(899999) + 100000;
        return String.valueOf(tmpCode);
    }

}
