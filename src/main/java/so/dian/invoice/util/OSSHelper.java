package so.dian.invoice.util;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.ObjectMetadata;
import org.springframework.beans.factory.annotation.Value;

import java.io.InputStream;

/**
 * OSS 访问工具类
 *
 * <AUTHOR>
 * @since 2015-6-6
 */
public class OSSHelper {

    private static final String ACCESS_ID = "LTAIUjY98NgKNiF9";
    private static final String ACCESS_KEY = "C7bkHrkXTvK2hIMqEZHVUUFqMf6RLC";
    private static final String BUCKET_NAME = "lhc-image";
    @Value("${invoice.oss.end-point}")
    private static String END_POINT;

    private static final OSSClient ossClient = new OSSClient(END_POINT, ACCESS_ID, ACCESS_KEY);

    public static void uploadFile(String saveFileName, InputStream input,
                                  String contentType, long contentLength)
            throws OSSException, ClientException {

        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(contentLength);
        objectMeta.setContentType(contentType);
        ossClient.putObject(BUCKET_NAME, saveFileName, input, objectMeta);
    }

    public static void uploadFile(String saveFileName, InputStream input, long contentLength, String fileType)
            throws OSSException, ClientException {

        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(contentLength);
        if(fileType.equals("xls") || fileType.equals("xlsx")) {
            objectMeta.setContentType("application/vnd.ms-excel");
        }else {
            objectMeta.setContentType("application/pdf");
        }
        String [] substr = saveFileName.split("/");
        objectMeta.setContentDisposition("inline; filename=" + substr[substr.length - 1]);
        ossClient.putObject(BUCKET_NAME, saveFileName, input, objectMeta);
    }
    public static void uploadFile(String saveFileName, InputStream input, long contentLength)
            throws OSSException, ClientException {

        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(contentLength);
        objectMeta.setContentType("application/pdf");
        String[] substr = saveFileName.split("/");
        objectMeta.setContentDisposition("inline; filename=" + substr[substr.length - 1]);
        ossClient.putObject(BUCKET_NAME, saveFileName, input, objectMeta);
    }
}
