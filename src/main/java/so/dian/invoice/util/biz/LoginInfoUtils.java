package so.dian.invoice.util.biz;

import java.util.stream.Stream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import so.dian.invoice.constant.RegexConstant;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;

/**
 * LoginInfoUtils
 *
 * <AUTHOR>
 * @desc 特为小电实现的登录信息获取接口
 * @date 2019-09-08
 */
public class LoginInfoUtils {

    private static final String COOKIE_NAME_EMPLOYEE_ID = "userId";
    private static final String COOKIE_NAME_EMPLOYEE_ROLE = "current_role";

    public static Long getEmployeeId(HttpServletRequest request) {
        String employeeStr = getCookieValue(request, COOKIE_NAME_EMPLOYEE_ID);
        if (StringUtils.isBlank(employeeStr) || !RegexConstant.NUMBER_PATTERN.matcher(employeeStr).matches()) {
            return null;
        }
        return Long.valueOf(employeeStr);
    }

    public static BaseEmpRoleReq getEmployeeRole(HttpServletRequest request) {
        if (ArrayUtils.isEmpty(request.getCookies())) {
            return null;
        }
        BaseEmpRoleReq req = new BaseEmpRoleReq();
        Stream.of(request.getCookies()).forEach(cookie -> {
            if(COOKIE_NAME_EMPLOYEE_ID.equals(cookie.getName())) {
                req.setEmployeeId(Long.valueOf(cookie.getValue()));
            } else if(COOKIE_NAME_EMPLOYEE_ROLE.equals(cookie.getName())) {
                req.setRole(cookie.getValue());
            }
        });
        return req;
    }

    public static String getCookieValue(HttpServletRequest request, String cookieName) {
        if (ArrayUtils.isEmpty(request.getCookies()) || StringUtils.isBlank(cookieName)) {
            return null;
        }
        return Stream.of(request.getCookies()).filter(cookie -> cookieName.equals(cookie.getName())).findFirst()
                .map(Cookie::getValue).orElse(null);
    }
}