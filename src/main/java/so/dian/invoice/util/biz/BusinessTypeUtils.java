package so.dian.invoice.util.biz;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.yandang.client.pojo.enums.BizTypeEnum;

public class BusinessTypeUtils {
    /**
     * 分成发票类型
     */
    public static final List<Integer> WITHDRAW_FEE_TYPE =
          Lists.newArrayList(BusinessTypeEnum.DEVIDE.getType(), BusinessTypeEnum.CHANNEL.getType());
    /**
     * 进场费发票类型
     */
    public static final List<Integer> ENTRANCE_FEE_TYPE =
          Lists.newArrayList(BusinessTypeEnum.ENTRANCE_FEE.getType(),
                BusinessTypeEnum.NORMAL_ENTRY_FEE.getType(),
                BusinessTypeEnum.BRAND_ENTRY_FEE.getType(),
                BusinessTypeEnum.JOIN_ENTRY_FEE.getType(),
                BusinessTypeEnum.RESOURCE_ENTRY_FEE.getType(),
                BusinessTypeEnum.KP_SERVICE_ENTRY_FEE.getType());
    /**
     * 进场费其他费用类型
     */
    public static final List<Integer> ENTRANCE_OTHER_FEE_TYPE = Lists
            .newArrayList(BusinessTypeEnum.NORMAL_ENTRANCE_OTHER_FEE.getType(),
                    BusinessTypeEnum.BRAND_ENTRANCE_OTHER_FEE.getType(),
                    BusinessTypeEnum.JOIN_ENTRANCE_OTHER_FEE.getType(),
                    BusinessTypeEnum.RESOURCE_ENTRANCE_OTHER_FEE.getType(),
                    BusinessTypeEnum.KP_ENTRANCE_OTHER_FEE.getType());
    /**
     * 供应链发票类型
     */
    public static final List<Integer> SCM_TYPE = Lists.newArrayList(BusinessTypeEnum.EQUIPMENT_PROCUREMENT.getType());



    public static BizTypeEnum toPayBillType(Integer businessType) {
        if (Objects.isNull(businessType)) {
            return null;
        }
        BizTypeEnum outBizStyleEnum = null;
        if (WITHDRAW_FEE_TYPE.contains(businessType)) {
            outBizStyleEnum = BizTypeEnum.MERCHANT_WITHDRAW;
        } else if (ENTRANCE_FEE_TYPE.contains(businessType)) {
            outBizStyleEnum = BizTypeEnum.ENTRANCE_FEE;
        } else if (ENTRANCE_OTHER_FEE_TYPE.contains(businessType)) {
            outBizStyleEnum = BizTypeEnum.ENTRANCE_OTHER;
        }
        return outBizStyleEnum;
    }
}
