package so.dian.invoice.util.biz;

import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.exception.BizException;

/**
 * AssertUtils
 *
 * <AUTHOR>
 * @date 2017/11/4
 */
public class AssertUtils {

    /**
     * 断言，若对象为null，则抛出业务异常code
     */
    public static void notNullWithBizExp(Object obj, EnumInterface codeEnum) {
        if (Objects.isNull(obj)) {
            throw BizException.create(codeEnum);
        }
    }

    /**
     * 断言，若对象为不为null，则抛出业务异常code
     */
    public static void isNotNullWithBizExp(Object obj, EnumInterface codeEnum) {
        if (Objects.nonNull(obj)) {
            throw BizException.create(codeEnum);
        }
    }

    /**
     * 断言，若对象为null，则抛出业务异常code
     */
    public static void notNullWithBizExp(Object obj, EnumInterface codeEnum, @NonNull String msg) {
        if (Objects.isNull(obj)) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }

    /**
     * 断言，若对象为null或对象值为空，则抛出业务异常code
     */
    public static void notEmptyWithBizExp(Object obj, EnumInterface codeEnum) {
        if (ObjectUtils.isEmpty(obj)) {
            throw BizException.create(codeEnum);
        }
    }

    /**
     * 断言，若对象为null或对象值为空，则抛出业务异常code
     */
    public static void notEmptyWithBizExp(Object obj, EnumInterface codeEnum, @NonNull String msg) {
        if (ObjectUtils.isEmpty(obj)) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }

    /**
     * 断言，若全为空，则抛出业务异常code
     */
    public static void notAllEmptyWithBizExp(Object obj1, Object obj2, EnumInterface codeEnum, @NonNull String msg) {
        if (ObjectUtils.isEmpty(obj1) && ObjectUtils.isEmpty(obj2)) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }

    /**
     * 断言，若字符串为null或字符串为空白，则抛出业务异常code
     */
    public static void notBlankWithBizExp(CharSequence str, EnumInterface codeEnum) {
        if (StringUtils.isBlank(str)) {
            throw BizException.create(codeEnum);
        }
    }

    /**
     * 断言，若字符串为null或字符串为空白，则抛出业务异常code
     */
    public static void notBlankWithBizExp(CharSequence str, EnumInterface codeEnum, @NonNull String msg) {
        if (StringUtils.isBlank(str)) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }

    /**
     * 断言，若为假，则抛出业务异常code
     */
    public static void notTrueWithBizExp(boolean bool, EnumInterface codeEnum) {
        if (!bool) {
            throw BizException.create(codeEnum);
        }
    }

    /**
     * 断言，若为假，则抛出业务异常code
     */
    public static void notTrueWithBizExp(boolean bool, EnumInterface codeEnum, @NonNull String msg) {
        if (!bool) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }

    public static void notEqualsWithBizExp(Object objA, Object objB,EnumInterface codeEnum) {
        if (!Objects.equals(objA, objB)) {
            throw BizException.create(codeEnum);
        }
    }

    public static void notEqualsWithBizExp(Object objA, Object objB,EnumInterface codeEnum, @NonNull String msg) {
        if (!Objects.equals(objA, objB)) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }

    public static void equalsWithBizExp(Object objA, Object objB,EnumInterface codeEnum) {
        if (Objects.equals(objA, objB)) {
            throw BizException.create(codeEnum);
        }
    }

    public static void equalsWithBizExp(Object objA, Object objB,EnumInterface codeEnum, @NonNull String msg) {
        if (Objects.equals(objA, objB)) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }

    public static <T extends Object> void notContainsWithBizExp(List<T> objList, T obj,EnumInterface codeEnum) {
        if (!objList.contains(obj)) {
            throw BizException.create(codeEnum);
        }
    }

    public static <T extends Object> void containsWithBizExp(List<T> objList, T obj,EnumInterface codeEnum) {
        if (objList.contains(obj)) {
            throw BizException.create(codeEnum);
        }
    }

    /**
     * 集合为空抛出异常
     * @param objList
     * @param codeEnum
     * @param <T>
     */
    public static <T extends Object> void collectionIsEmptyWithBizExp(List<T> objList, EnumInterface codeEnum) {
        if (CollectionUtils.isEmpty(objList)) {
            throw BizException.create(codeEnum);
        }
    }

    /**
     * 集合不为空抛出异常
     * @param objList
     * @param codeEnum
     * @param <T>
     */
    public static <T extends Object> void collectionIsNotEmptyWithBizExp(List<T> objList, EnumInterface codeEnum) {
        if (!CollectionUtils.isEmpty(objList)) {
            throw BizException.create(codeEnum);
        }
    }

    public static <T extends Object> void collectionIsNotEmptyWithBizExp(List<T> objList, EnumInterface codeEnum, @NonNull String msg) {
        if (!CollectionUtils.isEmpty(objList)) {
            throw BizException.create(codeEnum, codeEnum.getDesc() + "[" + msg + "]");
        }
    }
}
