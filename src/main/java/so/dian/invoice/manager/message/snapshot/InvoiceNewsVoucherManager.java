package so.dian.invoice.manager.message.snapshot;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import so.dian.invoice.dao.InvoiceNewsVoucherDAO;
import so.dian.invoice.enums.NewsVoucherTypeEnum;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;
import so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceNewsVoucherParam;
import so.dian.invoice.service.message.contain.MessageHandleContain;
import so.dian.invoice.service.message.context.MessageContext;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-18 17:10
 */
@Slf4j
@Component
public class InvoiceNewsVoucherManager {

    @Resource
    private InvoiceNewsVoucherDAO invoiceNewsVoucherDAO;

    @Resource
    private MessageHandleContain messageHandleContain;

    public void save(InvoiceNewsVoucherDTO voucherDTO){
	InvoiceNewsVoucherDO voucherDO = voucherDTO.toDO();
	voucherDO.init();
	invoiceNewsVoucherDAO.insert(voucherDO);
	voucherDTO.setId(voucherDO.getId());
    }

    public int update(InvoiceNewsVoucherDTO voucherDTO){
	InvoiceNewsVoucherDO voucherDO = voucherDTO.toDO();
	voucherDO.init();
	return invoiceNewsVoucherDAO.updateByPrimaryKey(voucherDO);
    }

    public InvoiceNewsVoucherDTO getById(Long id){
	InvoiceNewsVoucherDO voucherDO = invoiceNewsVoucherDAO.selectByPrimaryKey(id);
	if (Objects.isNull(voucherDO)) {
	    return null;
	}
	return InvoiceNewsVoucherDTO.fromDO(voucherDO);
    }

    public InvoiceNewsVoucherDTO getByBizNoAndBizType(String outBizId, Integer outBizType){
	InvoiceNewsVoucherDO voucherDO = invoiceNewsVoucherDAO.getByBizNoAndBizType(outBizId, outBizType);
	if (Objects.isNull(voucherDO)) {
	    return null;
	}
	return InvoiceNewsVoucherDTO.fromDO(voucherDO);
    }

    /**
     * 重试次数
     * @param param
     * @return
     */
    public List<InvoiceNewsVoucherDTO> list(InvoiceNewsVoucherParam param){
	List<InvoiceNewsVoucherDO> voucherDOS = invoiceNewsVoucherDAO.list(param);
	if (CollectionUtils.isEmpty(voucherDOS)) {
	    return null;
	}
	List<InvoiceNewsVoucherDTO> voucherDTOS = voucherDOS.stream().map(InvoiceNewsVoucherDTO::fromDO).collect(Collectors.toList());
	return voucherDTOS;
    }

    public int retry(List<InvoiceNewsVoucherDTO> voucherDTOS){
	for (InvoiceNewsVoucherDTO voucherDTO : voucherDTOS){
	    retry(voucherDTO);
	}
	return NumberUtils.INTEGER_ONE;
    }

    public int retry(InvoiceNewsVoucherDTO voucherDTO){
	try {
	    log.info("开始重试处理消息, newsId:{}, OutBizId:{}, outBizType:{}", voucherDTO.getId(), voucherDTO.getOutBizId(), voucherDTO.getOutBizType());
	    String eventVoucher = voucherDTO.getEventVoucher();
	    Integer integer = voucherDTO.getOutBizType();
	    NewsVoucherTypeEnum newsVoucherTypeEnum = NewsVoucherTypeEnum.getByCode(integer);
	    if (Objects.isNull(newsVoucherTypeEnum)) {
		log.warn("未找到对应的消息类型,newsId:{}, OutBizId:{}, outBizType:{}", voucherDTO.getId(), voucherDTO.getOutBizId(), voucherDTO.getOutBizType());
		return 0;
	    }
	    Object object = JSON.parseObject(eventVoucher, newsVoucherTypeEnum.getClazz());

	    MessageContext messageContext = new MessageContext();
	    messageContext.setMessage(object);
	    messageContext.setNewsID(voucherDTO.getId());
	    messageContext.setOutBizType(voucherDTO.getOutBizType());
	    messageContext.setOutBizId(voucherDTO.getOutBizId());
	    messageHandleContain.execute(messageContext);

	    return NumberUtils.INTEGER_ONE;
	}catch (Exception e){
	    log.warn("重试消息处理失败, newsId:{}, OutBizId:{}, outBizType:{}",voucherDTO.getId(), voucherDTO.getOutBizId(), voucherDTO.getOutBizType());
	    log.warn("重试消息处理失败",e);
	}finally {
	    //增加次数
	    InvoiceNewsVoucherDO newsVoucherDO  = invoiceNewsVoucherDAO.selectByPrimaryKey(voucherDTO.getId());
	    log.info("开始重试处理结束，更新消息重试次数, newsId:{}, OutBizId:{}, outBizType:{}",voucherDTO.getId(), voucherDTO.getOutBizId(), voucherDTO.getOutBizType());
	    if (Objects.nonNull(newsVoucherDO)) {
		newsVoucherDO.setRetryTime(newsVoucherDO.getRetryTime() + 1);
		newsVoucherDO.setGmtUpdate(System.currentTimeMillis());
		invoiceNewsVoucherDAO.updateByPrimaryKey(newsVoucherDO);
	    }
	}
	return NumberUtils.INTEGER_ZERO;
    }

}
