package so.dian.invoice.manager;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.invoice.dao.InvoiceValidateStatusDAO;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.param.InvoiceValidateStatusQueryParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class InvoiceValidateStatusManager {

    @Autowired
    private InvoiceValidateStatusDAO invoiceValidateStatusDAO;

    /**
     * 批量新增
     * @param invoiceValidateStatusDOList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsert(List<InvoiceValidateStatusDO> invoiceValidateStatusDOList) {
        if (CollectionUtils.isEmpty(invoiceValidateStatusDOList)) {
            return true;
        }
        return invoiceValidateStatusDAO.batchInsert(invoiceValidateStatusDOList) > 0;
    }

    public List<Long> listByParam(InvoiceValidateStatusQueryParam param) {
        List<Long> invoiceIds = invoiceValidateStatusDAO.listByParam(param);
        return invoiceIds;
    }

    public Boolean insert(InvoiceValidateStatusDO invoiceValidateStatusDO){
        return invoiceValidateStatusDAO.insert(invoiceValidateStatusDO) > 0;
    }

    /**
     * 获取分组记录中的最新记录
     * @return
     */
    public List<InvoiceValidateStatusDO> getAllValidateInvoice() {
        return invoiceValidateStatusDAO.getAllValidateInvoice();
    }
}
