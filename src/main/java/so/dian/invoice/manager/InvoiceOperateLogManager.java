package so.dian.invoice.manager;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.center.common.entity.BizResult;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.client.HrClient;
import so.dian.invoice.converter.InvoiceOperateLogConverter;
import so.dian.invoice.dao.InvoiceOperateLogDAO;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.entity.InvoiceOperateLogDO;

/**
 * @Author: jiaoge
 * @Date: 2019/9/12 11:42 AM
 * @Description:
 */
@Slf4j
@Component
public class InvoiceOperateLogManager {

    @Resource
    private InvoiceOperateLogDAO invoiceOperateLogDAO;
    @Resource
    private HrClient hrClient;

    public boolean insert(InvoiceOperateLogBO operateLogBO) {
        if (Objects.isNull(operateLogBO)) {
            log.error("添加发票操作日志失败，参数不能为空");
            return false;
        }
        InvoiceOperateLogDO logDO = InvoiceOperateLogConverter.buildBO2DO(operateLogBO);
        return invoiceOperateLogDAO.insert(logDO) > 0;
    }

    /**
     * 小电用户
     */
    public boolean insertByLogin(InvoiceOperateLogBO operateLogBO) {
        if (Objects.isNull(operateLogBO)) {
            log.error("添加发票操作日志失败，参数不能为空");
            return false;
        }
        if (StringUtils.isBlank(operateLogBO.getOperatorName())) {
            //查询用户名称
            BizResult<AgentEmployeeDTO> employeeRes
                  = hrClient.getById(Math.toIntExact(operateLogBO.getOperatorId()));
            if (employeeRes == null || !employeeRes.isSuccess() || employeeRes.getData() == null) {
                operateLogBO.setOperatorName("");
            } else {
                operateLogBO.setOperatorName(
                      employeeRes.getData().getNickName() == null ? "" : employeeRes.getData().getNickName());
            }
        }
        return insert(operateLogBO);
    }

    /**
     * 批量发票新增日志
     */
    public boolean insertBatch(List<InvoiceOperateLogBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("添加发票操作日志失败，参数不能为空");
            return false;
        }
        return invoiceOperateLogDAO.insertBatch(InvoiceOperateLogConverter.buildBOList2DOList(list)) > 0;
    }

    /**
     * 根据发票ID查询发票日志
     */
    public List<InvoiceOperateLogBO> findByInvoiceId(Long invoiceId) {
        if (Objects.isNull(invoiceId)) {
            return Lists.newArrayList();
        }
        List<InvoiceOperateLogDO> list = invoiceOperateLogDAO.findByInvoiceId(invoiceId);
        return InvoiceOperateLogConverter.toBOList(list);
    }
}
