package so.dian.invoice.manager;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import so.dian.agent.api.constant.AgentBaseEnum;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.agent.api.dto.PageDTO;
import so.dian.agent.api.dto.param.AgentQueryParamDTO;
import so.dian.agent.api.dto.request.QueryAgentConstant;
import so.dian.agent.api.dto.request.QueryAgentListRequest;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.client.AgentClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j(topic = "remote")
@Service
public class AgentManager {

    @Resource
    private AgentClient agentClient;



    /**
     * 合资和代理商 公司
     *
     * @return
     */
    public List<AgentDTO> findAgentList() {
        AgentQueryParamDTO paramDTO = new AgentQueryParamDTO();
        ArrayList<Integer> types = Lists.newArrayList();
        types.add(AgentBaseEnum.AgentTypeEnum.JV_COMPANY_TYPE.getId()); //合资公司
        types.add(AgentBaseEnum.AgentTypeEnum.AGENT_TYPE.getId()); //代理商
        ArrayList<Integer> subTypes = Lists.newArrayList();
        paramDTO.setTypeList(types);
        paramDTO.setSubTypeList(subTypes);
        paramDTO.setPageNo(0);
        paramDTO.setPageSize(10000);
        return findAgentListByType(paramDTO);
    }

    /**
     * 获取代理商信息
     */
    public List<AgentDTO> findAgentListByType(AgentQueryParamDTO paramDTO) {
        if (paramDTO == null) {
            log.error("远程获取agent信息 | paramDTO为空 | paramDTO:{}", paramDTO);
        }

        BizResult<PageDTO<AgentDTO>> bizResult = agentClient.listChannelByType(paramDTO);

        if (Objects.isNull(bizResult)) {
            log.error("远程获取agent信息 | fallback | paramDTO:{}", paramDTO);
            return Lists.newArrayList();
        }
        if (!bizResult.isSuccess()) {
            log.error("远程获取agent信息 | 接口返回错误信息 | paramDTO:{},result:{}", paramDTO, bizResult);
            return Lists.newArrayList();
        }
        PageDTO<AgentDTO> resultData = bizResult.getData();
        if (Objects.isNull(resultData)) {
            log.error("远程获取agent信息 | agent信息不存在 | paramDTO:{},result:{}", paramDTO, bizResult);
            return Lists.newArrayList();
        }
        return resultData.getList();
    }

    /**
     * 获取代理商信息
     */
    public AgentDTO findById(Integer agentId) {
        if (Objects.isNull(agentId)) {
            log.error("远程获取代理商信息 | 代理商ID不能为 | agentId:{}", agentId);
            return null;
        }
        BizResult<AgentDTO> bizResult = agentClient.getAgentById(agentId);
        if (Objects.isNull(bizResult)) {
            log.error("远程获取代理商信息 | fallback | agentId:{}", agentId);
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("远程获取代理商信息 | 接口返回错误信息 | agentId:{},result:{}", agentId, bizResult);
            return null;
        }
        AgentDTO agentDTO = bizResult.getData();
        if (Objects.isNull(agentDTO)) {
            log.error("远程获取代理商信息 | 用户不存在 | agentId:{},result:{}", agentId, bizResult);
            return null;
        }
        return agentDTO;
    }
    
    /**
     * 获取代理商信息
     */
    public List<AgentDTO> listByIds(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            log.error("远程获取代理商信息 | 代理商ID不能为 | agentIds:{}", agentIds);
            return new ArrayList<>();
        }
        List<Long> distinctList = agentIds.stream().distinct().collect(Collectors.toList());
        QueryAgentListRequest request = new QueryAgentListRequest();
        QueryAgentConstant queryAgentConstant = new QueryAgentConstant();
        request.setQueryAgentConstant(queryAgentConstant);
        request.setAgentIds(distinctList);
        
        BizResult<List<AgentDTO>> bizResult = agentClient.getAgentByIdsAndOptions(request);
        if (Objects.isNull(bizResult)) {
            log.error("远程获取代理商信息 | fallback | agentIds:{}", agentIds);
            return new ArrayList<>();
        }
        if (!bizResult.isSuccess()) {
            log.error("远程获取代理商信息 | 接口返回错误信息 | agentIds:{},result:{}", agentIds, bizResult);
            return new ArrayList<>();
        }
        List<AgentDTO> agentDTOS = bizResult.getData();
        if (CollectionUtils.isEmpty(agentDTOS)) {
            log.error("远程获取代理商信息 | 用户不存在 | agentIds:{},result:{}", agentIds, bizResult);
            return new ArrayList<>();
        }
        return agentDTOS;
    }

}
