package so.dian.invoice.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.himalaya.util.LocalObjectUtils;
import so.dian.invoice.dao.SupplierInvoiceOperateLogMapper;
import so.dian.invoice.enums.SupplierInvoiceOperateEnum;
import so.dian.invoice.pojo.entity.SupplierInvoiceOperateLogDO;
import so.dian.invoice.pojo.request.CurrentUserReq;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class SupplierInvoiceOperateLogManager {

    @Autowired
    private SupplierInvoiceOperateLogMapper supplierInvoiceOperateLogMapper;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insert(SupplierInvoiceOperateLogDO supplierInvoiceOperateLogDO) {
        if (Objects.isNull(supplierInvoiceOperateLogDO)) {
            return false;
        }
        supplierInvoiceOperateLogDO.init();
        int row = supplierInvoiceOperateLogMapper.insert(supplierInvoiceOperateLogDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateById(SupplierInvoiceOperateLogDO supplierInvoiceOperateLogDO) {
        if (Objects.isNull(supplierInvoiceOperateLogDO)) {
            return false;
        }
        if (Objects.isNull(supplierInvoiceOperateLogDO.getGmtUpdate())) {
            supplierInvoiceOperateLogDO.setGmtUpdate(System.currentTimeMillis());
        }
        int row = supplierInvoiceOperateLogMapper.updateById(supplierInvoiceOperateLogDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insert(SupplierInvoiceOperateEnum operateEnum, Integer supplierInvoiceId, String content, CurrentUserReq userReq) {
        if(LocalObjectUtils.anyNull(operateEnum,supplierInvoiceId,content,userReq)){
            return false;
        }
        SupplierInvoiceOperateLogDO operateLogDO = new SupplierInvoiceOperateLogDO();
        operateLogDO.setOperateType(operateEnum.getCode());
        operateLogDO.setSupplierInvoiceId(supplierInvoiceId);
        operateLogDO.setContent(content);
        operateLogDO.setOperatorName(userReq.getUserName());
        operateLogDO.setOperatorId(userReq.getUserId());
        operateLogDO.setOperateTime(LocalDateTime.now());

        return this.insert(operateLogDO);
    }
}
