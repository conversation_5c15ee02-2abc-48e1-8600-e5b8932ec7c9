package so.dian.invoice.manager;

import com.alibaba.fastjson.JSON;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import so.dian.invoice.enums.CacheEnum;
import so.dian.invoice.pojo.bo.InvoiceIdentifyRecordBO;
import so.dian.invoice.pojo.dto.identify.InvoiceIdentifyRecordDTO;
import so.dian.invoice.util.biz.AssertUtils;

import static so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum.*;
import static so.dian.invoice.enums.error.InvoiceErrorCodeEnum.*;

/**
 * @Author: jiaoge
 * @Date: 2019/12/27 2:41 PM
 * @Description: 发票小二端识别缓存封装
 */
@Slf4j(topic = "identify")
@Service
public class CheckInvoiceCacheManager {

	@Resource
	private RedissonClient redissonClient;

	/**
	 * 小二端发票识别信息预设置
	 * @param employeeId 员工ID
	 * @param recordDTO 识别记录
	 * @return newCache
	 */
	public InvoiceIdentifyRecordBO initInvoiceIdentifyPreset(Long employeeId, InvoiceIdentifyRecordDTO recordDTO) {
		RMapCache<Long, InvoiceIdentifyRecordBO> cacheMap
				= redissonClient.getMapCache(CacheEnum.INVOICE_IDENTIFY.getKey());
		InvoiceIdentifyRecordBO identifyRecordBO = cacheMap.get(employeeId);
		if (Objects.nonNull(identifyRecordBO)) {
			log.warn("用户有缓存中的识别记录, identifyRecordBO={}", JSON.toJSONString(identifyRecordBO));
			cacheMap.remove(employeeId);
		}
		InvoiceIdentifyRecordBO newCache = initInvoiceIdentifyBO(recordDTO);
		cacheMap.put(employeeId, newCache, CacheEnum.INVOICE_IDENTIFY.getExpire(), CacheEnum.INVOICE_IDENTIFY.getUnit());
		log.warn("初始化发票识别记录, newCache={}", JSON.toJSONString(newCache));
		return newCache;
	}

	/**
	 * 获取员工初始化入缓存的发票识别记录
	 * @param employeeId 员工ID
	 * @return
	 */
	public InvoiceIdentifyRecordBO getByEmployeeId(Long employeeId) {
		AssertUtils.notNullWithBizExp(employeeId, EMPLOYEE_NOT_NULL);
		RMapCache<Long, InvoiceIdentifyRecordBO> cacheMap = redissonClient.getMapCache(CacheEnum.INVOICE_IDENTIFY.getKey());
		return cacheMap.get(employeeId);
	}

	/**
	 * 删除员工发票识别缓存记录
	 * @param employeeId 员工ID
	 * @return identifyRecordBO
	 */
	public InvoiceIdentifyRecordBO removeByEmployeeId(Long employeeId) {
		AssertUtils.notNullWithBizExp(employeeId, EMPLOYEE_NOT_NULL);
		RMapCache<Long, InvoiceIdentifyRecordBO> cacheMap = redissonClient.getMapCache(CacheEnum.INVOICE_IDENTIFY.getKey());
		InvoiceIdentifyRecordBO identifyRecordBO = cacheMap.get(employeeId);
		AssertUtils.notNullWithBizExp(employeeId, INVOICE_IDENTIFY_CACHE_NOT_EXIST);
		cacheMap.remove(employeeId);
		return identifyRecordBO;
	}

	/**
	 * 初始化发票识别记录
	 * @param recordDTO
	 * @return invoiceIdentifyRecordBO
	 */
	private static InvoiceIdentifyRecordBO initInvoiceIdentifyBO(InvoiceIdentifyRecordDTO recordDTO) {
	    InvoiceIdentifyRecordBO invoiceIdentifyRecordBO = new InvoiceIdentifyRecordBO();
	    invoiceIdentifyRecordBO.setInvoiceNo(recordDTO.getInvoiceNo());
	    invoiceIdentifyRecordBO.setInvoiceCode(recordDTO.getInvoiceCode());
	    invoiceIdentifyRecordBO.setInvoiceDate(recordDTO.getInvoiceDate());
	    invoiceIdentifyRecordBO.setCheckCode(recordDTO.getCheckCode());
	    invoiceIdentifyRecordBO.setPretaxAmount(recordDTO.getPretaxAmount());
	    invoiceIdentifyRecordBO.setTotal(recordDTO.getTotal());
	    invoiceIdentifyRecordBO.setTax(recordDTO.getTax());
	    invoiceIdentifyRecordBO.setDetails(recordDTO.getDetails());
	    invoiceIdentifyRecordBO.setSeller(recordDTO.getSeller());
	    invoiceIdentifyRecordBO.setBuyer(recordDTO.getBuyer());
	    invoiceIdentifyRecordBO.setResult(recordDTO.getResult());
	    return invoiceIdentifyRecordBO;
	}
}
