package so.dian.invoice.manager;

import io.github.notoday.dingtalk.robot.DingRobotHelper;
import io.github.notoday.dingtalk.robot.message.MarkdownMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import so.dian.invoice.client.DingTalkClient;
import so.dian.invoice.properties.AppProperties;

@Slf4j
@Service
public class DingTalkManager {

    @Autowired
    private AppProperties appProperties;
    @Autowired
    private DingTalkClient dingTalkClient;

    /**
     * 告警消息
     */
    @Async
    public void sendWarnMsgAsync(String title,String text) {
        log.info("DingTalkSend");
        DingRobotHelper dingRobotHelper =
              dingTalkClient.getDingRobotHelper(appProperties.getDTalkWarnToken(), appProperties.getDTalkSecret());
        MarkdownMessage message = MarkdownMessage.builder().title(title).text(text).build();
        dingRobotHelper.sendMessage(message);
    }
}
