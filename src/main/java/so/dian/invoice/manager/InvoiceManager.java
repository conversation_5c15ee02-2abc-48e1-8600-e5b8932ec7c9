package so.dian.invoice.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.poi.excel.BigExcelWriter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.center.common.util.CollectionUtil;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.commons.eden.util.LocalObjectUtils;
import so.dian.hr.api.entity.employee.CityDepartmentDTO;
import so.dian.invoice.client.YandangClient;
import so.dian.invoice.client.manager.MailManager;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.converter.InvoiceConverter;
import so.dian.invoice.converter.InvoiceDeductionConverter;
import so.dian.invoice.dao.InvoiceDAO;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.enums.InvoiceProcessStatusEnum;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceDetailBO;
import so.dian.invoice.pojo.bo.InvoiceQueryBO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.param.InvoiceExportParam;
import so.dian.invoice.pojo.param.ScmInvoiceDeductQueryParam;
import so.dian.invoice.pojo.query.InvoiceFilterTimeQuery;
import so.dian.invoice.pojo.vo.InvoiceExportInfoVO;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.invoice.util.ExcelParser;
import so.dian.invoice.util.StringUtil;
import so.dian.yandang.client.pojo.request.PayBillBatchReq;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

import static so.dian.invoice.constant.InvoiceConstants.INVOICE_FILE_NAME;

/**
 * @Author: jiaoge
 * @Date: 2019/9/11 9:26 PM
 * @Description:
 */
@Component
@Slf4j
public class InvoiceManager {

    @Value("${temp.path}")
    private String tempPath;

    @Resource
    private InvoiceDAO invoiceDAO;

    @Resource
    private YandangClient yandangClient;

    @Resource
    private InvoiceDetailManager invoiceDetailManager;

    @Resource
    private AgentEmployeeService employeeService;

    @Resource
    private InvoiceDeductionDAO invoiceDeductionDAO;

    @Resource
    private MailManager mailManager;

    public InvoiceBO getInvoiceById(Integer invoiceId) {
        InvoiceDO invoiceDO = invoiceDAO.selectByPrimaryKey(invoiceId);
        if (Objects.isNull(invoiceDO)) {
            return null;
        }
        return InvoiceConverter.convertDO2BO(invoiceDO);
    }

    public Boolean invoiceReview(Integer invoiceId, Integer reviewer, String reviewRemark, InvoiceProcessStatusEnum
          processStatus, InvoiceProcessStatusEnum beforeProcessStatus) {
        int result = invoiceDAO.updateInvoiceToReview(invoiceId, reviewer, new Date(),
              reviewRemark, processStatus.getCode(), beforeProcessStatus.getCode());
        return result > 0;
    }

    public List<InvoiceBO> getInvoiceExportListPage(InvoiceExportParam param) {
        List<InvoiceDO> invoiceDOList = invoiceDAO.listInvoiceExportPage(param);
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            return Lists.newArrayList();
        }
        return InvoiceConverter.convertDO2BO(invoiceDOList);
    }

    public Long countInvoiceExportList(InvoiceExportParam param) {
        return invoiceDAO.countInvoiceExportList(param);
    }

    public void exportInvoiceList(List<InvoiceExportInfoVO> invoiceExportInfoVOList, BigExcelWriter writer) {
        if (CollectionUtils.isNotEmpty(invoiceExportInfoVOList)) {
            log.info("导出发票信息Invoice.size:{}", invoiceExportInfoVOList.size());
        } else {
            log.info("导出发票信息Invoice.size:{}", 0);
        }
        ExcelParser.writeHutoolExcel(invoiceExportInfoVOList, writer);
    }

    /**
     * 通过销售方名称模糊查询
     */
    public List<String> listSubjectNameListByParam(String subjectName, Integer belongSubjectId) {

        List<String> subjectNameList
              = invoiceDAO.listInvoiceBySubjectName(subjectName, belongSubjectId);
        return subjectNameList;
    }

    public List<InvoiceBO> listInvoice(InvoiceFilterTimeQuery query) {
        List<InvoiceDO> invoiceDOList = invoiceDAO.selectInvoice(query);
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            return Lists.newArrayList();
        }
        return InvoiceConverter.convertDO2BO(invoiceDOList);
    }

    /**
     * 根据发票录入时间来查询需要验真的发票
     */
    public List<InvoiceBO> listNeedValidateByCreateTime(Date startCreateTime, Date endCreateTime,
          List<InvoiceTypeEnum> invoiceTypeList,
          List<InvoiceIdentifyRecordEnum.IsRealEnum> isRealListEnum) {
        if (LocalObjectUtils.anyNull(startCreateTime, endCreateTime, invoiceTypeList) || CollectionUtils
              .isEmpty(invoiceTypeList) || CollectionUtils.isEmpty(isRealListEnum)) {
            return Lists.newArrayList();
        }
        List<Integer> typeList = LocalListUtils.transferList(invoiceTypeList, InvoiceTypeEnum::getType);
        List<InvoiceDO> invoiceDOS =
              invoiceDAO.listNeedValidateByCreateTime(startCreateTime, endCreateTime, typeList, isRealListEnum);
        return InvoiceConverter.convertDO2BONotNUll(invoiceDOS);
    }

    /**
     * 根据 ids 批量查询发票
     */
    public List<InvoiceBO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<InvoiceDO> invoiceDOS = invoiceDAO.listByIds(ids);
        return InvoiceConverter.convertDO2BO(invoiceDOS);
    }

    /**
     * 批量修改发票验真状态
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateRealStatus(InvoiceIdentifyRecordEnum.IsRealEnum isRealEnum, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return invoiceDAO.batchUpdateRealStatus(isRealEnum.getCode(), idList) > 0;
    }

    @Deprecated
    public List<InvoiceDO> listInvoiceByTime(Date beginTime, Date endTime) {
        List<InvoiceDO> invoiceDOList = invoiceDAO.listInvoiceByTime(beginTime, endTime);
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            return Lists.newArrayList();
        }
        return invoiceDOList;
    }

    public Boolean batchUpdateInCheckPool(List<Long> idList, Integer inCheckPool, Integer beforeInCheckPool) {
        Integer result = invoiceDAO.batchUpdateInCheckPool(idList, inCheckPool, beforeInCheckPool);
        return result > 0;
    }

    public Boolean updateValidationById(Integer id, Integer isReal, String validateCode) {
        return invoiceDAO.updateValidationById(id, isReal, validateCode) > 0;
    }

    /**
     *
     */
    public List<InvoiceBO> listNotValidate(DateTime startTime, DateTime endTime,
          List<InvoiceIdentifyRecordEnum.ValidationCodeEnum> validationCode) {
        if (LocalObjectUtils.anyNull(startTime, endTime)) {
            return Lists.newArrayList();
        }
        List<InvoiceDO> invoiceDOS = invoiceDAO.listNotValidate(startTime, endTime, validationCode);
        return InvoiceConverter.convertDO2BONotNUll(invoiceDOS);
    }

    public List<InvoiceBO> listNeedCheckInvoiceByIds(List<Long> invoiceIdList) {
        if (CollectionUtils.isEmpty(invoiceIdList)) {
            return Lists.newArrayList();
        }
        List<InvoiceDO> invoiceDOList = invoiceDAO.listNeedCheckInvoiceByIds(invoiceIdList);

        return InvoiceConverter.convertDO2BO(invoiceDOList);
    }

    public List<InvoiceBO> listInvoiceByEndTime(Date endTime) {
        List<InvoiceDO> invoiceDOList = invoiceDAO.listInvoiceByEndTime(endTime);
        if (CollectionUtils.isEmpty(invoiceDOList)) {
            return Lists.newArrayList();
        }
        return InvoiceConverter.convertDO2BO(invoiceDOList);
    }

    /**
     * 异步导出发票台账报表
     */
    @Async(value = "excelExportThreadPool")
    public void asyncExportInvoiceExcel(InvoiceExportParam param, Long totalCount, String email) {

        int batchSize = InvoiceConstants.BATCH_INVOICE_SIZE;
        long totalPage = totalCount % batchSize == 0 ? totalCount / batchSize : totalCount / batchSize + 1;
        int dealCount = 0;
        /**
         * excel路径
         */
        String fileName = INVOICE_FILE_NAME + System.currentTimeMillis() + InvoiceConstants.EXCEL_XLSX;
        String destFilePath = tempPath + fileName;
        BigExcelWriter writer = ExcelParser.getHutoolExcel(destFilePath);
        while (dealCount < totalPage) {

            param.setOffset(dealCount * batchSize);
            param.setPageSize(batchSize);
            dealCount++;
            List<InvoiceBO> invoiceBOList = getInvoiceExportListPage(param);
            if (CollectionUtils.isEmpty(invoiceBOList)) {
                continue;
            }
            List<InvoiceExportInfoVO> invoiceExportInfoVOList = fillInvoiceDetailAndEmployee(invoiceBOList);
            List<InvoiceExportInfoVO> finalExportInfoVOS = fillDeductionAndPayBillInfo(invoiceExportInfoVOList);
            if (StringUtil.isNotEmpty(param.getBillNo())) {
                finalExportInfoVOS = finalExportInfoVOS.stream().filter((invoice) -> {
                    if (StringUtil.equals(param.getBillNo(), invoice.getBusinessNo())) {
                        return true;
                    } else {
                        return false;
                    }
                }).collect(Collectors.toList());
            }
            exportInvoiceList(finalExportInfoVOS, writer);
            try {
                Thread.sleep(InvoiceConstants.BATCH_INVOICE_SLEEP_TIME);
            } catch (InterruptedException e) {
                log.error("exportInvoiceList InterruptedException e", e);
            }
        }
        writer.close();
        try {
            mailManager.sendEmail(email, fileName, "导出发票台账", new File(destFilePath));
        } catch (IOException e) {
            log.error("------ asyncExportInvoiceExcel mail send error, to = {}", email, e);
        }
    }

    /**
     * 填充物料和员工相关信息
     */
    public List<InvoiceExportInfoVO> fillInvoiceDetailAndEmployee(List<InvoiceBO> invoiceBOList) {
        Set<Integer> creatorIds = invoiceBOList.stream().map(InvoiceBO::getCreator).collect(Collectors.toSet());
        Map<Integer, String> employeeNickMap = employeeService.getAgentEmployeeNickMap(creatorIds);
        if (employeeNickMap.isEmpty()) {
            log.warn("获取员工昵称异常:{}", employeeNickMap);
        }

        //获取发票详情的物料名称list
        List<InvoiceDetailBO> invoiceDetailBOList = invoiceDetailManager.getInvoiceDetailList(invoiceBOList);

        List<InvoiceExportInfoVO> invoiceExportInfoVOList =
              InvoiceConverter.convertBO2VO(invoiceBOList, employeeNickMap, invoiceDetailBOList);
        return invoiceExportInfoVOList;
    }

    /**
     * 得到excel最终条数并填充核销和付款单信息
     */
    public List<InvoiceExportInfoVO> fillDeductionAndPayBillInfo(List<InvoiceExportInfoVO> invoiceExportInfoVOList) {
        List<InvoiceExportInfoVO> exportInfoVOS = new LinkedList<>();
        List<InvoiceDeductionDO> invoiceDeductionDOS = new LinkedList<>();
        /**
         * 发票记录和核销记录决定最终的条数
         * 同一个业务单号如果回滚则是把之前相同的业务单号的核销全部回滚
         */
        infoVOFor:
        for (InvoiceExportInfoVO infoVO : invoiceExportInfoVOList) {
            List<InvoiceDeductionDO> deductionDOS =
                  invoiceDeductionDAO.findByInvoiceNoAndInvoiceCode(infoVO.getInvoiceNo(), infoVO.getInvoiceCode());
            BigDecimal totalDeductionAmount = BigDecimal.ZERO;
            BigDecimal usedAmount = Objects.isNull(infoVO.getUsedAmount()) ? BigDecimal.ZERO : infoVO.getUsedAmount();
            if (CollectionUtil.isEmpty(deductionDOS)) {
                exportInfoVOS.add(infoVO);
                continue;
            }
            /**
             * 判断是否所有都回滚了。如果是则导出的记录中直接添加该发票
             */
            Boolean isAllRecover = true;
            /**
             * 回滚的业务单号,如果历史的业务单号相同则不会新增一条报表数据。(相同的业务单号会回滚历史的记录)
             */
            List<String> busniessNoRecovers = new ArrayList<>();
            deductionFor:
            for (InvoiceDeductionDO deductionDO : deductionDOS) {
                if (busniessNoRecovers.contains(deductionDO.getBusinessNo())) {
                    continue deductionFor;
                }
                if (OperateTypeEnum.RECOVER.getType() == deductionDO.getOperateType()) {
                    busniessNoRecovers.add(deductionDO.getBusinessNo());
                    continue deductionFor;
                }
                isAllRecover = false;
                if (null != deductionDO.getAmount()) {
                    totalDeductionAmount = totalDeductionAmount.add(deductionDO.getAmount());
                }
                InvoiceExportInfoVO exportInfoVO = infoVO.clone();
                InvoiceConverter.fillInvoiceExportInfoVOByInvocieDeduction(exportInfoVO, deductionDO);
                exportInfoVOS.add(exportInfoVO);
                invoiceDeductionDOS.add(deductionDO);
            }
            if (isAllRecover) {
                exportInfoVOS.add(infoVO);
            }
            // 测试发票核销和回滚逻辑验证最后核销的金额和落库的金额是否一致
            if (usedAmount.compareTo(totalDeductionAmount) != 0) {
                log.error("该笔发票核销的总金额和发票已核销金额不符 invoiceCode = {},invoiceNo = {},usedAmount = {},totalDeductionAmount = {}",
                      infoVO.getInvoiceCode(), infoVO.getInvoiceNo(), usedAmount, totalDeductionAmount);
            }
        }

        /**
         * 组装付款单参数批量查询付款单相关信息,并已业务单号为key付款单信息为value放到payBillMap中
         */
        PayBillBatchReq payBillBatchReq = InvoiceDeductionConverter.getPayBillBatchReq(invoiceDeductionDOS);
        /**
         * 根据业务单和业务类型查询付款单相关信息
         */
        Map<String, PayBillInfoRsp> payBillMap = Maps.newHashMap();
        List<Integer> cityCodes = Lists.newArrayList();

        so.dian.himalaya.common.entity.BizResult<List<PayBillInfoRsp>> payResult =
              yandangClient.getPayBillInfoByBatch(payBillBatchReq);
        if (Objects.isNull(payResult) || !payResult.isSuccess() || CollectionUtils.isEmpty(payResult.getData())) {
            log.warn("发票关联业务信息，付款单不存在.req:{}", payBillBatchReq);
        } else {
            List<PayBillInfoRsp> payBills = payResult.getData();
            /**
             * 一个业务单对应多个付款单,取最新的付款单(id最大的为最新的付款单)。
             * 直接排序然后把最新的付款单更新到map中
             */
            payBills.sort((a, b) -> a.getId().compareTo(b.getId()));
            payBills.forEach(obj -> {
                payBillMap.put(obj.getOutBizNo(), obj);
                cityCodes.add(obj.getCityCode());
            });
        }
        Map<Integer, String> cityRegionMap = Maps.newHashMap();
        if (!CollectionUtil.isEmpty(cityCodes)) {

            Map<Integer, CityDepartmentDTO> cityDepartmentDTOMap = employeeService.listRegionNameByCityCodes(cityCodes);
            cityDepartmentDTOMap.forEach((cityCode, regionDTO) -> {
                cityRegionMap.put(cityCode, regionDTO.getDepartmentName());
            });
        }
        List<InvoiceExportInfoVO> finalExportInfoVOS = exportInfoVOS.stream().map(obj -> {
            PayBillInfoRsp billInfoRsp = payBillMap.get(obj.getBusinessNo());
            if (Objects.nonNull(billInfoRsp)) {
                obj.setRegionCity(cityRegionMap.get(billInfoRsp.getCityCode()));
            }
            InvoiceConverter.fillInvoiceExportInfoVOByPayBillInfoRsp(obj, billInfoRsp);
            return obj;
        }).collect(Collectors.toList());

        return finalExportInfoVOS;
    }

    public List<InvoiceDO> listNeedCheckIsNoNull() {
        List<InvoiceDO> invoiceDOList = invoiceDAO.listNeedCheckIsNoNull();
        return invoiceDOList;
    }

    public void batchUpdateCityCode(List<Integer> invoiceIds, Map<Integer, Integer> creatorCityCodeMap) {
        invoiceDAO.batchUpdateCityCode(invoiceIds, creatorCityCodeMap);
    }

    public List<InvoiceBO> queryInvoiceList(ScmInvoiceDeductQueryParam param) {
        List<InvoiceDO> invoiceDOList = invoiceDAO.queryInvoiceList(param);
        return InvoiceConverter.convertDO2BO(invoiceDOList);
    }

    public int updateByPrimaryKeySelective(InvoiceDO record) {
        return invoiceDAO.updateByPrimaryKeySelective(record);
    }

    /**
     * 回滚发票
     */
    public int recoverInvoice(Integer id, Integer status, BigDecimal amount) {
        return invoiceDAO.recoverInvoice(id, status, amount);
    }

    /**
     * 核销发票
     */
    public int deductInvoice(Integer id, Integer status, BigDecimal source, BigDecimal target) {
        return invoiceDAO.deductInvoice(id, status, source, target);
    }

    /**
     * 批量查询发票信息
     */
    public List<InvoiceBO> findByInvoiceNoAndInvoiceCode(List<InvoiceQueryBO> list){
        List<InvoiceDO> invoiceDOList = invoiceDAO.findByInvoiceNoAndInvoiceCode(list);
        return InvoiceConverter.convertDO2BO(invoiceDOList);
    }
}
