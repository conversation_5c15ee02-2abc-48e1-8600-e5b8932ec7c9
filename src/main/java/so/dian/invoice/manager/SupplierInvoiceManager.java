package so.dian.invoice.manager;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import so.dian.commons.eden.util.LocalObjectUtils;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.invoice.dao.SupplierInvoiceMapper;
import so.dian.invoice.enums.SupplierInvoiceOperateEnum;
import so.dian.invoice.enums.SupplierInvoiceStatusEnum;
import so.dian.invoice.pojo.dto.BillNoDTO;
import so.dian.invoice.pojo.entity.*;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.service.InvoiceService;
import so.dian.invoice.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:04 AM
 * @Description:
 */
@Slf4j
@Component
public class SupplierInvoiceManager {

    @Resource
    private SupplierInvoiceMapper supplierInvoiceMapper;

    @Autowired
    private SupplierInvoiceDetailManager detailManager;

    @Autowired
    private SupplierInvoiceAttachmentManager attachmentManager;

    @Autowired
    private SupplierInvoiceDetailBillNoManager detailBillNoManager;

    @Autowired
    private SupplierInvoiceOperateLogManager operateLogManager;

    @Autowired
    private InvoiceService invoiceService;

    public Long countByReq(SupplierInvoicePageParam param, List<BillNoDTO> billNoList, Set<String> supplierNos) {
        if (Objects.isNull(param)) {
            return 0L;
        }
        return supplierInvoiceMapper.countByReq(param, billNoList, supplierNos);
    }

    public List<SupplierInvoiceDO> findPageByReq(SupplierInvoicePageParam param, List<BillNoDTO> billNoList,
                                                 Set<String> supplierNos, PageRequest pageRequest) {
        if (Objects.isNull(param)) {
            return null;
        }
        return supplierInvoiceMapper.findPageByReq(param, billNoList, supplierNos, pageRequest);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insert(SupplierInvoiceDO supplierInvoiceDO) {
        if (Objects.isNull(supplierInvoiceDO)) {
            return false;
        }
        supplierInvoiceDO.init();
        int row = supplierInvoiceMapper.insert(supplierInvoiceDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insertFetchId(SupplierInvoiceDO supplierInvoiceDO) {
        if (Objects.isNull(supplierInvoiceDO)) {
            return false;
        }
        supplierInvoiceDO.init();
        int row = supplierInvoiceMapper.insertFetchId(supplierInvoiceDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insertSelective(SupplierInvoiceDO supplierInvoiceDO) {
        if (Objects.isNull(supplierInvoiceDO)) {
            return false;
        }
        supplierInvoiceDO.init();
        int row = supplierInvoiceMapper.insertSelective(supplierInvoiceDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateById(SupplierInvoiceDO supplierInvoiceDO) {
        if (Objects.isNull(supplierInvoiceDO)) {
            return false;
        }
        if (Objects.isNull(supplierInvoiceDO.getGmtUpdate())) {
            supplierInvoiceDO.setGmtUpdate(System.currentTimeMillis());
        }
        int row = supplierInvoiceMapper.updateById(supplierInvoiceDO);
        return row > 0;
    }

    public SupplierInvoiceDO findById(Integer supplierInvoiceId) {
        if (Objects.isNull(supplierInvoiceId)) {
            return null;
        }
        return supplierInvoiceMapper.findById(supplierInvoiceId);
    }

    /**
     * 删除供应商发票主体和详情
     *
     * @param supplierInvoiceId
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void cascadeDeleteById(Integer supplierInvoiceId) {
        // 删除主体
        supplierInvoiceMapper.deleteById(supplierInvoiceId);
        // 删除明细
        detailManager.deleteBySupplierInvoiceId(supplierInvoiceId);
        // 删除发票图片
        attachmentManager.deleteBySupplierInvoiceId(supplierInvoiceId);
        // 删除业务号
        detailBillNoManager.deleteBySupplierInvoiceId(supplierInvoiceId);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void save(Map.Entry<String, SupplierInvoiceDO> entry, Map<String, List<SupplierInvoiceDetailDO>> ukInvoiceDetailListMap, CurrentUserReq userReq) {
        SupplierInvoiceDO supplierInvoiceDO = entry.getValue();
        // 获取主体id
        this.insertFetchId(supplierInvoiceDO);
        List<SupplierInvoiceDetailDO> invoiceDetailDOList = ukInvoiceDetailListMap.get(entry.getKey());
        for (SupplierInvoiceDetailDO detailDO : invoiceDetailDOList) {
            detailDO.setSupplierInvoiceId(supplierInvoiceDO.getId());
            // 获取明细id
            detailManager.insertFetchId(detailDO);
            List<SupplierInvoiceDetailBillNoDO> detailBillNoDOList = detailDO.getDetailBillNoDOList();
            for (SupplierInvoiceDetailBillNoDO billNoDO : detailBillNoDOList) {
                billNoDO.setSupplierInvoiceId(supplierInvoiceDO.getId());
                billNoDO.setSupplierInvoiceDetailId(detailDO.getId());
            }
            detailBillNoManager.insertBatch(detailBillNoDOList);

            String content = JSON.toJSONString(detailDO);
            operateLogManager.insert(SupplierInvoiceOperateEnum.导入发票, supplierInvoiceDO.getId(), content, userReq);
        }

    }

    public SupplierInvoiceDO findSupplierNoCode(String invoiceNo, String invoiceCode) {
        return supplierInvoiceMapper.findSupplierNoCode(invoiceNo, invoiceCode);
    }


    public SupplierInvoiceDO findByIdAndStatus(Integer supplierInvoiceId, SupplierInvoiceStatusEnum statusEnum) {
        return supplierInvoiceMapper.findByIdAndStatus(supplierInvoiceId, statusEnum.getCode());
    }

    public boolean updateStatusById(Integer supplierInvoiceId, SupplierInvoiceStatusEnum statusEnum) {
        if (LocalObjectUtils.anyNull(supplierInvoiceId, statusEnum)) {
            return false;
        }
        SupplierInvoiceDO supplierInvoiceDO = new SupplierInvoiceDO();
        supplierInvoiceDO.setId(supplierInvoiceId);
        supplierInvoiceDO.setStatus(statusEnum.getCode());

        return this.updateById(supplierInvoiceDO);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveInvoiceAndDetail(InvoiceDO invoiceDO, List<InvoiceDetailDO> invoiceDetailDOList, Integer supplierInvoiceId, CurrentUserReq userReq) {
        invoiceService.saveInvoiceAndDetail(invoiceDO, invoiceDetailDOList);

        Assert.isTrue(updateStatusById(supplierInvoiceId, SupplierInvoiceStatusEnum.DEAL_WITH), "供应商发票主体更新为[已转发票台账]失败");
        Assert.isTrue(detailManager.updateStatusBySupplierInvoiceId(supplierInvoiceId, SupplierInvoiceStatusEnum.DEAL_WITH),
                "供应商发票明细更新为[已转发票台账]失败");

        String content = JSON.toJSONString(supplierInvoiceId);
        operateLogManager.insert(SupplierInvoiceOperateEnum.转发票台账, supplierInvoiceId, content, userReq);
    }
}

