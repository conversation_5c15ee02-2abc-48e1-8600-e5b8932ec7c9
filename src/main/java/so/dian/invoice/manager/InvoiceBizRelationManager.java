package so.dian.invoice.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.commons.eden.enums.DeletedEnum;
import so.dian.invoice.dao.InvoiceBizRelationDAO;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.pojo.entity.InvoiceBizRelationDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;

@Service
@Slf4j(topic = "biz")
public class InvoiceBizRelationManager {
    @Resource
    private InvoiceDeductionDAO invoiceDeductionDAO;
    @Resource
    private InvoiceBizRelationDAO invoiceBizRelationDAO;
    @Resource
    private DingTalkManager dingTalkManager;

    /**
     * 整理发票关联的业务单号
     *
     * @param invoiceCode 发票代码
     * @param invoiceNo 发票号
     */
    @Transactional(rollbackFor = Exception.class)
    public int disposal(String invoiceCode, String invoiceNo) {
        if (StringUtils.isBlank(invoiceNo)) {
            log.error("整理发票关联的业务单号,参数不能为空.invoiceCode:{},invoiceNo:{}", invoiceCode, invoiceNo);
            return 0;
        }
        //1.查询发票核销记录
        List<InvoiceDeductionDO> invoiceDeductionDOList =
              invoiceDeductionDAO.findByInvoiceNoAndInvoiceCode(invoiceNo, invoiceCode);
        if (CollectionUtil.isEmpty(invoiceDeductionDOList)) {
            if (log.isDebugEnabled()) {
                log.info("整理发票关联的业务单号,没有核销记录.invoiceCode:{},invoiceNo:{}", invoiceCode, invoiceNo);
            }
            return 0;
        }
        List<InvoiceBizRelationDO> insertList = Lists.newArrayList();
        List<InvoiceBizRelationDO> updateList = Lists.newArrayList();
        //2.计算核销信息，找出核销的单号
        //是否一票多核
        Map<String, List<InvoiceDeductionDO>> map = invoiceDeductionDOList.stream().collect(Collectors.groupingBy(
              invoiceDeductionDO -> invoiceDeductionDO.getBusinessType() + "#" + invoiceDeductionDO.getBusinessNo()));
        for (Map.Entry<String, List<InvoiceDeductionDO>> entry : map.entrySet()) {
            String businessNo = null;
            Integer businessType = null;
            BigDecimal amount = BigDecimal.ZERO;
            //是否有多次核销、回滚
            for (InvoiceDeductionDO invoiceDeductionDO : entry.getValue()) {
                if (StringUtils.isBlank(businessNo)) {
                    businessNo = invoiceDeductionDO.getBusinessNo();
                    businessType = invoiceDeductionDO.getBusinessType();
                }
                //业务单号需要匹配
                if (!Objects.equals(businessNo, invoiceDeductionDO.getBusinessNo())
                      || !Objects.equals(businessType, invoiceDeductionDO.getBusinessType())) {
                    log.error("整理发票关联的业务单号，业务单号分组异常,key:{},invoiceDeductionDO:{}", entry.getKey(), invoiceDeductionDO);
                    continue;
                }
                if (Objects.equals(invoiceDeductionDO.getOperateType(), OperateTypeEnum.RECOVER.getType())) {
                    amount = amount.subtract(invoiceDeductionDO.getAmount());
                } else if (Objects.equals(invoiceDeductionDO.getOperateType(), OperateTypeEnum.DEDUCT.getType())) {
                    amount = amount.add(invoiceDeductionDO.getAmount());
                }
            }
            //查询是已存在记录
            InvoiceBizRelationDO invoiceBizRelationDO =
                  invoiceBizRelationDAO.findOne(invoiceCode, invoiceNo, businessNo, businessType);
            long now = System.currentTimeMillis();
            if (Objects.isNull(invoiceBizRelationDO)) {
                invoiceBizRelationDO = new InvoiceBizRelationDO();
                invoiceBizRelationDO.setInvoiceNo(invoiceNo);
                invoiceBizRelationDO.setInvoiceCode(invoiceCode == null ? "" : invoiceCode);
                invoiceBizRelationDO.setBusinessNo(businessNo);
                invoiceBizRelationDO.setBusinessType(businessType);
                invoiceBizRelationDO.setGmtCreate(now);
                invoiceBizRelationDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
            }
            invoiceBizRelationDO.setAmount(amount);
            invoiceBizRelationDO.setGmtUpdate(now);
            //核销金额负值：存在回滚记录大于核销记录，告警
            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                log.error("整理发票关联的业务单号，核销金额负值.InvoiceBizRelationDO:{}", invoiceBizRelationDO);
                sendWarnMsg(invoiceBizRelationDO);
            } else if (amount.compareTo(BigDecimal.ZERO) == 0) {
                //核销后回滚的，置为删除
                invoiceBizRelationDO.setDeleted(DeletedEnum.DELETED.getCode());
            }
            if (invoiceBizRelationDO.getId() != null) {
                updateList.add(invoiceBizRelationDO);
            } else {
                insertList.add(invoiceBizRelationDO);
            }
        }
        //3.入库
        if (CollectionUtil.isNotEmpty(insertList)) {
            invoiceBizRelationDAO.batchInsert(insertList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            for (InvoiceBizRelationDO invoiceBizRelationDO : updateList) {
                invoiceBizRelationDAO.update(invoiceBizRelationDO);
            }
        }
        return insertList.size() + updateList.size();
    }

    private void sendWarnMsg(InvoiceBizRelationDO invoiceBizRelationDO) {
        String title = "发票关联业务单号整理";
        StringBuilder text = new StringBuilder();
        text.append("### invoice-发票关联业务单号整理");
        text.append("\n\n 核销金额小于0");
        text.append("\n\n 发票号: ").append(invoiceBizRelationDO.getInvoiceNo());
        text.append("\n\n 发票代码: ").append(invoiceBizRelationDO.getInvoiceCode());
        text.append("\n\n 业务单号: ").append(invoiceBizRelationDO.getBusinessNo());
        String businessTypeStr = BusinessTypeEnum.getField(invoiceBizRelationDO.getBusinessType());
        text.append("\n\n 业务类型: ").append(businessTypeStr);
        dingTalkManager.sendWarnMsgAsync(title, text.toString());
    }
}