//package so.dian.invoice.manager.invoice.manage;
//
//import com.google.common.base.Preconditions;
//import org.springframework.stereotype.Component;
//import so.dian.invoice.converter.InvoiceManageConverter;
//import so.dian.invoice.dao.InvoiceManageDAO;
//import so.dian.invoice.dao.InvoiceManageDetailDAO;
//import so.dian.invoice.dao.InvoiceRequestRecordDetailDAO;
//import so.dian.invoice.manager.AgentManager;
//import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
//import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceManageDetail;
//import so.dian.invoice.pojo.entity.InvoiceManageDO;
//import so.dian.invoice.pojo.entity.InvoiceManageDetailDO;
//import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * @program: invoice
// * @description:
// * @author: yuechuan
// * @create: 2025-03-12 15:38
// */
//@Component
//public class InvoiceManageDetailManager {
//
//    @Resource
//    private InvoiceManageDAO invoiceManageDAO;
//
//    @Resource
//    private InvoiceManageDetailDAO invoiceManageDetailDAO;
//
//    @Resource
//    private InvoiceRequestRecordDetailDAO invoiceRequestRecordDetailDAO;
//
//    @Resource
//    private AgentManager agentManager;
//
//    public List<InvoiceManageDetailDTO> getInvoiceManageDetailById(Long id) {
//	InvoiceManageDO invoiceManageDO = Preconditions.checkNotNull(invoiceManageDAO.selectByPrimaryKey(id), "发票管理不存在");
//	List<InvoiceManageDetailDO> invoiceManageDetailDOS = invoiceManageDetailDAO.getByManageId(invoiceManageDO.getId());
//	List<InvoiceRequestRecordDetailDO> invoiceRequestRecordDetailDOS = invoiceRequestRecordDetailDAO.getByManageId(invoiceManageDO.getId());
//	//构建发票管理详情集并计算开票中、已开票金额
//	List<InvoiceManageDetail> manageDetails = InvoiceManageConverter.build(invoiceManageDetailDOS, invoiceRequestRecordDetailDOS);
//	List<InvoiceManageDetailDTO> manageDTOS = manageDetails.stream().map(e -> e.getManageDetailDTO()).collect(Collectors.toList());
//	return manageDTOS;
//    }
//
//}
