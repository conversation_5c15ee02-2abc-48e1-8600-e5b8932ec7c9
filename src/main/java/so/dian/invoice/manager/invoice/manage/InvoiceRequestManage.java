package so.dian.invoice.manager.invoice.manage;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.dao.InvoiceOperateRecordDAO;
import so.dian.invoice.dao.InvoiceRequestDAO;
import so.dian.invoice.dao.InvoiceRequestRecordDAO;
import so.dian.invoice.dao.InvoiceRequestRecordDetailDAO;
import so.dian.invoice.enums.CacheEnum;
import so.dian.invoice.enums.InvoiceManageStatusEnum;
import so.dian.invoice.enums.InvoiceOperateTypeEnum;
import so.dian.invoice.enums.InvoiceRequestStatusEnum;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestRecordDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceManage;
import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceRequestRecord;
import so.dian.invoice.pojo.entity.InvoiceOperateRecordDO;
import so.dian.invoice.pojo.entity.InvoiceRequestDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceRequestParam;
import so.dian.invoice.pojo.request.CurrentUserReq;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:38
 */
@Slf4j
@Component
public class InvoiceRequestManage {

    @Resource
    private InvoiceRequestDAO invoiceRequestDAO;

    @Resource
    private InvoiceRequestRecordDAO invoiceRequestRecordDAO;

    @Resource
    private InvoiceRequestRecordDetailDAO invoiceRequestRecordDetailDAO;

    @Resource
    private InvoiceOperateRecordDAO invoiceOperateRecordDAO;

    @Resource
    private InvoiceManageManager invoiceManageManager;

    @Autowired
    private RedissonClient redissonClient;


    @Transactional
    public int safeCreateInvoiceRequest(InvoiceRequestParam param, CurrentUserReq userReq){
        List<Long> manageIds = param.getInvoiceManageIds();
        CacheEnum lockEnum = CacheEnum.CREATE_INVOICE_REQUEST_LOCK;
        String lockKey = lockEnum + ":" + JSON.toJSONString(manageIds);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!lock.tryLock(lockEnum.getExpire(), lockEnum.getUnit())) {
                return NumberUtils.INTEGER_ZERO;
            }
            return this.createInvoiceRequest(param, userReq);
        } catch (BizException e) {
            log.error("BizException safeCreateInvoiceRequest error.", e);
            throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("safeCreateInvoiceRequest error.", e);
            throw BizException.create(InvoiceCodeEnum.BIZ_ERROR, "创建开票申请失败");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public int createInvoiceRequest(InvoiceRequestParam param, CurrentUserReq userReq){

        List<Long> manageIds = param.getInvoiceManageIds();
        //查询开票信息
        List<InvoiceManage> invoiceManages = invoiceManageManager.listInvoiceManageByIds(manageIds);

        boolean checkStatus = invoiceManages.stream().map(e -> e.getInvoiceManageDTO()).allMatch(e -> InvoiceManageStatusEnum.PENDING_INVOICE.getCode().equals(e.getStatus()));
        //校验所有的开票单据是否都为开票中的状态
        if (!checkStatus) {
            throw BizException.create(InvoiceCodeEnum.INVOICE_APPLY, "申请单中存在状态为非开票中的单据");
        }
        //校验开票金额是否一致
        Long totalAmount = invoiceManages.stream().map(e -> e.getPendingInvoiceAmount()).mapToLong(Long::valueOf).sum();

        Set<String> subject = invoiceManages.stream().map(e -> e.getInvoiceManageDTO().getSubjectId()+"_"+e.getInvoiceManageDTO().getSubjectType()).collect(Collectors.toSet());
        if (subject.size() > 1) {
            throw BizException.create(InvoiceCodeEnum.PARAM_ERROR,"申请开票勾选的业务单渠道商需要一致");
        }
        //校验开票金额是否一致
        boolean checkAmount = param.getAmount().equals(totalAmount);
        if (!checkAmount) {
            throw BizException.create(InvoiceCodeEnum.INVOICE_APPLY);
        }
        InvoiceManageDTO invoiceManageDTO = invoiceManages.get(0).getInvoiceManageDTO();

        //构建开票申请记录和开票申请记录详情
        List<InvoiceRequestRecord> invoiceRequestRecords = Lists.newArrayList();
        for(InvoiceManage invoiceManage : invoiceManages){
            InvoiceRequestRecord invoiceRequestRecord = InvoiceRequestRecord.build(invoiceManage.getPendingInvoiceAmount(),invoiceManage);
            invoiceRequestRecords.add(invoiceRequestRecord);
        }
        InvoiceRequestDO invoiceRequestDO = new InvoiceRequestDO();
        BeanUtils.copyProperties(param, invoiceRequestDO);
        invoiceRequestDO.setSubjectId(invoiceManageDTO.getSubjectId());
        invoiceRequestDO.setSubjectType(invoiceManageDTO.getSubjectType());
        invoiceRequestDO.setSubjectName(invoiceManageDTO.getSubjectName());
        invoiceRequestDO.setStatus(InvoiceRequestStatusEnum.PENDING_INVOICE.getCode());
        invoiceRequestDO.setApplicantId(Long.valueOf(userReq.getUserId()));
        invoiceRequestDO.init();
        //保存开票申请
        invoiceRequestDAO.insert(invoiceRequestDO);

        //保存开票申请记录和开票申请记录详情
        for(InvoiceRequestRecord invoiceRequestRecord : invoiceRequestRecords){
            InvoiceRequestRecordDTO requestRecordDTO = invoiceRequestRecord.getRequestRecordDTO();
            requestRecordDTO.setRequestId(invoiceRequestDO.getId());
            //保存开票申请记录
            InvoiceRequestRecordDO requestRecordDO = requestRecordDTO.toDO();

            requestRecordDO.init();
            invoiceRequestRecordDAO.insert(requestRecordDO);

            //保存开票申请记录详情
            for(InvoiceRequestRecordDetailDTO requestRecordDetailDTO : invoiceRequestRecord.getRequestRecordDetailDTOS()){
                requestRecordDetailDTO.setRequestRecordId(requestRecordDO.getId());
                InvoiceRequestRecordDetailDO detailDO = requestRecordDetailDTO.toDO();
                detailDO.init();
                invoiceRequestRecordDetailDAO.insert(detailDO);
            }
        }
        //更新开票管理状态 为开票中
        for (InvoiceManage manage : invoiceManages){
            invoiceManageManager.updateStatus(manage.getManageId(), InvoiceManageStatusEnum.PENDING_INVOICE.getCode());
        }
        return NumberUtils.INTEGER_ONE;
    }

    public InvoiceRequestDTO getInvoiceRequestById(Long requestId){
        InvoiceRequestDO requestDO = invoiceRequestDAO.selectByPrimaryKey(requestId);
        if (Objects.isNull(requestDO)){
            return null;
        }
        return InvoiceRequestDTO.fromDO(requestDO);
    }

    /**
     * 查询开票申请记录和开票申请记录详情, 过滤了开票详情中，产品金额为0的数据
     * @param requestId
     * @return
     */
    public List<InvoiceRequestRecordDTO> listRequestRecordWithDetail(Long requestId){
        //查询开票申请记录 一个记录对应一个 invoice_manager
        List<InvoiceRequestRecordDO> requestRecordDOS = invoiceRequestRecordDAO.getByRequestId(requestId);

        List<InvoiceRequestRecordDTO> recordDTOS = requestRecordDOS.stream().map(InvoiceRequestRecordDTO::fromDO).collect(Collectors.toList());

        for(InvoiceRequestRecordDTO recordDTO : recordDTOS){
            InvoiceManageDTO dto = invoiceManageManager.getInvoiceManageById(recordDTO.getManageId());
            recordDTO.setBizType(dto.getBizType());
            //设置开票申请记录详情
            List<InvoiceRequestRecordDetailDO> recordDetailDOS = invoiceRequestRecordDetailDAO.getByRequestRecordId(recordDTO.getId());
            if (CollectionUtils.isEmpty(recordDetailDOS)) {
                continue;
            }
            List<InvoiceRequestRecordDetailDTO> recordDetails = recordDetailDOS.stream()
                    .filter(e -> Objects.nonNull(e) && Objects.nonNull(e.getAmount()) && e.getAmount() > 0)
                    .map(InvoiceRequestRecordDetailDTO::fromDO).collect(Collectors.toList());

            recordDTO.setDetails(recordDetails);
        }

        return recordDTOS;
    }

    /**
     * 批量审核开票申请
     * @param requestIds
     * @return
     */
    @Transactional
    public int batchApprove(List<Long> requestIds, int status, String reason){
        return batchApprove(requestIds, status, reason, null, null);
    }

    /**
     * 批量审核开票申请（带发票号码和操作人）
     * @param requestIds
     * @param status
     * @param reason
     * @param invoiceCode
     * @param operator
     * @return
     */
    @Transactional
    public int batchApprove(List<Long> requestIds, int status, String reason, String invoiceCode, String operator){
        for (Long requestId : requestIds){
            InvoiceRequestDO requestDO = invoiceRequestDAO.selectByPrimaryKey(requestId);
            requestDO.setStatus(status);
            requestDO.setFinanceFeedback(reason);
            // 设置数电发票号码
            if (StringUtils.isNotBlank(invoiceCode)) {
                requestDO.setInvoiceCode(invoiceCode);
            }
            //只有开票申请通过才保存开票完成时间
            if (InvoiceRequestStatusEnum.COMPLETED_INVOICE.getCode().equals(status)) {
                requestDO.setInvoiceCompletedTime(System.currentTimeMillis());
            }
            requestDO.setGmtUpdate(System.currentTimeMillis());
            invoiceRequestDAO.updateByPrimaryKeySelective(requestDO);

            // 新增发票操作记录
            if (StringUtils.isNotBlank(operator)) {
                InvoiceOperateRecordDO operateRecord = new InvoiceOperateRecordDO();
                operateRecord.setRequestId(requestId);
                operateRecord.setOperateType(InvoiceOperateTypeEnum.INVOICE_AUDIT.getCode());
                operateRecord.setOperator(operator);
                operateRecord.setOperateTime(System.currentTimeMillis());
                operateRecord.setRemark("批量审核：" + reason);
                operateRecord.init();
                invoiceOperateRecordDAO.insertSelective(operateRecord);
            }

            List<InvoiceRequestRecordDO> requestRecordDOS = invoiceRequestRecordDAO.getByRequestId(requestId);
            for (InvoiceRequestRecordDO requestRecordDO : requestRecordDOS){
                requestRecordDO.setStatus(status);
                invoiceRequestRecordDAO.updateByPrimaryKeySelective(requestRecordDO);
                invoiceRequestRecordDetailDAO.updateByRequestRecordId(requestRecordDO.getId(),status);
                //判断是否开票完成
                InvoiceManage invoiceManageAgg = invoiceManageManager.getInvoiceManageAggById(requestRecordDO.getManageId());
                if (invoiceManageAgg.isCompletedInvoiceStatus()) {
                    invoiceManageManager.updateStatus(requestRecordDO.getManageId(), InvoiceManageStatusEnum.COMPLETED_INVOICE.getCode());
                }
            }
        }
        return NumberUtils.INTEGER_ONE;
    }
}
