package so.dian.invoice.manager.invoice.manage;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.invoice.dao.InvoiceChangeRecordDAO;
import so.dian.invoice.dao.InvoiceChangeRecordDetailDAO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDetailDTO;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDO;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:38
 */
@Component
public class InvoiceChangeRecordManager {

    @Resource
    private InvoiceChangeRecordDAO invoiceChangeRecordDAO;

    @Resource
    private InvoiceChangeRecordDetailDAO invoiceChangeRecordDetailDAO;


    /**
     * 保存变更记录
     * @param changeRecordDTO
     * @return
     */
    @Transactional
    public int save(InvoiceChangeRecordDTO changeRecordDTO) {
	InvoiceChangeRecordDO manageDO = changeRecordDTO.toDO();
	manageDO.init();
	invoiceChangeRecordDAO.insert(manageDO);
	for(InvoiceChangeRecordDetailDTO changeRecordDetailDTO : changeRecordDTO.getChangeRecordDetailDTOList()){
	    InvoiceChangeRecordDetailDO manageDetailDO = changeRecordDetailDTO.toDO();
	    manageDetailDO.setChangeRecordId(manageDO.getId());
	    manageDetailDO.init();
	    invoiceChangeRecordDetailDAO.insert(manageDetailDO);
	}
	return NumberUtils.INTEGER_ONE;
    }

//    public List<InvoiceChangeRecordDTO> getByManageId(Long manageId) {
//	List<InvoiceChangeRecordDO> recordDOS = invoiceChangeRecordDAO.listByManageId(manageId);
//
//	List<InvoiceChangeRecordDetailDO> detailDOS = invoiceChangeRecordDetailDAO.getByManageId(manageId);
//	Map<Long,List<InvoiceChangeRecordDetailDTO>> detailMap = detailDOS.stream().map(InvoiceChangeRecordDetailDTO::fromDO)
//		.collect(Collectors.groupingBy(InvoiceChangeRecordDetailDTO::getChangeRecordId));
//
//	List<InvoiceChangeRecordDTO> list = new ArrayList<>();
//	for (InvoiceChangeRecordDO recordDO : recordDOS) {
//	    InvoiceChangeRecordDTO recordDTO = InvoiceChangeRecordDTO.fromDO(recordDO);
//	    List<InvoiceChangeRecordDetailDTO> detailDTOS = detailMap.get(recordDO.getId());
//	    recordDTO.setChangeRecordDetailDTOList(detailDTOS);
//
//	    list.add(recordDTO);
//	}
//
//	return list;
//    }

    public List<InvoiceChangeRecordDetailDTO> getByManageId(Long manageId) {
	List<InvoiceChangeRecordDetailDO> detailDOS = invoiceChangeRecordDetailDAO.getByManageId(manageId);

	return detailDOS.stream().map(InvoiceChangeRecordDetailDTO::fromDO).collect(Collectors.toList());
    }

    public InvoiceChangeRecordDTO getByBizNoAndBizType(String outBizId, Integer outBizType) {
	InvoiceChangeRecordDO recordDO = invoiceChangeRecordDAO.getByBizNoAndBizType(outBizId,outBizType);
	if (Objects.isNull(recordDO)) {
	    return null;
	}
	return InvoiceChangeRecordDTO.fromDO(recordDO);
    }
}
