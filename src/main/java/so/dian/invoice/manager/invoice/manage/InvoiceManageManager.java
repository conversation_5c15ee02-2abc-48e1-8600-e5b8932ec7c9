package so.dian.invoice.manager.invoice.manage;

import com.github.pagehelper.page.PageMethod;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.himalaya.common.exception.BizException;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.invoice.converter.InvoiceManageConverter;
import so.dian.invoice.dao.InvoiceManageDAO;
import so.dian.invoice.dao.InvoiceManageDetailDAO;
import so.dian.invoice.dao.InvoiceRequestRecordDAO;
import so.dian.invoice.dao.InvoiceRequestRecordDetailDAO;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.invoice.facade.SunReaverFacade;
import so.dian.invoice.facade.TiantaiFacade;
import so.dian.invoice.manager.AgentManager;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceManage;
import so.dian.invoice.pojo.dto.invoice.manage.agg.InvoiceManageDetail;
import so.dian.invoice.pojo.entity.InvoiceManageDO;
import so.dian.invoice.pojo.entity.InvoiceManageDetailDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDO;
import so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.service.AgentEmployeeService;
import so.dian.sunreaver.common.TenantAdminTypeEnum;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 15:38
 */
@Slf4j
@Component
public class InvoiceManageManager {

    @Resource
    private InvoiceManageDAO invoiceManageDAO;

    @Resource
    private InvoiceManageDetailDAO invoiceManageDetailDAO;

    @Resource
    private InvoiceRequestRecordDAO invoiceRequestRecordDAO;

    @Resource
    private InvoiceRequestRecordDetailDAO invoiceRequestRecordDetailDAO;

    @Resource
    private AgentManager agentManager;

    @Resource
    private SunReaverFacade sunReaverManager;

    @Resource
    private TiantaiFacade tiantaiFacade;

    @Resource
    private AgentEmployeeService agentEmployeeService;


    public List<Long> getAgentIds(CurrentUserReq userReq){
	//财务经理 可查看所有公司的应开数据，含一代、合资和二代
	List<Long> agentIds = new ArrayList<>();
	if(StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())){
	    List<AgentDTO> agentDTOS = agentManager.findAgentList();
	    agentIds = agentDTOS.stream().map(AgentDTO::getAgentId).collect(Collectors.toList());
	    return agentIds;
	}else
	if(StringUtils.equals(UserRoleEnum.CHANNEL_MANAGER.getRoleName(), userReq.getCurrentRole())
		|| StringUtils.equals(UserRoleEnum.CHANNEL_CHIEF.getRoleName(), userReq.getCurrentRole())
		|| StringUtils.equals(UserRoleEnum.CHANNEL_AREA_CHIEF.getRoleName(), userReq.getCurrentRole())) {
	    //渠道经理 渠道总监 渠道区域总监
	    agentIds = sunReaverManager.getAgentIds(Long.valueOf(userReq.getUserId()), TenantAdminTypeEnum.AUTHORIZE_OVER_TENANT_ADMINISTRATOR);
	} else if(StringUtils.equals(UserRoleEnum.CITY_PLANING_MANAGER.getRoleName(), userReq.getCurrentRole())){
	    // 小电运营(城市策划), 仅可查看发布公司为本人及下属运营负责公司
	    agentIds = sunReaverManager.getAgentIds(Long.valueOf(userReq.getUserId()), TenantAdminTypeEnum.TENANT_SUPER_ADMINISTRATOR);
	}else if (StringUtils.equals(UserRoleEnum.AGENT_SELLER_MANAGER.getRoleName(), userReq.getCurrentRole())) {
	    // 渠道商老板 BD主管（且为老板身份）
	    AgentDTO agentDTO = agentManager.findById(userReq.getBelongSubjectId());
	    if (Objects.nonNull(agentDTO) && Objects.nonNull(agentDTO.getAgentContactId())
		    && NumberUtils.compare(agentDTO.getAgentContactId(),  userReq.getUserId()) == 0) {
		agentIds = Lists.newArrayList(Long.valueOf(userReq.getBelongSubjectId()));
	    }else {
		//不让去查看数据
		agentIds = Lists.newArrayList(-1L);
	    }
	}else {
	    //代理商  渠道商财务 只能查看本人归属公司的应开数据
	    agentIds = Lists.newArrayList(Long.valueOf(userReq.getBelongSubjectId()));
	}
	if (CollectionUtils.isEmpty(agentIds)) {
	    log.warn("远程获取代理商信息 | 公司不存在，不让查看数据 | userId:{},role:{}", userReq.getUserId(), userReq.getCurrentRole());
	    agentIds = Lists.newArrayList(-1L);
	}
	return agentIds;
    }

    public List<InvoiceManageDTO> list(InvoiceManageQueryParam param){
	// 查询发票管理
	PageMethod.orderBy("gmt_update desc,gmt_create desc");
	List<InvoiceManageDO> invoiceManageDOS = invoiceManageDAO.list(param);
	List<Long> manageIds = invoiceManageDOS.stream().map(e -> e.getId()).collect(Collectors.toList());

	List<InvoiceManage> invoiceManages = this.listInvoiceManageByIds(manageIds);

	List<InvoiceManageDTO> manageDTOS = invoiceManages.stream().map(e -> e.getInvoiceManageDTO()).collect(Collectors.toList());

	return manageDTOS;
    }

    protected List<InvoiceManage> listInvoiceManageByIds(List<Long> manageIds) {
	if (CollectionUtils.isEmpty(manageIds)) {
	    return Lists.newArrayList();
	}
	List<InvoiceManageDO> invoiceManageDOS = invoiceManageDAO.listByIds(manageIds);
	if (CollectionUtils.isEmpty(invoiceManageDOS)) {
	    throw BizException.create(InvoiceCodeEnum.INVOICE_MANAGE_NOT_EXISTS);
	}
	// 查询供应商信息
	List<Long> agentIds = invoiceManageDOS.stream().map(e -> e.getSubjectId()).collect(Collectors.toList());
	List<AgentDTO> agentDTOS = agentManager.listByIds(agentIds);
	//查询发票管理详情
	List<InvoiceManageDetailDO> detailDOS = invoiceManageDetailDAO.listByManageIds(manageIds);
	// 查询开票申请记录
	List<InvoiceRequestRecordDO> requestRecordDOS = invoiceRequestRecordDAO.listByManageIds(manageIds);
	// 查询开票申请记录详情
	List<InvoiceRequestRecordDetailDO> requestRecordDetailDOS = invoiceRequestRecordDetailDAO.listByManageIds(manageIds);
	//构建发票管理集并计算开票中、已开票金额
	List<InvoiceManage> invoiceManages = InvoiceManageConverter.build(invoiceManageDOS, detailDOS, requestRecordDOS, requestRecordDetailDOS);

	//填充公司名称信息
	this.fillAgentName(invoiceManages,agentDTOS);

	return invoiceManages;
    }


    public InvoiceManageDTO getInvoiceManageById(Long manageId) {

	InvoiceManageDO invoiceManageDO = invoiceManageDAO.selectByPrimaryKey(manageId);
	if (Objects.isNull(invoiceManageDO)) {
	    throw BizException.create(InvoiceCodeEnum.INVOICE_MANAGE_NOT_EXISTS);
	}
	InvoiceManageDTO dto = InvoiceManageDTO.fromDO(invoiceManageDO);
	return dto;
    }

    public InvoiceManage getInvoiceManageAggById(Long manageId) {

	InvoiceManageDO invoiceManageDO = invoiceManageDAO.selectByPrimaryKey(manageId);
	if (Objects.isNull(invoiceManageDO)) {
	    throw BizException.create(InvoiceCodeEnum.INVOICE_MANAGE_NOT_EXISTS);
	}
	//查询发票管理详情
	List<InvoiceManageDetailDO> detailDOS = invoiceManageDetailDAO.getByManageId(invoiceManageDO.getId());
	// 查询开票申请记录
	List<InvoiceRequestRecordDO> requestRecordDOS = invoiceRequestRecordDAO.listByManageIds(Lists.newArrayList(manageId));
	// 查询开票申请记录详情
	List<InvoiceRequestRecordDetailDO> requestRecordDetailDOS = invoiceRequestRecordDetailDAO.listByManageIds(Lists.newArrayList(manageId));
	//构建发票管理集并计算开票中、已开票金额
	InvoiceManage invoiceManage = InvoiceManage.build(invoiceManageDO, detailDOS, requestRecordDOS, requestRecordDetailDOS);


	return invoiceManage;
    }

    public List<InvoiceManageDetailDTO> getInvoiceManageDetailById(Long manageId) {
	InvoiceManageDO invoiceManageDO = Preconditions.checkNotNull(invoiceManageDAO.selectByPrimaryKey(manageId), "发票管理不存在");
	List<InvoiceManageDetailDO> invoiceManageDetailDOS = invoiceManageDetailDAO.getByManageId(invoiceManageDO.getId());
	List<InvoiceRequestRecordDetailDO> invoiceRequestRecordDetailDOS = invoiceRequestRecordDetailDAO.getByManageId(invoiceManageDO.getId());
	//构建发票管理详情集并计算开票中、已开票金额
	List<InvoiceManageDetail> manageDetails = InvoiceManageConverter.build(invoiceManageDetailDOS, invoiceRequestRecordDetailDOS);
	List<InvoiceManageDetailDTO> manageDTOS = manageDetails.stream().map(e -> e.getManageDetailDTO()).collect(Collectors.toList());
	//查询产品信息
	List<String> spuCodes = manageDetails.stream().map(e -> e.getManageDetailDTO().getProductCode()).collect(Collectors.toList());
	Map<String, String> map = tiantaiFacade.getSpuName(spuCodes);
	if (MapUtils.isEmpty(map)) {
	    log.info("产品信息为null,params:{}", spuCodes);
	    return manageDTOS;
	}
	manageDTOS.forEach(e -> {
	    e.setProductName(map.get(e.getProductCode()));
	});
	return manageDTOS;
    }

    public List<InvoiceManageDetailDTO> getManageDetailByMangeId(Long mangeId) {
	List<InvoiceManageDetailDO> invoiceManageDetailDOS = invoiceManageDetailDAO.getByManageId(mangeId);
	//构建发票管理详情集并计算开票中、已开票金额
	List<InvoiceManageDetailDTO> manageDTOS = invoiceManageDetailDOS.stream().map(InvoiceManageDetailDTO::fromDO).collect(Collectors.toList());
	return manageDTOS;
    }

    public List<InvoiceManageDTO> listManageById(List<Long> mangeIds) {
	if (CollectionUtils.isEmpty(mangeIds)) {
	    return Lists.newArrayList();
	}
	List<InvoiceManageDO> invoiceManageDOS = invoiceManageDAO.listByIds(mangeIds);
	List<InvoiceManageDTO> manageDTOS = invoiceManageDOS.stream().map(InvoiceManageDTO::fromDO).collect(Collectors.toList());
	return manageDTOS;
    }

    public void fillAgentName(List<InvoiceManage> invoiceManages,List<AgentDTO> agentDTOS){
	Map<Long,AgentDTO> agentNameMap = agentDTOS.stream().collect(Collectors.toMap(AgentDTO::getAgentId, Function.identity(), (v1, v2) -> v1));
	for (InvoiceManage invoiceManager : invoiceManages){
	    AgentDTO agentDTO = agentNameMap.get(invoiceManager.getInvoiceManageDTO().getSubjectId());
		if (Objects.isNull(agentDTO)) {
		    continue;
		}
		invoiceManager.getInvoiceManageDTO().setSubjectName(agentDTO.getAgentName());
//	    e.getInvoiceManageDTO().setSubjectType(agentDTO.getType());
	}
    }

    public InvoiceManageDTO getByBizNoAndBizType(String bizNo, Integer bizType) {
	InvoiceManageDO invoiceManageDO = invoiceManageDAO.getByBizNoAndBizType(bizNo, bizType);
	if (Objects.isNull(invoiceManageDO)) {
	    return null;
	}
	InvoiceManageDTO invoiceManageDTO = InvoiceManageDTO.fromDO(invoiceManageDO);
	List<InvoiceManageDetailDTO> manageDetailDTOS = this.getManageDetailByMangeId(invoiceManageDTO.getId());

	invoiceManageDTO.setManageDetailDTOS(manageDetailDTOS);
	return invoiceManageDTO;
    }

    /**
     * 保存发票管理及发票详情
     * @param manageDTO
     * @return
     */
    @Transactional
    public int save(InvoiceManageDTO manageDTO) {
	InvoiceManageDO manageDO = manageDTO.toDO();
	manageDO.init();
	invoiceManageDAO.insert(manageDO);
	manageDTO.setId(manageDO.getId());
	for(InvoiceManageDetailDTO manageDetailDTO : manageDTO.getManageDetailDTOS()){
	    manageDetailDTO.setManageId(manageDTO.getId());
	    InvoiceManageDetailDO manageDetailDO = manageDetailDTO.toDO();
	    manageDetailDO.init();
	    invoiceManageDetailDAO.insert(manageDetailDO);
	    manageDetailDTO.setId(manageDetailDO.getId());
	}

	return NumberUtils.INTEGER_ONE;
    }

    /**
     * 保存发票管理及发票详情
     * @param manageDTO
     * @return
     */
    @Transactional
    public int update(InvoiceManageDTO manageDTO) {
	InvoiceManageDO manageDO = manageDTO.toDO();
	manageDO.setGmtUpdate(System.currentTimeMillis());
	invoiceManageDAO.updateByPrimaryKey(manageDO);
	for(InvoiceManageDetailDTO manageDetailDTO : manageDTO.getManageDetailDTOS()){
	    InvoiceManageDetailDO manageDetailDO = manageDetailDTO.toDO();
	    manageDetailDO.setGmtUpdate(System.currentTimeMillis());
	    invoiceManageDetailDAO.updateByPrimaryKey(manageDetailDO);
	}
	return NumberUtils.INTEGER_ONE;
    }

    public int updateStatus(Long manageId,int status){
	InvoiceManageDO manageDO = invoiceManageDAO.selectByPrimaryKey(manageId);
	if(Objects.isNull(manageDO)){
	    return NumberUtils.INTEGER_ZERO;
	}
	manageDO.setGmtUpdate(System.currentTimeMillis());
	manageDO.setStatus(status);
	return invoiceManageDAO.updateByPrimaryKey(manageDO);
    }
}
