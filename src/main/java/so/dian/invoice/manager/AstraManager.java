package so.dian.invoice.manager;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import so.dian.astra.client.pojo.request.MaskingDataEncryptRequest;
import so.dian.astra.client.pojo.response.MaskingDataResponse;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.client.AstraClient;
import so.dian.invoice.pojo.dto.MaskingDataDTO;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class AstraManager {

    @Resource
    private AstraClient astraClient;

    /**
     * 单个数据脱敏
     */
    public MaskingDataResponse encrypt(MaskingDataEncryptRequest maskingDataEncryptRequest) {
        if (Objects.isNull(maskingDataEncryptRequest)) {
            log.error("maskingDataEncryptRequest不能为空");
            return null;
        }
        BizResult<MaskingDataResponse> bizResult = astraClient.encrypt(maskingDataEncryptRequest);
        if (Objects.isNull(bizResult)) {
            log.error("maskingDataEncryptRequest | fallback | params:{}", JSONObject.toJSONString(maskingDataEncryptRequest));
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("maskingDataEncryptRequest | 接口返回错误信息 | params:{},result:{}", JSONObject.toJSONString(maskingDataEncryptRequest), bizResult);
            return null;
        }
        return bizResult.getData();
    }

    /**
     * 多个数据脱敏
     * @param list
     * @return
     */
    public Map<String, MaskingDataResponse> encrypts(List<MaskingDataEncryptRequest> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("List<MaskingDataEncryptRequest>不能为空");
            return null;
        }
        BizResult<Map<String, MaskingDataResponse>> bizResult = astraClient.encrypts(list);
        if (Objects.isNull(bizResult)) {
            log.error("List<MaskingDataEncryptRequest> | fallback | params:{}", JSONObject.toJSONString(list));
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("List<MaskingDataEncryptRequest> | 接口返回错误信息 | params:{},result:{}", JSONObject.toJSONString(list), bizResult);
            return null;
        }
        return bizResult.getData();
    }

    /**
     * 多个数据脱敏
     * @param userId 用户id
     * @param list 单键值对， key 为 脱敏数据类型 maskingDataType，value 为 脱敏数据 maskingSourceData
     * @return
     */
    public Map<String, MaskingDataDTO> encrypts(Long userId, List<Pair<String, String>> list) {
        List<MaskingDataEncryptRequest> requests = new ArrayList<>();
        for (Pair<String, String> pair : list) {
            MaskingDataEncryptRequest maskingDataEncryptRequest = this.buildEncrypt(userId, pair.getKey(), pair.getValue());
            requests.add(maskingDataEncryptRequest);
        }
        Map<String, MaskingDataDTO> maskingDataDTO = new HashMap<>();
        Map<String, MaskingDataResponse> maskingDataResponses = encrypts(requests);
        for (String key : maskingDataResponses.keySet()){
            maskingDataDTO.put(key, MaskingDataDTO.builder()
                    .displayText(maskingDataResponses.get(key).getMaskingData())
                    .encryptStr(maskingDataResponses.get(key).getCiphertext())
                    .build());
        }
        return maskingDataDTO;
    }

    private MaskingDataEncryptRequest buildEncrypt(Long userId,String maskingDataType,String maskingSourceData){
        MaskingDataEncryptRequest maskingDataEncryptRequest = new MaskingDataEncryptRequest();
        maskingDataEncryptRequest.setMaskingDataType(maskingDataType);
        maskingDataEncryptRequest.setMaskingSourceData(maskingSourceData);
        maskingDataEncryptRequest.setUserId(userId);
        return maskingDataEncryptRequest;
    }
}
