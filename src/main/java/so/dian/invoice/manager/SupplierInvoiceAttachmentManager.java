package so.dian.invoice.manager;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import so.dian.invoice.dao.SupplierInvoiceAttachmentMapper;
import so.dian.invoice.enums.SupplierAttachmentTypeEnum;
import so.dian.invoice.pojo.entity.SupplierInvoiceAttachmentDO;

import java.util.List;
import java.util.Objects;

@Service
public class SupplierInvoiceAttachmentManager {

    @Autowired
    private SupplierInvoiceAttachmentMapper supplierInvoiceAttachmentMapper;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insert(Integer supplierInvoiceId, SupplierAttachmentTypeEnum attachmentTypeEnum, String url) {
        SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO = new SupplierInvoiceAttachmentDO();
        supplierInvoiceAttachmentDO.setSupplierInvoiceId(supplierInvoiceId);
        supplierInvoiceAttachmentDO.setType(attachmentTypeEnum.getCode());
        supplierInvoiceAttachmentDO.setUrl(url);
        supplierInvoiceAttachmentDO.init();
        return supplierInvoiceAttachmentMapper.insert(supplierInvoiceAttachmentDO) > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insert(SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO) {
        if (Objects.isNull(supplierInvoiceAttachmentDO)) {
            return false;
        }
        supplierInvoiceAttachmentDO.init();
        int row = supplierInvoiceAttachmentMapper.insert(supplierInvoiceAttachmentDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateById(SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO) {
        if (Objects.isNull(supplierInvoiceAttachmentDO)) {
            return false;
        }
        if (Objects.isNull(supplierInvoiceAttachmentDO.getGmtUpdate())) {
            supplierInvoiceAttachmentDO.setGmtUpdate(System.currentTimeMillis());
        }
        int row = supplierInvoiceAttachmentMapper.updateById(supplierInvoiceAttachmentDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean deleteById(Integer id) {
        if (Objects.isNull(id)) {
            return false;
        }
        SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO = new SupplierInvoiceAttachmentDO();
        supplierInvoiceAttachmentDO.setId(id);
        supplierInvoiceAttachmentDO.setGmtUpdate(System.currentTimeMillis());
        supplierInvoiceAttachmentDO.setDeleted(1);
        int row = supplierInvoiceAttachmentMapper.updateById(supplierInvoiceAttachmentDO);
        return row > 0;
    }

    public SupplierInvoiceAttachmentDO findInvoicePicture(Integer supplierInvoiceId) {
        List<SupplierInvoiceAttachmentDO> invoiceAttachmentDOList =
                supplierInvoiceAttachmentMapper.findListBySupplierInvoiceId(supplierInvoiceId, SupplierAttachmentTypeEnum.INVOICE_PIC.getCode());
        SupplierInvoiceAttachmentDO supplierInvoiceAttachmentDO = null;
        if (CollectionUtils.isNotEmpty(invoiceAttachmentDOList)) {
            supplierInvoiceAttachmentDO = invoiceAttachmentDOList.get(0);
        }
        return supplierInvoiceAttachmentDO;
    }


    public SupplierInvoiceAttachmentDO findById(Integer attachmentId) {
        if (Objects.isNull(attachmentId)) {
            return null;
        }
        return supplierInvoiceAttachmentMapper.findById(attachmentId);
    }


    public boolean deleteBySupplierInvoiceId(Integer supplierInvoiceId) {
        if (Objects.isNull(supplierInvoiceId)) {
            return false;
        }
        return supplierInvoiceAttachmentMapper.deleteBySupplierInvoiceId(supplierInvoiceId) > 0;
    }
}
