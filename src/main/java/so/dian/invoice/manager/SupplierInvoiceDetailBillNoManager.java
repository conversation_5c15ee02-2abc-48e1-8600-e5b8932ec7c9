package so.dian.invoice.manager;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.invoice.dao.SupplierInvoiceDetailBillNoMapper;
import so.dian.invoice.enums.SupplierInvoiceBillNoEnum;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailBillNoDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;

@Service
public class SupplierInvoiceDetailBillNoManager {

    @Resource
    private SupplierInvoiceDetailBillNoMapper supplierInvoiceDetailBillNoMapper;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insert(SupplierInvoiceDetailBillNoDO supplierInvoiceDetailBillNoDO) {
        if (Objects.isNull(supplierInvoiceDetailBillNoDO)) {
            return false;
        }
        supplierInvoiceDetailBillNoDO.init();
        int row = supplierInvoiceDetailBillNoMapper.insert(supplierInvoiceDetailBillNoDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insertBatch(List<SupplierInvoiceDetailBillNoDO> supplierInvoiceDetailBillNoDOList) {
        if (CollectionUtils.isEmpty(supplierInvoiceDetailBillNoDOList)) {
            return false;
        }
        Date date = new Date();
        supplierInvoiceDetailBillNoDOList.forEach(supplierInvoiceDetailBillNoDO -> {
            supplierInvoiceDetailBillNoDO.init(date);
        });
        int row = supplierInvoiceDetailBillNoMapper.insertBatch(supplierInvoiceDetailBillNoDOList);
        return row == supplierInvoiceDetailBillNoDOList.size();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateById(SupplierInvoiceDetailBillNoDO supplierInvoiceDetailBillNoDO) {
        if (Objects.isNull(supplierInvoiceDetailBillNoDO)) {
            return false;
        }
        if (Objects.isNull(supplierInvoiceDetailBillNoDO.getGmtUpdate())) {
            supplierInvoiceDetailBillNoDO.setGmtUpdate(System.currentTimeMillis());
        }
        int row = supplierInvoiceDetailBillNoMapper.updateById(supplierInvoiceDetailBillNoDO);
        return row > 0;
    }

    public boolean deleteBySupplierInvoiceId(Integer supplierInvoiceId) {
        if (Objects.isNull(supplierInvoiceId)) {
            return false;
        }
        return supplierInvoiceDetailBillNoMapper.deleteBySupplierInvoiceId(supplierInvoiceId) > 0;
    }

    /**
     * 第一层： k:SupplierInvoiceDetailId————v:Map<Integer,String>
     * 第二层： k:bill_type————v:bill_no
     */
    public Map<Integer, Map<Integer, String>> findDetailIdBillTypeMap(List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList) {
        if (CollectionUtils.isEmpty(supplierInvoiceDetailDOList)) {
            return new HashMap<>();
        }
        List<Integer> detailIdList =
              supplierInvoiceDetailDOList.stream().map(SupplierInvoiceDetailDO::getId).distinct().collect(Collectors.toList());
        List<SupplierInvoiceDetailBillNoDO> billNoDOList = supplierInvoiceDetailBillNoMapper.findInDetailId(detailIdList);

        Map<Integer, Map<Integer, String>> detailIdBillTypeMap = new HashMap<>();

        for (SupplierInvoiceDetailBillNoDO detailIdBillNoDO : billNoDOList) {
            Integer supplierInvoiceDetailId = detailIdBillNoDO.getSupplierInvoiceDetailId();
            Map<Integer, String> billTypeMap = detailIdBillTypeMap.get(supplierInvoiceDetailId);
            if (MapUtils.isEmpty(billTypeMap)) {
                billTypeMap = new HashMap<>();
                detailIdBillTypeMap.put(supplierInvoiceDetailId, billTypeMap);
            }
            billTypeMap.put(detailIdBillNoDO.getBillType(), detailIdBillNoDO.getBillNo());
        }
        return detailIdBillTypeMap;
    }

    public List<SupplierInvoiceDetailBillNoDO> findByBillTypeAndBillNo(SupplierInvoiceBillNoEnum billType, String billNo) {
        return supplierInvoiceDetailBillNoMapper.findByBillTypeAndBillNo(billType.getCode(), billNo);
    }
}
