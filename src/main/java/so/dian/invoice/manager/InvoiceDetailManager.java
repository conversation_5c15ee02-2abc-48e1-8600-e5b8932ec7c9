package so.dian.invoice.manager;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import so.dian.invoice.converter.InvoiceConverter;
import so.dian.invoice.converter.InvoiceDetailConverter;
import so.dian.invoice.dao.InvoiceDetailDAO;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceDetailBO;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;

/**
 * @Author: jiaoge
 * @Date: 2019/9/16 11:37 AM
 * @Description:
 */
@Component
public class InvoiceDetailManager {

    @Resource
    private InvoiceDetailDAO invoiceDetailDAO;

    public List<InvoiceDetailBO> getInvoiceDetailList(List<InvoiceBO> invoiceBOList) {
        List<InvoiceDetailDO> invoiceDetailDOList =
              invoiceDetailDAO.listInvoiceDetailByNoAndCode(InvoiceConverter.convertByBOList(invoiceBOList));
        if (CollectionUtils.isEmpty(invoiceDetailDOList)) {
            return Lists.newArrayList();
        }
        return InvoiceDetailConverter.convertDO2BO(invoiceDetailDOList);
    }

    public List<Integer> findSupplierInvoiceDetailId(List<InvoiceBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<Map<String, String>> mapList = list.stream().map(obj -> {
            Map<String, String> map = Maps.newHashMap();
            map.put("invoiceNo", obj.getInvoiceNo());
            map.put("invoiceCode", obj.getInvoiceCode() == null ? "" : obj.getInvoiceCode());
            return map;
        }).collect(Collectors.toList());
        return invoiceDetailDAO.findSupplierInvoiceDetailId(mapList);
    }
}
