package so.dian.invoice.manager;

import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import so.dian.invoice.dao.CheckInvoiceConclusionDAO;
import so.dian.invoice.pojo.entity.CheckInvoiceConclusionDO;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:06 AM
 * @Description:
 */
@Component
public class CheckInvoiceConclusionManager {

	@Resource
	private CheckInvoiceConclusionDAO checkInvoiceConclusionDAO;

	public List<CheckInvoiceConclusionDO> listConclusion() {
		return checkInvoiceConclusionDAO.selectAll();
	}

	public void insert(CheckInvoiceConclusionDO checkInvoiceConclusionDO) {
		checkInvoiceConclusionDAO.insert(checkInvoiceConclusionDO);
	}

	public Boolean update(Long id, String code,Long gmtUpdate, Long beforeGmtUpdate) {
		Integer update = checkInvoiceConclusionDAO.update(id, code, gmtUpdate, beforeGmtUpdate);
		if (update <= 0) {
			return false;
		}
		return true;
	}

	public Boolean logicDeleted(Long id) {
		Integer result = checkInvoiceConclusionDAO.logicDeleted(id, System.currentTimeMillis());
		return result > 0;
	}

	public CheckInvoiceConclusionDO getById(Long id) {
		CheckInvoiceConclusionDO conclusionDO = checkInvoiceConclusionDAO.getById(id);
		if (Objects.isNull(conclusionDO)) {
			return null;
		}
		return conclusionDO;
	}

	public CheckInvoiceConclusionDO getByCode(String code) {
		CheckInvoiceConclusionDO conclusionDO = checkInvoiceConclusionDAO.getByCode(code);
		if (Objects.isNull(conclusionDO)) {
			return null;
		}
		return conclusionDO;
	}

	public Boolean updateIsUsedById(Long id, Integer isUsed, Long gmtUpdate, Integer beforeIsUsed) {
		Integer updateResult = checkInvoiceConclusionDAO.updateIsUsedById(id, isUsed, gmtUpdate, beforeIsUsed);
		return updateResult > 0;
	}
}
