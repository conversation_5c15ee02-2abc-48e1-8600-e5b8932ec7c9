package so.dian.invoice.manager;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import so.dian.center.common.util.CollectionUtil;
import so.dian.invoice.client.manager.MailManager;
import so.dian.invoice.converter.CheckInvoiceConverter;
import so.dian.invoice.dao.CheckInvoiceDAO;
import so.dian.invoice.enums.CheckInvoiceStatusEnum;
import so.dian.invoice.pojo.bo.CheckInvoiceBO;
import so.dian.invoice.pojo.dto.CheckInvoiceDTO;
import so.dian.invoice.pojo.entity.CheckInvoiceDO;
import so.dian.invoice.pojo.entity.CheckInvoiceEmployeeDO;
import so.dian.invoice.pojo.param.InvoiceCheckPageParam;
import so.dian.invoice.pojo.vo.CheckInvoiceExportVO;
import so.dian.invoice.pojo.vo.NoCheckInvoiceExportVO;
import so.dian.invoice.util.ExcelParser;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static so.dian.invoice.constant.InvoiceConstants.*;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:04 AM
 * @Description:
 */
@Slf4j
@Component
public class CheckInvoiceManager {

	@Resource
	private CheckInvoiceDAO checkInvoiceDAO;

	@Resource
	private MailManager mailManager;

	public Long count(InvoiceCheckPageParam param) {
		return checkInvoiceDAO.count(param);
	}

	public List<CheckInvoiceBO> selectCheckInvoiceList(InvoiceCheckPageParam param) {
		param.setOffset((param.getPageNo().intValue() - 1L) * param.getPageSize());
		List<CheckInvoiceDO> checkInvoiceDOList = checkInvoiceDAO.selectCheckInvoiceList(param);
		if (CollectionUtils.isEmpty(checkInvoiceDOList)) {
			return Lists.newArrayList();
		}
		return CheckInvoiceConverter.convertDO2BO(checkInvoiceDOList);
	}

	/**
	 * 分页查询发票质检列表
	 * @param param
	 * @return
	 */
	public PageInfo<CheckInvoiceDTO> selectCheckInvoiceForPage(InvoiceCheckPageParam param) {
		PageHelper.startPage(param.getPageNo().intValue(),param.getPageSize());
		List<CheckInvoiceDTO> checkInvoiceDOList = checkInvoiceDAO.checkInvoicePage(param);
		PageInfo<CheckInvoiceDTO> invoiceDTOPageInfo = new PageInfo<CheckInvoiceDTO>(checkInvoiceDOList);
		return invoiceDTOPageInfo;
	}

	public List<CheckInvoiceBO> listCheckInvoiceDto(InvoiceCheckPageParam param) {
		List<CheckInvoiceDTO> checkInvoiceDTOS = checkInvoiceDAO.checkInvoicePage(param);
		if (CollectionUtils.isEmpty(checkInvoiceDTOS)) {
			return Lists.newArrayList();
		}
		return CheckInvoiceConverter.convertDTO2BO(checkInvoiceDTOS);
	}

	public List<CheckInvoiceBO> listCheckInvoice(InvoiceCheckPageParam param) {
		List<CheckInvoiceDO> checkInvoiceDOList = checkInvoiceDAO.listCheckInvoice(param);
		if (CollectionUtils.isEmpty(checkInvoiceDOList)) {
			return Lists.newArrayList();
		}
		return CheckInvoiceConverter.convertDO2BO(checkInvoiceDOList);
	}

	public Boolean hasUsedConclusionId(Long conclusionId) {
		Long isUsed = checkInvoiceDAO.countByConclusionId(conclusionId);
		return isUsed <= 0;
	}

	public Boolean update(CheckInvoiceDO checkInvoiceDO) {
		Integer updateResult = checkInvoiceDAO.update(checkInvoiceDO);
		return updateResult > 0;
	}

	public CheckInvoiceDO getById(Long id) {
		CheckInvoiceDO checkInvoiceDO = checkInvoiceDAO.getById(id);
		if (Objects.isNull(checkInvoiceDO)) {
			return null;
		}
		return checkInvoiceDO;
	}

	public Boolean batchInsert(List<CheckInvoiceDO> checkInvoiceDOList) {
		Integer result = checkInvoiceDAO.batchInsert(checkInvoiceDOList);
		return result > 0;
	}

	@Value("${temp.path}")
	private String tempPath;

	@Async(value = "excelExportThreadPool")
	public void exportCheckInvoiceList(List<CheckInvoiceExportVO> invoiceExportVOList, String employeeEmail, Integer status) {
		log.info("导出发票信息:email:{},Invoice:{}", employeeEmail, JSON.toJSON(invoiceExportVOList));
		String fileName = "";
		String filePath = "";
		if (CheckInvoiceStatusEnum.NOT_CHECK.getCode().equals(status)) {
			fileName = TO_BE_INSPECTION_CHECK_INVOICE_FILE_NAME + System.currentTimeMillis() + EXCEL_XLSX;
			filePath = tempPath + fileName;
			List<NoCheckInvoiceExportVO> noCheckInvoiceExportVOS = CheckInvoiceConverter.convertEVO2NEVOBO(invoiceExportVOList);
			ExcelParser.getHutoolExcelNoCheckInvoice(noCheckInvoiceExportVOS, filePath);
		} else if(CheckInvoiceStatusEnum.ALREADY_CHECK.getCode().equals(status)){
			fileName = ALREADY_INSPECTION_CHECK_INVOICE_FILE_NAME + System.currentTimeMillis() + EXCEL_XLSX;
			filePath = tempPath + fileName;
			ExcelParser.getHutoolExcelCheckInvoice(invoiceExportVOList, filePath);
		}

		try {
			mailManager.sendEmail(employeeEmail, fileName, "导出发票信息", new File(filePath));
		} catch (IOException e) {
			log.error("------ exportCheckInvoiceList mail send error, to = {}", employeeEmail, e);
		}
	}

	/**
	 * 校验该用户是否有此大区的权限
	 */
	public Boolean checkRegion(List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList, String reion){
		Boolean result = false;
		if(StringUtils.isEmpty(reion)){
			return true;
		}
		if(!CollectionUtil.isEmpty(checkInvoiceEmployeeDOList)){
			for(CheckInvoiceEmployeeDO invoiceEmployeeDO : checkInvoiceEmployeeDOList){
				if(reion.equals(invoiceEmployeeDO.getRegion())){
					result = true;
					break;
				}
			}
		}
		return result;
	}
}

