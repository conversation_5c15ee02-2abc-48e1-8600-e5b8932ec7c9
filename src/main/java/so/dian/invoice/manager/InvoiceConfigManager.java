package so.dian.invoice.manager;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.dao.InvoiceConfigDAO;
import so.dian.invoice.pojo.entity.InvoiceConfigDO;

/**
 * @Author: jiaoge
 * @Date: 2019/12/26 2:50 PM
 * @Description:
 */
@Component
@Slf4j
public class InvoiceConfigManager {

	@Resource
	private InvoiceConfigDAO invoiceConfigDAO;

	public Boolean insert(InvoiceConfigDO invoiceConfigDO) {
		Integer insert = invoiceConfigDAO.insert(invoiceConfigDO);
		return insert > 0;
	}

	public Boolean update(InvoiceConfigDO invoiceConfigDO) {
		Integer update = invoiceConfigDAO.update(invoiceConfigDO);
		return update > 0;
	}

	public InvoiceConfigDO getByKey(String key) {
		InvoiceConfigDO invoiceConfigDO = invoiceConfigDAO.getByKey(key);
		if (Objects.isNull(invoiceConfigDO)) {
			return null;
		}
		return invoiceConfigDO;
	}

	/**
	 * 可以查看所有大区的财务经理的userId列表
	 * @param key = check_invoice_affairs_admin
	 * @return
	 */
	public List<Long> getUserIdsByKey(String key) {
		List<Long> affairsAdmin = new LinkedList<>();
		try{
			InvoiceConfigDO invoiceConfigDO = invoiceConfigDAO.getByKey(key);
			if (Objects.nonNull(invoiceConfigDO) && Objects.nonNull(invoiceConfigDO.getValue())) {
				String[] strs = invoiceConfigDO.getValue().split(InvoiceConstants.COMMA);
				affairsAdmin = Arrays.asList(strs).stream().map(Long::valueOf).collect(Collectors.toList());
			}
		}catch (Exception e){
			log.error("getUserIdsByKey exception e",e);
		}
		return affairsAdmin;
	}

}
