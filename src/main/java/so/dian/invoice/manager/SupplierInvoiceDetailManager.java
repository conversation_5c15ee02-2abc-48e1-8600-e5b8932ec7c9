package so.dian.invoice.manager;

import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import so.dian.commons.eden.util.LocalObjectUtils;
import so.dian.invoice.dao.SupplierInvoiceDetailMapper;
import so.dian.invoice.enums.SupplierInvoiceBillNoEnum;
import so.dian.invoice.enums.SupplierInvoiceStatusEnum;
import so.dian.invoice.pojo.dto.BillNoDTO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailBillNoDO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.param.SupplierInvoiceDetailPageParam;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;

@Slf4j
@Service
public class SupplierInvoiceDetailManager {

    @Resource
    private SupplierInvoiceDetailMapper supplierInvoiceDetailMapper;

    @Autowired
    private SupplierInvoiceDetailBillNoManager detailBillNoManager;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insert(SupplierInvoiceDetailDO supplierInvoiceDetailDO) {
        if (Objects.isNull(supplierInvoiceDetailDO)) {
            return false;
        }
        supplierInvoiceDetailDO.init();
        int row = supplierInvoiceDetailMapper.insert(supplierInvoiceDetailDO);
        return row > 0;
    }

    public boolean insertFetchId(SupplierInvoiceDetailDO supplierInvoiceDetailDO) {
        if (Objects.isNull(supplierInvoiceDetailDO)) {
            return false;
        }
        supplierInvoiceDetailDO.init();
        int row = supplierInvoiceDetailMapper.insertFetchId(supplierInvoiceDetailDO);
        return row > 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean insertBatch(List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList) {
        if (CollectionUtils.isEmpty(supplierInvoiceDetailDOList)) {
            return false;
        }
        Date date = new Date();
        for (SupplierInvoiceDetailDO supplierInvoiceDetailDO : supplierInvoiceDetailDOList) {
            supplierInvoiceDetailDO.init(date);
        }
        int row = supplierInvoiceDetailMapper.insertBatch(supplierInvoiceDetailDOList);
        return row == supplierInvoiceDetailDOList.size();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateById(SupplierInvoiceDetailDO supplierInvoiceDetailDO) {
        if (Objects.isNull(supplierInvoiceDetailDO)) {
            return false;
        }
        if (Objects.isNull(supplierInvoiceDetailDO.getGmtUpdate())) {
            supplierInvoiceDetailDO.setGmtUpdate(System.currentTimeMillis());
        }
        int row = supplierInvoiceDetailMapper.updateById(supplierInvoiceDetailDO);
        return row > 0;
    }

    public List<SupplierInvoiceDetailDO> findListByReq(SupplierInvoiceDetailPageParam param, Set<String> supplierNos) {
        List<SupplierInvoiceDetailDO> SupplierInvoiceDetailDOList = supplierInvoiceDetailMapper.findListByReq(param, supplierNos);
        return SupplierInvoiceDetailDOList;
    }

    public Long countByReq(SupplierInvoicePageParam param, List<BillNoDTO> billNoDTOList, Set<String> supplierNos) {
        return supplierInvoiceDetailMapper.countByReq(param, billNoDTOList, supplierNos);
    }

    public List<SupplierInvoiceDetailDO> findExcelListByReq(SupplierInvoicePageParam param, List<BillNoDTO> billNoDTOList,
          Set<String> supplierNos) {
        List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList =
              supplierInvoiceDetailMapper.findExcelListByReq(param, billNoDTOList, supplierNos);
        return supplierInvoiceDetailDOList;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean deleteBySupplierInvoiceId(Integer supplierInvoiceId) {
        return supplierInvoiceDetailMapper.deleteBySupplierInvoiceId(supplierInvoiceId) > 0;
    }

    public List<SupplierInvoiceDetailDO> findListBySupplierInvoiceId(Integer supplierInvoiceId) {
        if (Objects.isNull(supplierInvoiceId)) {
            return null;
        }
        return supplierInvoiceDetailMapper.findListBySupplierInvoiceId(supplierInvoiceId);
    }

    public boolean updateStatusBySupplierInvoiceId(Integer supplierInvoiceId, SupplierInvoiceStatusEnum statusEnum) {
        if (LocalObjectUtils.anyNull(supplierInvoiceId, statusEnum)) {
            return false;
        }
        return supplierInvoiceDetailMapper.updateStatusBySupplierInvoiceId(supplierInvoiceId, statusEnum.getCode()) > 0;
    }

    public List<SupplierInvoiceDetailDO> findListById(List<Integer> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList = supplierInvoiceDetailMapper.findListById(ids);

        Map<Integer, Map<Integer, String>> detailIdBillNoListMap =
              detailBillNoManager.findDetailIdBillTypeMap(supplierInvoiceDetailDOList);

        for (SupplierInvoiceDetailDO supplierInvoiceDetailDO : supplierInvoiceDetailDOList) {
            Map<Integer, String> billTypeNoMap = detailIdBillNoListMap.get(supplierInvoiceDetailDO.getId());

            supplierInvoiceDetailDO.setPurchaseBatchBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.PURCHASE_BATCH.getCode()));
            supplierInvoiceDetailDO.setPurchaseOrderBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.PURCHASE_ORDER.getCode()));
            supplierInvoiceDetailDO.setVerifyBillNo(billTypeNoMap.get(SupplierInvoiceBillNoEnum.VERIFY.getCode()));
        }

        return supplierInvoiceDetailDOList;
    }

    public List<SupplierInvoiceDetailDO> findByBillTypeAndBillNo(SupplierInvoiceBillNoEnum billType, String billNo) {
        List<SupplierInvoiceDetailBillNoDO> billDOList =
              detailBillNoManager.findByBillTypeAndBillNo(billType, billNo);
        if (CollectionUtils.isEmpty(billDOList)) {
            return Lists.newArrayList();
        }
        List<Integer> ids =
              billDOList.stream().map(SupplierInvoiceDetailBillNoDO::getSupplierInvoiceDetailId).collect(Collectors.toList());
        return findListById(ids);
    }
}
