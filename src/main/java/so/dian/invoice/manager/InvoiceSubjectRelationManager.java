package so.dian.invoice.manager;

import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.center.common.entity.BizResult;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.commons.eden.util.LocalMapUtils;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.invoice.client.HrClient;
import so.dian.invoice.converter.SubjectRelationConverter;
import so.dian.invoice.dao.InvoiceSubjectRelationDAO;
import so.dian.invoice.enums.InvoiceSubjectRelationEnum.BizTypeEnum;
import so.dian.invoice.enums.InvoiceSubjectRelationStatusEnum;
import so.dian.invoice.pojo.bo.InvoiceSubjectRelationBO;
import so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO;
import so.dian.invoice.pojo.param.AddInvoiceSubjectRelationParam;
import so.dian.invoice.pojo.query.InvoiceSubjectRelationQuery;

/**
 * @Author: jiaoge
 * @Date: 2019/8/27 3:23 PM
 * @Description:
 */
@Service
public class InvoiceSubjectRelationManager {

    @Resource
    private InvoiceSubjectRelationDAO invoiceSubjectRelationDAO;

    @Resource
    private HrClient hrClient;

    public Long countInvoiceSubjectRelation(InvoiceSubjectRelationQuery query) {
        Long totalCount = invoiceSubjectRelationDAO.countInvoiceSubjectRelation(query);
        return totalCount;
    }

    public List<InvoiceSubjectRelationBO> selectInvoiceSubjectRelationList(InvoiceSubjectRelationQuery query) {
        List<InvoiceSubjectRelationDO> invoiceSubjectRelationDOList =
              invoiceSubjectRelationDAO.selectInvoiceSubjectRelationList(query);
        if (CollectionUtils.isEmpty(invoiceSubjectRelationDOList)) {
            return Lists.newArrayList();
        }
        return SubjectRelationConverter.convertDO2BO(invoiceSubjectRelationDOList);
    }

    public Map<Integer, AgentEmployeeDTO> getAgentEmployeeDTO(
          List<InvoiceSubjectRelationBO> invoiceSubjectRelationBOList) {
        List<Integer> employeeIdList = Lists.newArrayList();
        List<Integer> creatorIdList =
              LocalListUtils.transferList(invoiceSubjectRelationBOList, InvoiceSubjectRelationBO::getCreator);
        List<Integer> updaterIdList =
              LocalListUtils.transferList(invoiceSubjectRelationBOList, InvoiceSubjectRelationBO::getUpdater);
        employeeIdList.addAll(creatorIdList);
        employeeIdList.addAll(updaterIdList);

        BizResult<List<AgentEmployeeDTO>> employeeBizResult =
              hrClient.getByIds(SubjectRelationConverter.buildIds2DTO(employeeIdList));

        return LocalMapUtils.collectionToMap(employeeBizResult.getData(), AgentEmployeeDTO::getId);
    }

    public List<InvoiceSubjectRelationBO> listByMerchantIdAndRelationSubjectName(
          Integer merchantId, String signSubjectName) {
        List<InvoiceSubjectRelationDO> invoiceSubjectRelationDOList =
              invoiceSubjectRelationDAO.listByMerchantIdAndRelationSubjectName(merchantId, signSubjectName);
        if (CollectionUtils.isEmpty(invoiceSubjectRelationDOList)) {
            return Lists.newArrayList();
        }
        return SubjectRelationConverter.convertDO2BO(invoiceSubjectRelationDOList);
    }

    public void insert(AddInvoiceSubjectRelationParam param) {
        List<InvoiceSubjectRelationDO> relationDOList = SubjectRelationConverter.buildParam2DO(param);
        invoiceSubjectRelationDAO.batchInsert(relationDOList);
    }

    public InvoiceSubjectRelationBO getInvoiceSubjectRelationById(Integer id) {
        InvoiceSubjectRelationDO relationDO = invoiceSubjectRelationDAO.selectByPrimaryKey(id);
        if (Objects.isNull(relationDO)) {
            return null;
        }
        return SubjectRelationConverter.convertDO2BO(relationDO);
    }

    public boolean updateStatusById(Integer id, Integer userId, InvoiceSubjectRelationStatusEnum status,
          InvoiceSubjectRelationStatusEnum beforeStatus) {
        InvoiceSubjectRelationDO relationDO =
              SubjectRelationConverter.buildUpdateParam2DO(id, userId, status, beforeStatus);
        Integer updateResult = invoiceSubjectRelationDAO.updateInvoiceSubjectRelation(relationDO);
        return updateResult > 0;
    }

    /**
     * 查询商家映射主体
     */
    public List<InvoiceSubjectRelationDO> findByBizIdAndBizType(Long bizId, Integer bizType) {
        if (Objects.isNull(bizId) || Objects.isNull(bizType)) {
            return new ArrayList<>();
        }
        return invoiceSubjectRelationDAO.findByBizIdAndBizType(bizId, bizType);
    }

    /**
     * 处理特殊字符
     */
    public int trimRelation(InvoiceSubjectRelationDO record) {
        if (Objects.isNull(record)) {
            return 0;
        }
        return invoiceSubjectRelationDAO.trimRelation(record);
    }

    /**
     * 批量查询映射主体
     */
    public List<InvoiceSubjectRelationDO> batchQuery(Long lastId) {
        if (Objects.isNull(lastId)) {
            return new ArrayList<>();
        }
        return invoiceSubjectRelationDAO.batchQuery(lastId);
    }

    public List<InvoiceSubjectRelationBO> listByRelationSubjectNameAndBizType(String relationSubjectName,
            BizTypeEnum bizType) {
        List<InvoiceSubjectRelationDO> invoiceSubjectRelationDOList = invoiceSubjectRelationDAO
                .listByRelationSubjectNameAndBizType(relationSubjectName, bizType.getCode());
        return SubjectRelationConverter.convertDO2BO(invoiceSubjectRelationDOList);
    }
}
