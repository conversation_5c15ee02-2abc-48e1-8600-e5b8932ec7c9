package so.dian.invoice.manager;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import so.dian.invoice.dao.CheckInvoiceEmployeeDAO;
import so.dian.invoice.pojo.entity.CheckInvoiceEmployeeDO;

/**
 * @Author: jiaoge
 * @Date: 2019/12/25 9:06 AM
 * @Description:
 */
@Component
@Slf4j
public class CheckInvoiceEmployeeManager {

	@Resource
	private CheckInvoiceEmployeeDAO checkInvoiceEmployeeDAO;

	public List<CheckInvoiceEmployeeDO> getByUserId(Long userId) {
		List<CheckInvoiceEmployeeDO> employeeDOList = checkInvoiceEmployeeDAO.getByUserId(userId);
		if (CollectionUtils.isEmpty(employeeDOList)) {
			return Lists.newArrayList();
		}
		return employeeDOList;
	}

	public List<String> getAllRegionsDistinct() {
		List<String> regions = null;
		try {
			regions = checkInvoiceEmployeeDAO.getAllRegionsDistinct();
		} catch (Exception e) {
			log.error("getAllRegionsDistinct exception e",e);
		}
		return regions;
	}

	public Boolean batchInsert(List<CheckInvoiceEmployeeDO> checkInvoiceEmployeeDOList) {
		Integer result = checkInvoiceEmployeeDAO.batchInsert(checkInvoiceEmployeeDOList);
		if (result <= 0) {
			return false;
		}
		return true;
	}

	public CheckInvoiceEmployeeDO getById(Long id) {
		CheckInvoiceEmployeeDO employeeDO = checkInvoiceEmployeeDAO.getById(id);
		if (Objects.isNull(employeeDO)) {
			return null;
		}
		return employeeDO;
	}

	public Boolean batchLogicDeleted(List<Long> idList) {
		Integer result = checkInvoiceEmployeeDAO.batchLogicDeleted(idList);
		if (result <= 0) {
			return false;
		}
		return true;
	}

	public List<CheckInvoiceEmployeeDO> selectAll() {
		return checkInvoiceEmployeeDAO.selectAll();
	}

	public CheckInvoiceEmployeeDO getByUserIdAndRegion(Long userId,String region) {
		return checkInvoiceEmployeeDAO.getByUserIdAndRegion(userId,region);
	}

}
