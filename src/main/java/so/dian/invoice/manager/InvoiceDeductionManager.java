package so.dian.invoice.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.enums.BusinessTypeEnum;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;

@Component
public class InvoiceDeductionManager {

    @Resource
    private InvoiceDeductionDAO invoiceDeductionDAO;

    /**
     * 根据发票核销明细ID查询
     */
    public List<InvoiceDeductionDO> findByIdIn(List<Long> ids) {
        if(CollectionUtil.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return invoiceDeductionDAO.findByIdIn(ids);
    }

    /**
     * 查询核销的发票明细
     */
    public List<InvoiceDeductionDO> selectInvoiceDeductionList(String invoiceCode, String invoiceNo,
          String businessNo, BusinessTypeEnum businessType, OperateTypeEnum operateType) {
        return invoiceDeductionDAO.selectInvoiceDeductionList(businessNo, invoiceCode, invoiceNo, businessType.getType(),
              operateType.getType());
    }

    /**
     * 新增核销记录
     */
    public int insertSelective(InvoiceDeductionDO record) {
        return invoiceDeductionDAO.insertSelective(record);
    }

    public InvoiceDeductionDO selectByPrimaryKey(Integer id) {
        return invoiceDeductionDAO.selectByPrimaryKey(id);
    }

    public int recoverInvoice(Integer deductionId) {
        return invoiceDeductionDAO.recoverInvoice(deductionId, OperateTypeEnum.DEDUCT.getType(),
              OperateTypeEnum.RECOVER.getType());
    }
}
