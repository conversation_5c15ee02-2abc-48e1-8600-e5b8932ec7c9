package so.dian.invoice.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;
import so.dian.customer.dto.request.AuthenticationSubjectDTO;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.mofa3.lang.domain.Result;

import java.util.List;

@FeignClient(name = "mch-passport", fallbackFactory = PassportClient.PassportClientFallbackFactory.class)
public interface PassportClient {
    /**
     * 根据账户获取认证服务信息
     */
    @GetMapping({"/passport/authentication/queryByAccountId"})
    BizResult<List<AuthenticationSubjectDTO>> queryByAccountId(@RequestParam(value = "accountId", required = false) Long var1);

    /**
     * 通过代理商id查询关联的认证主体
     *
     * @param agentId
     * @return
     */
    @GetMapping(value = "/passport/authent/subject/queryByAgentId")
    Result<List<AuthenticationSubjectDTO>> getAuthenticationSubjectByAgentId(@RequestParam(value = "agentId", required = false) Long agentId);

    @Slf4j
    @Component
    public static class PassportClientFallbackFactory implements FallbackFactory<PassportClient> {

        @Override
        public PassportClient create(Throwable cause) {
            return new PassportClient() {
                @Override
                public BizResult<List<AuthenticationSubjectDTO>> queryByAccountId(Long var1) {
                    log.error("------ 远程服务出错，根据账户获取认证服务信息：queryByAccountId");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }
                @Override
                public Result<List<AuthenticationSubjectDTO>> getAuthenticationSubjectByAgentId(Long var1) {
                    log.error("------ 远程服务出错，根据账户获取认证服务信息：queryByAccountId");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }
            };
        }
    }
}

