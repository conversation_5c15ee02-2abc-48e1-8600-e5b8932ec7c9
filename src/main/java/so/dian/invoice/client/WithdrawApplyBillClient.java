package so.dian.invoice.client;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.invoice.client.WithdrawApplyBillClient.WithdrawApplyBillFallback;
import so.dian.withdraw.platform.dto.WithdrawApplyBillDetailResponse;

/**
 * WIthdrawApplyBillClient
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 13:56
 */

@FeignClient(name = "withdraw-platform", fallbackFactory = WithdrawApplyBillFallback.class)
public interface WithdrawApplyBillClient {

    @RequestMapping(value = "/accounts/withdrawals/detail/{applyNo}", method = RequestMethod.GET)
    @Deprecated
    BizResult<WithdrawApplyBillDetailResponse> getWithdrawApplyWithApplyNo(@PathVariable("applyNo") String applyNo);


    @Component
    static class WithdrawApplyBillFallback implements FallbackFactory<WithdrawApplyBillClient> {

        @Override
        public WithdrawApplyBillClient create(Throwable cause) {
            return new WithdrawApplyBillClient() {
                @Override
                public BizResult<WithdrawApplyBillDetailResponse> getWithdrawApplyWithApplyNo(String applyNo) {
                    return BizResult.error(ErrorCodeEnum.FALLBACK);
                }
            };
        }
    }

}
