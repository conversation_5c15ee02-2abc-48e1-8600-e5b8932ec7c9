package so.dian.invoice.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.center.common.entity.BizResult;
import so.dian.hr.api.entity.common.HrCommonDTO;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.hr.api.entity.employee.AgentEmployeeDTO;
import so.dian.hr.api.entity.employee.CityDepartmentDTO;
import so.dian.hr.api.entity.employee.DMDepartmentDTO;

import java.util.List;

/**
 * @Author: jiaoge
 * @Date: 2020/7/14 11:41 AM
 * @Description:
 */
@FeignClient(name = "hr", fallbackFactory = HrClient.HrFallbackFactory.class)
public interface HrClient {

	/**
	 * 根据id获取员工信息
	 *
	 * @param id 必传 会返回员工的权限及仓库列表信息
	 */
	@RequestMapping(value = "/employee/getById", method = RequestMethod.GET)
	BizResult<AgentEmployeeDTO> getById(@RequestParam("id") Integer id);

	/**
	 * 根据ids获取员工信息
	 *
	 * @param hrCommonDTO ids 必传，不能为空集合
	 */
	@RequestMapping(value = "/employee/getByIds", method = RequestMethod.POST)
	BizResult<List<AgentEmployeeDTO>> getByIds(@RequestBody HrCommonDTO hrCommonDTO);

	/**
	 * 根据花名获取员工信息
	 *
	 * @param nickName 必传 不会返回员工的权限及仓库列表信息
	 */
	@RequestMapping(value = "/employee/getByNickName", method = RequestMethod.GET)
	BizResult<AgentEmployeeDTO> getByNickName(@RequestParam("nickName") String nickName);

	/**
	 * 获取员工的agent信息
	 */
	@RequestMapping(value = "/employee/getAgentByUserId")
	BizResult<AgentDTO> getAgentByUserId(@RequestParam("userId") Long userId);

	/**
	 * 根据城市code集合，获取对应大区名称（批量操作）
	 *
	 * @param cityCodes 城市code集合
	 */
	@RequestMapping(value = "/department/batchGetDMNameByCityCodes", method = RequestMethod.POST)
	BizResult<List<CityDepartmentDTO>> batchGetDMNameByCityCodes(@RequestBody List<Integer> cityCodes);

	/**
	 * 大区名称和城市code
	 */
	@RequestMapping(value = "/department/getDMDepWithCitysForSelect", method = RequestMethod.GET)
	BizResult<List<DMDepartmentDTO>> getDMDepWithCitysForSelect();

	@Slf4j(topic = "error")
	@Service class HrFallbackFactory implements FallbackFactory<HrClient> {

		@Override
		public HrClient create(Throwable throwable) {
			return new HrClient() {
				@Override
				public BizResult<AgentEmployeeDTO> getById(Integer id) {
					return null;
				}

				@Override
				public BizResult<List<AgentEmployeeDTO>> getByIds(HrCommonDTO hrCommonDTO) {
					return null;
				}

				@Override
				public BizResult<AgentEmployeeDTO> getByNickName(String nickName) {
					return null;
				}

				@Override
				public BizResult<AgentDTO> getAgentByUserId(Long userId) {
					return null;
				}

				@Override
				public BizResult<List<CityDepartmentDTO>> batchGetDMNameByCityCodes(List<Integer> cityCodes) {
					return null;
				}

				@Override
				public BizResult<List<DMDepartmentDTO>> getDMDepWithCitysForSelect() {
					return null;
				}
			};
		}
	}
}
