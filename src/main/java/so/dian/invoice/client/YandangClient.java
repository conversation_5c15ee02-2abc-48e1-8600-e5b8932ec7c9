package so.dian.invoice.client;

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.yandang.client.pojo.request.PayBillBatchReq;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

@FeignClient(name = "yandang", fallbackFactory = YandangClient.YandangFallbackFactory.class)
public interface YandangClient {
    /**
     * 批量查询付款单
     */
    @PostMapping(value = "/payBill/info/batch", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<List<PayBillInfoRsp>> getPayBillInfoByBatch(@RequestBody PayBillBatchReq req);

    @Slf4j(topic = "error")
    @Service
    class YandangFallbackFactory implements FallbackFactory<YandangClient> {

        @Override
        public YandangClient create(Throwable throwable) {

            return new YandangClient() {

                @Override
                public BizResult<List<PayBillInfoRsp>> getPayBillInfoByBatch(PayBillBatchReq req) {
                    log.error("yandang fallback | getPayBillInfoByBatch | params:{}", req, throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
