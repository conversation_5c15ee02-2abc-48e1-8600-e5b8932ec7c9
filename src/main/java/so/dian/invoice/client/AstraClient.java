package so.dian.invoice.client;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.astra.client.pojo.request.MaskingDataEncryptRequest;
import so.dian.astra.client.pojo.response.MaskingDataResponse;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;

import java.util.List;
import java.util.Map;

/**
 * 脱敏数据 <br/>
 *
 * <AUTHOR>
 * @date 2024-10-17 11:20
 */
@FeignClient(name = "astra", fallbackFactory = AstraClient.AstraFallbackFactory.class)
public interface AstraClient {

    /**
     * 单个数据脱敏
     */
    @PostMapping({"/masking/encrypt"})
    BizResult<MaskingDataResponse> encrypt(@RequestBody MaskingDataEncryptRequest var1);

    /**
     * 多个数据脱敏
     * @param var1
     * @return
     */
    @PostMapping({"/masking/encrypts"})
    BizResult<Map<String, MaskingDataResponse>> encrypts(@RequestBody List<MaskingDataEncryptRequest> var1);
    @Slf4j(topic = "remote")
    @Service
    class AstraFallbackFactory implements FallbackFactory<AstraClient> {

        @Override
        public AstraClient create(Throwable throwable) {

            return new AstraClient() {

                @Override
                public BizResult<MaskingDataResponse> encrypt(MaskingDataEncryptRequest var1) {
                    log.error("astra fallback | encrypt | params:{}", JSONObject.toJSONString(var1), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Map<String, MaskingDataResponse>> encrypts(List<MaskingDataEncryptRequest> var1) {
                    log.error("astra fallback | encrypts | params:{}", JSONObject.toJSONString(var1), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
