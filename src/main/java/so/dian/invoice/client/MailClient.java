package so.dian.invoice.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.mail.dto.MailInfoParamDto;
import so.dian.mail.model.ResultEntity;

/**
 * <EMAIL>
 * 2021/6/30
 */
@FeignClient(name = "dove", fallbackFactory = so.dian.invoice.client.MailClient.MailClientFallbackFactory.class)
public interface MailClient {

    @RequestMapping(path = "/mail/send_mail", method = RequestMethod.POST)
    ResultEntity sendMail(@RequestBody MailInfoParamDto mailInfoParamDto);

    @Service
    @Slf4j
    class MailClientFallbackFactory implements FallbackFactory<MailClient> {

        @Override
        public MailClient create(Throwable throwable) {

            return new MailClient() {
                @Override
                public ResultEntity sendMail(MailInfoParamDto mailInfoParamDto) {
                    log.error("------ dove client error - sendMail");
                    throw BizException.create(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }

    }
}
