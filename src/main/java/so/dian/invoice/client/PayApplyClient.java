package so.dian.invoice.client;


import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.invoice.client.PayApplyClient.PayApplyFallbackFactory;
import so.dian.payapply.platform.dto.PayApplyDTO;

/**
 * PayApplyClient
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 14:51
 */
@FeignClient(name = "payapply-platform",fallbackFactory = PayApplyFallbackFactory.class)
public interface PayApplyClient {


    @GetMapping(value = "/payApply/info/{applyNo}")
    BizResult<PayApplyDTO> getDetailByApplyNo(@PathVariable(name = "applyNo") String applyNo);

    @Component
    static class PayApplyFallbackFactory implements FallbackFactory<PayApplyClient> {

        @Override
        public PayApplyClient create(Throwable cause) {
            return new PayApplyClient() {
                @Override
                public BizResult<PayApplyDTO> getDetailByApplyNo(String applyNo) {
                    return BizResult.error(ErrorCodeEnum.FALLBACK);
                }
            };
        }
    }

}
