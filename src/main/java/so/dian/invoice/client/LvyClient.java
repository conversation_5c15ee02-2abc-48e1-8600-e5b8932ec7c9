package so.dian.invoice.client;


import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.lvy.pojo.dto.InvoiceInfoDTO;
import so.dian.lvy.pojo.dto.WithdrawApplyBatchAuditListDTO;
import so.dian.lvy.pojo.query.WithdrawBatchAuditListQuery;
import so.dian.lvy.pojo.query.WithdrawInvoiceListQuery;
import so.dian.lvy.pojo.query.WithdrawListQuery;

import java.util.List;


@FeignClient(name = "lvy", fallback = LvyClient.LvyClientFallback.class)
public interface LvyClient  {

    /**
     * (发票台账)发票列表查询
     * @param withdrawInvoiceListQuery
     * @return
     */
    @RequestMapping(value = "/withdrawals/invoices/gets", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<List<InvoiceInfoDTO>> getInvoicelist(@RequestBody WithdrawInvoiceListQuery withdrawInvoiceListQuery);

    /**
     * 获取批量审核操作列表
     * @param withdrawBatchAuditListQuery
     * @return
     */
    @RequestMapping(value = "/withdrawals/batchAudit/gets", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<List<WithdrawApplyBatchAuditListDTO>> queryBatchAuditWithdrawList(@RequestBody WithdrawBatchAuditListQuery withdrawBatchAuditListQuery);

    /**
     * 根据关键字前置模糊匹配查询提现主体列表
     * @param withdrawListQuery
     * @return
     */
    @RequestMapping(value = "/withdrawals/subjectName/gets", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<List<String>> queryWithdrawSubjectNameList(@RequestBody WithdrawListQuery withdrawListQuery);


    @Slf4j
    @Service
    class LvyClientFallback implements LvyClient {
        @Override
        public BizResult<List<InvoiceInfoDTO>> getInvoicelist(WithdrawInvoiceListQuery withdrawInvoiceListQuery) {
            log.error("LvyClient-->getInvoicelist fall");
            return BizResult.error(ErrorCodeEnum.FALLBACK);
        }

        @Override
        public BizResult<List<WithdrawApplyBatchAuditListDTO>> queryBatchAuditWithdrawList(@RequestBody WithdrawBatchAuditListQuery withdrawBatchAuditListQuery) {
            log.error("LvyClient-->queryBatchAuditWithdrawList fall");
            return BizResult.error(ErrorCodeEnum.FALLBACK);
        }

        @Override
        public BizResult<List<String>> queryWithdrawSubjectNameList(@RequestBody WithdrawListQuery withdrawListQuery) {
            log.error("LvyClient-->queryWithdrawSubjectNameList fall");
            return BizResult.error(ErrorCodeEnum.FALLBACK);
        }
    }
}
