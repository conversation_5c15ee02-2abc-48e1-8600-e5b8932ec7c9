package so.dian.invoice.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.virgo.client.dto.AreaInfoDTO;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2022/11/04 16:55
 * @description:
 */
@FeignClient(value = "virgo", fallbackFactory = VirgoClient.VirgoFallbackFactory.class)
public interface VirgoClient {

    @GetMapping({"/getByCode"})
    BizResult<AreaInfoDTO> getByCode(@RequestParam("code") Integer code);

    @Service
    @Slf4j
    class VirgoFallbackFactory implements FallbackFactory<VirgoClient> {

        @Override
        public VirgoClient create(Throwable throwable) {

            return new VirgoClient() {

                @Override
                public BizResult<AreaInfoDTO> getByCode(Integer code) {
                    log.error("------ virgo client error - getByCode");
                    throw BizException.create(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }

    }
}
