package so.dian.invoice.client;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.mofa3.lang.domain.Result;
import so.dian.newyork.center.client.v5.req.TradeOrderQueryV5Req;
import so.dian.newyork.center.client.v5.resp.TradeOrderQueryV5Resp;

@FeignClient(name = "newyork-center", fallbackFactory = TradeV5Client.TradeV5CFallbackFactory.class)
public interface TradeV5Client {

    /**
     * 交易单查询
     * @param req 参数
     * @return 返回
     */
    @PostMapping(value = "/newyork-center/trade/V5/tradeQuery", consumes = MediaType.APPLICATION_JSON_VALUE)
    Result<TradeOrderQueryV5Resp> tradeQuery(@RequestBody TradeOrderQueryV5Req req);

    @Slf4j(topic = "remote")
    @Service
    class TradeV5CFallbackFactory implements FallbackFactory<TradeV5Client> {

	@Override
	public TradeV5Client create(Throwable throwable) {

	    return new TradeV5Client() {
		@Override
		public Result<TradeOrderQueryV5Resp> tradeQuery(TradeOrderQueryV5Req req){
		    log.error("astra fallback | encrypts | params:{}", JSONObject.toJSONString(req), throwable);
		    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
		}
	    };
	}
    }
}
