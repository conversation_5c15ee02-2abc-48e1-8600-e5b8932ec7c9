package so.dian.invoice.client;

import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;

/**
 * @Author: jiaoge
 * @Date: 2019/9/4 5:47 PM
 * @Description:
 */
@FeignClient(name = "leo", url = "${leo.baseUrl}", fallback = LeoClient.LeoClientFallback.class)
public interface LeoClient {

	@RequestMapping(value = "/1.0/merchant/subjectName/toInvoice", method = RequestMethod.GET)
	BizResult<String> getMerchantNameByIdToInvoice(@RequestParam("merchantId") Long merchantId);

	@RequestMapping(value = "/city/user/list", method = RequestMethod.GET)
	BizResult<Set<Long>> listCityUserByRegion(@RequestParam("regionList")List<String> regionList);

	@Service
	@Slf4j(topic = "error")
	class LeoClientFallback implements LeoClient {

		@Override
		public BizResult<String> getMerchantNameByIdToInvoice(Long merchantId) {
			log.error("获取商户认证主体名称失败,merchantId:{}", merchantId);
			return BizResult.error(ErrorCodeEnum.FALLBACK);
		}

		@Override
		public BizResult<Set<Long>> listCityUserByRegion(List<String> regionList) {
			log.error("获取城市行政idList失败,regionList:{}", regionList);
			return BizResult.error(ErrorCodeEnum.FALLBACK);
		}
	}
}
