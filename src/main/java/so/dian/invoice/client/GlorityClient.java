package so.dian.invoice.client;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.invoice.configuration.GlorityFeignConfiguration;
import so.dian.invoice.pojo.dto.identify.InvoiceResultDTO;
import so.dian.invoice.pojo.param.GlorityDefaultParam;
import so.dian.invoice.pojo.param.GlorityFullPowerParam;
import so.dian.invoice.pojo.param.GlorityParam;

@FeignClient(name = "glority", url = "${glority.host}", configuration = GlorityFeignConfiguration.class, fallbackFactory = GlorityClient.GlorityClientFallback.class)
public interface GlorityClient {
    @PostMapping("/v1/item/fapiao_validation")
    InvoiceResultDTO invoiceValidation(@RequestBody GlorityParam param);

    @PostMapping("/v1/item/fapiao_validation")
    InvoiceResultDTO invoiceFullPowerValidation(@RequestBody GlorityFullPowerParam param);

    @PostMapping("/v1/item/fapiao_validation")
    InvoiceResultDTO invoiceValidDefaultation(@RequestBody GlorityDefaultParam param);

    @Slf4j
    @Service
    class GlorityClientFallback implements FallbackFactory<GlorityClient> {

        @Override
        public GlorityClient create(Throwable throwable) {
            return new GlorityClient() {
                @Override
                public InvoiceResultDTO invoiceValidation(GlorityParam param) {
                    log.error("GlorityClient-->invoiceValidation fall:{}", JSONObject.toJSONString(param));
                    return null;
                }

                @Override
                public InvoiceResultDTO invoiceFullPowerValidation(GlorityFullPowerParam param) {
                    log.error("GlorityClient-->invoiceFullPowerValidation fall:{}", JSONObject.toJSONString(param));
                    return null;
                }

                @Override
                public InvoiceResultDTO invoiceValidDefaultation(GlorityDefaultParam param) {
                    log.error("GlorityClient-->invoiceValidDefaultation fall:{}", JSONObject.toJSONString(param));
                    return null;
                }
            };
        }
    }
}
