package so.dian.invoice.client.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.client.PayApplyClient;
import so.dian.payapply.platform.dto.PayApplyDTO;

/**
 * PayApplyManager
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 14:53
 */
@Component
public class PayApplyManager {

    @Autowired
    private PayApplyClient payApplyClient;


    public PayApplyDTO getByApplyNo(String applyNo) {
        BizResult<PayApplyDTO> result = payApplyClient.getDetailByApplyNo(applyNo);
        return result.getData();
    }

}
