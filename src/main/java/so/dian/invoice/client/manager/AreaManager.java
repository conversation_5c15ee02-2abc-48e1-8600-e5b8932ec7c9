package so.dian.invoice.client.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.client.VirgoClient;
import so.dian.virgo.client.dto.AreaInfoDTO;

import javax.validation.constraints.NotNull;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/11/04 16:59
 * @description:
 */
@Component
public class AreaManager {

    @Autowired
    private VirgoClient virgoClient;

    public AreaInfoDTO get(@NotNull Integer code) {

        BizResult<AreaInfoDTO> result = virgoClient.getByCode(code);
        if (null == result || !result.isSuccess()) {
            return null;
        }

        return result.getData();
    }
}
