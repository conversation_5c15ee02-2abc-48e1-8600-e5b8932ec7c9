package so.dian.invoice.client.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.invoice.client.WithdrawApplyBillClient;
import so.dian.withdraw.platform.dto.WithdrawApplyBillDetailResponse;

/**
 * WithdrawApplyBillManager
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 14:16
 */
@Component
public class WithdrawApplyBillManager {

    @Autowired
    private WithdrawApplyBillClient withdrawApplyBillClient;


    public WithdrawApplyBillDetailResponse getByApplyNo(String applyNo) {
        BizResult<WithdrawApplyBillDetailResponse> result = withdrawApplyBillClient.getWithdrawApplyWithApplyNo(
                applyNo);
        return result.getData();
    }

}
