package so.dian.invoice.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.jinyun.client.pojo.request.scm.SupplierBaseListReq;
import so.dian.jinyun.client.pojo.response.scm.PurchaseOrderDetailRsp;
import so.dian.jinyun.client.pojo.response.scm.SCMSupplierRsp;

import java.util.List;

/**
 * @Author: chenan
 * @Date: 2020/9/21 11:02
 */
@FeignClient(name = "jinyun", fallbackFactory = SupplierClient.SupplierFallbackFactory.class)
public interface SupplierClient {

    /**
     * 供应商查询
     * @param supplierNo 供应商编号 必填
     * @return
     */
    @RequestMapping(value = "/scm/supplier/supplierNo", method = RequestMethod.GET)
    BizResult<SCMSupplierRsp> getSupplierBySupplierNo(@RequestParam("supplierNo") String supplierNo);

    /**
     * 供应商查询
     * @param mobile 手机号 必填
     * @return
     */
    @RequestMapping(value = "/scm/supplier/mobile", method = RequestMethod.GET)
    BizResult<List<SCMSupplierRsp>> getSupplierByMobile(@RequestParam("mobile") String mobile);

    /**
     * 供应商查询 - 批量
     */
    @RequestMapping(value = "/scm/supplier/baseList", method = RequestMethod.POST)
    BizResult<List<SCMSupplierRsp>> getSupplierList(@RequestBody SupplierBaseListReq supplierBaseListReq);

    /**
     * 采购订单查询
     * @param purchaseNo
     * @return
     */
    @GetMapping(value = "/purchase/order/query/{purchaseNo}")
    BizResult<PurchaseOrderDetailRsp> getPurchaseOrderByPurchaseNo(@PathVariable("purchaseNo") String purchaseNo);

    /**
     * 根据平台供应商ID查询供应商信息
     * @param platformSupplierId 平台供应商ID(工厂作业平台，也称为供应商平台)
     * @return 供应商信息
     */
    @GetMapping("/scm/supplier/getByPlatformSupplierId")
    BizResult<SCMSupplierRsp> getByPlatformSupplierId(@RequestParam("platformSupplierId") Long platformSupplierId);

    @Service
    @Slf4j(topic = "error")
    class SupplierFallbackFactory implements FallbackFactory<SupplierClient> {

        @Override
        public SupplierClient create(Throwable throwable) {

            return new SupplierClient() {

                @Override
                public BizResult<SCMSupplierRsp> getSupplierBySupplierNo(String supplierNo) {
                    log.error("------ 远程服务出错，供应商接口：getSupplierBySupplierNo");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }

                @Override
                public BizResult<List<SCMSupplierRsp>> getSupplierByMobile(String mobile) {
                    log.error("------ 远程服务出错，供应商接口：getSupplierByMobile");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }

                @Override
                public BizResult<List<SCMSupplierRsp>> getSupplierList(SupplierBaseListReq supplierBaseListReq) {
                    log.error("------ 远程服务出错，供应商接口：getSupplierList");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }

                @Override
                public BizResult<PurchaseOrderDetailRsp> getPurchaseOrderByPurchaseNo(String purchaseNo) {
                    log.error("------ 远程服务出错，供应商接口：getPurchaseOrderByPurchaseNo");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }

                @Override
                public BizResult<SCMSupplierRsp> getByPlatformSupplierId(Long platformSupplierId) {
                    log.error("------ 远程服务出错，供应商接口：getByPlatformSupplierId");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }
            };
        }


    }
}
