package so.dian.invoice.client;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.wutai.client.pojo.dto.OperatorLogCreateRspDTO;
import so.dian.wutai.client.pojo.dto.OperatorLogUpdateRspDTO;

/**
 * 脱敏数据 <br/>
 *
 * <AUTHOR>
 * @date 2024-10-17 11:20
 */
@FeignClient(name = "wutai", fallbackFactory = WutaiClient.WutaiFallbackFactory.class)
public interface WutaiClient {
    @PostMapping(value = "/operator/log/create")
    BizResult<Long> operatorLogCreate(@RequestBody OperatorLogCreateRspDTO operatorLogCreateRspDTO);

    @PostMapping(value = "/operator/log/update")
    BizResult<Boolean> operatorLogUpdate(@RequestBody OperatorLogUpdateRspDTO operatorLogUpdateRspDTO);

    @Slf4j(topic = "remote")
    @Service
    class WutaiFallbackFactory implements FallbackFactory<WutaiClient> {

        @Override
        public WutaiClient create(Throwable throwable) {

            return new WutaiClient() {

                @Override
                public BizResult<Long> operatorLogCreate(OperatorLogCreateRspDTO operatorLogCreateRspDTO) {
                    log.error("wutai fallback | operatorLogCreate | params:{}", JSONObject.toJSONString(operatorLogCreateRspDTO), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<Boolean> operatorLogUpdate(OperatorLogUpdateRspDTO operatorLogUpdateRspDTO) {
                    log.error("wutai fallback | operatorLogUpdate | params:{}", JSONObject.toJSONString(operatorLogUpdateRspDTO), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
