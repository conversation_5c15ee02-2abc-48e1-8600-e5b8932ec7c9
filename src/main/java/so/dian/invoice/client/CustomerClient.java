package so.dian.invoice.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.center.common.entity.BizResult;
import so.dian.customer.dto.CustomerAccountDTO;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.enums.error.InvoiceCodeEnum;

@FeignClient(name = "customer", fallbackFactory = CustomerClient.CustomerFallbackFactory.class)
public interface CustomerClient {
    /**
     * 根据公司类型和agentId获取用户信息
     */
    @GetMapping("/customer/account/getByReferId")
    BizResult<CustomerAccountDTO> getByReferId(@RequestParam("referType") Integer referType, @RequestParam("referId") Long referId);

    @Slf4j(topic = "remote")
    @Service
    public class CustomerFallbackFactory implements FallbackFactory<CustomerClient> {

        @Override
        public CustomerClient create(Throwable cause) {
            return new CustomerClient() {
                @Override
                public BizResult<CustomerAccountDTO> getByReferId(Integer referType, Long referId) {
                    log.error("------ 远程服务出错，根据公司类型和agentId获取用户信息：getByReferId");
                    throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
                }
            };
        }
    }
}

