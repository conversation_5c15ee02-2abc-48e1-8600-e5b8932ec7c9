package so.dian.invoice.client;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.tiantai.client.pojo.request.spu.SpuDetailReq;
import so.dian.tiantai.client.pojo.response.remote.spu.SpuSimpleRsp;

import java.util.List;

/**
 * 脱敏数据 <br/>
 *
 * <AUTHOR>
 * @date 2024-10-17 11:20
 */
@FeignClient(name = "tiantai", fallbackFactory = TiantaiClient.TiantaiFallbackFactory.class)
public interface TiantaiClient {

    /**
     * 查询产品信息
     */
    @GetMapping({"/spu/simple/detail/{spuCode}"})
    BizResult<SpuSimpleRsp> spuSimpleDetail(@PathVariable("spuCode") String spuCode);

    /**
     * 产品简详情列表查询
     */
    @PostMapping(value = "/spu/simple/detail/listNew")
    BizResult<List<SpuSimpleRsp>> spuSimpleDetailNew(@RequestBody SpuDetailReq req);

    @Slf4j(topic = "remote")
    @Service
    class TiantaiFallbackFactory implements FallbackFactory<TiantaiClient> {

        @Override
        public TiantaiClient create(Throwable throwable) {

            return new TiantaiClient() {

                @Override
                public BizResult<SpuSimpleRsp> spuSimpleDetail(String spuCode){
                    log.error("astra fallback | encrypts | params:{}", JSONObject.toJSONString(spuCode), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }

                @Override
                public BizResult<List<SpuSimpleRsp>> spuSimpleDetailNew(SpuDetailReq spuCodes){
                    log.error("astra fallback | encrypts | params:{}", JSONObject.toJSONString(spuCodes), throwable);
                    return BizResult.error(BaseErrorCodeEnum.FALLBACK);
                }
            };
        }
    }
}
