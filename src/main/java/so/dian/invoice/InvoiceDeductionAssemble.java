package so.dian.invoice;

import static so.dian.invoice.constant.InvoiceConstants.MANUAL_DEDUCT_BUSINESS_NO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import so.dian.invoice.enums.OperateTypeEnum;
import so.dian.invoice.pojo.bo.UserBO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;

/**
 * InvoiceDeductionAssemble
 *
 * <AUTHOR>
 * @desc
 * @date 2023/5/8 16:57
 */
public class InvoiceDeductionAssemble {

    public static InvoiceDeductionDO assembleDO(UserBO userBO, InvoiceDeductionDO invoiceDeductionDO) {

        InvoiceDeductionDO rollbackDO = new InvoiceDeductionDO();
        rollbackDO.setInvoiceCode(
                invoiceDeductionDO.getInvoiceCode() == null ? "" : invoiceDeductionDO.getInvoiceCode());
        rollbackDO.setInvoiceNo(invoiceDeductionDO.getInvoiceNo());
        rollbackDO.setBusinessNo(invoiceDeductionDO.getBusinessNo());
        rollbackDO.setBusinessType(invoiceDeductionDO.getBusinessType());
        rollbackDO.setOperateType(OperateTypeEnum.RECOVER.getType());

        rollbackDO.setCreator(ObjectUtils.defaultIfNull(Long.valueOf(userBO.getUserId()), 0L));
        rollbackDO.setCreateName(StringUtils.defaultIfBlank(userBO.getNickName(), ""));
        rollbackDO.setAmount(invoiceDeductionDO.getAmount());
        String businessNo = Objects.equals(invoiceDeductionDO.getBusinessNo(),
                MANUAL_DEDUCT_BUSINESS_NO) ? "-" : invoiceDeductionDO.getBusinessNo();
        String reason = StringUtils.truncate(
                "业务单号:" + businessNo + ",回滚金额:"
                        + invoiceDeductionDO.getAmount().setScale(2, RoundingMode.HALF_UP), 250);
        rollbackDO.setReason(StringUtils.defaultIfBlank(reason, ""));
        return rollbackDO;
    }

}
