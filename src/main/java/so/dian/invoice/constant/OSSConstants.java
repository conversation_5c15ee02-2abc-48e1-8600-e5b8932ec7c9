package so.dian.invoice.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OSSConstants {

    public static String ACCESS_KEY_ID;

    public static String ACCESS_KEY_SECRET;

    public static String BUCKET_NAME;

    public static String END_POINT;


    @Value("${aliyun.oss.accessKeyId}")
    public void setAccessKeyId(String accessKeyId) {
        OSSConstants.ACCESS_KEY_ID = accessKeyId;
    }

    @Value("${aliyun.oss.accessKeySecret}")
    public void setAccessKeySecret(String accessKeySecret) {
        OSSConstants.ACCESS_KEY_SECRET = accessKeySecret;
    }

    @Value("${aliyun.oss.bucketName}")
    public void setBucketName(String bucketName) {
        OSSConstants.BUCKET_NAME = bucketName;
    }

    @Value("${aliyun.oss.endPoint}")
    public void setEndPoint(String endPoint) {
        OSSConstants.END_POINT = endPoint;
    }

}