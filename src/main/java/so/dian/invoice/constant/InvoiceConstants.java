package so.dian.invoice.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 常量类
 */
public class InvoiceConstants {

    /**
     * 友电/小电发票归属ID
     */
    public static final Set<Integer> DIAN_INVOICE_BELONG_ID = Sets.newHashSet(0, 1, 3, 4, 5);


    /**
     * 手动核销发票：默认业务编号
     */
    public static final String MANUAL_DEDUCT_BUSINESS_NO = "00000000";

    /**
     * 导出发票台账文件名称
     */
    public static final String INVOICE_FILE_NAME = "发票台账-";

    public static final String EXCEL_XLSX = ".xlsx";

    /**
     * 导出发票台账文件名称
     */
    public static final String TO_BE_INSPECTION_CHECK_INVOICE_FILE_NAME = "待质检发票-";

    public static final String ALREADY_INSPECTION_CHECK_INVOICE_FILE_NAME = "已质检发票-";

    public static final Integer INVOICE_EXPORT_COUNT = 40000;

    public static final Integer BATCH_INVOICE_SIZE = 100;

    public static final Integer BATCH_INVOICE_SLEEP_TIME = 100;

    public static final String COMMA = ",";

    /**
     * 开票申请单导出结果
     */
    public final static String EXPORT_RESULT_TEMPLATE = "开票申请单导出成功，共导出%d条记录";

    public static String PASSWORD = "xiaodian@123@456";

    /**
     * 数据来源于裴杉
     * CREATED(0, "已创建"),
     * WAIT_PAY(1, "待付款"),
     * WAIT_SHIP(2, "待发货"),
     * WAIT_RECEIVE(3, "待收货"),
     * COMPLETED(4, "交易完成"),
     * CLOSED(5, "交易关闭"),
     * WAIT_APPROVAL(6, "待审核"),
     */
    public static final int WAIT_APPROVAL = 6;
    /**
     * CLOSED(5, "交易关闭"),
     */
    public static final int CLOSED = 5;

    public static List<String> typeList = Lists
            .newArrayList(InvoiceTypeEnum.VAT.getBizType(), InvoiceTypeEnum.ORD.getBizType(),
                    InvoiceTypeEnum.ORD_ELECTR.getBizType(), InvoiceTypeEnum.ORD_ROLL.getBizType(),
                    InvoiceTypeEnum.MACHINE.getBizType(), InvoiceTypeEnum.QUOTA.getBizType(),
                    InvoiceTypeEnum.RAODTOLL.getBizType(), InvoiceTypeEnum.TAXI.getBizType(),
                    InvoiceTypeEnum.TRAIN.getBizType(), InvoiceTypeEnum.VEHICLE.getBizType(),
                    InvoiceTypeEnum.PASSCAR.getBizType(), InvoiceTypeEnum.AVIATION.getBizType(),
                    InvoiceTypeEnum.IST.getBizType(), InvoiceTypeEnum.IBT.getBizType(),
                    InvoiceTypeEnum.FULL_POWER_MAJOR.getBizType(),InvoiceTypeEnum.FULL_POWER_GENERAL.getBizType());

    public static List<InvoiceTypeEnum> NEED_VALIDATE_INVOICE_TYPE_ENUM_LIST = Lists
            .newArrayList(InvoiceTypeEnum.VAT, InvoiceTypeEnum.ORD, InvoiceTypeEnum.ORD_ELECTR,
                    InvoiceTypeEnum.ORD_ROLL,InvoiceTypeEnum.FULL_POWER_MAJOR,InvoiceTypeEnum.FULL_POWER_GENERAL);

    public static List<String> NEED_VALIDATE_INVOICE_BIZ_TYPE = LocalListUtils
            .transferList(NEED_VALIDATE_INVOICE_TYPE_ENUM_LIST, InvoiceTypeEnum::getBizType);

    /**
     * 发票OCR识别的队列
     */
    public static final String INVOICE_OCR_QUEUE = "invoice_ocr_queue";



}
