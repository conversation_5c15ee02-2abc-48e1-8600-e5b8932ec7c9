package so.dian.invoice.constant;

import java.util.regex.Pattern;

/**
 * RegexConstant
 *
 * <AUTHOR>
 * @desc 正则常量
 * @date 2019-09-08
 */
public class RegexConstant {

    /**
     * 匹配数字字符串
     */
    public final static String NUMBER_FORMAT = "[0-9]*";

    /**
     * 匹配数字字符串
     */
    public final static Pattern NUMBER_PATTERN = Pattern.compile(NUMBER_FORMAT);

    /**
     * 匹配单个空格、tab、空行...
     */
    public static final String BLANK_FORMAT = "\\s*|\t|\r|\n";

    /**
     * 匹配单个空格、tab、空行...
     */
    public static final Pattern BLANK_PATTERN = Pattern.compile(BLANK_FORMAT);

    /**
     * 多空行、空白匹配...
     */
    public final static String EMPTY_FORMAT = "[\\s\n ]+";

    /**
     * 多空行、空白匹配...
     */
    public final static Pattern EMPTY_PATTERN = Pattern.compile(EMPTY_FORMAT);

    /**
     * SQL占位符?匹配
     */
    public final static String SQL_PLACEHOLDER_FORMAT = "\\?";

    /**
     * SQL占位符?匹配
     */
    public final static Pattern SQL_PLACEHOLDER_PATTERN = Pattern.compile(SQL_PLACEHOLDER_FORMAT);


}
