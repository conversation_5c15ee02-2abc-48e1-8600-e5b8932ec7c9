package so.dian.invoice.constant;

import java.math.BigDecimal;

/**
 * @Author: jiaoge
 * @Date: 2019/12/26 2:46 PM
 * @Description: 发票质检相关常量类
 */
public class CheckInvoiceConstants {

	/**
	 * 发票质检比例KEY
	 */
	public static final String CHECK_INVOICE_CHECK_RATE = "check.invoice.checkRate";

	/**
	 * 发票质检管理员KEY
	 */
	public static final String CHECK_INVOICE_ADMIN = "check.invoice.admin";

	/**
	 * 发票质检结论长度
	 */
	public static final Integer CONCLUSION_LENGTH = 10;

	public static final BigDecimal DEFAULT_CHECK_INVOICE_CHECK_RATE = BigDecimal.valueOf(1);

	public static final Long CHECK_INVOICE_EXPORT_LENGTH_LIMIT = 10000L;

	public static final Long CHECK_INVOICE_ADMIN_USER = 9398L;

	/**
	 * 该配置下的用户可以查看所有大区的质检列表
	 */
	public static final String CHECK_INVOICE_AFFAIRS_ADMIN = "check_invoice_affairs_admin";

}
