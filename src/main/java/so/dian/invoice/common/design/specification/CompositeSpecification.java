package so.dian.invoice.common.design.specification;

/**
 * AbstractSpecification
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 16:27
 */
public abstract class CompositeSpecification<T> implements Specification<T> {

    public CompositeSpecification<T> or(Specification<T> specification) {
        return new OrSpecification<T>(this, specification);
    }

    public CompositeSpecification<T> and(Specification<T> specification) {
        return new AndSpecification<T>(this, specification);
    }

    public CompositeSpecification<T> not() {
        return new NotSpecification<T>(this);
    }
}
