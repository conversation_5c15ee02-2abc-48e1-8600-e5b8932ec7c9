package so.dian.invoice.common.design.specification;

/**
 * AndSpecification
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 16:33
 */
public class AndSpecification<T> extends CompositeSpecification<T> {

    private final Specification<T> left;

    private final Specification<T> right;

    public AndSpecification(Specification<T> left, Specification<T> right) {
        this.left = left;
        this.right = right;
    }

    @Override
    public boolean isSatisfiedBy(T t) {
        return left.isSatisfiedBy(t) && right.isSatisfiedBy(t);
    }
}
