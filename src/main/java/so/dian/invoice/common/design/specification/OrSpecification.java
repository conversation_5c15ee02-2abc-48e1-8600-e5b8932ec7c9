package so.dian.invoice.common.design.specification;

/**
 * OrSpecification
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 16:28
 */
public class OrSpecification<T> extends CompositeSpecification<T> {

    public final Specification<T> left;

    private final Specification<T> right;

    public OrSpecification(Specification<T> left, Specification<T> right) {
        this.left = left;
        this.right = right;
    }

    @Override
    public boolean isSatisfiedBy(T t) {
        return left.isSatisfiedBy(t) || right.isSatisfiedBy(t);
    }
}
