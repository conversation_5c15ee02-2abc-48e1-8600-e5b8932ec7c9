package so.dian.invoice.common.design.specification;

/**
 * NotSpecification
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 16:32
 */
public class NotSpecification<T> extends CompositeSpecification<T> {

    private final Specification<T> specification;

    public NotSpecification(Specification<T> specification) {
        this.specification = specification;
    }

    @Override
    public boolean isSatisfiedBy(T t) {
        return !specification.isSatisfiedBy(t);
    }
}
