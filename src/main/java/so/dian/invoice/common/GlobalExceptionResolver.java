package so.dian.invoice.common;

import com.alibaba.fastjson.JSON;
import com.meidalife.common.exception.*;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;
import so.dian.invoice.pojo.vo.BaseVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.text.MessageFormat;

@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
public class GlobalExceptionResolver implements HandlerExceptionResolver {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionResolver.class);
    private static Logger bizLog = LoggerFactory.getLogger("biz");
    private static Logger errLog = LoggerFactory.getLogger("err");
    private static Logger daoLog = LoggerFactory.getLogger("dao");
    private static Logger rpcLog = LoggerFactory.getLogger("rpc");

    private static final String FORMAT_PARAM_INVALID = "[{0}:{1}]";

//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(so.dian.himalaya.common.exception.BizException.class)
//    public BizResult<String> handleBizException(so.dian.himalaya.common.exception.BizException e) {
//        log.error("业务异常: {}", e.getMessage(), e);
//        return BizResult.error(e.getCode(), e.getMessage());
//    }

    @Override
    @ExceptionHandler(Exception.class)
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        String data = request.getRequestURI() + " data= " + request.getParameter("data");
        logException(ex, data);
        BaseVO baseVO;
        if (ex instanceof BaseException) {
            baseVO = new BaseVO(((BaseException) ex).getExcsEnum().getMessage(),((BaseException) ex).getExcsEnum().getCode(),false,null);
        } else if (ex instanceof so.dian.commons.eden.exception.BizException) {
            baseVO = new BaseVO(((so.dian.commons.eden.exception.BizException) ex).getMsg(),((so.dian.commons.eden.exception.BizException) ex).getCode(),false,null);
        }else if(ex instanceof MethodArgumentNotValidException){
            MethodArgumentNotValidException validException = (MethodArgumentNotValidException)ex;
            StringBuilder msg = joinErrorMsg(validException.getBindingResult());
            baseVO = new BaseVO(msg.toString(),CommonExcs.CHECK_NULL.getCode(),false,null);
        } else if (ex instanceof so.dian.himalaya.common.exception.BizException) {
            String code = ((so.dian.himalaya.common.exception.BizException) ex).getCode();
            baseVO = new BaseVO(((so.dian.himalaya.common.exception.BizException) ex).getMsg(), code,false,null);
        } else {
            baseVO = new BaseVO(CommonExcs.INTERNAL_ERROR.getMessage(),CommonExcs.INTERNAL_ERROR.getCode(),false,null);
        }
        PrintWriter out = null;
        try {
            response.setContentType("application/json; charset=utf-8");
            //这里必须返回sc_ok=200
            response.setStatus(HttpServletResponse.SC_OK);
            out = response.getWriter();
            out.println(handleJsonOrJsonp(request.getParameter("callback"), JSON.toJSONString(baseVO)));
            out.flush();
        } catch (Exception e) {
            log.error("Output Response exception", e);
        } finally {
            if(out != null){
                out.close();
            }
        }
        return new ModelAndView();
    }

    /**
     * 组装拼接参数验证 @valid 错误信息
     *
     * @param bindingResult 绑定结果
     */
    private static StringBuilder joinErrorMsg(BindingResult bindingResult) {
        StringBuilder msg = new StringBuilder();
        bindingResult.getFieldErrors().forEach(err -> msg.append(
                MessageFormat.format(FORMAT_PARAM_INVALID, err.getField(), err.getDefaultMessage())));
        return msg;
    }

    private String handleJsonOrJsonp(String jsonpCallback, String result) {
        if (StringUtils.isNotBlank(result)) {
            result = result.replaceAll("\t", " ");
        }
        if (jsonpCallback != null) {
            jsonpCallback = StringEscapeUtils.escapeHtml(jsonpCallback);
            result = jsonpCallback + "(" + result + ")";
        }
        return result;
    }

    private void logException(Exception ex, String data) {
        if (ex instanceof ValidateException) {
            errLog.error("ValidateException#Input parameters:" + data, ex);
        } else if (ex instanceof DosException) {
            bizLog.warn("DosException#Input parameters:" + data, ex);
        } else if (ex instanceof DaoException) {
            daoLog.error("DaoException#Input parameters: " + data, ex);
        } else if (ex instanceof BizException) {
            bizLog.warn("BizException#Input parameters: " + data, ex);
        } else if (ex instanceof RpcException) {
            rpcLog.error("RpcException#Input parameters: " + data, ex);
        } else if (ex instanceof HsfException) {
            rpcLog.error("HsfException#Input parameters: " + data, ex);
        } else if (ex instanceof ConflictException) {
            errLog.error("ConflictException#Input parameters :" + data, ex);
        } else if (ex instanceof NullPointerException) {
            errLog.error("NullPointerException#Input parameters :" + data, ex);
        } else {
            errLog.error("otherException#Input parameters :" + data, ex);
        }
    }
}
