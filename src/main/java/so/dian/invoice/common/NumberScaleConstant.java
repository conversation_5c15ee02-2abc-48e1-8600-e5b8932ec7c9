package so.dian.invoice.common;

import java.math.BigDecimal;

/**
 * @program: plutus
 * @description:
 * @author: yuechuan
 * @create: 2023-05-26 16:03
 */
public class NumberScaleConstant {
    //精度
    public static final Integer FOUR_SCALE = 4;

    public static final Integer EIGHT_SCALE = 8;

    public static final Integer TEN_SCALE = 10;
    //精度
    public static final Integer TWO_SCALE = 2;
    //舍入模式
    public static final Integer ROUNDING_MODE = BigDecimal.ROUND_HALF_UP;
    //精度类型
    public static final String RATE_SCALE_TYPE = "rate";
    //精度类型
    public static final String AMT_SCALE_TYPE = "amt";
    /**
     * 系统默认的计算精度
     * @return
     */
    public static Integer getDefaultScale() {
        return TWO_SCALE;
    }

    /**
     * 系统默认的舍入模式
     * @return
     */
    public static Integer getDefaultRoundingMode() {
        return ROUNDING_MODE;
    }
}
