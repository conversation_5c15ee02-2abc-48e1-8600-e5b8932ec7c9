package so.dian.invoice.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import so.dian.invoice.annotation.MediaRequestDataSpec;

public class MediaRequestDataSpecCustomArgumentResolver implements HandlerMethodArgumentResolver {

//    @Resource
//    private SessionUtil sessionUtil;

//    @Resource
//    private SupplierService supplierService;

//    @Resource
//    private AgentEmployeeService agentEmployeeService;

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.getParameterAnnotation(MediaRequestDataSpec.class) != null;
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {

        String dataAsJson = nativeWebRequest.getParameter("data");
        JSONObject jsonObject = JSON.parseObject(dataAsJson);

        // needFilter、supplierNoFilterList、tempRole 前端已经没这个字段，没用了

//        HttpServletRequest request = (HttpServletRequest) nativeWebRequest.getNativeRequest();
//        UserSessionDO userSessionDO = sessionUtil.getSessionDO(request);
//        Integer role = userSessionDO.getRole();

//        if(UserRoleEnum.PO_OPERATOR.getSessionValue().equals(role)
//                || UserRoleEnum.SUPPLIER_MANAGER.getSessionValue().equals(role)
//                || UserRoleEnum.FACTORY_WAREHOUSE_MANAGER.getSessionValue().equals(role)) {
//
//            List<String> supplierNoList = new ArrayList<>();
//            AgentEmployeeDTO employeeDTO = agentEmployeeService.getEmployeeById(userSessionDO.getUid());
//            if (null != employeeDTO && StringUtils.isNotBlank(employeeDTO.getMobile())) {
//                List<SupplierDTO> supplierDTOList = supplierService.getByMobile(employeeDTO.getMobile());
//                if (!CollectionUtils.isEmpty(supplierDTOList)) {
//                    for (SupplierDTO supplierDTO : supplierDTOList) {
//                        supplierNoList.add(supplierDTO.getSupplierNo());
//                    }
//                }
//                jsonObject.put("needFilter", true);
//                jsonObject.put("supplierNoFilterList", supplierNoList);
//            }
//        }

//        jsonObject.put("tempRole", role);

        return JSON.parseObject(jsonObject.toJSONString(), methodParameter.getParameterType());
    }

}
