package so.dian.invoice.common;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import so.dian.invoice.annotation.MeidaRequestData;

/**
 * 用于统一的接口入参解析和处理，如有特殊解析逻辑，最好自行扩展 HandlerMethodArgumentResolver 实现，不要放在此处
 *
 * <AUTHOR>
 * @since 2016-08-13
 */
public class MeidaRequestDataCustomArgumentResolver implements HandlerMethodArgumentResolver {

    private Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(MeidaRequestData.class) != null;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter,
                                  ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) throws Exception {
        String dataAsJson = webRequest.getParameter("data");

        return gson.fromJson(dataAsJson, parameter.getParameterType());
    }
}
