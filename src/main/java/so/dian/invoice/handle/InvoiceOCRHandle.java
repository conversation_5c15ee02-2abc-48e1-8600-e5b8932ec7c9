package so.dian.invoice.handle;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.enums.CacheEnum;
import so.dian.invoice.handle.factory.OCRThreadFactory;
import so.dian.invoice.pojo.dto.OCRQueueDTO;
import so.dian.invoice.service.InvoiceValidateService;

/**
 * 针对批量OCR识别情况，放入redis队列中，异步处理
 *
 * @Author: chenan
 * @Date: 2020/8/4 11:07
 */
@Component
@Slf4j
public class InvoiceOCRHandle {

    @Resource
    private InvoiceValidateService invoiceValidateService;
    @Resource
    private RedissonClient redisson;

    private ExecutorService scheduledExecutorService = Executors.newSingleThreadExecutor(new OCRThreadFactory());

    public void ocrThreadTask() {
        try {
            scheduledExecutorService.submit(new OcrHandleRunnable());
        } catch (Exception e) {
            log.error("-------------- OCR task submit exception", e);
        }

    }

    private class OcrHandleRunnable implements Runnable {
        @Override
        public void run() {
            ocrHandle();
        }
    }

    private void ocrHandle() {
        RLock rLock = redisson.getLock(CacheEnum.INVOICE_OCR_LOCK.getKey());
        boolean lock = false;
        try {
            lock = rLock.tryLock(30, CacheEnum.INVOICE_OCR_LOCK.getExpire(), CacheEnum.INVOICE_OCR_LOCK.getUnit());
            if (lock) {
                RQueue<OCRQueueDTO> redissonQueue = redisson.getQueue(InvoiceConstants.INVOICE_OCR_QUEUE);
                // 队列不为空，且锁被当前线程持有，进行OCR业务处理
                while (!redissonQueue.isEmpty() && rLock.isHeldByCurrentThread()) {
                    OCRQueueDTO ocrQueueDTO = redissonQueue.peek();
                    if (Objects.isNull(ocrQueueDTO)) {
                        continue;
                    }
                    // ocr识别成功，删除队列
                    if (invoiceValidateService.handleOCR(ocrQueueDTO)) {
                        redissonQueue.remove(ocrQueueDTO);
                    } else {
                        // ocr识别失败，达到5次删除队列
                        redissonQueue.remove(ocrQueueDTO);
                        if (ocrQueueDTO.getTimes() < 5) {
                            ocrQueueDTO.setTimes(ocrQueueDTO.getTimes() + 1);
                            redissonQueue.add(ocrQueueDTO);
                        }
                    }
                    // ocr限流，批量的识别，每秒调动一次
                    Thread.sleep(1000);
                }
            }
        } catch (Exception e) {
            log.error("--------------  OCR task error", e);
        } finally {
            if (lock && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }


}
