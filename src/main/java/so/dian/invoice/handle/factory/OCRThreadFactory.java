package so.dian.invoice.handle.factory;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: chenan
 * @Date: 2020/8/4 12:37
 */
public class OCRThreadFactory implements ThreadFactory {
    private final String namePrefix = "OCRHandle-";
    private final AtomicInteger nextId = new AtomicInteger(1);

    @Override
    public Thread newThread(Runnable task) {
        String name = namePrefix + nextId.getAndIncrement();
        return new Thread(task, name);
    }
}