package so.dian.invoice.handle;

import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import so.dian.invoice.annotation.ExcelEntity;
import so.dian.invoice.annotation.ExcelField;
import so.dian.invoice.client.manager.MailManager;
import so.dian.invoice.converter.SupplierInvoiceDetailConverter;
import so.dian.invoice.enums.BuyerTaxIdEnum;
import so.dian.invoice.enums.SupplierInvoiceTypeEnum;
import so.dian.invoice.manager.SupplierInvoiceDetailBillNoManager;
import so.dian.invoice.manager.SupplierInvoiceDetailManager;
import so.dian.invoice.pojo.dto.BillNoDTO;
import so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO;
import so.dian.invoice.pojo.param.SupplierInvoicePageParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.SupplierInvoiceDetailExcelVO;
import so.dian.invoice.util.InvoiceEnumUtils;
import so.dian.invoice.util.LocalDateUtil;
import so.dian.invoice.util.ResponseUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/3/22 10:09 上午
 */
@Slf4j(topic = "biz")
@Component
public class ExcelHandler {

    @Value("${temp.path}")
    private String tempPath;

    @Autowired
    private SupplierInvoiceDetailManager detailManager;

    @Autowired
    private SupplierInvoiceDetailBillNoManager detailBillNoManager;

    @Resource
    private MailManager mailManager;

    @Async("excelExportThreadPool")
    public void asyncExportSupplierInvoiceDetail(SupplierInvoicePageParam param, Set<String> supplierNos, CurrentUserReq userReq) {
        List<BillNoDTO> billNoDTOList = SupplierInvoiceDetailConverter.toBillNoList(param);
        List<SupplierInvoiceDetailDO> supplierInvoiceDetailDOList = detailManager.findExcelListByReq(param, billNoDTOList, supplierNos);
        if (CollectionUtils.isEmpty(supplierInvoiceDetailDOList)) {
            return;
        }
        // k:SupplierInvoiceDetailId————v:Map<Integer,String>
        // k:bill_type————v:bill_no
        Map<Integer, Map<Integer, String>> detailIdBillNoListMap = detailBillNoManager.findDetailIdBillTypeMap(supplierInvoiceDetailDOList);

        List<SupplierInvoiceDetailExcelVO> excelVOList = SupplierInvoiceDetailConverter.toExcel(supplierInvoiceDetailDOList, detailIdBillNoListMap);

        exportToEmail(excelVOList, SupplierInvoiceDetailExcelVO.class, userReq);
    }

    @Async("excelExportThreadPool")
    public void asyncExportToEmail(Collection<?> rowList, Class<?> clazz, CurrentUserReq userReq) {
        exportToEmail(rowList, clazz, userReq);
    }

    public void exportToEmail(Collection<?> rowList, Class<?> clazz, CurrentUserReq userReq) {
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }
        ExcelEntity excelEntity = clazz.getAnnotation(ExcelEntity.class);
        if (Objects.isNull(excelEntity)) {
            log.warn("未包含excel导出注解");
            return;
        }

        String subject = excelEntity.tip() + "-" + userReq.getUserName() + "-" + System.currentTimeMillis();
        String tmpExcelPath = tempPath + subject + ".xlsx";
        exportToFile(rowList, clazz, tmpExcelPath);
        // 发送邮件
        String body = String.format("%s导出成功，共导出%d条记录", excelEntity.tip(), rowList.size());
        try {
            mailManager.sendEmail(userReq.getEmail(), subject, body, new File(tmpExcelPath));
        } catch (IOException e) {
            log.error("------ exportToEmail mail send error, to = {}", userReq.getEmail(), e);
        }
        log.info("{}导出成功,user:{},条数:{}", excelEntity.tip(), userReq, rowList.size());
    }

    /**
     * 导出excel到临时目录
     *
     * @param rowList
     * @param clazz
     * @param templateFilePath
     */
    public void exportToFile(Collection<?> rowList, Class<?> clazz, String templateFilePath) {
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }
        BigExcelWriter writer = ExcelUtil.getBigWriter(templateFilePath);

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            ExcelField excelField = field.getAnnotation(ExcelField.class);
            if (Objects.isNull(excelField)) {
                continue;
            }
            writer.addHeaderAlias(field.getName(), excelField.headerAlias());
        }

        writer.write(rowList);
        writer.close();
    }


    public void supplierInvoiceExcelTemplate(HttpServletResponse response) {
        String fileName = "供应商发票导入模板-" + System.nanoTime() + ".xlsx";
        String pathFileName = tempPath + fileName;
        File tempFile = new File(pathFileName);
        // 将excel写入响应流
        try {
            // 设置Excel head
            BigExcelWriter excelWriter = ExcelUtil.getBigWriter(pathFileName);
            List<String> headerList = Lists.newArrayList();
            headerList.add("发票类型");
            headerList.add("发票代码");
            headerList.add("发票号码");
            headerList.add("发票日期");
            headerList.add("开票方名称");

            headerList.add("开票方税号");
            headerList.add("校验码");
            headerList.add("购买方名称");
            headerList.add("购买方税号");
            headerList.add("物料名称");

            headerList.add("规格型号");
            headerList.add("设备单位");
            headerList.add("数量");
            headerList.add("不含税单价");
            headerList.add("不含税金额");

            headerList.add("税率");
            headerList.add("税额");
            headerList.add("价税合计");
            headerList.add("采购批次");
            headerList.add("采购单号");
            headerList.add("对账单号");
            excelWriter.writeHeadRow(headerList);

            // 设置Excel body
            String dateStr = LocalDateUtil.localDateToStr(LocalDate.now(), LocalDateUtil.yyyy_slash_MM_slash_dd);
            excelWriter.writeCellValue("D2",dateStr);// 发票日期

            // 设置某一行列的内容
            // 发票类型
            CellRangeAddressList cellRangeAddressList1 = new CellRangeAddressList();
            cellRangeAddressList1.addCellRangeAddress(CellRangeAddress.valueOf("A2"));
            excelWriter.addSelect(cellRangeAddressList1, InvoiceEnumUtils.getEnumDesc(SupplierInvoiceTypeEnum.class));

            // 购买方名称
            CellRangeAddressList cellRangeAddressList2 = new CellRangeAddressList();
            cellRangeAddressList2.addCellRangeAddress(CellRangeAddress.valueOf("H2"));
            List<String> buyers = BuyerTaxIdEnum.getBuyers();
            excelWriter.addSelect(cellRangeAddressList2, buyers.toArray(new String[0]));

            // 购买方税号
            CellRangeAddressList cellRangeAddressList3 = new CellRangeAddressList();
            cellRangeAddressList3.addCellRangeAddress(CellRangeAddress.valueOf("I2"));
            List<String> taxIds = BuyerTaxIdEnum.getTaxIds();
            excelWriter.addSelect(cellRangeAddressList3, taxIds.toArray(new String[0]));

            ResponseUtils.exportExcel(response, excelWriter, fileName);
        } catch (IOException e) {
            log.error("模板导出失败。", e);
        } finally {
            // 删除临时文件
            boolean ignore = tempFile.delete();
        }
    }


}
