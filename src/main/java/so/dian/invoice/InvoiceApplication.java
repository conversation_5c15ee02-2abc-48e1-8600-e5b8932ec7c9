package so.dian.invoice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import so.dian.himalaya.boot.configuration.HimalayaAutoConfiguration;
import so.dian.invoice.common.MediaRequestDataSpecCustomArgumentResolver;
import so.dian.invoice.common.MeidaRequestDataCustomArgumentResolver;

import java.util.List;


@ServletComponentScan
@EnableDiscoveryClient
@SpringBootApplication(exclude = {HimalayaAutoConfiguration.class})
@EnableAspectJAutoProxy(exposeProxy=true)
public class InvoiceApplication extends WebMvcConfigurerAdapter {


    public static void main(String[] args) {
        SpringApplication.run(InvoiceApplication.class, args);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        super.addArgumentResolvers(argumentResolvers);
        argumentResolvers.add(new MeidaRequestDataCustomArgumentResolver());
        argumentResolvers.add(new MediaRequestDataSpecCustomArgumentResolver());
    }
}
