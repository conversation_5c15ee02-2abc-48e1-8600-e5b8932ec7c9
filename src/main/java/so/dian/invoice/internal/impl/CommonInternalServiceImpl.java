package so.dian.invoice.internal.impl;

import org.springframework.stereotype.Component;
import so.dian.center.common.entity.BizResult;
import so.dian.invoice.client.ContractClient;
import so.dian.invoice.internal.CommonInternalService;

import javax.annotation.Resource;

/**
 * 发票通用内部服务实现类
 *
 * <AUTHOR> 2019/5/8
 */
@Component
public class CommonInternalServiceImpl implements CommonInternalService {

    @Resource
    private ContractClient contractClient;

    @Override
    public boolean checkMerchantPermission(Integer userId, Integer merchantId) {
        if (merchantId == null || userId == null) {
            return false;
        }
        // 根据merchantId查询商户信息
        BizResult<Boolean> contractResult = contractClient.checkDivideMerchantPermission(userId, merchantId);
        // 返回
        return contractResult.getData();
    }
}
