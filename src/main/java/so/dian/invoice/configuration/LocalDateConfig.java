package so.dian.invoice.configuration;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
@Configuration
public class LocalDateConfig {
    final static DateTimeFormatter localDateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    final static DateTimeFormatter localDateFormatter =DateTimeFormatter.ofPattern("yyyy-MM-dd");
    @Bean
    public Converter<String, LocalDate> localDateConverter() {
        return new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String source) {
                if(StringUtils.isBlank(source)){
                    return null;
                }
                return LocalDate.parse(org.apache.commons.lang3.StringUtils.trimToNull(source),localDateFormatter);
            }
        };
    }

    @Bean
    public Converter<String, LocalDateTime> localDateTimeConverter() {
        return new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(String source) {
                if(StringUtils.isBlank(source)){
                    return null;
                }
                return LocalDateTime.parse(org.apache.commons.lang3.StringUtils.trimToNull(source), localDateTimeFormatter);
            }
        };
    }
}
