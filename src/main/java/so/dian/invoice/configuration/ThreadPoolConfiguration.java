package so.dian.invoice.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolConfiguration {

    @Bean(name = "mqMessageTaskExecutor")
    public Executor mqMessageTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("mqMessageAsync-");
        executor.initialize();
        return executor;
    }
    @Bean("excelExportThreadPool")
    public Executor excelExportThreadPool() {
        // 线程池
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //线程池核心线程数，控制每次开启子线程数量
        taskExecutor.setCorePoolSize(1);
        //线程池维护线程的最大数量
        taskExecutor.setMaxPoolSize(10);
        //线程池所使用的缓冲队列
        taskExecutor.setQueueCapacity(25);
        //线程池维护线程所允许的空闲时间
        taskExecutor.setKeepAliveSeconds(30000);
        taskExecutor.initialize();
        return taskExecutor;
    }

    @Bean("batchUploadHistoryInvoiceExecutor")
    public Executor batchUploadHistoryInvoiceExecutor() {
        // 线程池
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //线程池核心线程数，控制每次开启子线程数量
        executor.setCorePoolSize(1);
        //线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        //线程池所使用的缓冲队列
        executor.setQueueCapacity(25);
        //线程池维护线程所允许的空闲时间
        executor.setKeepAliveSeconds(30000);
        executor.initialize();
        return executor;
    }

    @Bean("invoiceBizRelationExecutor")
    public Executor invoiceBizRelationExecutor() {
        // 线程池
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //线程池核心线程数，控制每次开启子线程数量
        executor.setCorePoolSize(1);
        //线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        //线程池所使用的缓冲队列
        executor.setQueueCapacity(25);
        //线程池维护线程所允许的空闲时间
        executor.setKeepAliveSeconds(30000);
        executor.setRejectedExecutionHandler((r, executor1) -> {
            if (!executor1.isShutdown()) {
                try {
                    executor1.getQueue().put(r);
                } catch (InterruptedException e) {
                    log.error("invoiceBizRelationExecutor, {}", e.toString(), e);
                    Thread.currentThread().interrupt();
                }
            }
        });
        executor.initialize();
        return executor;
    }
}
