package so.dian.invoice.configuration.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.util.Objects;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.util.ContentCachingRequestWrapper;
import so.dian.commons.eden.exception.BizException;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.pojo.request.BaseEmpRoleReq;
import so.dian.invoice.util.biz.LoginInfoUtils;

/**
 * DianFilter
 *
 * <AUTHOR>
 * @desc 处理小电leo申请的data=URLencode(json) 方式入参
 * 暂未支持 get方式如 @RequestParam、@ModelAttribute，就当本服务默认采用POST + @RequestBody接收参数
 * @date 2019-10-15
 */
@Order(-1)
@WebFilter(
        filterName = "dianFilter",
        value = {"/*"}
)
@Slf4j(topic = "dian")
public class DianFilter implements Filter {
    public DianFilter() {
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
			ServletException {
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper((HttpServletRequest)request);
        String dataStr = request.getParameter("data");
        if(StringUtils.isNotBlank(dataStr)) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            HttpMethod method = HttpMethod.resolve(httpServletRequest.getMethod());
            //构造请求封装类
            DianServletRequestWrapper dianServletRequestWrapper = new DianServletRequestWrapper(httpServletRequest);

            //偷梁换柱
            JSONObject requestBody = tryGetData(dataStr);
            //构造cookie参数
            additionCookieValue(requestWrapper, requestBody);

            if(HttpMethod.GET.equals(method)) {
                request = dianServletRequestWrapper.getDianRequestWrapper(request, requestBody);
            } else {
                String bodyStr = IOUtils.toString(requestWrapper.getInputStream());
                if (StringUtils.isBlank(bodyStr) && StringUtils.isNotBlank(request.getContentType()) &&
                        request.getContentType().contains(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                    switch (method) {
                        case POST:
                        case PUT:
                            request = dianServletRequestWrapper.getDianRequestWrapper(request, requestBody.toJSONString());
                            break;
                        default:
                            log.error("小电不支持的HttpMethod类型, method={}, data={}", method, dataStr);
                            throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL,
                                    "小电不支持的HttpMethod类型, method=" + method + "data=" + dataStr);
                    }
                }
            }
        }
        chain.doFilter(request, response);
    }

    /**
     * 附加Cookie内userId和role值给employeeId和role
     * @param requestWrapper 请求缓存
     * @param requestBody body请求参数
     */
    private static void additionCookieValue(ContentCachingRequestWrapper requestWrapper, JSONObject requestBody) {
        BaseEmpRoleReq req = LoginInfoUtils.getEmployeeRole(requestWrapper);
        if (Objects.nonNull(req)) {
            requestBody.put("employeeId", req.getEmployeeId());
            requestBody.put("role", req.getRole());
        }
    }

    /**
     * 尝试解析data=内json数据成一个JSONObject
     * @param dataStr 小电定制前端json入参
     */
    private static JSONObject tryGetData(String dataStr) {
        try {
            return JSON.parseObject(dataStr);
        } catch (Exception e) {
            log.error("小电参数无法解析, data={}", dataStr);
            throw BizException.create(InvoiceCommentErrorCodeEnum.PARAM_RESOLVER_FAIL, "参数无法解析, data=" + dataStr);
        }
    }

    @Override
    public void destroy() {
    }
}
