package so.dian.invoice.configuration.filter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

/**
 * DianServletRequestWrapper
 *
 * <AUTHOR>
 * @desc 偷梁换柱后的request
 * @date 2019-10-15
 */
public class DianServletRequestWrapper extends HttpServletRequestWrapper {

    private String body = null;
    private Map<String, String[]> parameterMap = null;
    private Enumeration<String> parameterNames = null;

    public DianServletRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    /**
     * 构造一个带有body参数的request请求
     */
    public DianServletRequestWrapper(HttpServletRequest request, String body) {
        super(request);
        this.body = body;
    }

    /**
     * 构造一个带有请求url参数的request请求
     */
    public DianServletRequestWrapper(HttpServletRequest request, JSONObject jsonParameter) {
        super(request);
        convertMap(jsonParameter);
    }

    private void convertMap(JSONObject jsonObject) {
        Map<String, String[]> parameterMap = Maps.newHashMap();
        Vector<String> vector = new Vector<>();
        jsonObject.forEach((name, value) -> {
            vector.add(name);
            if (value != null) {
                if (value instanceof ArrayList) {
                    List list = (ArrayList) value;
                    String[] valueArray = new String[list.size()];
                    for (int i = 0; i < list.size(); i++) {
                        valueArray[i] = list.get(i).toString();
                    }
                    parameterMap.put(name, valueArray);
                } else if (value instanceof String[]) {
                    parameterMap.put(name, (String[]) value);
                } else if (value instanceof String) {
                    parameterMap.put(name, new String[]{(String) value});
                } else {
                    parameterMap.put(name, new String[]{String.valueOf(value)});
                }
            }
        });
        this.parameterNames = vector.elements();
        this.parameterMap = parameterMap;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        ServletInputStream inputStream = null;
        if (StringUtils.isNotEmpty(body)) {
            inputStream = new DianServletInputStream(body);
        }
        return inputStream;
    }

    @Override
    public BufferedReader getReader() throws IOException {
        String enc = getCharacterEncoding();
        if (enc == null) {
            enc = "UTF-8";
        }
        return new BufferedReader(new InputStreamReader(getInputStream(), enc));
    }

    @Override
    public String getHeader(String name) {
        if (HttpHeaders.CONTENT_TYPE.equalsIgnoreCase(name)) {
            return getContentType();
        }
        return super.getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaderNames() {
        Vector<String> vector = new Vector<>();
        for (Enumeration<String> names = super.getHeaderNames(); names.hasMoreElements(); ) {
            String headerName = names.nextElement();
            if (!HttpHeaders.CONTENT_TYPE.equalsIgnoreCase(headerName)) {
                vector.add(headerName);
            }
        }
        return vector.elements();
    }

    @Override
    public Enumeration<String> getHeaders(String name) {
        if (HttpHeaders.CONTENT_TYPE.equalsIgnoreCase(name)) {
            return null;
        }
        return super.getHeaders(name);
    }

    @Override
    public String getContentType() {
        if (!HttpMethod.GET.equals(HttpMethod.resolve(super.getMethod()))) {
            return MediaType.APPLICATION_JSON_UTF8_VALUE;
        } else {
            return super.getContentType();
        }
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return parameterMap;
    }

    @Override
    public Enumeration<String> getParameterNames() {
        return parameterNames;
    }

    @Override
    public String[] getParameterValues(String name) {
        return parameterMap.get(name);
    }

    /**
     * 将post或put解析过后的request进行封装改写
     */
    public ServletRequest getDianRequestWrapper(ServletRequest request, String body) {
        return new DianServletRequestWrapper((HttpServletRequest) request, body);
    }

    /**
     * 将get解析过后的request进行封装改写
     */
    public ServletRequest getDianRequestWrapper(ServletRequest request, JSONObject urlJson) {
        return new DianServletRequestWrapper((HttpServletRequest) request, urlJson);
    }

    private class DianServletInputStream extends ServletInputStream {

        private InputStream inputStream;
        /**
         * 解析json之后的文本
         */
        private String body;

        public DianServletInputStream(String body) throws IOException {
            this.body = body;
            inputStream = null;
        }

        private InputStream acquireInputStream() throws IOException {
            if (inputStream == null) {
                //通过解析之后传入的文本生成inputStream以便后面control调用
                inputStream = new ByteArrayInputStream(body.getBytes());
            }
            return inputStream;
        }

        @Override
        public void close() throws IOException {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                throw e;
            } finally {
                inputStream = null;
            }
        }

        @Override
        public int read() throws IOException {
            return acquireInputStream().read();
        }

        @Override
        public boolean markSupported() {
            return false;
        }

        @Override
        public synchronized void mark(int i) {
            throw new UnsupportedOperationException("mark not supported");
        }

        @Override
        public synchronized void reset() throws IOException {
            throw new IOException(new UnsupportedOperationException("reset not supported"));
        }

        @Override
        public boolean isFinished() {
            return false;
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setReadListener(ReadListener readListener) {

        }
    }
}