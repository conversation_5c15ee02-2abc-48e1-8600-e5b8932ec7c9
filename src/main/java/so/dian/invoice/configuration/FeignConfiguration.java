package so.dian.invoice.configuration;

import feign.Logger;
import feign.Request.Options;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableFeignClients(basePackages = "so.dian.invoice.client")
public class FeignConfiguration {

    @Bean
    public Logger.Level loggerLevel() {
        return Logger.Level.FULL;
    }

    @Bean
    public Options feignRequestOptions() {
        return new Options(10 * 1000, 3 * 60 * 1000);
    }
}