package so.dian.invoice.configuration;

import lombok.Data;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Created by dam<PERSON> on 2017/8/10.
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.redis")
public class RedisCacheConfig extends CachingConfigurerSupport {

    private static org.slf4j.Logger logger = LoggerFactory.getLogger(RedisCacheConfig.class);

    private String host;

    private int port;

    private int timeout;

    private int maxIdle;

    private long maxWaitMillis;

    private String password;

    @Bean
    @RefreshScope
    public JedisPool redisPoolFactory() {
        logger.info("redis地址：" + host + ":" + port);
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxIdle(maxIdle);
        jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);

        JedisPool jedisPool = new JedisPool(jedisPoolConfig, host, port, timeout, password);
        logger.info("JedisPool注入成功！！");
        return jedisPool;
    }
}
