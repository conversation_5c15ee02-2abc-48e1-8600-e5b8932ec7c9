package so.dian.invoice.configuration;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


@Configuration
@EnableSwagger2
@ConditionalOnExpression("${swagger.enable:true}")
public class SwaggerConfiguration implements ApplicationContextAware {

    @Autowired
    private Environment environment;

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
//              .groupName(this.environment.getProperty("info.name"))
              .apiInfo(apiInfo()).select()
		.apis(RequestHandlerSelectors.basePackage("so.dian.invoice.controller.invoice.manage"))
//              .apis(RequestHandlerSelectors.withClassAnnotation(RestController.class))
              .paths(PathSelectors.any())
              .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder().title(environment.getProperty("info.component"))
              .description("API文档地址：http://rap.dian.so/org/group.do?plid=12")
              .termsOfServiceUrl("")
              .contact(new Contact("北京伊电园网络科技有限公司", "http://www.dian.so", ""))
              .version("1.0")
              .build();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // force the bean to get loaded as soon as possible
        applicationContext.getBean("requestMappingHandlerAdapter");
    }
}