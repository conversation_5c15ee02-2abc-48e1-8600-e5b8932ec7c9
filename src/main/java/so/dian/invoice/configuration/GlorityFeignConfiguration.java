package so.dian.invoice.configuration;

import feign.Logger;
import feign.Request.Options;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static java.util.concurrent.TimeUnit.SECONDS;

@Configuration
public class GlorityFeignConfiguration {


    @Bean
    public Options feignRequestOptionsGlority() {
        return new Options(10 * 1000, 10*1000);
    }

    @Bean
    public Retryer feignRetryerGlority() {
        return new Retryer.Default(100, SECONDS.toMillis(1), 3);
    }
}