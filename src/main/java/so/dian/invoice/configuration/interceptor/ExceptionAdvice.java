//package so.dian.invoice.configuration.interceptor;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.converter.HttpMessageNotReadableException;
//import org.springframework.validation.BindException;
//import org.springframework.validation.BindingResult;
//import org.springframework.validation.FieldError;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.MissingServletRequestParameterException;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.bind.annotation.ResponseStatus;
//import so.dian.himalaya.common.entity.BizResult;
//import so.dian.himalaya.common.exception.BizException;
//import so.dian.invoice.enums.error.InvoiceCodeEnum;
//
//import javax.validation.ConstraintViolation;
//import javax.validation.ConstraintViolationException;
//import java.util.Set;
//
//@Slf4j(topic = "exceptionAdvice")
//@ControllerAdvice
//@ResponseBody
//public class ExceptionAdvice {
//
//    private static final String MSG_PARMA_INVALID = "参数验证失败:{}";
//
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(MissingServletRequestParameterException.class)
//    public BizResult<String> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
//        log.error("缺少请求参数: {}", e.getMessage(), e);
//        return BizResult.error(InvoiceCodeEnum.PARAM_ERROR, "缺少请求参数: " + e.getParameterName());
//    }
//
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(HttpMessageNotReadableException.class)
//    public BizResult<String> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
//        log.error("参数解析失败: {}", e.getMessage(), e);
//        return BizResult.error(InvoiceCodeEnum.PARAM_ERROR, "参数解析失败");
//    }
//
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(MethodArgumentNotValidException.class)
//    public BizResult<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
//        log.error(MSG_PARMA_INVALID, e.getMessage(), e);
//        String message = getErrorMessage(e.getBindingResult());
//        return BizResult.error(InvoiceCodeEnum.PARAM_ERROR, message);
//    }
//
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(BindException.class)
//    public BizResult<String> handleBindException(BindException e) {
//        log.error("参数绑定失败: {}", e.getMessage(), e);
//        String message = getErrorMessage(e.getBindingResult());
//        return BizResult.error(InvoiceCodeEnum.PARAM_ERROR, message);
//    }
//
//    private String getErrorMessage(BindingResult result) {
//        if (result == null) {
//            return "未知错误";
//        }
//        FieldError error = result.getFieldError();
//        if (error == null) {
//            return "未知的错误";
//        }
//        String field = error.getField();
//        String code = error.getDefaultMessage();
//
//        return String.format("%s: %s", field, code);
//    }
//
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(ConstraintViolationException.class)
//    public BizResult<String> handleConstraintViolationException(ConstraintViolationException e) {
//        log.error(MSG_PARMA_INVALID, e.getMessage(), e);
//        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
//        if (CollectionUtils.isNotEmpty(violations)) {
//            ConstraintViolation<?> violation = violations.iterator().next();
//            String message = violation.getMessage();
//            return BizResult.error(InvoiceCodeEnum.PARAM_ERROR, message);
//        }
//        return BizResult.error(InvoiceCodeEnum.PARAM_ERROR, "ConstraintViolationException");
//    }
//
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(BizException.class)
//    public BizResult<String> handleBizException(BizException e) {
//        log.error("业务异常: {}", e.getMessage(), e);
//        return BizResult.error(e.getCode(), e.getMessage());
//    }
//
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    @ExceptionHandler(Exception.class)
//    public BizResult<String> handleException(Exception e) {
//        String errorMsg = e.getMessage();
//        // 错误消息为空 或者消息长度超过一定长度的认定为未捕获到的异常
//        if (StringUtils.isBlank(errorMsg)) {
//            errorMsg = InvoiceCodeEnum.BIZ_ERROR.getDesc();
//        }
//        log.error("未捕获的异常: {}", errorMsg, e);
//        return BizResult.error(InvoiceCodeEnum.BIZ_ERROR.getCode(), errorMsg);
//    }
//}
