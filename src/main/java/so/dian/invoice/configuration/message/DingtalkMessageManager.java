package so.dian.invoice.configuration.message;

import io.github.notoday.dingtalk.robot.DingRobotHelper;
import io.github.notoday.dingtalk.robot.message.MarkdownMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Created by qiumu on 2020/10/19.
 */

@Service("dingtalkMessageManager")
@Slf4j
public class DingtalkMessageManager {

    @Value("${dingtalk2.accesstoken}")
    private String token;
    @Value("${dingtalk2.secret}")
    private String secret;

    /**
     * 推送钉钉消息
     */
    public void sendDingDingTextMessage(String title, String text) {
        DingRobotHelper dingRobotHelper = new DingRobotHelper(token, secret);
        MarkdownMessage markdownMessage = MarkdownMessage.builder().title(title).text(text).build();
        dingRobotHelper.sendMessage(markdownMessage);
    }
}
