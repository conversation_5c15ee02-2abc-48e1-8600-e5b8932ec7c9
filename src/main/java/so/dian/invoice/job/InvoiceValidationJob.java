package so.dian.invoice.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Validator;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.enums.CacheEnum;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum.IsRealEnum;
import so.dian.invoice.enums.InvoiceIdentifyRecordEnum.ValidationCodeEnum;
import so.dian.invoice.enums.InvoiceValidateTypeEnum;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.param.InvoiceValidateStatusJobParam;
import so.dian.invoice.service.InvoiceValidateService;

/**
 * 发票验真定时任务，每15天跑一次
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InvoiceValidationJob {

    @Autowired
    private InvoiceManager invoiceManager;
    @Autowired
    Validator validator;
    @Autowired
    private InvoiceValidateService invoiceValidateService;

    // 默认值
    private static final Integer STAY_VALIDATE_DAY = -14;
    private static final Integer FAIL_VALIDATE_DAY = -28;
    // 默认新录入发票为0，2的状态需要验真
    private List<IsRealEnum> isRealListEnum = Lists.newArrayList(IsRealEnum.STAY, IsRealEnum.FAIL);
    private List<InvoiceIdentifyRecordEnum.ValidationCodeEnum> validationCode = Lists.newArrayList(
            ValidationCodeEnum.NO,
            ValidationCodeEnum.OVER_LIMIT,
            ValidationCodeEnum.NO_SUPPORT,
            ValidationCodeEnum.INVALID_PARAM,
            ValidationCodeEnum.OTHER);

    @Resource
    private RedissonClient redisson;

    @XxlJob(value = "invoiceValidationJob")
    public ReturnT<String> execute(String param) {
        log.info("invoiceValidationJob start job，param:{}", param);
        InvoiceValidateStatusJobParam jobParam = buildJobParam(param);
        // 手动执行，使用强制执行标记，以防污染间隔执行标记
        if (jobParam.getEnforceFlag() != null && jobParam.getEnforceFlag()) {
            executeJob(jobParam);
            return ReturnT.SUCCESS;
        }
        // 间隔执行标记
        RBucket<Boolean> bucket = redisson.getBucket(CacheEnum.VALIDETA_JOB_FLAG.getKey());
        Boolean jobFlag = bucket.get();
        if (jobFlag == null) {
            // 初始化间隔执行
            jobFlag = true;
            bucket.set(jobFlag, CacheEnum.VALIDETA_JOB_FLAG.getExpire(), CacheEnum.VALIDETA_JOB_FLAG.getUnit());
        }
        if (jobFlag) {
            executeJob(jobParam);
        }
        // 置反间隔执行标记
        bucket.set(!jobFlag, CacheEnum.VALIDETA_JOB_FLAG.getExpire(), CacheEnum.VALIDETA_JOB_FLAG.getUnit());
        return ReturnT.SUCCESS;
    }

    private void executeJob(InvoiceValidateStatusJobParam jobParam) {
        DateTime endTime = calcEndTime(jobParam);
        // 根据录入时间段搜索发票
        DateTime startCreateTime = calcStartTime(endTime, jobParam);
        DateTime failValidateTime = calcFailStartTime(endTime, jobParam.getFailValidateDay());
        List<InvoiceBO> waitValidateInvoiceList = getWaitValidateInvoice(startCreateTime, failValidateTime, endTime);
        if (CollectionUtils.isEmpty(waitValidateInvoiceList)) {
            log.info("invoiceValidationJob end job(no data)");
            return;
        }

        // 调用睿琪接口
        for (InvoiceBO validateInvoiceBO : waitValidateInvoiceList) {
            try {
                Integer count = 1;
                invoiceValidateService.requireGlority(validateInvoiceBO, count, InvoiceValidateTypeEnum.TASK_VERIFICATION);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("invoiceValidationJob sigle job error:invoiceId:{},errorMsg:{}", validateInvoiceBO.getId(),
                        e.getMessage());
            }
        }
        // 批量修改发票验真状态,批量新增日志表
        // invoiceFacadeImpl.invoiceValidate(passInvoiceBOS, notPassInvoiceBOS, validateStatusForInsertList);
        log.info("invoiceValidationJob end job");
    }



    private InvoiceValidateStatusJobParam buildJobParam(String jobParam) {
        InvoiceValidateStatusJobParam result = new InvoiceValidateStatusJobParam();
        if (StringUtils.isNotBlank(jobParam)) {
            try {
                result = JSON.parseObject(jobParam, InvoiceValidateStatusJobParam.class);
                if (result != null && result.getIsRealListEnum() != null){
                    isRealListEnum = result.getIsRealListEnum();
                }
                if (result != null && result.getValidationCode() != null) {
                    validationCode = result.getValidationCode();
                }
            } catch (Exception e) {
                log.error("invoiceValidationJob 参数解析出错:{}", e.getMessage());
            }
        }
        return result;
    }

    private DateTime calcStartTime(DateTime endTime, InvoiceValidateStatusJobParam jobParam) {
        // 指定开始时间，从指定开始时间执行，优先级最高（第一次执行，设置起始时间为11月1日）
        if (StringUtils.isNotBlank(jobParam.getStartTime())) {
            return DateUtil.parse(jobParam.getStartTime(), "yyyy-MM-dd");
        }
        int offset = STAY_VALIDATE_DAY;
        if (Objects.nonNull(jobParam.getStayValidateDay())) {
            offset = jobParam.getStayValidateDay();
        }
        return DateUtil.offsetDay(endTime, offset);
    }

    private DateTime calcFailStartTime(DateTime endTime, Integer failValidateDay) {
        int offset = FAIL_VALIDATE_DAY;
        if (Objects.nonNull(failValidateDay)) {
            offset = failValidateDay;
        }
        return DateUtil.offsetDay(endTime, offset);
    }

    private DateTime calcEndTime(InvoiceValidateStatusJobParam jobParam) {
        if (StringUtils.isNotBlank(jobParam.getEndTime())) {
            return DateUtil.parse(jobParam.getEndTime(), "yyyy-MM-dd");
        }
        return DateUtil.beginOfDay(DateUtil.date());
    }

    private List<InvoiceBO> getWaitValidateInvoice(DateTime startCreateTime, DateTime failValidateTime,
                                                   DateTime endTime) {
        List<InvoiceBO> waitValidateInvoiceList = Lists.newArrayList();
        // 指定时间范围需要待验真的发票数据
        if (isRealListEnum.contains(IsRealEnum.STAY)){
            List<InvoiceBO> newInvoiceList = invoiceManager.listNeedValidateByCreateTime(startCreateTime,
                    endTime,
                    InvoiceConstants.NEED_VALIDATE_INVOICE_TYPE_ENUM_LIST,
                    Lists.newArrayList(IsRealEnum.STAY));
            waitValidateInvoiceList.addAll(newInvoiceList);
        }

        // 指定时间范围验真不通过的发票数据
        if (isRealListEnum.contains(IsRealEnum.FAIL)){
            List<InvoiceBO> notValidateList = invoiceManager.listNotValidate(failValidateTime,
                    endTime,
                    validationCode);
            waitValidateInvoiceList.addAll(notValidateList);
        }
        return waitValidateInvoiceList;
    }

}
