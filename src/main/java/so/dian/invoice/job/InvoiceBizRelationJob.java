package so.dian.invoice.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import javax.annotation.Resource;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.invoice.service.InvoiceBizRelationService;

@Slf4j
@Component
public class InvoiceBizRelationJob {

    @Resource
    private InvoiceBizRelationService invoiceBizRelationService;

    @XxlJob(value = "invoiceBizRelationJob")
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> execute(String param) {
        log.info("发票关联核销单据任务开始");
        int count = invoiceBizRelationService.disposalByDeduction();
        log.info("发票关联核销单据任务结束.共计{}条", count);
        return ReturnT.SUCCESS;
    }
}
