package so.dian.invoice.job;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.invoice.manager.message.snapshot.InvoiceNewsVoucherManager;
import so.dian.invoice.pojo.dto.message.InvoiceNewsVoucherDTO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceNewsVoucherParam;
import so.dian.invoice.service.message.contain.MessageHandleContain;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: yuechuan
 * @Date: 2025/05/28 3:32 PM
 * @Description: 开票重试任务
 */
@Slf4j
@Component
public class InvoiceManageRetryJob {

    @Resource
    private MessageHandleContain messageHandleContain;

    @Resource
    private InvoiceNewsVoucherManager invoiceNewsVoucherManager;

    @XxlJob(value = "invoiceManageRetryJob")
    public ReturnT<String> execute(String param) {
	InvoiceNewsVoucherParam query = buildJobParam(param);
	log.info("开票重试消息开始，param:{}", param);

	List<InvoiceNewsVoucherDTO> voucherDTOS = invoiceNewsVoucherManager.list(query);
	if (CollectionUtils.isEmpty(voucherDTOS)) {
	    log.info("invoiceManageRetryJob 获取待重试数据为空,任务结束");
	    return ReturnT.SUCCESS;
	}
	invoiceNewsVoucherManager.retry(voucherDTOS);
	log.info("开票重试消息结束，voucherDTOS.size:{}", voucherDTOS.size());
	return ReturnT.SUCCESS;
    }

    private InvoiceNewsVoucherParam buildJobParam(String jobParam) {
	InvoiceNewsVoucherParam result = new InvoiceNewsVoucherParam();
	if (StringUtils.isNotBlank(jobParam)) {
	    try {
		result = JSON.parseObject(jobParam, InvoiceNewsVoucherParam.class);
	    } catch (Exception e) {
		log.error("invoiceManageRetryJob 参数解析出错:{}", e.getMessage());
	    }
	}
	return result;
    }

}
