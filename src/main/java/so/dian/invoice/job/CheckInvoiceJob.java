package so.dian.invoice.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.invoice.constant.CheckInvoiceConstants;
import so.dian.invoice.converter.CheckInvoiceConverter;
import so.dian.invoice.dao.InvoiceExpressesRelationDAO;
import so.dian.invoice.enums.InCheckPoolStatusEnum;
import so.dian.invoice.manager.CheckInvoiceManager;
import so.dian.invoice.manager.InvoiceConfigManager;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.entity.CheckInvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceConfigDO;
import so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO;
import so.dian.invoice.util.biz.AssertUtils;

import static so.dian.invoice.enums.error.CheckInvoiceErrorCodeEnum.*;

/**
 * @Author: jiaoge
 * @Date: 2019/12/26 3:32 PM
 * @Description:
 */
@Slf4j
@Component
public class CheckInvoiceJob {

	@Resource
	private InvoiceManager invoiceManager;

	@Resource
	private InvoiceConfigManager invoiceConfigManager;

	@Resource
	private CheckInvoiceManager checkInvoiceManager;

	@Resource
	private InvoiceExpressesRelationDAO invoiceExpressesRelationDAO;

	@XxlJob(value = "checkInvoiceJob")
	@Transactional(rollbackFor = Exception.class)
	public ReturnT<String> execute(String param) {
		//获取上上周发票状态为‘部分核销’和‘全部核销’，已寄出，直营分成，需要质检的发票
		Date now = new Date();
		Date weekBeginTime = getWeekBeginTime(now);
		Date weekEndTime = getWeekEndTime(now);
		//分批查询：1.查询通过时间查询上上周发票物流关系-小二端上传已寄送的发票 idList
		List<InvoiceExpressesRelationDO> expressesRelationDOList =
				invoiceExpressesRelationDAO.listInvoiceExpressesRelationByTime(weekBeginTime, weekEndTime);
		List<Long> invoiceIdList = expressesRelationDOList.stream()
				.map(InvoiceExpressesRelationDO::getInvoiceId).collect(Collectors.toList());
		//2.idList查询发票台账+限制条件，获取符合条件的发票录入质检池
		List<InvoiceBO> invoiceBOList = invoiceManager.listNeedCheckInvoiceByIds(invoiceIdList);
		if (CollectionUtils.isEmpty(invoiceBOList)) {
			log.info("上上周没有需要质检的发票,weekBeginTime:{},weekEndTime:{}", weekBeginTime, weekEndTime);
			return ReturnT.SUCCESS;
		}
		//获取抽取比例
		InvoiceConfigDO invoiceConfigDO = invoiceConfigManager.getByKey(CheckInvoiceConstants.CHECK_INVOICE_CHECK_RATE);
		//获取发票关联物流单号

		List<CheckInvoiceDO> checkInvoiceDOList =
				CheckInvoiceConverter.convertCheckInvoiceDO(invoiceBOList, invoiceConfigDO, expressesRelationDOList);
		log.info("录入质检池的发票,checkInvoiceDOList:{}", JSON.toJSON(checkInvoiceDOList));
		if (CollectionUtils.isEmpty(checkInvoiceDOList)) {
			return ReturnT.SUCCESS;
		}
		Boolean result = checkInvoiceManager.batchInsert(checkInvoiceDOList);
		AssertUtils.notTrueWithBizExp(result, BATCH_ADD_CHECK_INVOICE_FAILED);
		//发票台账更新为已录入质检池
		List<Long> checkInvoiceList
				= checkInvoiceDOList.stream().map(CheckInvoiceDO::getInvoiceId).collect(Collectors.toList());
		Boolean updateResult
				= invoiceManager.batchUpdateInCheckPool(checkInvoiceList, InCheckPoolStatusEnum.ENTERED.getCode(),
				InCheckPoolStatusEnum.NOT_ENTERED.getCode());
		AssertUtils.notTrueWithBizExp(updateResult, BATCH_UPDATE_INVOICE_IN_CHECK_POOL_FAILED);
		return ReturnT.SUCCESS;
	}

	private Date getWeekBeginTime(Date now) {
		DateTime dateTime = DateUtil.offsetWeek(now, -2);
		return DateUtil.beginOfWeek(dateTime);
	}

	private Date getWeekEndTime(Date now) {
		DateTime dateTime = DateUtil.offsetWeek(now, -2);
		return DateUtil.endOfWeek(dateTime);
	}


}
