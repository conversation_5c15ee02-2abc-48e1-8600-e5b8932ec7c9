package so.dian.invoice.job;

import com.xxl.job.core.biz.model.ReturnT;
import javax.annotation.Resource;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.invoice.handle.InvoiceOCRHandle;

/**
 * OCR识别异常补偿处理，定时任务
 */
@Slf4j
@Component
public class OcrAsyncCheckJob {

    @Resource
    private InvoiceOCRHandle invoiceOCRHandle;

    @XxlJob(value = "ocrAsyncCheckJob")
    public ReturnT<String> execute(String param) {
        invoiceOCRHandle.ocrThreadTask();
        return ReturnT.SUCCESS;
    }

}
