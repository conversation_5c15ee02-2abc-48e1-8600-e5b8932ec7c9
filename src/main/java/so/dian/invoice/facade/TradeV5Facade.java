package so.dian.invoice.facade;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.invoice.client.TradeV5Client;
import so.dian.mofa3.lang.domain.Result;
import so.dian.newyork.center.client.v5.req.TradeOrderQueryV5Req;
import so.dian.newyork.center.client.v5.resp.TradeOrderQueryV5Resp;

import javax.annotation.Resource;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-27 19:09
 */
@Slf4j
@Service
public class TradeV5Facade {
    @Resource
    private TradeV5Client tradeV5SClient;


    public TradeOrderQueryV5Resp getTradeV5Service(Long tradeOrderId, String buyerId) {
        TradeOrderQueryV5Req query = new TradeOrderQueryV5Req();
        query.setBuyerId(buyerId);
        query.setTradeOrderId(tradeOrderId);
        try {
            Result<TradeOrderQueryV5Resp> result = tradeV5SClient.tradeQuery(query);
            log.info("查询支付订单结果:{}", JSON.toJSONString(result));
            if(result.isSuccess()){
                return result.getData();
            }
        }catch (Exception e){
            log.info("查询支付订单异常",e);
        }
        return null;
    }
}
