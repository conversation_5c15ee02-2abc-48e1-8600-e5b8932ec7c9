package so.dian.invoice.facade;

import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.invoice.converter.InvoiceValidateConverter;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.manager.CheckInvoiceCacheManager;
import so.dian.invoice.pojo.bo.InvoiceIdentifyRecordBO;
import so.dian.invoice.pojo.dto.identify.InvoiceIdentifyRecordDTO;
import so.dian.invoice.pojo.param.InvoiceImgUrlParam;
import so.dian.invoice.pojo.vo.InvoiceIdentifyRecordVO;
import so.dian.invoice.service.InvoiceValidateService;

/**
 * @Author: jiaoge
 * @Date: 2019/9/27 11:01 AM
 * @Description:
 */
@Slf4j
@Service
public class InvoiceValidateFacadeImpl {

	@Autowired
	private InvoiceValidateService invoiceValidateService;

	@Resource
	private CheckInvoiceCacheManager checkInvoiceCacheManager;

	public BizResult<InvoiceIdentifyRecordVO> getInvoiceIdentifyDetail(InvoiceImgUrlParam param, Integer userId) {
		if (Objects.isNull(param) || StringUtils.isBlank(param.getImageUrl())) {
			throw BizException.create(InvoiceErrorCodeEnum.INVOICE_IMAGE_MUST_NOT_NULL);
		}
		BizResult<InvoiceIdentifyRecordDTO> identifyDetail =
				invoiceValidateService.getInvoiceIdentifyDetail(param.getImageUrl());
		InvoiceIdentifyRecordVO invoiceIdentifyRecordVO =
				InvoiceValidateConverter.buildRecordDTO2VO(identifyDetail.getData());
		//初始化发票上传缓存
		checkInvoiceCacheManager.initInvoiceIdentifyPreset(userId.longValue(), identifyDetail.getData());
		return BizResult.create(invoiceIdentifyRecordVO);
	}

	public BizResult<InvoiceIdentifyRecordBO> getInvoiceIdentifyCache(Integer userId) {
		InvoiceIdentifyRecordBO recordBO = checkInvoiceCacheManager.getByEmployeeId(userId.longValue());
		return BizResult.create(recordBO);
	}

}
