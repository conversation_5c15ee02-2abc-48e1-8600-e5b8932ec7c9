package so.dian.invoice.facade;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.customer.dto.request.AuthenticationSubjectDTO;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.client.PassportClient;
import so.dian.invoice.enums.error.InvoiceCodeEnum;
import so.dian.mofa3.lang.domain.Result;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class PassportFacade {
    @Autowired
    private PassportClient passportClient;
    /**
     * 根据账户获取认证服务信息
     */
    public List<AuthenticationSubjectDTO> queryByAccountId(Long accountId) {
        log.info("PassportFacade queryByAccountId get accountId:{}", accountId);
        BizResult<List<AuthenticationSubjectDTO>> bizResult = passportClient.queryByAccountId(accountId);
        log.info("PassportFacade queryByAccountId get result:{}", JSON.toJSON(bizResult));
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            log.error("PassportFacade queryByAccountId get accountId:{},result:{}",accountId,bizResult);
            throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
        }
        return bizResult.getData();
    }

    public List<AuthenticationSubjectDTO> getAuthenticationSubjectByAgentId(Long agentId) {
        log.info("PassportFacade getAuthenticationSubjectByAgentId get agentId:{}", agentId);
        Result<List<AuthenticationSubjectDTO>> bizResult = passportClient.getAuthenticationSubjectByAgentId(agentId);
        log.info("PassportFacade getAuthenticationSubjectByAgentId get result:{}", JSON.toJSON(bizResult));
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            log.error("PassportFacade getAuthenticationSubjectByAgentId get agentId:{},result:{}",agentId,bizResult);
            throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
        }
        return bizResult.getData();
    }
}
