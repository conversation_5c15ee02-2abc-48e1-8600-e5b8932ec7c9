package so.dian.invoice.facade;

import static so.dian.invoice.constant.InvoiceConstants.MANUAL_DEDUCT_BUSINESS_NO;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.hr.api.entity.employee.AgentDTO;
import so.dian.invoice.client.YandangClient;
import so.dian.invoice.converter.InvoiceConverter;
import so.dian.invoice.converter.InvoiceDeductionConverter;
import so.dian.invoice.converter.InvoiceDetailConverter;
import so.dian.invoice.converter.InvoiceOperateLogConverter;
import so.dian.invoice.converter.InvoiceValidateConverter;
import so.dian.invoice.dao.InvoiceDeductionDAO;
import so.dian.invoice.dao.InvoiceIdentifyRecordDAO;
import so.dian.invoice.enums.*;
import so.dian.invoice.enums.error.InvoiceCommentErrorCodeEnum;
import so.dian.invoice.enums.error.InvoiceErrorCodeEnum;
import so.dian.invoice.manager.CheckInvoiceCacheManager;
import so.dian.invoice.manager.InvoiceManager;
import so.dian.invoice.manager.InvoiceOperateLogManager;
import so.dian.invoice.manager.InvoiceValidateStatusManager;
import so.dian.invoice.pojo.bo.InvoiceBO;
import so.dian.invoice.pojo.bo.InvoiceIdentifyRecordBO;
import so.dian.invoice.pojo.bo.InvoiceOperateLogBO;
import so.dian.invoice.pojo.dto.*;
import so.dian.invoice.pojo.dto.identify.DetailAndExtraDTO;
import so.dian.invoice.pojo.entity.InvoiceDO;
import so.dian.invoice.pojo.entity.InvoiceDeductionDO;
import so.dian.invoice.pojo.entity.InvoiceDetailDO;
import so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO;
import so.dian.invoice.pojo.entity.InvoiceValidateStatusDO;
import so.dian.invoice.pojo.enums.InvoiceTypeEnum;
import so.dian.invoice.pojo.param.AddSingleInvoiceParam;
import so.dian.invoice.pojo.param.InvoiceInfoParam;
import so.dian.invoice.pojo.param.InvoiceParam;
import so.dian.invoice.service.*;
import so.dian.invoice.volidator.InvoiceBuyerValidator;
import so.dian.invoice.volidator.InvoiceValidator;
import so.dian.yandang.client.pojo.request.PayBillBatchReq;
import so.dian.yandang.client.pojo.response.PayBillInfoRsp;

/**
 * @Author: jiaoge
 * @Date: 2019/9/25 3:43 PM
 * @Description:
 */
@Slf4j
@Service
public class InvoiceFacadeImpl {

    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private AgentEmployeeService employeeService;
    @Resource
    private InvoiceIdentifyRecordDAO invoiceIdentifyRecordDAO;
    @Resource
    private InvoiceOperateLogManager invoiceOperateLogManager;
    @Resource
    private InvoiceDeductionService invoiceDeductionService;
    @Resource
    private YandangClient yandangClient;
    @Resource
    private InvoiceManager invoiceManager;
    @Resource
    private InvoiceValidateStatusManager invoiceValidateStatusManager;
    @Autowired
    private InvoiceValidateService invoiceValidateService;
    @Resource
    private CheckInvoiceCacheManager checkInvoiceCacheManager;
    @Resource
    private SupplierService supplierService;

    @Autowired
    private InvoiceDeductionDAO invoiceDeductionDAO;

    public InvoiceDto getInvoiceDetails(InvoiceParam param) {
        InvoiceDto dto = new InvoiceDto();
        InvoiceDO invoice;
        if (Objects.nonNull(param.getId())) {
            invoice = invoiceService.getInvoiceById(param.getId());
        } else {
            if (StringUtils.isBlank(param.getInvoiceNo())) {
                return null;
            }
            if (param.getInvoiceCode() == null) {
                param.setInvoiceCode("");
            }
            invoice = invoiceService.getInvoiceByInvoiceCodeAndNo(param.getInvoiceCode(), param.getInvoiceNo());
        }
        if (Objects.isNull(invoice)) {
            return null;
        }

        BeanUtils.copyProperties(invoice, dto);

        dto.setTaxPrice(dto.getTax());
        if (null == dto.getTaxPrice()) {
            BigDecimal rawPrice = dto.getRawPrice() == null ? new BigDecimal(0.00) : dto.getRawPrice();
            BigDecimal price = dto.getPrice() == null ? new BigDecimal(0.00) : dto.getPrice();
            dto.setTaxPrice(price.subtract(rawPrice));
        }

        String supplierName = supplierService.getSupplierNameBySupplierNo(invoice.getSupplierNo());
        dto.setSupplierName(supplierName);
        dto.setUrl(invoice.getUrl());
        dto.setTypeStr(InvoiceTypeEnum.getField(dto.getType()));
        dto.setStatusStr(InvoiceStatusEnum.getField(dto.getStatus()));
        dto.setCreatorNick(employeeService.getEmployeeNickById(dto.getCreator()));
        dto.setSeller(invoice.getSubjectName());
        dto.setTax(invoice.getTax());
        dto.setSubjectTypeStr(SubjectTypeEnum.getField(dto.getSubjectType()));
        List<InvoiceDetailDO> invoiceDetailDOList =
                invoiceService.getInvoiceDetailByInvoiceCodeAndNo(invoice.getInvoiceCode(), invoice.getInvoiceNo());
        if (invoiceDetailDOList != null && invoiceDetailDOList.size() > 0) {
            List<InvoiceDetailDto> invoiceDetailDtoList = new ArrayList<>();
            for (InvoiceDetailDO invoiceDetail : invoiceDetailDOList) {
                InvoiceDetailDto detailDto = new InvoiceDetailDto();
                BeanUtils.copyProperties(invoiceDetail, detailDto);
                invoiceDetailDtoList.add(detailDto);
            }
            dto.setDetails(invoiceDetailDOList.get(0).getMaterialName() == null ? ""
                    : invoiceDetailDOList.get(0).getMaterialName());
            dto.setInvoiceDetailDtoList(invoiceDetailDtoList);
        }

        return dto;
    }

    public BizResult<Boolean> addSingleInvoiceAndRecord(AddSingleInvoiceParam param) {
        InvoiceValidator.checkAddSingleInvoiceParam(param);
        //获取发票缓存，对比，有差异则该发票打标 need_check = 1
        InvoiceIdentifyRecordBO identifyRecordBO =
                checkInvoiceCacheManager.getByEmployeeId(param.getLoginUserId().longValue());
        if (Objects.isNull(identifyRecordBO)) {
            throw BizException.create(InvoiceErrorCodeEnum.ADD_INVOICE_ERROR, "新增发票异常，请重新识别");
        }

        //新增发票识别记录
        InvoiceIdentifyRecordDO recordDO = InvoiceConverter.buildParam2RecordDO(param, identifyRecordBO.getResult());
        invoiceIdentifyRecordDAO.insertSelective(recordDO);

        //发票识别记录对比
        Boolean comparedResult = InvoiceValidateConverter.invoiceIdentifyCompared(identifyRecordBO, recordDO);
        invoiceService.addSingleInvoice(param, recordDO, comparedResult);

        return BizResult.create(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Boolean> updateInvoiceToApp(InvoiceInfoParam param, Integer loginUserId) {
        InvoiceValidator.checkUpdateParam(param);
        AgentDTO agentDTO = invoiceService.getInvoiceBelongInfo(loginUserId);
        if (agentDTO == null) {
            log.error("当前登录者的渠道信息获取失败。userId：" + loginUserId);
            throw BizException.create(InvoiceCommentErrorCodeEnum.USER_LOGIN_IS_INVALID_MAST_REGISTER);
        }
        Integer paramBelongSubjectType = param.getBelongSubjectType();
        Integer paramBelongSubjectId = param.getBelongSubjectId();
        Integer loginBelongSubjectType = agentDTO.getType();
        Integer loginBelongSubjectId = agentDTO.getAgentId().intValue();
        if (paramBelongSubjectType == null && paramBelongSubjectId == null) {
            // 填充发票归属信息
            param.setBelongSubjectType(loginBelongSubjectType);
            param.setBelongSubjectId(loginBelongSubjectId);
        } else {
            if (paramBelongSubjectType == null || !paramBelongSubjectType.equals(loginBelongSubjectType)
                    || paramBelongSubjectId == null || !paramBelongSubjectId.equals(loginBelongSubjectId)) {
                log.error("发票更新操作数据越权");
                throw BizException.create(InvoiceErrorCodeEnum.INVOICE_UPDATE_DATA_EXCEEDS_PERMISSIONS);
            }
        }

        String invoiceCode = param.getInvoiceCode() == null ? "" : param.getInvoiceCode();
        InvoiceDO invoiceDO = invoiceService.getInvoiceByInvoiceCodeAndNo(invoiceCode, param.getInvoiceNo());
        if (invoiceDO != null && !invoiceDO.getId().equals(param.getId())) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_IS_ALREADY_EXIST);
        }

        if (invoiceDO != null && !invoiceDO.getStatus().equals(InvoiceStatusEnum.WAIT.getType())) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_IS_ALREADY_USED);
        }

        InvoiceIdentifyRecordDO recordDO =
                invoiceIdentifyRecordDAO.selectInvoiceRecodeByInvoiceCodeAndNo(param.getInvoiceCode(),
                        param.getInvoiceNo());
        if (Objects.isNull(recordDO)) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_IDENTIFY_RECORD_NOT_EXIST);
        }
        Boolean comparedResult = true;
        if (StringUtils.isNotBlank(recordDO.getResult())) {
            DetailAndExtraDTO extraDTO = JSONObject.parseObject(recordDO.getResult(), DetailAndExtraDTO.class);
            comparedResult = InvoiceValidateConverter.updateInvoiceCompared(extraDTO.getDetails(), param);
        }
        // 购买方税号校验，小电集团的只能填写小电集团的，非小电集团的购买方税号只能填写非小电集团的
        boolean checkResult = invoiceValidateService.checkInvoiceBuyer(CheckInvoiceBuyerDTO.builder()
                .belongSubjectType(param.getCurrentUserReq().getBelongSubjectType())
                .buyer(param.getBuyer())
                .belongSubjectId(Long.valueOf(param.getCurrentUserReq().getBelongSubjectId()))
                .buyerTaxId(param.getBuyerTaxId()).build());
        if (!checkResult) {
            throw BizException.create(InvoiceErrorCodeEnum.NO_PERMISSION_BUYER);
        }

        Boolean checkBuyerResult =
                InvoiceBuyerValidator.validDianInvoiceBuyer(loginBelongSubjectType, param.getBuyer(),
                        param.getGmtCreate(), true);
        if (!checkBuyerResult) {
            throw BizException.create(InvoiceErrorCodeEnum.INVOICE_BUYER_INVALID);
        }

        InvoiceDO invoice = InvoiceConverter.buildInvoiceParam2DO(param, comparedResult);
        invoiceService.updateInvoice(invoice);
        List<InvoiceDetailDto> invoiceDetailDtoList = Lists.newArrayList();
        InvoiceDetailDto detailDto =
                InvoiceDetailConverter.initInvoiceDetailDto(param.getDetails(), invoice.getInvoiceCode(),
                        invoice.getInvoiceNo());
        invoiceDetailDtoList.add(detailDto);
        invoiceService.saveInvoiceDetail(invoice.getInvoiceCode(), invoice.getInvoiceNo(), invoiceDetailDtoList);

        return BizResult.create(true);
    }

    /**
     * 发票关联业务信息
     */
    public BizResult<List<InvoiceBusinessRelationDTO>> invoiceBusinessRelation(Long invoiceId) {
        if (Objects.isNull(invoiceId)) {
            log.error("发票关联业务信息,参数错误。invoiceId:{}", invoiceId);
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }

        InvoiceDO invoice = invoiceService.getInvoiceById(invoiceId.intValue());
        if (Objects.isNull(invoice) || invoice.getIsDelete() != 0) {
            log.error("发票关联业务信息,发票不存在或已作废。invoiceId:{}", invoiceId);
            return BizResult.error(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST);
        }

        //查询发票核销记录
        List<InvoiceDeductionDO> list = invoiceDeductionService.getInvoiceByBusinessRelation(invoice.getInvoiceNo(),
                invoice.getInvoiceCode());
        if (CollectionUtils.isEmpty(list)) {
            return BizResult.create(Lists.newArrayList());
        }
        PayBillBatchReq payBillBatchReq = InvoiceDeductionConverter.getPayBillBatchReq(list);
        //根据业务单号查询付款单
        Map<String, Set<String>> payMap = Maps.newHashMap();
        so.dian.himalaya.common.entity.BizResult<List<PayBillInfoRsp>> payResult =
                yandangClient.getPayBillInfoByBatch(payBillBatchReq);
        if (Objects.isNull(payResult) || !payResult.isSuccess() || CollectionUtils.isEmpty(payResult.getData())) {
            log.warn("发票关联业务信息，付款单不存在.req:{}", payBillBatchReq);
        } else {
            payResult.getData().forEach(obj -> {
                Set<String> payNoList = payMap.get(obj.getOutBizNo());
                if (CollectionUtils.isEmpty(payNoList)) {
                    payNoList = Sets.newHashSet();
                }
                payNoList.add(obj.getBillNo());
                payMap.put(obj.getOutBizNo(), payNoList);
            });
        }
        //根据提现单号组装
        List<InvoiceBusinessRelationDTO> reList = Lists.newArrayList();
        for (InvoiceDeductionDO obj : list) {
            if (Objects.equals(obj.getBusinessNo(), MANUAL_DEDUCT_BUSINESS_NO)) {
                continue;
            }
            Set<String> payNoList = payMap.get(obj.getBusinessNo());
            InvoiceBusinessRelationDTO invoiceBusinessRelationDTO = new InvoiceBusinessRelationDTO();
            invoiceBusinessRelationDTO.setBusinessNo(obj.getBusinessNo());
            invoiceBusinessRelationDTO.setBusinessType(obj.getBusinessType());
            invoiceBusinessRelationDTO.setPayNoList(payNoList);
            invoiceBusinessRelationDTO.setAmount(obj.getAmount());
            invoiceBusinessRelationDTO.setCreator(obj.getCreator());
            invoiceBusinessRelationDTO.setCreateName(obj.getCreateName());
            invoiceBusinessRelationDTO.setCreateTime(obj.getCreateTime());
            reList.add(invoiceBusinessRelationDTO);
        }

        return BizResult.create(reList);
    }

    /**
     * 发票操作记录
     */
    public BizResult<List<InvoiceOperateLogDTO>> invoiceOperationLog(Long invoiceId) {
        if (Objects.isNull(invoiceId)) {
            log.error("发票操作记录,参数错误。invoiceId:{}", invoiceId);
            return BizResult.error(ErrorCodeEnum.PARAMS_ERROR);
        }

        InvoiceDO invoice = invoiceService.getInvoiceById(invoiceId.intValue());
        if (Objects.isNull(invoice) || invoice.getIsDelete() != 0) {
            log.error("发票操作记录,发票不存在或已作废。invoiceId:{}", invoiceId);
            return BizResult.error(InvoiceErrorCodeEnum.INVOICE_NOT_EXIST);
        }
        List<InvoiceOperateLogBO> list = invoiceOperateLogManager.findByInvoiceId(invoiceId);
        return BizResult.create(InvoiceOperateLogConverter.toDTOList(list));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Boolean> invoiceValidate(List<InvoiceBO> passInvoiceBOS, List<InvoiceBO> notPassInvoiceBOS,
            List<InvoiceValidateStatusDO> validateStatusForInsertList) {
        if (CollectionUtils.isNotEmpty(passInvoiceBOS)) {
            invoiceManager.batchUpdateRealStatus(InvoiceIdentifyRecordEnum.IsRealEnum.SUC,
                    LocalListUtils.transferList(passInvoiceBOS, invoiceBO -> Long.valueOf(invoiceBO.getId())));
        }
        if (CollectionUtils.isNotEmpty(notPassInvoiceBOS)) {
            invoiceManager.batchUpdateRealStatus(InvoiceIdentifyRecordEnum.IsRealEnum.FAIL,
                    LocalListUtils.transferList(notPassInvoiceBOS, invoiceBO -> Long.valueOf(invoiceBO.getId())));
        }
        // 批量新增日志表
        if (CollectionUtils.isNotEmpty(validateStatusForInsertList)) {
            invoiceValidateStatusManager.batchInsert(validateStatusForInsertList);
        }
        return BizResult.create(true);
    }

    /**
     * 更新从小二端录入历史发票的城市code
     *
     * @return
     */
    public BizResult<Boolean> updateCityCode() {
        log.info("updateCityCode start............");
        List<InvoiceDO> invoiceDOS = invoiceManager.listNeedCheckIsNoNull();
        Set<Integer> creators = invoiceDOS.stream().map(InvoiceDO::getCreator).collect(Collectors.toSet());
        Map<Integer, Integer> creatorCityMap = employeeService.getAgentEmployeeCityCodeMap(creators);
        List<Integer> invoiceIds = invoiceDOS.stream().map(InvoiceDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invoiceIds)) {
            invoiceManager.batchUpdateCityCode(invoiceIds, creatorCityMap);
        }
        log.info("updateCityCode sucess.............");
        return BizResult.create(true);
    }

    public List<InvoiceDeductDTO> deductList(Integer invoiceId) {
        InvoiceBO invoiceBO = invoiceManager.getInvoiceById(invoiceId);
        if (Objects.isNull(invoiceBO)) {
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "发票不存在");
        }

        List<InvoiceDeductionDO> deductionDOS = invoiceDeductionDAO.findByInvoiceNoAndInvoiceCode(
                invoiceBO.getInvoiceNo(), invoiceBO.getInvoiceCode());
        if (CollectionUtils.isEmpty(deductionDOS)) {
            return Lists.newArrayList();
        }
        Map<String, List<InvoiceDeductionDO>> businessInvoiceDeductMap = deductionDOS.stream()
                .collect(Collectors.groupingBy(InvoiceDeductionDO::getBusinessNo));
        List<InvoiceDeductionDO> needShowDeductList = Lists.newArrayList();
        for (Entry<String, List<InvoiceDeductionDO>> entry : businessInvoiceDeductMap.entrySet()) {
            List<InvoiceDeductionDO> value = entry.getValue();
            List<InvoiceDeductionDO> deductList = value.stream()
                    .filter(invoiceDeductionDO -> Objects.equals(invoiceDeductionDO.getOperateType(),
                            OperateTypeEnum.DEDUCT.getType())).collect(Collectors.toList());
            List<InvoiceDeductionDO> recoverList = value.stream()
                    .filter(invoiceDeductionDO -> Objects.equals(invoiceDeductionDO.getOperateType(),
                            OperateTypeEnum.RECOVER.getType())).collect(Collectors.toList());
            for (InvoiceDeductionDO deductionDO : deductList) {
                needShowDeductList.add(deductionDO);
                int recoverSize = recoverList.size();
                while (recoverList.iterator().hasNext()) {
                    recoverSize--;
                    InvoiceDeductionDO recoverDO = recoverList.iterator().next();
                    if (recoverDO.getAmount().compareTo(deductionDO.getAmount()) == 0) {
                        needShowDeductList.remove(deductionDO);
                        recoverList.remove(recoverDO);
                        break;
                    }
                    if (recoverSize == 0) {
                        break;
                    }
                }
            }
        }

        List<InvoiceDeductDTO> invoiceDeductDTOS = Lists.newArrayList();
        needShowDeductList.sort(Comparator.comparing(InvoiceDeductionDO::getId).reversed());
        for (InvoiceDeductionDO deductionDO : needShowDeductList) {
            InvoiceDeductDTO invoiceDeductDTO = new InvoiceDeductDTO();
            invoiceDeductDTO.setBusinessNo(
                    deductionDO.getBusinessNo().equals(MANUAL_DEDUCT_BUSINESS_NO) ? "-" : deductionDO.getBusinessNo());
            invoiceDeductDTO.setId(deductionDO.getId());
            invoiceDeductDTO.setDeductDate(DateUtil.format(deductionDO.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            invoiceDeductDTO.setDeductUser(deductionDO.getCreateName());
            invoiceDeductDTO.setDeductAmount(deductionDO.getAmount());

            invoiceDeductDTOS.add(invoiceDeductDTO);
        }

        return invoiceDeductDTOS;
    }
}
