package so.dian.invoice.facade;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.client.TiantaiClient;
import so.dian.tiantai.client.pojo.request.spu.SpuDetailReq;
import so.dian.tiantai.client.pojo.response.remote.spu.SpuSimpleRsp;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TiantaiFacade {

    @Resource
    private TiantaiClient tiantaiClient;


    /**
     * 查询产品信息
     */
    public String getSpuName(String spuCode) {
        SpuSimpleRsp spuRsp = spuSimpleDetail(spuCode);
        if (Objects.isNull(spuRsp)) {
            log.error("spuDetail | fallback | params:{}", spuCode);
            return null;
        }

        return spuRsp.getSpuName();
    }

    /**
     * 查询产品信息
     */
    public SpuSimpleRsp spuSimpleDetail(String spuCode) {
        if (StringUtils.isBlank(spuCode)) {
            log.error("spuCode不能为空");
            return null;
        }
        BizResult<SpuSimpleRsp> bizResult = tiantaiClient.spuSimpleDetail(spuCode);
        if (Objects.isNull(bizResult)) {
            log.error("spuDetail | fallback | params:{}", spuCode);
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("spuDetail | 接口返回错误信息 | params:{},result:{}", spuCode, bizResult);
            return null;
        }
        return bizResult.getData();
    }

    /**
     * 查询产品信息
     */
    public Map<String, String> getSpuName(List<String> spuCodes) {
        List<SpuSimpleRsp> spuRsp = spuSimpleDetailNew(spuCodes);
        if (Objects.isNull(spuRsp)) {
            log.error("spuDetail | fallback | params:{}", spuCodes);
            return null;
        }
        Map<String, String> map = spuRsp.stream().collect(Collectors.toMap(SpuSimpleRsp::getSpuCode, SpuSimpleRsp::getSpuName));
        return map;
    }

    /**
     * 查询产品信息
     */
    public List<SpuSimpleRsp> spuSimpleDetailNew(List<String> spuCodes) {
        if (CollectionUtils.isEmpty(spuCodes)) {
            log.error("spuCode不能为空");
            return null;
        }
        Set<String> set = new HashSet<>(spuCodes);
        SpuDetailReq spuDetailReq = new SpuDetailReq();
        spuDetailReq.setSpuCodes(set);
        BizResult<List<SpuSimpleRsp>> bizResult = tiantaiClient.spuSimpleDetailNew(spuDetailReq);
        if (Objects.isNull(bizResult)) {
            log.error("spuDetail | fallback | params:{}", spuCodes);
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("spuDetail | 接口返回错误信息 | params:{},result:{}", spuCodes, bizResult);
            return null;
        }
        return bizResult.getData();
    }
}
