package so.dian.invoice.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.center.common.entity.BizResult;
import so.dian.customer.dto.CustomerAccountDTO;
import so.dian.himalaya.common.exception.BizException;
import so.dian.invoice.client.CustomerClient;
import so.dian.invoice.enums.error.InvoiceCodeEnum;

import java.util.Objects;

@Component
@Slf4j
public class CustomerFacade {
    @Autowired
    private CustomerClient customerClient;

    /**
     * 根据公司类型和agentId获取用户信息
     */
    public CustomerAccountDTO getByReferId(Integer referType, Long referId) {
        log.info("根据公司类型和agentId获取用户信息req referType:{},referId:{}",referType,referId);
        BizResult<CustomerAccountDTO> bizResult =  customerClient.getByReferId(referType, referId);
        log.info("根据公司类型和agentId获取用户信息 result:{}",JSON.toJSON(bizResult));
        if (Objects.isNull(bizResult)||!bizResult.isSuccess()) {
            log.error("CustomerFacade getByReferId referType:{},referId:{}, result:{}",
                    referType,referId, bizResult);
            throw BizException.create(InvoiceCodeEnum.REMOTE_INTERNAL_ERROR);
        }
        return bizResult.getData();
    }
}
