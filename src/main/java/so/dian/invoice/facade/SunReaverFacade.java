package so.dian.invoice.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import so.dian.common.rpc.result.DianResult;
import so.dian.fis.credit.dto.request.TenantUserAdminAgentDTO;
import so.dian.invoice.client.SunReaverClient;
import so.dian.sunreaver.api.req.TenantUserAdminAgentReq;
import so.dian.sunreaver.api.resp.TenantUserAdminAgentResp;
import so.dian.sunreaver.common.PermissionDomainTypeEnum;
import so.dian.sunreaver.common.TenantAdminTypeEnum;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class SunReaverFacade {

    @Resource
    private SunReaverClient sunReaverClient;


    /**
     * 获取城市策划的大区
     */
    public List<Long> getAgentIds(Long userId, TenantAdminTypeEnum tenantAdminTypeEnum) {
        log.info("PermissionsCommon getAgentIds get userId:{}", userId);
        TenantUserAdminAgentDTO tenantUserAdminAgentDTO = new TenantUserAdminAgentDTO();
        tenantUserAdminAgentDTO.setUserId(userId);
        // todo 确认是否需要改变业务域
        tenantUserAdminAgentDTO.setDomain(PermissionDomainTypeEnum.FINANCIAL.getCode());
        tenantUserAdminAgentDTO.setAdminTypes(Arrays.asList(tenantAdminTypeEnum.getCode()));

        log.info("租户查询参数:{}", JSON.toJSONString(tenantUserAdminAgentDTO));
        TenantUserAdminAgentResp tenantUserAdminAgent = this.getTenantUserAdminAgent(tenantUserAdminAgentDTO);
        log.info("租户查询结果:{}", JSON.toJSONString(tenantUserAdminAgent));
        if (Objects.isNull(tenantUserAdminAgent) || tenantUserAdminAgent.getTenantAdminTypeMap().isEmpty()) {
            return new ArrayList<>();
        }
        return new ArrayList<>(tenantUserAdminAgent.getTenantAdminTypeMap().keySet());
    }

    /**
     * 查询授权代理
     */
    public TenantUserAdminAgentResp getTenantUserAdminAgent(TenantUserAdminAgentDTO tenantUserAdminAgentDTO) {
        log.info("getTenantUserAdminAgent get tenantUserAdminAgentDTO:{}", JSONObject.toJSON(tenantUserAdminAgentDTO));
        TenantUserAdminAgentReq req = new TenantUserAdminAgentReq();
        BeanUtils.copyProperties(tenantUserAdminAgentDTO,req);
        DianResult<TenantUserAdminAgentResp> result = sunReaverClient.findAuthorizeAgentIdsByUser(req);
        log.info("getTenantUserAdminAgent get result:{}", JSONObject.toJSON(result));
        if (!result.getSuccess()) {
            log.error("getTenantUserAdminAgent tenantUserAdminAgentDTO:{}, result:{}",
                    JSONObject.toJSONString(tenantUserAdminAgentDTO), result);
            return null;
        }
       return result.getData();
    }

}
