package so.dian.invoice.facade;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.client.WutaiClient;
import so.dian.wutai.client.pojo.dto.OperatorLogCreateRspDTO;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service
public class WutaiFacade {

    @Resource
    private WutaiClient wutaiClient;

    /**
     * 单个数据脱敏
     */
    public Long operatorLogCreate(OperatorLogCreateRspDTO operatorLogCreateRspDTO) {
        if (Objects.isNull(operatorLogCreateRspDTO)) {
            log.error("operatorLogCreateRspDTO不能为空");
            return null;
        }
        BizResult<Long> bizResult = wutaiClient.operatorLogCreate(operatorLogCreateRspDTO);
        if (Objects.isNull(bizResult)) {
            log.error("operatorLogCreateRspDTO | fallback | params:{}", JSONObject.toJSONString(operatorLogCreateRspDTO));
            return null;
        }
        if (!bizResult.isSuccess()) {
            log.error("operatorLogCreateRspDTO | 接口返回错误信息 | params:{},result:{}", JSONObject.toJSONString(operatorLogCreateRspDTO), bizResult);
            return null;
        }
        return bizResult.getData();
    }
}
