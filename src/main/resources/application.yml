server:
  port: 8080
#  context-path: /invoice
# 应用基本信息
info:
  component: "@project.description@"
  name: "@project.name@"
  os: ${os.name}
  version: "@project.version@"
  instance-id: ${eureka.instance.instance-id}

eureka:
  client:
    fetch-registry: true
    register-with-eureka: true
    healthcheck:
      enabled: true
    service-url:
      defaultZone: https://eureka.dian-stable.com/eureka
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ipAddress}:${server.port}
#    health-check-url-path: ${management.context-path}/health
#    status-page-url-path: ${management.context-path}/info
    metadata-map:
      instance-id: ${eureka.instance.instance-id}
      user.name: ${security.user.name}
      user.password: ${security.user.password}

management:
  context-path: /admin
  security:
    enabled: true
  endpoint:
    health:
      probes:
        enabled: true

#  安全认证
security:
  basic:
    enabled: true
    path: ${management.context-path}
  user:
    name: helloEureka
    password: zhimakaimen

logging:
  path: ${user.home}/logs/${spring.application.name}
  file: ${logging.path}/${spring.application.name}.log

spring:
  output:
    ansi:
      enabled: always

# 默认超时时间
hystrix:
  command:
    default.execution.isolation.thread.timeoutInMilliseconds: 30000

mybatis:
  config-location: classpath:mybatis.xml


###################本地公共配置#################
dingtalk:
  warn: 00a21d008a6768ed5a885cab4faa85e573096a8108c07bc42ec055d7fc1981ac
  secret: SEC23c5ea7b791a00629a2f7712dd9f56d8fee43129cacad5f656135b3413246943
