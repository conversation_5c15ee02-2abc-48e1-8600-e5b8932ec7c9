<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceDAO">
    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="raw_price" jdbcType="DECIMAL" property="rawPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="tax_rate" jdbcType="DECIMAL" property="taxRate"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="used_amount" jdbcType="DECIMAL" property="usedAmount"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="creator" jdbcType="INTEGER" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="subject_type" jdbcType="INTEGER" property="subjectType"/>
        <result column="subject_name" jdbcType="VARCHAR" property="subjectName"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="kind" jdbcType="VARCHAR" property="kind"/>
        <result column="check_code" jdbcType="VARCHAR" property="checkCode"/>
        <result column="tax" jdbcType="DECIMAL" property="tax"/>
        <result column="is_real" jdbcType="TINYINT" property="isReal"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="buyer_tax_id" jdbcType="VARCHAR" property="buyerTaxId"/>
        <result column="belong_subject_type" jdbcType="INTEGER" property="belongSubjectType"/>
        <result column="belong_subject_id" jdbcType="INTEGER" property="belongSubjectId"/>
        <result column="process_status" jdbcType="INTEGER" property="processStatus"/>
        <result column="city_code" jdbcType="INTEGER" property="cityCode"/>
    </resultMap>
    <resultMap id="DtoResultMap" type="so.dian.invoice.pojo.dto.InvoiceDto">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="raw_price" jdbcType="DECIMAL" property="rawPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="tax_rate" jdbcType="DECIMAL" property="taxRate"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="used_amount" jdbcType="DECIMAL" property="usedAmount"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="creator" jdbcType="INTEGER" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="subject_type" jdbcType="INTEGER" property="subjectType"/>
        <result column="subject_name" jdbcType="VARCHAR" property="subjectName"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="kind" jdbcType="VARCHAR" property="kind"/>
        <result column="check_code" jdbcType="VARCHAR" property="checkCode"/>
        <result column="tax" jdbcType="DECIMAL" property="tax"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="is_real" jdbcType="TINYINT" property="isReal"/>
        <result column="belong_subject_type" jdbcType="INTEGER" property="belongSubjectType"/>
        <result column="belong_subject_id" jdbcType="INTEGER" property="belongSubjectId"/>
    </resultMap>

    <resultMap id="ExpressesDtoResultMap" type="so.dian.invoice.pojo.dto.InvoiceExpressesDto">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="used_amount" jdbcType="DECIMAL" property="usedAmount"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="creator" jdbcType="INTEGER" property="creator"/>
        <result column="seller" jdbcType="VARCHAR" property="seller"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="expresses_id" jdbcType="INTEGER" property="expressesId"/>
        <result column="express_tracking_no" jdbcType="VARCHAR" property="expressTrackingNo"/>
        <result column="express_name" jdbcType="VARCHAR" property="expressName"/>
        <result column="process_status" jdbcType="VARCHAR" property="processStatus"/>
        <result column="subject_name" jdbcType="VARCHAR" property="subjectName"/>
    </resultMap>

    <resultMap id="DetailDtoResultMap" type="so.dian.invoice.pojo.dto.InvoiceDetailDto">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="material_spec" jdbcType="VARCHAR" property="materialSpec"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="unit_price" jdbcType="DECIMAL" property="unitPrice"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="raw_price" jdbcType="DECIMAL" property="rawPrice"/>
        <result column="tax_rate" jdbcType="DECIMAL" property="taxRate"/>
        <result column="tax_price" jdbcType="DECIMAL" property="taxPrice"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="subject_type" jdbcType="INTEGER" property="subjectType"/>
        <result column="subject_name" jdbcType="VARCHAR" property="subjectName"/>
        <result column="belong_subject_type" jdbcType="INTEGER" property="belongSubjectType"/>
        <result column="belong_subject_id" jdbcType="INTEGER" property="belongSubjectId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, invoice_no, supplier_no, type, raw_price, price, tax_rate, receive_time, memo, status,
    used_amount, gmt_create, gmt_modified, is_delete, bill_no, creator, create_time,
    source, invoice_code, subject_type, subject_name,batch_no, kind, check_code, tax,is_real,url,buyer_tax_id,
    belong_subject_type,belong_subject_id,process_status,reviewer,review_time,review_remark,buyer,seller,validate_code,city_code
    </sql>

    <sql id="QueryCondition">
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="invoiceCode != null and invoiceCode != ''">
            and invoice_code = #{invoiceCode}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and invoice_no = #{invoiceNo}
        </if>
        <if test="belongSubjectType != null and belongSubjectType != ''">
            and belong_subject_type = #{belongSubjectType}
        </if>
        <if test="belongSubjectId != null and belongSubjectId != ''">
            and belong_subject_id = #{belongSubjectId}
        </if>
        <if test="isDelete != null">
            and is_delete = #{isDelete}
        </if>
        <if test="subjectName != null and subjectName != ''">
            and subject_name like concat('%',#{subjectName},'%')
        </if>
        <if test="invoiceCodeList != null and invoiceCodeList.size > 0">
            and invoice_code in
            <foreach close=")" collection="invoiceCodeList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNoList != null and invoiceNoList.size > 0">
            and invoice_no in
            <foreach close=")" collection="invoiceNoList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scm_invoice
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectInvoiceByInvoiceCodeAndNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scm_invoice
        where invoice_no = #{invoiceNo,jdbcType=VARCHAR}
        <if test="invoiceCode != null">
            and invoice_code = #{invoiceCode,jdbcType=VARCHAR}
        </if>
        and is_delete = 0
        order by gmt_create desc limit 1
    </select>
    <select id="selectCodeAndNoByInvoiceNoList" resultType="java.lang.String">
        select concat(invoice_code, '_', invoice_no)
        from scm_invoice
        where invoice_no in
        <foreach item="invoiceNo" index="index" collection="invoiceNoList" open="(" separator="," close=")">
            #{invoiceNo}
        </foreach>
        and is_delete = 0
    </select>
    <select id="selectInvoiceByInvoiceNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scm_invoice
        where invoice_no in
        <foreach item="invoiceNo" index="index" collection="invoiceNoList" open="(" separator="," close=")">
            #{invoiceNo}
        </foreach>
        and is_delete = 0
    </select>

    <select id="selectInvoiceDetailAll" parameterType="so.dian.invoice.pojo.param.InvoiceParam"
            resultMap="DetailDtoResultMap">
        select sid.id, sid.invoice_no, sid.material_name, sid.material_spec, sid.unit, sid.unit_price, sid.quantity,
        sid.raw_price,
        sid.tax_price, sid.gmt_create, sid.gmt_modified, sid.is_delete, sid.price, si.supplier_no, si.type,
        si.receive_time, si.memo, si.source, si.invoice_code, si.subject_type,
        si.subject_name, si.batch_no,
        si.belong_subject_type, si.belong_subject_id,sid.tax_rate
        from scm_invoice si
        left join scm_invoice_detail sid on si.invoice_no = sid.invoice_no
        <where>
            <trim>
                <if test="id != null and id &gt; 0">
                    and si.id = #{id}
                </if>
                <if test="invoiceNo != null and invoiceNo != ''">
                    and si.invoice_no like CONCAT('%',#{invoiceNo},'%')
                </if>
                <if test="supplierNo != null and supplierNo != ''">
                    and si.supplier_no like CONCAT('%',#{supplierNo},'%')
                </if>
                <if test="subjectType != null and subjectType &gt; 0">
                    and si.subject_type = #{subjectType}
                </if>
                <if test="subjectName != null and subjectName != ''">
                    and si.subject_name like CONCAT('%',#{subjectName}'%')
                </if>
<!--                <if test="supplierName != null and supplierName != ''">-->
<!--                    and ss.name like CONCAT('%',#{supplierName},'%')-->
<!--                </if>-->
                <if test="creator != null and creator &gt; 0">
                    and si.creator = #{creator}
                </if>
                <if test="status != null and status &gt; 0">
                    and si.status = #{status}
                </if>
                <if test="startCreateTime != null and startCreateTime != ''">
                    and si.create_time &gt;= #{startCreateTime}
                </if>
                <if test="endCreateTime != null and endCreateTime != ''">
                    and si.create_time <![CDATA[ <= ]]> #{endCreateTime}
                </if>
                <if test="startGmtCreate != null and startGmtCreate != ''">
                    and si.gmt_create &gt;= #{startGmtCreate}
                </if>
                <if test="endGmtCreate != null and endGmtCreate != ''">
                    and si.gmt_create <![CDATA[ < ]]> #{endGmtCreate}
                </if>
                <if test="belongSubjectType != null">
                    and si.belong_subject_type = #{belongSubjectType}
                </if>
                <if test="belongSubjectId != null">
                    and si.belong_subject_id = #{belongSubjectId}
                </if>
<!--                <if test="supplierNoFilterList != null and supplierNoFilterList.size() > 0">-->
<!--                    and si.supplier_no in-->
<!--                    <foreach close=")" collection="supplierNoFilterList" item="supplierNo" open="(" separator=",">-->
<!--                        #{supplierNo}-->
<!--                    </foreach>-->
<!--                </if>-->
                and si.is_delete = 0
                and sid.is_delete = 0
                order by si.supplier_no, si.create_time desc
            </trim>
        </where>
    </select>


    <select id="selectInvoicePage" parameterType="so.dian.invoice.pojo.param.InvoiceParam" resultMap="DtoResultMap">
        select
        si.id, si.invoice_no, si.supplier_no, si.type, si.raw_price, si.price, si.tax_rate,
        si.receive_time,si.memo,si.status,si.tax,
        si.used_amount, si.gmt_create, si.gmt_modified, si.is_delete, si.bill_no, si.creator, si.create_time, si.source,
        si.invoice_code, si.subject_type, si.subject_name,si.is_real,si.url,si.batch_no,si.check_code,
        si.belong_subject_type,
        si.belong_subject_id,si.process_status,si.review_remark,si.reviewer,si.review_time,si.in_check_pool,
        si.validate_code
        from scm_invoice si
        <where>
            <trim>
                <if test="id != null and id &gt; 0">
                    and si.id = #{id}
                </if>
                <if test="invoiceCode != null and invoiceCode != ''">
                    and si.invoice_code like CONCAT('%',#{invoiceCode},'%')
                </if>
                <if test="invoiceNo != null and invoiceNo != ''">
                    and si.invoice_no like CONCAT('%',#{invoiceNo},'%')
                </if>
                <if test="supplierNo != null and supplierNo != ''">
                    and si.supplier_no like CONCAT('%',#{supplierNo},'%')
                </if>
                <if test="subjectType != null and subjectType &gt; 0">
                    and si.subject_type = #{subjectType}
                </if>
                <if test="subjectName != null and subjectName != ''">
                    and si.subject_name like CONCAT('%', #{subjectName}, '%')
                </if>
                <if test="creator != null and creator &gt; 0">
                    and si.creator = #{creator}
                </if>
                <if test="nickName != null  and nickName != ''">
                    and si.create_name = #{nickName}
                </if>
                <if test="status != null and status &gt; 0">
                    and si.status = #{status}
                </if>
                <if test="startCreateTime != null and startCreateTime != ''">
                    and si.create_time &gt;= #{startCreateTime}
                </if>
                <if test="endCreateTime != null and endCreateTime != ''">
                    and si.create_time <![CDATA[ <= ]]> #{endCreateTime}
                </if>
                <if test="startGmtCreate != null and startGmtCreate != ''">
                    and si.gmt_create &gt;= #{startGmtCreate}
                </if>
                <if test="endGmtCreate != null and endGmtCreate != ''">
                    and si.gmt_create <![CDATA[ < ]]> #{endGmtCreate}
                </if>
                <if test="belongSubjectType != null">
                    and si.belong_subject_type = #{belongSubjectType}
                </if>
                <!--当belongSubjectId=1或0，默认小电发票-->
                <if test="belongSubjectId != null">
                    <choose>
                        <when test="belongSubjectId ==0 or belongSubjectId ==1 or belongSubjectId ==3">
                            <!--and si.belong_subject_id in (0,1,3)-->
                        </when>
                        <otherwise>
                            and si.belong_subject_id = #{belongSubjectId}
                        </otherwise>
                    </choose>
                </if>
<!--                <if test="supplierNoFilterList != null and supplierNoFilterList.size() > 0">-->
<!--                    and si.supplier_no in-->
<!--                    <foreach close=")" collection="supplierNoFilterList" item="supplierNo" open="(" separator=",">-->
<!--                        #{supplierNo}-->
<!--                    </foreach>-->
<!--                </if>-->
                <if test="isReal != null">
                    and si.is_real = #{isReal}
                </if>
                <if test="batchNo != null and batchNo != ''">
                    and si.batch_no=#{batchNo}
                </if>
                <if test="source != null and source &gt; 0">
                    and si.source=#{source}
                </if>
                <if test="type != null">
                    and si.type=#{type}
                </if>
                <if test="processStatus != null">
                    and si.process_status=#{processStatus}
                </if>
                <if test="isInCheckPool != null">
                    and si.in_check_pool=#{isInCheckPool}
                </if>
                <if test="validateCode != null">
                    and si.validate_code=#{validateCode}
                </if>
                <if test="invoiceIdList != null and invoiceIdList.size > 0">
                    AND si.id IN
                    <foreach collection="invoiceIdList" index="index" item="invoiceId" open="(" separator="," close=")">
                        #{invoiceId}
                    </foreach>
                </if>
                and si.is_delete = 0
                order by si.create_time desc
                limit #{offset}, #{pageSize}
            </trim>
        </where>
    </select>
    <select id="count" parameterType="so.dian.invoice.pojo.param.InvoiceParam" resultType="java.lang.Integer">
        select count(*)
        from scm_invoice si
        <where>
            <trim>
                <if test="id != null and id &gt; 0">
                    and si.id = #{id}
                </if>
                <if test="invoiceCode != null and invoiceCode != ''">
                    and si.invoice_code like CONCAT('%',#{invoiceCode},'%')
                </if>
                <if test="invoiceNo != null and invoiceNo != ''">
                    and si.invoice_no like CONCAT('%',#{invoiceNo},'%')
                </if>
                <if test="supplierNo != null and supplierNo != ''">
                    and si.supplier_no like CONCAT('%',#{supplierNo}, '%')
                </if>
                <if test="subjectType != null and subjectType &gt; 0">
                    and si.subject_type = #{subjectType}
                </if>
                <if test="subjectName != null and subjectName != ''">
                    and si.subject_name like CONCAT('%',#{subjectName}, '%')
                </if>
                <if test="creator != null and creator &gt; 0">
                    and si.creator = #{creator}
                </if>
                <if test="nickName != null  and nickName != ''">
                    and si.create_name = #{nickName}
                </if>
                <if test="status != null and status &gt; 0">
                    and si.status = #{status}
                </if>
                <if test="startCreateTime != null and startCreateTime != ''">
                    and si.create_time &gt;= #{startCreateTime}
                </if>
                <if test="endCreateTime != null and endCreateTime != ''">
                    and si.create_time <![CDATA[ <= ]]> #{endCreateTime}
                </if>
                <if test="startGmtCreate != null and startGmtCreate != ''">
                    and si.gmt_create &gt;= #{startGmtCreate}
                </if>
                <if test="endGmtCreate != null and endGmtCreate != ''">
                    and si.gmt_create <![CDATA[ < ]]> #{endGmtCreate}
                </if>
                <if test="belongSubjectType != null">
                    and si.belong_subject_type = #{belongSubjectType}
                </if>
<!--                <if test="supplierNoFilterList != null and supplierNoFilterList.size() > 0">-->
<!--                    and si.supplier_no in-->
<!--                    <foreach close=")" collection="supplierNoFilterList" item="supplierNo" open="(" separator=",">-->
<!--                        #{supplierNo}-->
<!--                    </foreach>-->
<!--                </if>-->
                <if test="isReal != null">
                    and si.is_real = #{isReal}
                </if>
                <if test="batchNo != null and batchNo != ''">
                    and si.batch_no=#{batchNo}
                </if>
                <if test="source != null and source &gt; 0">
                    and si.source=#{source}
                </if>
                <if test="type != null">
                    and si.type=#{type}
                </if>
                <if test="processStatus != null">
                    and si.process_status=#{processStatus}
                </if>
                <!--当belongSubjectId=1或0，默认小电发票-->
                <if test="belongSubjectId != null">
                    <choose>
                        <when test="belongSubjectId ==0 or belongSubjectId ==1">
                            and si.belong_subject_id in (0,1)
                        </when>
                        <otherwise>
                            and si.belong_subject_id = #{belongSubjectId}
                        </otherwise>
                    </choose>
                </if>
                <if test="invoiceIdList != null and invoiceIdList.size > 0">
                    AND si.id IN
                    <foreach collection="invoiceIdList" index="index" item="invoiceId" open="(" separator="," close=")">
                        #{invoiceId}
                    </foreach>
                </if>
                <if test="isInCheckPool != null">
                    and si.in_check_pool=#{isInCheckPool}
                </if>
                <if test="validateCode != null">
                    and si.validate_code=#{validateCode}
                </if>
                and si.is_delete = 0
            </trim>
        </where>
    </select>

    <select id="selectInvoiceExpressesDto" parameterType="so.dian.invoice.pojo.query.InvoiceExpressQuery"
            resultMap="ExpressesDtoResultMap">
        select a.id, a.invoice_no, a.supplier_no, a.type, a.price, a.receive_time, a.memo, a.status,
        a.used_amount, a.gmt_create, a.gmt_modified, a.is_delete, a.bill_no, a.creator,a.create_name, a.create_time,
        a.source,
        a.seller,a.invoice_code, a.subject_type, a.subject_name,a.belong_subject_type,a.belong_subject_id,a.batch_no,
        a.process_status ,b.express_name,b.express_tracking_no,b.id as expresses_id
        from scm_invoice as a left join scm_invoice_expresses_relation as b
        on a.id=b.invoice_id
        <where>
            a.is_delete=#{deleted}
            <if test="endCreateTime != null">
                <![CDATA[ and  a.create_time <#{endCreateTime} ]]>
            </if>
            <if test="creator != null">
                <![CDATA[and   a.creator >=#{creator} ]]>

            </if>
            <if test="startCreateTime != null">
                <![CDATA[and   a.create_time >=#{startCreateTime} ]]>

            </if>
            <if test="batchNo != null and batchNo != ''">
                <![CDATA[ and  a.batch_no =#{batchNo} ]]>
            </if>
            <if test="creatorName != null and creatorName != ''">
                and a.create_name =#{creatorName}

            </if>
            <if test="processStatus != null">
                and a.process_status =#{processStatus}
            </if>
            <if test="startGmtCreate != null">
                <![CDATA[ and  a.gmt_create >=#{startGmtCreate} ]]>

            </if>
            <if test="endGmtCreate  != null">
                <![CDATA[ and  a.gmt_create <#{endGmtCreate} ]]>

            </if>

        </where>
        order by a.id ASC
        limit 2000
    </select>

    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        insert into scm_invoice (invoice_no, supplier_no, type,
        raw_price, price, tax_rate,receive_time,
        memo, creator, status,
        used_amount, source, gmt_create,
        invoice_code, subject_type, subject_name,batch_no,is_real,check_code,
        belong_subject_type,belong_subject_id,buyer_tax_id,buyer,create_name,tax)
        values
        <foreach collection="list" item="obj" index="index" separator=",">
            (#{obj.invoiceNo,jdbcType=VARCHAR}, #{obj.supplierNo,jdbcType=VARCHAR}, #{obj.type,jdbcType=INTEGER},
            #{obj.rawPrice,jdbcType=DECIMAL}, #{obj.price,jdbcType=DECIMAL}, #{obj.taxRate,jdbcType=DECIMAL},
            #{obj.receiveTime,jdbcType=TIMESTAMP},
            #{obj.memo,jdbcType=VARCHAR}, #{obj.creator,jdbcType=INTEGER}, #{obj.status,jdbcType=INTEGER},
            #{obj.usedAmount,jdbcType=DECIMAL}, #{obj.source,jdbcType=INTEGER}, #{obj.gmtCreate,jdbcType=TIMESTAMP},
            #{obj.invoiceCode,jdbcType=VARCHAR}, #{obj.subjectType,jdbcType=INTEGER},
            #{obj.subjectName,jdbcType=VARCHAR},
            #{obj.batchNo,jdbcType=VARCHAR},#{obj.isReal,jdbcType=INTEGER},#{obj.checkCode,jdbcType=VARCHAR},
            #{obj.belongSubjectType,jdbcType=INTEGER},#{obj.belongSubjectId,jdbcType=INTEGER},#{obj.buyerTaxId,jdbcType=VARCHAR},#{obj.buyer,jdbcType=VARCHAR},
            #{obj.createName,jdbcType=VARCHAR},#{obj.tax,jdbcType=DECIMAL})
        </foreach>
    </insert>
    <insert id="insertSelective" keyProperty="id" keyColumn="id" parameterType="so.dian.invoice.pojo.entity.InvoiceDO">
        insert into scm_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="invoiceNo != null">
                invoice_no,
            </if>
            <if test="supplierNo != null">
                supplier_no,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="rawPrice != null">
                raw_price,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="taxRate != null">
                tax_rate,
            </if>
            <if test="receiveTime != null">
                receive_time,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="usedAmount != null">
                used_amount,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="billNo != null">
                bill_no,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="invoiceCode != null">
                invoice_code,
            </if>
            <if test="subjectType != null">
                subject_type,
            </if>
            <if test="subjectName != null">
                subject_name,
            </if>
            <if test="kind != null">
                kind,
            </if>
            <if test="checkCode != null">
                check_code,
            </if>
            <if test="tax != null">
                tax,
            </if>
            <if test="seller != null">
                seller,
            </if>
            <if test="sellerTaxId != null">
                seller_tax_id,
            </if>
            <if test="buyerTaxId != null">
                buyer_tax_id,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="isReal != null">
                is_real,
            </if>
            <if test="buyer != null">
                buyer,
            </if>
            <if test="batchNo != null">
                batch_no,
            </if>
            <if test="belongSubjectType != null">
                belong_subject_type,
            </if>
            <if test="belongSubjectId != null">
                belong_subject_id,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="needCheck != null">
                need_check,
            </if>
            <if test="inCheckPool != null">
                in_check_pool,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="invoiceNo != null">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="supplierNo != null">
                #{supplierNo,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="rawPrice != null">
                #{rawPrice,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="receiveTime != null">
                #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="usedAmount != null">
                #{usedAmount,jdbcType=DECIMAL},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="billNo != null">
                #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="subjectType != null">
                #{subjectType,jdbcType=INTEGER},
            </if>
            <if test="subjectName != null">
                #{subjectName,jdbcType=VARCHAR},
            </if>
            <if test="kind != null">
                #{kind,jdbcType=VARCHAR},
            </if>
            <if test="checkCode != null">
                #{checkCode,jdbcType=VARCHAR},
            </if>
            <if test="tax != null">
                #{tax,jdbcType=DECIMAL},
            </if>
            <if test="seller != null">
                #{seller,jdbcType=VARCHAR},
            </if>
            <if test="sellerTaxId != null">
                #{sellerTaxId,jdbcType=VARCHAR},
            </if>
            <if test="buyerTaxId != null">
                #{buyerTaxId,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="isReal != null">
                #{isReal,jdbcType=TINYINT},
            </if>
            <if test="buyer != null">
                #{buyer,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="belongSubjectType != null">
                #{belongSubjectType,jdbcType=INTEGER},
            </if>
            <if test="belongSubjectId != null">
                #{belongSubjectId,jdbcType=INTEGER},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="needCheck != null">
                #{needCheck,jdbcType=INTEGER},
            </if>
            <if test="inCheckPool != null">
                #{inCheckPool,jdbcType=INTEGER},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=INTEGER},
            </if>
            now()
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceDO">
        update scm_invoice
        <set>
            <if test="invoiceNo != null">
                invoice_no = #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="supplierNo != null">
                supplier_no = #{supplierNo,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="rawPrice != null">
                raw_price = #{rawPrice,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="usedAmount != null">
                used_amount = #{usedAmount,jdbcType=DECIMAL},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="billNo != null">
                bill_no = #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null">
                invoice_code = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="subjectType != null">
                subject_type = #{subjectType,jdbcType=INTEGER},
            </if>
            <if test="subjectName != null">
                subject_name = #{subjectName,jdbcType=VARCHAR},
            </if>
            <if test="kind != null">
                kind = #{kind,jdbcType=VARCHAR},
            </if>
            <if test="checkCode != null">
                check_code = #{checkCode,jdbcType=VARCHAR},
            </if>
            <if test="tax != null">
                tax = #{tax,jdbcType=DECIMAL},
            </if>
            <if test="seller != null">
                seller = #{seller,jdbcType=VARCHAR},
            </if>
            <if test="sellerTaxId != null">
                seller_tax_id = #{sellerTaxId,jdbcType=VARCHAR},
            </if>
            <if test="buyerTaxId != null">
                buyer_tax_id = #{buyerTaxId,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="isReal != null">
                is_real = #{isReal,jdbcType=TINYINT},
            </if>
            <if test="buyer != null">
                buyer = #{buyer,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                batch_no=#{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="belongSubjectType != null">
                belong_subject_type=#{belongSubjectType,jdbcType=INTEGER},
            </if>
            <if test="belongSubjectId != null">
                belong_subject_id=#{belongSubjectId,jdbcType=INTEGER},
            </if>
            <if test="processStatus != null">
                process_status=#{processStatus,jdbcType=INTEGER},
            </if>
            <if test="needCheck != null">
                need_check=#{needCheck,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateInvoiceByInvoiceCodeAndNo" parameterType="so.dian.invoice.pojo.entity.InvoiceDO">
        update scm_invoice
        <set>
            <if test="supplierNo != null">
                supplier_no = #{supplierNo,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="rawPrice != null">
                raw_price = #{rawPrice,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="usedAmount != null">
                used_amount = #{usedAmount,jdbcType=DECIMAL},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="billNo != null">
                bill_no = #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null">
                invoice_code = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="subjectType != null">
                subject_type = #{subjectType,jdbcType=INTEGER},
            </if>
            <if test="subjectName != null">
                subject_name = #{subjectName,jdbcType=VARCHAR},
            </if>
            <if test="kind != null">
                kind = #{kind,jdbcType=VARCHAR},
            </if>
            <if test="checkCode != null">
                check_code = #{checkCode,jdbcType=VARCHAR},
            </if>
            <if test="tax != null">
                tax = #{tax,jdbcType=DECIMAL},
            </if>
            <if test="seller != null">
                seller = #{seller,jdbcType=VARCHAR},
            </if>
            <if test="sellerTaxId != null">
                seller_tax_id = #{sellerTaxId,jdbcType=VARCHAR},
            </if>
            <if test="buyerTaxId != null">
                buyer_tax_id = #{buyerTaxId,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="isReal != null">
                is_real = #{isReal,jdbcType=TINYINT},
            </if>
            <if test="buyer != null">
                buyer = #{buyer,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                batch_no=#{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="belongSubjectType != null">
                belong_subject_type=#{belongSubjectType,jdbcType=INTEGER},
            </if>
            <if test="belongSubjectId != null">
                belong_subject_id=#{belongSubjectId,jdbcType=INTEGER},
            </if>
        </set>
        where invoice_no = #{invoiceNo,jdbcType=VARCHAR}
        and invoice_code = #{invoiceCode,jdbcType=VARCHAR}
    </update>

    <select id="selectInvoiceList" parameterType="so.dian.invoice.pojo.param.InvoiceParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scm_invoice
        where 1=1
        <include refid="QueryCondition"/>
        limit #{offset}, #{pageSize}
    </select>

    <!--
     更新：发票复核状态更新
     频次：100次/日
     数据量：10
     -->
    <update id="updateInvoiceToReview">
        UPDATE scm_invoice
        SET process_status=#{processStatus,jdbcType=INTEGER},
            reviewer=#{reviewer,jdbcType=INTEGER},
            review_time=#{reviewTime,jdbcType=TIMESTAMP},
            review_remark=#{reviewRemark,jdbcType=VARCHAR},
            gmt_modified=now()
        WHERE id = #{id,jdbcType=INTEGER}
          AND process_status = #{beforeProcessStatus,jdbcType=INTEGER}
          AND is_delete = 0
    </update>

    <select id="listInvoiceExportPage" parameterType="so.dian.invoice.pojo.param.InvoiceExportParam"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE
        <include refid="whereCondition"/>
        order by id
        limit #{offset}, #{pageSize}
    </select>

    <select id="countInvoiceExportList" parameterType="so.dian.invoice.pojo.param.InvoiceExportParam"
            resultType="java.lang.Long">
        SELECT count(*)
        FROM scm_invoice
        WHERE
        <include refid="whereCondition"/>
    </select>

    <select id="countInvoice" parameterType="so.dian.invoice.pojo.query.InvoiceFilterTimeQuery"
            resultType="java.lang.Long">
        SELECT count(*)
        FROM scm_invoice
        WHERE
        <include refid="queryCondition"/>
    </select>

    <select id="selectInvoice" parameterType="so.dian.invoice.pojo.query.InvoiceFilterTimeQuery"
            resultMap="BaseResultMap">
        SELECT id, invoice_no, `type`, price, status, create_time, invoice_code, subject_name
        FROM scm_invoice
        WHERE
        <include refid="queryCondition"/>
        ORDER BY create_time DESC
        limit #{offset}, #{pageSize}
    </select>

    <sql id="queryCondition">
        is_delete = 0
        AND belong_subject_type = #{belongSubjectType}
        AND creator = #{loginUserId}
        AND <![CDATA[`create_time` <= DATE_ADD(#{filterTime, jdbcType=TIMESTAMP},INTERVAL 1 MONTH )
        and `create_time` >= #{filterTime, jdbcType=TIMESTAMP}]]>
    </sql>


    <sql id="whereCondition">
        is_delete = 0
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="invoiceCode != null and invoiceCode != ''">
            and invoice_code = #{invoiceCode}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and invoice_no = #{invoiceNo}
        </if>
        <if test="type != null">
            and `type` = #{type}
        </if>
        <if test="subjectType != null">
            and subject_type = #{subjectType}
        </if>
        <if test="isReal != null">
            and is_real = #{isReal}
        </if>
        <if test="batchNo != null">
            and batch_no = #{batchNo}
        </if>
        <if test="source != null">
            and source = #{source}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="processStatus != null">
            and process_status = #{processStatus}
        </if>
        <if test="subjectName != null and subjectName != ''">
            and subject_name like CONCAT('%', #{subjectName}, '%')
        </if>
        <if test="startCreateTime != null and startCreateTime != ''">
            and create_time &gt;= #{startCreateTime}
        </if>
        <if test="endCreateTime != null and endCreateTime != ''">
            and create_time <![CDATA[ <= ]]> CONCAT(#{endCreateTime}, ' 23:59:59.999')
        </if>
        <if test="startGmtCreate != null and startGmtCreate != ''">
            and gmt_create &gt;= #{startGmtCreate}
        </if>
        <if test="endGmtCreate != null and endGmtCreate != ''">
            and gmt_create <![CDATA[ < ]]>  CONCAT(#{endGmtCreate}, ' 23:59:59.999')
        </if>
        <if test="creator != null">
            and creator =#{creator}
        </if>
        <if test="nickName != null and nickName != ''">
            and create_name =#{nickName}
        </if>
        <if test="validateCode != null and validateCode != ''">
            and validate_code =#{validateCode}
        </if>
        <if test="isInCheckPool != null">
            and in_check_pool=#{isInCheckPool}
        </if>
        <if test="invoiceIdList != null and invoiceIdList.size > 0">
            AND id IN
            <foreach collection="invoiceIdList" index="index" item="invoiceId" open="(" separator="," close=")">
                #{invoiceId}
            </foreach>
        </if>
        <if test="belongSubjectId != null">
            <choose>
                <when test="belongSubjectId ==0 or belongSubjectId ==1 or belongSubjectId ==3">
                    <!--and si.belong_subject_id in (0,1,3)-->
                </when>
                <otherwise>
                    and belong_subject_id = #{belongSubjectId}
                </otherwise>
            </choose>
        </if>
    </sql>

    <!--
       更新：批量更新发票
       频次：100次/日
       数据量：20
    -->
    <update id="batchUpdateInvoiceProcessStatus" parameterType="map">
        UPDATE scm_invoice
        SET process_status=#{processStatus,jdbcType=INTEGER},
        gmt_modified=now()
        WHERE id in
        <foreach collection="invoiceIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <!--
       查询：通过销售方名称模糊查询
       频次：100次/日
       数据量：20
    -->
    <select id="listInvoiceBySubjectName" resultType="java.lang.String">
        SELECT DISTINCT subject_name
        FROM scm_invoice
        WHERE is_delete=0 AND subject_name!=''
        <!--当belongSubjectId=1或0，默认小电发票-->
        <if test="belongSubjectId != null">
            <choose>
                <when test="belongSubjectId ==0 or belongSubjectId ==1">
                    AND belong_subject_id in (0,1)
                </when>
                <otherwise>
                    AND belong_subject_id = #{belongSubjectId}
                </otherwise>
            </choose>
        </if>
        AND subject_name like CONCAT(#{subjectName},'%')
        limit 20
    </select>

    <!--
       查询：通过销售方名称模糊查询
       频次：100次/日
       数据量：20
    -->
    <select id="findByInvoiceDeductQueryParam"
            parameterType="so.dian.invoice.pojo.param.InvoiceDeductQueryParam"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE is_delete = 0
        AND status in (1,2)
        AND belong_subject_type in (0, 5)
        <if test="invoiceId != null">
            and id = #{invoiceId}
        </if>
        <if test="businessType != null">
            and subject_type = #{businessType}
        </if>
        <if test="invoiceCode != null and invoiceCode != ''">
            and invoice_code = #{invoiceCode}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and invoice_no = #{invoiceNo}
        </if>
        <if test="buyerName != null and buyerName != ''">
            AND buyer = #{buyerName}
        </if>
        <if test="subjectName != null and subjectName != ''">
            AND subject_name = #{subjectName}
        </if>
        <if test="belongSubjectId != null">
            <choose>
                <when test="belongSubjectId ==0 or belongSubjectId ==1 or belongSubjectId ==3">
                    <!--and si.belong_subject_id in (0,1,3)-->
                </when>
                <otherwise>
                    and belong_subject_id = #{belongSubjectId}
                </otherwise>
            </choose>
        </if>
        <if test="subjectNameList != null and subjectNameList.size > 0">
            and subject_name in
            <foreach collection="subjectNameList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY id DESC
        limit 500
    </select>
    <!--
        查询：批量查询发票信息
        频次：100次/日
        数据量：20万
     -->
    <select id="findByInvoiceNoAndInvoiceCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE is_delete = 0
        AND (
        <foreach collection="list" item="item" separator="OR">
            (invoice_no=#{item.invoiceNo} and invoice_code=#{item.invoiceCode})
        </foreach>
        )
    </select>
    <!-- 回滚发票 -->
    <update id="recoverInvoice">
        update scm_invoice
        set status       = #{status},
            used_amount  = used_amount - #{amount},
            gmt_modified = now()
        where id = #{id}
    </update>
    <!-- 核销发票 -->
    <update id="deductInvoice">
        update scm_invoice
        set status   = #{status},
        used_amount  =#{target},
        gmt_modified = now()
        where id = #{id}
        and used_amount = #{source}
    </update>

    <!--
       查询：根据类型批量查询发票信息
       频次：100次/日
       数据量：20万
    -->
    <select id="findBySubjectType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE is_delete = 0
        AND subject_type = #{subjectType}
        AND id > #{lastId}
        limit 1000
    </select>

    <update id="trimInvoice" parameterType="so.dian.invoice.pojo.entity.InvoiceDO">
        UPDATE scm_invoice
        SET subject_name = #{subjectName},
            seller       = #{seller},
            buyer        = #{buyer}
        where id = #{id}
    </update>

    <!--
    查询：根据录入时间查询
    -->
    <select id="listNeedValidateByCreateTime" resultType="so.dian.invoice.pojo.entity.InvoiceDO">
        select
        <include refid="Base_Column_List"/>
        from scm_invoice
        where is_delete = 0 and belong_subject_id IN (0,1)
        and create_time <![CDATA[ >= ]]> #{startCreateTime}
        and create_time <![CDATA[ < ]]> #{endCreateTime}
        and is_real in
        <foreach collection="isRealListEnum" item="isReal" open="(" separator="," close=")">
            #{isReal.code}
        </foreach>
        <!-- 排除供应链发票 -->
        and subject_type != 1
        and type IN
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>

    <select id="listByIds" resultType="so.dian.invoice.pojo.entity.InvoiceDO">
        select
        <include refid="Base_Column_List"/>
        from scm_invoice
        where is_delete = 0 and belong_subject_id IN (0,1)
        and id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdateRealStatus">
        update scm_invoice
        set is_real = #{isReal}
        where is_delete = 0 and belong_subject_id IN (0,1)
        and id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="listInvoiceByTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE create_time <![CDATA[ >= ]]> #{beginTime}
        AND create_time <![CDATA[ <= ]]> #{endTime}
        AND status IN(2,3)
        AND subject_type = 2
        AND process_status = 1
        AND in_check_pool = 0
        AND need_check = 1
        AND is_delete = 0
    </select>

    <select id="listNeedCheckInvoiceByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE subject_type = 2
        AND in_check_pool = 0
        AND need_check = 1
        AND status IN(2,3)
        AND is_delete = 0
        AND process_status = 1
        AND id IN
        <foreach collection="invoiceIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="batchUpdateInCheckPool">
        UPDATE scm_invoice
        SET in_check_pool = #{inCheckPool}
        WHERE is_delete = 0
        AND in_check_pool = #{beforeInCheckPool}
        AND id IN
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateValidationById">
        update scm_invoice
        set is_real       = #{isReal},
            validate_code = #{validateCode}
        where is_delete = 0
          and belong_subject_id IN (0, 1)
          and id = #{id}
    </update>

    <select id="listNotValidate" resultType="so.dian.invoice.pojo.entity.InvoiceDO">
        select
        <include refid="Base_Column_List"/>
        from scm_invoice
        where is_delete = 0 and belong_subject_id IN (0,1)
        and create_time <![CDATA[ >= ]]> #{startTime}
        and create_time <![CDATA[ < ]]> #{endTime}
        <!-- 验真未通过 -->
        and is_real = 2
        <!-- 排除供应链发票 -->
        and subject_type != 1
        <if test="validationCode != null and validationCode.size > 0">
            and validate_code IN
            <foreach collection="validationCode" item="code" open="(" separator="," close=")">
                #{code.code}
            </foreach>
        </if>
    </select>
    <select id="selectValidateInvoice" resultType="so.dian.invoice.pojo.entity.InvoiceDO">
        select
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE is_delete = 0 and belong_subject_id IN (0,1)
        and create_time <![CDATA[ >= ]]> #{startTime}
        and create_time <![CDATA[ < ]]> #{endTime}
        and is_real = #{isReal.code}
        <!-- 排除供应链发票 -->
        and subject_type != 1
    </select>

    <select id="listInvoiceByEndTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE create_time <![CDATA[ <= ]]> #{endTime}
        AND status IN(2,3)
        AND subject_type = 2
        AND process_status = 1
        AND in_check_pool = 0
        AND need_check = 1
        AND is_delete = 0
    </select>
    <select id="listNeedCheckIsNoNull" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice WHERE need_check IS NOT NULL AND ISNULL(city_code)
    </select>

    <update id="batchUpdateCityCode">
        update scm_invoice
        SET city_code =
        <foreach collection="creatorCityCodeMap" item="cityCode" index="creator"
                 separator=" " open="case creator" close="end">
            when #{creator} then #{cityCode}
        </foreach>
        , gmt_modified = now()
        where id in (
        <foreach collection="invoiceIds" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="findByIdGreaterThan" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scm_invoice
        WHERE is_delete = 0
        and id <![CDATA[ > ]]>  ${id}
        order by id asc
        limit 100
    </select>
    
    <select id="queryInvoiceList" parameterType="so.dian.invoice.pojo.param.ScmInvoiceDeductQueryParam" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"/>
      FROM scm_invoice
      <where>
        <if test="invoiceMapList!=null and invoiceMapList.size()>0">
          <foreach item="item" collection="invoiceMapList" separator="OR" open="(" close=")">
            (invoice_no=#{item.invoiceNo} and invoice_code=#{item.invoiceCode})
          </foreach>
        </if>
        <if test="invoiceCode!=null and invoiceCode!=''">
          and invoice_code = #{invoiceCode}
        </if>
        <if test="invoiceNo!=null and invoiceNo!=''">
          and invoice_no = #{invoiceNo}
        </if>
        <if test="businessType!=null">
          and subject_type = #{businessType}
        </if>
        <if test="sellerName != null">
          and seller = #{sellerName}
        </if>
        <if test="buyerName != null">
          and buyer = #{buyerName}
        </if>
        <if test="sellerNames != null and sellerNames.size() > 0">
          and seller in
          <foreach collection="sellerNames" item="sellerName" open="(" close=")" separator=",">
            #{sellerName}
          </foreach>
        </if>
        <if test="buyerNames != null and buyerNames.size() > 0">
          and buyer in
          <foreach collection="buyerNames" item="buyerName" open="(" close=")" separator=",">
            #{buyerName}
          </foreach>
        </if>
        and status in (1,2)
        and is_delete = 0
      </where>
      limit 1000
    </select>
    
    

    
</mapper>