<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceIdentifyRecordDAO">
  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType"/>
    <result column="invoice_date" jdbcType="VARCHAR" property="invoiceDate"/>
    <result column="check_code" jdbcType="VARCHAR" property="checkCode"/>
    <result column="pretax_amount" jdbcType="VARCHAR" property="pretaxAmount"/>
    <result column="total" jdbcType="VARCHAR" property="total"/>
    <result column="tax" jdbcType="VARCHAR" property="tax"/>
    <result column="seller" jdbcType="VARCHAR" property="seller"/>
    <result column="seller_tax_id" jdbcType="VARCHAR" property="sellerTaxId"/>
    <result column="buyer" jdbcType="VARCHAR" property="buyer"/>
    <result column="buyer_tax_id" jdbcType="VARCHAR" property="buyerTaxId"/>
    <result column="kind" jdbcType="VARCHAR" property="kind"/>
    <result column="is_real" jdbcType="TINYINT" property="isReal"/>
    <result column="is_related" jdbcType="TINYINT" property="isRelated"/>
    <result column="url" jdbcType="VARCHAR" property="url"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="creator" jdbcType="INTEGER" property="creator"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="updater" jdbcType="INTEGER" property="updater"/>
    <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    <result column="details" jdbcType="VARCHAR" property="details"/>
    <result column="is_identify" jdbcType="TINYINT" property="isIdentify"/>
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
    <result column="reason" jdbcType="VARCHAR" property="reason"/>
    <result column="result" jdbcType="VARCHAR" property="result"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, invoice_no, invoice_code, invoice_type, invoice_date, check_code, pretax_amount, 
    total, tax, seller, seller_tax_id, buyer, buyer_tax_id, kind, is_real, is_related, 
    url, create_time, creator, update_time, updater, deleted,details,is_identify,batch_no,reason,result
  </sql>
  <sql id="QueryCondition">
    <if test="id != null and id != ''">
      and id = #{id}
    </if>
    <if test="invoiceCode != null and invoiceCode != ''">
      and invoice_code = #{invoiceCode}
    </if>
    <if test="invoiceNo != null and invoiceNo != ''">
      and invoice_no = #{invoiceNo}
    </if>
    <if test="seller != null and seller != ''">
      and seller like '%${seller}%'
    </if>
    <if test="deleted != null">
      and deleted = #{deleted}
    </if>
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scm_invoice_identify_record
    where deleted=0 and id = #{id,jdbcType=INTEGER}
  </select>

  <insert id="insertSelective" parameterType="so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO" keyProperty="id">
    insert into scm_invoice_identify_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="invoiceDate != null">
        invoice_date,
      </if>
      <if test="checkCode != null">
        check_code,
      </if>
      <if test="pretaxAmount != null">
        pretax_amount,
      </if>
      <if test="total != null">
        total,
      </if>
      <if test="tax != null">
        tax,
      </if>
      <if test="seller != null">
        seller,
      </if>
      <if test="sellerTaxId != null">
        seller_tax_id,
      </if>
      <if test="buyer != null">
        buyer,
      </if>
      <if test="buyerTaxId != null">
        buyer_tax_id,
      </if>
      <if test="kind != null">
        kind,
      </if>
      <if test="isReal != null">
        is_real,
      </if>
      <if test="isRelated != null">
        is_related,
      </if>
      <if test="url != null">
        url,
      </if>
      create_time,
      <if test="creator != null">
        creator,
      </if>
      update_time,
      <if test="updater != null">
        updater,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="details != null">
        details,
      </if>
      <if test="isIdentify != null">
        is_identify,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="result != null">
        result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceDate != null">
        #{invoiceDate,jdbcType=VARCHAR},
      </if>
      <if test="checkCode != null">
        #{checkCode,jdbcType=VARCHAR},
      </if>
      <if test="pretaxAmount != null">
        #{pretaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        #{total,jdbcType=VARCHAR},
      </if>
      <if test="tax != null">
        #{tax,jdbcType=VARCHAR},
      </if>
      <if test="seller != null">
        #{seller,jdbcType=VARCHAR},
      </if>
      <if test="sellerTaxId != null">
        #{sellerTaxId,jdbcType=VARCHAR},
      </if>
      <if test="buyer != null">
        #{buyer,jdbcType=VARCHAR},
      </if>
      <if test="buyerTaxId != null">
        #{buyerTaxId,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        #{kind,jdbcType=VARCHAR},
      </if>
      <if test="isReal != null">
        #{isReal,jdbcType=TINYINT},
      </if>
      <if test="isRelated != null">
        #{isRelated,jdbcType=TINYINT},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      now(),
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      now(),
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="details != null">
        #{details,jdbcType=VARCHAR},
      </if>
      <if test="isIdentify != null">
        #{isIdentify,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="so.dian.invoice.pojo.entity.InvoiceIdentifyRecordDO">
    update scm_invoice_identify_record
    <set>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceDate != null">
        invoice_date = #{invoiceDate,jdbcType=VARCHAR},
      </if>
      <if test="checkCode != null">
        check_code = #{checkCode,jdbcType=VARCHAR},
      </if>
      <if test="pretaxAmount != null">
        pretax_amount = #{pretaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        total = #{total,jdbcType=VARCHAR},
      </if>
      <if test="tax != null">
        tax = #{tax,jdbcType=VARCHAR},
      </if>
      <if test="seller != null">
        seller = #{seller,jdbcType=VARCHAR},
      </if>
      <if test="sellerTaxId != null">
        seller_tax_id = #{sellerTaxId,jdbcType=VARCHAR},
      </if>
      <if test="buyer != null">
        buyer = #{buyer,jdbcType=VARCHAR},
      </if>
      <if test="buyerTaxId != null">
        buyer_tax_id = #{buyerTaxId,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        kind = #{kind,jdbcType=VARCHAR},
      </if>
      <if test="isReal != null">
        is_real = #{isReal,jdbcType=TINYINT},
      </if>
      <if test="isRelated != null">
        is_related = #{isRelated,jdbcType=TINYINT},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      update_time =now(),
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="details != null">
        details = #{details,jdbcType=VARCHAR},
      </if>
      <if test="isIdentify != null">
        is_identify=#{isIdentify,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        batch_no=#{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason=#{reason,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result=#{result,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchDelete">
    update scm_invoice_identify_record set deleted = -1, updater = #{updater}
    where id in
    <foreach collection="idList" close=")" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="getListCount" resultType="java.lang.Integer">
    select count(*) from scm_invoice_identify_record where deleted=0 and is_related=0
    <if test="isIdentify != null">
      and is_identify=#{isIdentify,jdbcType=TINYINT}
    </if>
    <if test="isReal != null">
      and is_real=#{isReal,jdbcType=VARCHAR}
    </if>
    <if test="creator != null">
      and creator = #{creator}
    </if>
  </select>

  <select id="getList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scm_invoice_identify_record
    where deleted=0 and is_related=0
    <if test="isIdentify != null">
      and is_identify=#{isIdentify,jdbcType=TINYINT}
    </if>
    <if test="isReal != null">
      and is_real=#{isReal,jdbcType=VARCHAR}
    </if>
    <if test="creator != null">
      and creator = #{creator}
    </if>
    order by id desc
  </select>

  <select id="getListByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scm_invoice_identify_record
    where deleted=0
    AND id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="selectInvoiceByInvoiceCodeAndNo" resultType="java.lang.Integer">
    select
    count(*)
    from scm_invoice_identify_record
    where deleted=0
    and invoice_code=#{invoiceCode}
    and invoice_no=#{invoiceNo}
  </select>

  <select id="selectInvoiceRecodeByInvoiceCodeAndNo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM scm_invoice_identify_record
    WHERE deleted=0
    AND invoice_code=#{invoiceCode}
    AND invoice_no=#{invoiceNo}
    limit 1
  </select>


  <update id="updateIsRelatedByIds">
    update scm_invoice_identify_record set is_related = #{isRelated},update_time=now()
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <select id="selectInvoiceRecordList" parameterType="so.dian.invoice.pojo.param.InvoiceRecordParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from scm_invoice_identify_record
    where 1=1
    <include refid="QueryCondition" />
    limit #{offset}, #{pageSize}
  </select>

  <select id="selectInvoiceRecordByMinId" parameterType="long" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM scm_invoice_identify_record
    WHERE id >= #{minId}
    AND deleted = 0
    AND create_time >= '2019-03-01 00:00:00'
    AND create_time<![CDATA[ <= ]]>'2019-10-31 23:59:59.999'
    limit 200;
  </select>
</mapper>