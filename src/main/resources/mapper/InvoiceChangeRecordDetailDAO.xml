<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceChangeRecordDetailDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="changeRecordId" column="change_record_id" jdbcType="BIGINT"/>
            <result property="manageId" column="manage_id" jdbcType="BIGINT"/>
            <result property="manageDetailId" column="manage_detail_id" jdbcType="BIGINT"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productCount" column="product_count" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="BIGINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,manage_id,manage_detail_id,
        product_code,product_count,type,amount,
        deleted,gmt_create,gmt_update
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_change_record_detail
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_change_record_detail
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO" useGeneratedKeys="true">
        insert into invoice_change_record_detail
        ( id,change_record_id,manage_id,manage_detail_id
        ,product_code,product_count,type,amount
        ,deleted,gmt_create,gmt_update
        )
        values (#{id,jdbcType=BIGINT},#{changeRecordId,jdbcType=BIGINT},#{manageId,jdbcType=BIGINT},#{manageDetailId,jdbcType=BIGINT}
        ,#{productCode,jdbcType=VARCHAR},#{productCount,jdbcType=BIGINT},#{type,jdbcType=VARCHAR},#{amount,jdbcType=BIGINT}
        ,#{deleted,jdbcType=TINYINT},#{gmtCreate,jdbcType=BIGINT},#{gmtUpdate,jdbcType=BIGINT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO" useGeneratedKeys="true">
        insert into invoice_change_record_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="change_record_id != null">change_record_id,</if>
                <if test="manageId != null">manage_id,</if>
                <if test="manageDetailId != null">manage_detail_id,</if>
                <if test="productCode != null">product_code,</if>
                <if test="productCount != null">product_count,</if>
                <if test="type != null">type,</if>
                <if test="amount != null">amount,</if>
                <if test="deleted != null">deleted,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>changeRecordId
                <if test="change_record_id != null">#{changeRecordId,jdbcType=BIGINT},</if>
                <if test="manageDetailId != null">#{manageDetailId,jdbcType=BIGINT},</if>
                <if test="manageId != null">#{manageId,jdbcType=BIGINT},</if>
                <if test="manageDetailId != null">#{manageDetailId,jdbcType=BIGINT},</if>
                <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
                <if test="productCount != null">#{productCount,jdbcType=BIGINT},</if>
                <if test="type != null">#{type,jdbcType=VARCHAR},</if>
                <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO">
        update invoice_change_record_detail
        <set>
                <if test="manageId != null">
                    manage_id = #{manageId,jdbcType=BIGINT},
                </if>
                <if test="manageDetailId != null">
                    change_record_id = #{changeRecordId,jdbcType=BIGINT},
                </if>
                <if test="manageDetailId != null">
                    manage_detail_id = #{manageDetailId,jdbcType=BIGINT},
                </if>
                <if test="productCode != null">
                    product_code = #{productCode,jdbcType=VARCHAR},
                </if>
                <if test="productCount != null">
                    product_count = #{productCount,jdbcType=BIGINT},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=VARCHAR},
                </if>
                <if test="amount != null">
                    amount = #{amount,jdbcType=BIGINT},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO">
        update invoice_change_record_detail
        set 
            manage_id =  #{manageId,jdbcType=BIGINT},
            change_record_id =  #{changeRecordId,jdbcType=BIGINT},
            manage_detail_id =  #{manageDetailId,jdbcType=BIGINT},
            product_code =  #{productCode,jdbcType=VARCHAR},
            product_count =  #{productCount,jdbcType=BIGINT},
            type =  #{type,jdbcType=VARCHAR},
            amount =  #{amount,jdbcType=BIGINT},
            deleted =  #{deleted,jdbcType=TINYINT},
            gmt_create =  #{gmtCreate,jdbcType=BIGINT},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="getByManageId" resultType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO">
        SELECT * FROM invoice_change_record_detail WHERE deleted = 0 and manage_id = #{manageId}
    </select>
</mapper>
