<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.SupplierInvoiceDetailBillNoMapper">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.SupplierInvoiceDetailBillNoDO">
        <id property="id" column="id"/>
        <result property="supplierInvoiceDetailId" column="supplier_invoice_detail_id"/>
        <result property="supplierInvoiceId" column="supplier_invoice_id"/>
        <result property="billType" column="bill_type"/>
        <result property="billNo" column="bill_no"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtUpdate" column="gmt_update"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Insert_Column_List">
        supplier_invoice_detail_id,supplier_invoice_id,bill_type,bill_no,
gmt_create,gmt_update,deleted
    </sql>

    <sql id="Select_Column_List">
        id,
        <include refid="Insert_Column_List"/>
    </sql>

    <sql id="Where_Column_List">
    </sql>

    <sql id="Insert_Value_List">
        (
        #{do.supplierInvoiceDetailId},
        #{do.supplierInvoiceId},
        #{do.billType},
        #{do.billNo},
        #{do.gmtCreate},
        #{do.gmtUpdate},
        #{do.deleted}
        )
    </sql>

    <!--
        功能：新增SupplierInvoiceDetailBillNo
    -->
    <insert id="insert">
        INSERT INTO supplier_invoice_detail_bill_no(<include refid="Insert_Column_List"/>)
        VALUES
        <include refid="Insert_Value_List"/>
    </insert>

    <!--
        功能：批量新增SupplierInvoiceDetailBillNo
    -->
    <insert id="insertBatch">
        INSERT INTO supplier_invoice_detail_bill_no(<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="doList" item="do" index="index" separator=",">
            <include refid="Insert_Value_List"/>
        </foreach>
    </insert>

    <!--
        功能：根据id更新SupplierInvoiceDetailBillNo
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <update id="updateById">
        UPDATE supplier_invoice_detail_bill_no
        <trim prefix="SET" suffixOverrides=",">
            <if test="do.supplierInvoiceDetailId != null">supplier_invoice_detail_id = #{do.supplierInvoiceDetailId},</if>
            <if test="do.supplierInvoiceId != null">supplier_invoice_id = #{do.supplierInvoiceId},</if>
            <if test="do.billType != null">bill_type = #{do.billType},</if>
            <if test="do.billNo != null">bill_no = #{do.billNo},</if>
            <if test="do.gmtUpdate != null">gmt_update = #{do.gmtUpdate},</if>
            <if test="do.deleted != null">deleted = #{do.deleted},</if>
        </trim>
        where id = #{do.id}
    </update>

    <!--
        功能：根据id查询SupplierInvoiceDetailBillNo
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_detail_bill_no
        WHERE
            id = #{id}
        AND deleted = 0
    </select>

    <update id="deleteBySupplierInvoiceId">
        update supplier_invoice_detail_bill_no set deleted = 1
        where supplier_invoice_id = #{supplierInvoiceId}
          AND deleted = 0
    </update>

    <select id="findInDetailId" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_detail_bill_no
        WHERE supplier_invoice_detail_id in
        <foreach collection="detailIdList" item="detailId" separator="," open="(" close=")">
            #{detailId}
        </foreach>
        AND deleted = 0
    </select>
    
    <select id="findByBillTypeAndBillNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_detail_bill_no
        WHERE bill_type = #{billType}
        and bill_no = #{billNo}
        AND deleted = 0
    </select>
</mapper>