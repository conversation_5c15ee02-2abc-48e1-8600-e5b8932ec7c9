<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.SupplierInvoiceDetailMapper">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.SupplierInvoiceDetailDO">
        <id property="id" column="id"/>
        <result property="supplierInvoiceId" column="supplier_invoice_id"/>
        <result property="invoiceNo" column="invoice_no"/>
        <result property="invoiceCode" column="invoice_code"/>
        <result property="checkCode" column="check_code"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="invoiceDate" column="invoice_date"/>
        <result property="sellerName" column="seller_name"/>
        <result property="sellerTaxId" column="seller_tax_id"/>
        <result property="buyer" column="buyer"/>
        <result property="buyerTaxId" column="buyer_tax_id"/>
        <result property="rawPrice" column="raw_price"/>
        <result property="tax" column="tax"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="price" column="price"/>
        <result property="status" column="status"/>
        <result property="materialName" column="material_name"/>
        <result property="materialSpec" column="material_spec"/>
        <result property="unit" column="unit"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="quantity" column="quantity"/>
        <result property="supplierNo" column="supplier_no"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtUpdate" column="gmt_update"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Insert_Column_List">
        supplier_invoice_id,invoice_no,invoice_code,check_code,
invoice_type,invoice_date,seller_name,seller_tax_id,buyer,
buyer_tax_id,raw_price,tax,tax_rate,price,
status,material_name,material_spec,unit,unit_price,
quantity,supplier_no,gmt_create,gmt_update,deleted
    </sql>

    <sql id="Select_Column_List">
        id,
        <include refid="Insert_Column_List"/>
    </sql>

    <sql id="Where_Column_List">
        <if test="do.invoiceNo != null">and sid.invoice_no = #{do.invoiceNo}</if>
        <if test="do.invoiceCode != null">and sid.invoice_code = #{do.invoiceCode}</if>
        <if test="do.buyer != null">and sid.buyer = #{do.buyer}</if>
        <if test="do.status != null">and sid.status = #{do.status}</if>
        <if test="do.invoiceType != null">and sid.invoice_type = #{do.invoiceType}</if>
        <if test="do.invoiceDateStart != null">and sid.invoice_date <![CDATA[ >= ]]> #{do.invoiceDateStart}</if>
        <if test="do.invoiceDateEnd != null">and sid.invoice_date <![CDATA[ <= ]]> #{do.invoiceDateEnd}</if>
        <if test="supplierNos != null and supplierNos.size > 0 ">
            and sid.supplier_no in
            <foreach collection="supplierNos" item="supplierNo" separator="," open="(" close=")">
                #{supplierNo}
            </foreach>
        </if>
        <if test="billNoDTOList != null and billNoDTOList.size > 0 ">
            <foreach collection="billNoDTOList" item="billNoDtO" separator="" open="and (" close=")" index="index">
                <if test="index != 0">
                    or
                </if>
                (dbn.bill_no = #{billNoDtO.billNo} and dbn.bill_type = #{billNoDtO.billType})
            </foreach>
        </if>
        AND sid.deleted=0 AND dbn.deleted=0
    </sql>

    <sql id="Insert_Value_List">
        (
        #{do.supplierInvoiceId},
        #{do.invoiceNo},
        #{do.invoiceCode},
        #{do.checkCode},
        #{do.invoiceType},
        #{do.invoiceDate},
        #{do.sellerName},
        #{do.sellerTaxId},
        #{do.buyer},
        #{do.buyerTaxId},
        #{do.rawPrice},
        #{do.tax},
        #{do.taxRate},
        #{do.price},
        #{do.status},
        #{do.materialName},
        #{do.materialSpec},
        #{do.unit},
        #{do.unitPrice},
        #{do.quantity},
        #{do.supplierNo},
        #{do.gmtCreate},
        #{do.gmtUpdate},
        #{do.deleted}
        )
    </sql>

    <!--
        功能：新增SupplierInvoiceDetail
    -->
    <insert id="insert">
        INSERT INTO supplier_invoice_detail(<include refid="Insert_Column_List"/>)
        VALUES
        <include refid="Insert_Value_List"/>
    </insert>

    <insert id="insertFetchId" useGeneratedKeys="true" keyProperty="do.id">
        INSERT INTO supplier_invoice_detail(<include refid="Insert_Column_List"/>)
        VALUES
        <include refid="Insert_Value_List"/>
    </insert>

    <!--
        功能：批量新增SupplierInvoiceDetail
    -->
    <insert id="insertBatch">
        INSERT INTO supplier_invoice_detail(<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="doList" item="do" index="index" separator=",">
            <include refid="Insert_Value_List"/>
        </foreach>
    </insert>

    <!--
        功能：根据id更新SupplierInvoiceDetail
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <update id="updateById">
        UPDATE supplier_invoice_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="do.supplierInvoiceId != null">supplier_invoice_id = #{do.supplierInvoiceId},</if>
            <if test="do.invoiceNo != null">invoice_no = #{do.invoiceNo},</if>
            <if test="do.invoiceCode != null">invoice_code = #{do.invoiceCode},</if>
            <if test="do.checkCode != null">check_code = #{do.checkCode},</if>
            <if test="do.invoiceType != null">invoice_type = #{do.invoiceType},</if>
            <if test="do.invoiceDate != null">invoice_date = #{do.invoiceDate},</if>
            <if test="do.sellerName != null">seller_name = #{do.sellerName},</if>
            <if test="do.sellerTaxId != null">seller_tax_id = #{do.sellerTaxId},</if>
            <if test="do.buyer != null">buyer = #{do.buyer},</if>
            <if test="do.buyerTaxId != null">buyer_tax_id = #{do.buyerTaxId},</if>
            <if test="do.rawPrice != null">raw_price = #{do.rawPrice},</if>
            <if test="do.tax != null">tax = #{do.tax},</if>
            <if test="do.taxRate != null">tax_rate = #{do.taxRate},</if>
            <if test="do.price != null">price = #{do.price},</if>
            <if test="do.status != null">status = #{do.status},</if>
            <if test="do.materialName != null">material_name = #{do.materialName},</if>
            <if test="do.materialSpec != null">material_spec = #{do.materialSpec},</if>
            <if test="do.unit != null">unit = #{do.unit},</if>
            <if test="do.unitPrice != null">unit_price = #{do.unitPrice},</if>
            <if test="do.quantity != null">quantity = #{do.quantity},</if>
            <if test="do.supplierNo != null">supplier_no = #{do.supplierNo},</if>
            <if test="do.gmtUpdate != null">gmt_update = #{do.gmtUpdate},</if>
            <if test="do.deleted != null">deleted = #{do.deleted},</if>
        </trim>
        where id = #{do.id}
    </update>

    <update id="updateStatusBySupplierInvoiceId">
        UPDATE supplier_invoice_detail
        SET status = #{status} WHERE supplier_invoice_id = #{supplierInvoiceId}
    </update>

    <!--
        功能：根据id查询SupplierInvoiceDetail
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_detail
        WHERE
            id = #{id}
        AND deleted = 0
    </select>

    <select id="findListBySupplierInvoiceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_detail
        WHERE
        supplier_invoice_id = #{supplierInvoiceId}
        AND deleted = 0
    </select>


    <update id="deleteBySupplierInvoiceId">
        update supplier_invoice_detail set deleted = 1
        where supplier_invoice_id = #{supplierInvoiceId}
          AND deleted = 0
    </update>

    <!--
        功能：条件查询所有未删除记录
        数据量：2000内
        结果集：50内
        频次：500/日
    -->
    <select id="findListByReq" resultMap="BaseResultMap">
        select <include refid="Select_Column_List"/> from supplier_invoice_detail
        <where>
            <if test="do.invoiceId != null">AND supplier_invoice_id = #{do.invoiceId}</if>
            <if test="supplierNos != null and supplierNos.size > 0 ">
                and supplier_no in
                <foreach collection="supplierNos" item="supplierNo" separator="," open="(" close=")">
                    #{supplierNo}
                </foreach>
            </if>
            AND deleted=0
        </where>
        ORDER BY id DESC
    </select>

    <!--
       功能：根据条件查询SupplierInvoiceDetail总数
       数据量：2000内
       结果集：1内
       频次：500/日
   -->
    <select id="countByReq" resultType="long">
        select count(DISTINCT sid.id) from supplier_invoice_detail sid inner join
            supplier_invoice_detail_bill_no dbn on sid.id = dbn.supplier_invoice_detail_id
        <where>
            <include refid="Where_Column_List"/>
        </where>
    </select>

    <!--
        功能：条件查询所有未删除记录
        数据量：2000内
        结果集：50内
        频次：500/日
    -->
    <select id="findExcelListByReq" resultMap="BaseResultMap">
        select DISTINCT sid.* from supplier_invoice_detail sid inner join
        supplier_invoice_detail_bill_no dbn on sid.id = dbn.supplier_invoice_detail_id
        <where>
            <include refid="Where_Column_List"/>
        </where>
        ORDER BY sid.id DESC
    </select>

    <select id="findListById" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_detail
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </select>
</mapper>