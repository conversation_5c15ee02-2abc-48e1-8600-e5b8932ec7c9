<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceBizRelationDAO">

  <sql id="Base_Column_List">
    id, invoice_no, invoice_code, business_no, business_type, amount, gmt_create, gmt_update, deleted
  </sql>

  <insert id="insert" parameterType="so.dian.invoice.pojo.entity.InvoiceBizRelationDO">
    insert into scm_invoice_biz_relation (
    invoice_no, invoice_code, business_no, business_type, amount,
    gmt_create, gmt_update, deleted
    ) values ( 
    #{invoiceNo}, #{invoiceCode},  #{businessNo}, #{businessType}, #{amount},
    #{gmtCreate}, #{gmtUpdate}, #{deleted}
    )
  </insert>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into scm_invoice_biz_relation (
    invoice_no, invoice_code, business_no, business_type, amount,
    gmt_create, gmt_update, deleted
    ) values
    <foreach collection="list" index="index" item="item" separator=",">
    (
    #{item.invoiceNo}, #{item.invoiceCode},  #{item.businessNo}, #{item.businessType}, #{item.amount},
    #{item.gmtCreate}, #{item.gmtUpdate}, #{item.deleted}
    )
    </foreach>
  </insert>

  <update id="update" parameterType="so.dian.invoice.pojo.entity.InvoiceBizRelationDO">
    update scm_invoice_biz_relation
    set
    amount = #{amount},
    <if test="deleted != null">
      deleted = #{deleted},
    </if>
    gmt_update = #{gmtUpdate}
    where id = #{id}
    and invoice_code = #{invoiceCode}
    and invoice_no = #{invoiceNo}
  </update>

  <select id="findOne"  resultType="so.dian.invoice.pojo.entity.InvoiceBizRelationDO">
    select
    <include refid="Base_Column_List" />
    from  scm_invoice_biz_relation
    where invoice_no = #{invoiceNo}
    <if test="invoiceCode != null and invoiceCode != ''">
      and invoice_code = #{invoiceCode,jdbcType=VARCHAR}
    </if>
    and business_no = #{businessNo}
    and business_type = #{businessType}
    limit 1
  </select>

</mapper>