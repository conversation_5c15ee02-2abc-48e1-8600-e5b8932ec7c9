<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceRequestDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceRequestDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="subjectId" column="subject_id" jdbcType="BIGINT"/>
            <result property="subjectType" column="subject_type" jdbcType="BIGINT"/>
            <result property="subjectName" column="subject_name" jdbcType="VARCHAR"/>
            <result property="titleType" column="title_type" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="invoiceNo" column="invoice_no" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="amount" column="amount" jdbcType="BIGINT"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="applicantId" column="applicant_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="financeFeedback" column="finance_feedback" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
            <result property="invoiceCompletedTime" column="invoice_completed_time" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,subject_id,subject_type,
        subject_name,title_type,title,
        invoice_no,type,amount,
        email,applicant_id,status,
        finance_feedback,deleted,gmt_create,
        gmt_update,invoice_completed_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_request
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_request
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestDO" useGeneratedKeys="true">
        insert into invoice_request
        ( id,subject_id,subject_type
        ,subject_name,title_type,title
        ,invoice_no,type,amount
        ,email,applicant_id,status
        ,finance_feedback,deleted,gmt_create
        ,gmt_update,invoice_completed_time)
        values (#{id,jdbcType=BIGINT},#{subjectId,jdbcType=BIGINT},#{subjectType,jdbcType=BIGINT}
        ,#{subjectName,jdbcType=VARCHAR},#{titleType,jdbcType=VARCHAR},#{title,jdbcType=VARCHAR}
        ,#{invoiceNo,jdbcType=VARCHAR},#{type,jdbcType=TINYINT},#{amount,jdbcType=BIGINT}
        ,#{email,jdbcType=VARCHAR},#{applicantId,jdbcType=BIGINT},#{status,jdbcType=TINYINT}
        ,#{financeFeedback,jdbcType=VARCHAR},#{deleted,jdbcType=TINYINT},#{gmtCreate,jdbcType=BIGINT}
        ,#{gmtUpdate,jdbcType=BIGINT},#{invoiceCompletedTime,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestDO" useGeneratedKeys="true">
        insert into invoice_request
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="subjectId != null">subject_id,</if>
                <if test="subjectType != null">subject_type,</if>
                <if test="subjectName != null">subject_name,</if>
                <if test="titleType != null">title_type,</if>
                <if test="title != null">title,</if>
                <if test="invoiceNo != null">invoice_no,</if>
                <if test="type != null">type,</if>
                <if test="amount != null">amount,</if>
                <if test="email != null">email,</if>
                <if test="applicantId != null">applicant_id,</if>
                <if test="status != null">status,</if>
                <if test="financeFeedback != null">finance_feedback,</if>
                <if test="deleted != null">deleted,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
                <if test="invoiceCompletedTime != null">invoice_completed_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="subjectId != null">#{subjectId,jdbcType=BIGINT},</if>
                <if test="subjectType != null">#{subjectType,jdbcType=BIGINT},</if>
                <if test="subjectName != null">#{subjectName,jdbcType=VARCHAR},</if>
                <if test="titleType != null">#{titleType,jdbcType=VARCHAR},</if>
                <if test="title != null">#{title,jdbcType=VARCHAR},</if>
                <if test="invoiceNo != null">#{invoiceNo,jdbcType=VARCHAR},</if>
                <if test="type != null">#{type,jdbcType=TINYINT},</if>
                <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
                <if test="email != null">#{email,jdbcType=VARCHAR},</if>
                <if test="applicantId != null">#{applicantId,jdbcType=BIGINT},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="financeFeedback != null">#{financeFeedback,jdbcType=VARCHAR},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
                <if test="invoiceCompletedTime != null">#{invoiceCompletedTime,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestDO">
        update invoice_request
        <set>
                <if test="subjectId != null">
                    subject_id = #{subjectId,jdbcType=BIGINT},
                </if>
                <if test="subjectType != null">
                    subject_type = #{subjectType,jdbcType=BIGINT},
                </if>
                <if test="subjectName != null">
                    subject_name = #{subjectName,jdbcType=VARCHAR},
                </if>
                <if test="titleType != null">
                    title_type = #{titleType,jdbcType=VARCHAR},
                </if>
                <if test="title != null">
                    title = #{title,jdbcType=VARCHAR},
                </if>
                <if test="invoiceNo != null">
                    invoice_no = #{invoiceNo,jdbcType=VARCHAR},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=TINYINT},
                </if>
                <if test="amount != null">
                    amount = #{amount,jdbcType=BIGINT},
                </if>
                <if test="email != null">
                    email = #{email,jdbcType=VARCHAR},
                </if>
                <if test="applicantId != null">
                    applicant_id = #{applicantId,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="financeFeedback != null">
                    finance_feedback = #{financeFeedback,jdbcType=VARCHAR},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
                <if test="invoiceCompletedTime != null">
                    invoice_completed_time = #{invoiceCompletedTime,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestDO">
        update invoice_request
        set 
            subject_id =  #{subjectId,jdbcType=BIGINT},
            subject_type =  #{subjectType,jdbcType=BIGINT},
            subject_name =  #{subjectName,jdbcType=VARCHAR},
            title_type =  #{titleType,jdbcType=VARCHAR},
            title =  #{title,jdbcType=VARCHAR},
            invoice_no =  #{invoiceNo,jdbcType=VARCHAR},
            type =  #{type,jdbcType=TINYINT},
            amount =  #{amount,jdbcType=BIGINT},
            email =  #{email,jdbcType=VARCHAR},
            applicant_id =  #{applicantId,jdbcType=BIGINT},
            status =  #{status,jdbcType=TINYINT},
            finance_feedback =  #{financeFeedback,jdbcType=VARCHAR},
            deleted =  #{deleted,jdbcType=TINYINT},
            gmt_create =  #{gmtCreate,jdbcType=BIGINT},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT},
            invoice_completed_time =  #{invoiceCompletedTime,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="listByParam" resultType="so.dian.invoice.pojo.dto.invoice.manage.InvoiceRequestDTO">
        select distinct a.id,a.subject_id,a.subject_type,a.subject_name,a.title_type,a.title,a.invoice_no,a.type,a.amount,a.email,a.applicant_id
        ,a.status,a.finance_feedback,a.gmt_create,a.invoice_completed_time
        from invoice_request a
        left join invoice_request_record b on a.id = b.request_id
        left join invoice_manage c on b.manage_id = c.id
        <where>
            a.deleted = 0
            <if test="bizNo != null and bizNo.length() > 0">
                AND c.biz_no = #{bizNo}
            </if>
            <if test="requestId != null">
                AND a.id = #{requestId}
            </if>
            <if test="subjectType != null">
                AND a.subject_type = #{subjectType}
            </if>
            <if test="subjectIds != null and subjectIds.size > 0">
                AND a.subject_id IN
                <foreach item="subjectId" index="index" collection="subjectIds"
                         open="(" separator="," close=")">
                    #{subjectId}
                </foreach>
            </if>
            <if test="status != null">
                AND a.status = #{status}
            </if>
            <if test="statusList != null and statusList.size > 0">
                AND a.status IN
                <foreach item="status" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="applyStartTime != null and applyEndTime != null">
                AND a.gmt_create BETWEEN #{applyStartTime} AND #{applyEndTime}
            </if>
        </where>
    </select>
</mapper>
