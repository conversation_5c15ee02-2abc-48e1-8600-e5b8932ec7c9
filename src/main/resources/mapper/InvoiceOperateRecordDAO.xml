<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceOperateRecordDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceOperateRecordDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="requestId" column="request_id" jdbcType="BIGINT"/>
        <result property="operateType" column="operate_type" jdbcType="INTEGER"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="operateTime" column="operate_time" jdbcType="BIGINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
        <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,request_id,operate_type,operator,operate_time,remark,deleted,gmt_create,gmt_update
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from invoice_operate_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_operate_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceOperateRecordDO" useGeneratedKeys="true">
        insert into invoice_operate_record
        (id, request_id, operate_type, operator, operate_time, remark, deleted, gmt_create, gmt_update)
        values (#{id,jdbcType=BIGINT}, #{requestId,jdbcType=BIGINT}, #{operateType,jdbcType=INTEGER},
                #{operator,jdbcType=VARCHAR}, #{operateTime,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR},
                #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceOperateRecordDO" useGeneratedKeys="true">
        insert into invoice_operate_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="requestId != null">request_id,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="operator != null">operator,</if>
            <if test="operateTime != null">operate_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deleted != null">deleted,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="requestId != null">#{requestId,jdbcType=BIGINT},</if>
            <if test="operateType != null">#{operateType,jdbcType=INTEGER},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="operateTime != null">#{operateTime,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
            <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
            <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceOperateRecordDO">
        update invoice_operate_record
        <set>
            <if test="requestId != null">request_id = #{requestId,jdbcType=BIGINT},</if>
            <if test="operateType != null">operate_type = #{operateType,jdbcType=INTEGER},</if>
            <if test="operator != null">operator = #{operator,jdbcType=VARCHAR},</if>
            <if test="operateTime != null">operate_time = #{operateTime,jdbcType=BIGINT},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="deleted != null">deleted = #{deleted,jdbcType=TINYINT},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate,jdbcType=BIGINT},</if>
            <if test="gmtUpdate != null">gmt_update = #{gmtUpdate,jdbcType=BIGINT},</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceOperateRecordDO">
        update invoice_operate_record
        set request_id = #{requestId,jdbcType=BIGINT},
            operate_type = #{operateType,jdbcType=INTEGER},
            operator = #{operator,jdbcType=VARCHAR},
            operate_time = #{operateTime,jdbcType=BIGINT},
            remark = #{remark,jdbcType=VARCHAR},
            deleted = #{deleted,jdbcType=TINYINT},
            gmt_create = #{gmtCreate,jdbcType=BIGINT},
            gmt_update = #{gmtUpdate,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listByRequestId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from invoice_operate_record
        where request_id = #{requestId,jdbcType=BIGINT} and deleted = 0
        order by operate_time desc
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into invoice_operate_record
        (request_id, operate_type, operator, operate_time, remark, deleted, gmt_create, gmt_update)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.requestId,jdbcType=BIGINT}, #{record.operateType,jdbcType=INTEGER},
             #{record.operator,jdbcType=VARCHAR}, #{record.operateTime,jdbcType=BIGINT},
             #{record.remark,jdbcType=VARCHAR}, #{record.deleted,jdbcType=TINYINT},
             #{record.gmtCreate,jdbcType=BIGINT}, #{record.gmtUpdate,jdbcType=BIGINT})
        </foreach>
    </insert>

</mapper>
