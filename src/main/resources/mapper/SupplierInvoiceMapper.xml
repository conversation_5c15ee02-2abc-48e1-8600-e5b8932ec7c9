<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.SupplierInvoiceMapper">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.SupplierInvoiceDO">
        <id property="id" column="id"/>
        <result property="invoiceNo" column="invoice_no"/>
        <result property="invoiceCode" column="invoice_code"/>
        <result property="checkCode" column="check_code"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="invoiceDate" column="invoice_date"/>
        <result property="sellerName" column="seller_name"/>
        <result property="sellerTaxId" column="seller_tax_id"/>
        <result property="buyer" column="buyer"/>
        <result property="buyerTaxId" column="buyer_tax_id"/>
        <result property="rawPrice" column="raw_price"/>
        <result property="tax" column="tax"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="price" column="price"/>
        <result property="status" column="status"/>
        <result property="supplierNo" column="supplier_no"/>
        <result property="creator" column="creator"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtUpdate" column="gmt_update"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Insert_Column_List">
        invoice_no
        ,invoice_code,check_code,invoice_type,
        invoice_date,seller_name,seller_tax_id,buyer,buyer_tax_id,
        raw_price,tax,tax_rate,price,status,
        supplier_no,creator,gmt_create,gmt_update,deleted
    </sql>

    <sql id="Select_Column_List">
        id,
        <include refid="Insert_Column_List"/>
    </sql>

    <sql id="Where_Column_List">
        <if test="do.invoiceNo != null">and si.invoice_no = #{do.invoiceNo}</if>
        <if test="do.invoiceCode != null">and si.invoice_code = #{do.invoiceCode}</if>
        <if test="do.buyer != null">and si.buyer = #{do.buyer}</if>
        <if test="do.status != null">and si.status = #{do.status}</if>
        <if test="do.invoiceType != null">and si.invoice_type = #{do.invoiceType}</if>
        <if test="do.invoiceDateStart != null">and si.invoice_date <![CDATA[ >= ]]> #{do.invoiceDateStart}</if>
        <if test="do.invoiceDateEnd != null">and si.invoice_date <![CDATA[ <= ]]> #{do.invoiceDateEnd}</if>
        <if test="supplierNos != null and supplierNos.size > 0 ">
            and si.supplier_no in
            <foreach collection="supplierNos" item="supplierNo" separator="," open="(" close=")">
                #{supplierNo}
            </foreach>
        </if>
        <if test="billNoDTOList != null and billNoDTOList.size > 0 ">
            <foreach collection="billNoDTOList" item="billNoDtO" separator="" open="and (" close=")" index="index">
                <if test="index != 0">
                    or
                </if>
                (dbn.bill_no = #{billNoDtO.billNo} and dbn.bill_type = #{billNoDtO.billType})
            </foreach>
        </if>
        AND si.deleted=0 AND dbn.deleted=0
    </sql>

    <sql id="Insert_Value_List">
        (
            #{do.invoiceNo}, #{do.invoiceCode},
            #{do.checkCode},
            #{do.invoiceType},
            #{do.invoiceDate},
            #{do.sellerName},
            #{do.sellerTaxId},
            #{do.buyer},
            #{do.buyerTaxId},
            #{do.rawPrice},
            #{do.tax},
            #{do.taxRate},
            #{do.price},
            #{do.status},
            #{do.supplierNo},
            #{do.creator},
            #{do.gmtCreate},
            #{do.gmtUpdate},
            #{do.deleted}
        )
    </sql>

    <!--
        功能：新增SupplierInvoice
    -->
    <insert id="insert">
        INSERT INTO supplier_invoice(<include refid="Insert_Column_List"/>)
        VALUES
        <include refid="Insert_Value_List"/>
    </insert>

    <insert id="insertFetchId" useGeneratedKeys="true" keyProperty="do.id">
        INSERT INTO supplier_invoice(<include refid="Insert_Column_List"/>)
        VALUES
        <include refid="Insert_Value_List"/>
    </insert>

    <!--
        功能：新增SupplierInvoice（非空字段）
    -->
    <insert id="insertSelective">
        insert into supplier_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="do.invoiceNo != null">invoice_no,</if>
            <if test="do.invoiceCode != null">invoice_code,</if>
            <if test="do.checkCode != null">check_code,</if>
            <if test="do.invoiceType != null">invoice_type,</if>
            <if test="do.invoiceDate != null">invoice_date,</if>
            <if test="do.sellerName != null">seller_name,</if>
            <if test="do.sellerTaxId != null">seller_tax_id,</if>
            <if test="do.buyer != null">buyer,</if>
            <if test="do.buyerTaxId != null">buyer_tax_id,</if>
            <if test="do.rawPrice != null">raw_price,</if>
            <if test="do.tax != null">tax,</if>
            <if test="do.taxRate != null">tax_rate,</if>
            <if test="do.price != null">price,</if>
            <if test="do.status != null">status,</if>
            <if test="do.supplierNo != null">supplier_no,</if>
            <if test="do.creator != null">creator,</if>
            <if test="do.gmtCreate != null">gmt_create,</if>
            <if test="do.gmtUpdate != null">gmt_update,</if>
            <if test="do.deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="do.invoiceNo != null">#{do.invoice_no},</if>
            <if test="do.invoiceCode != null">#{do.invoice_code},</if>
            <if test="do.checkCode != null">#{do.check_code},</if>
            <if test="do.invoiceType != null">#{do.invoice_type},</if>
            <if test="do.invoiceDate != null">#{do.invoice_date},</if>
            <if test="do.sellerName != null">#{do.seller_name},</if>
            <if test="do.sellerTaxId != null">#{do.seller_tax_id},</if>
            <if test="do.buyer != null">#{do.buyer},</if>
            <if test="do.buyerTaxId != null">#{do.buyer_tax_id},</if>
            <if test="do.rawPrice != null">#{do.raw_price},</if>
            <if test="do.tax != null">#{do.tax},</if>
            <if test="do.taxRate != null">#{do.tax_rate},</if>
            <if test="do.price != null">#{do.price},</if>
            <if test="do.status != null">#{do.status},</if>
            <if test="do.supplierNo != null">#{do.supplier_no},</if>
            <if test="do.creator != null">#{do.creator},</if>
            <if test="do.gmtUpdate != null">#{do.gmt_update},</if>
            <if test="do.deleted != null">#{do.deleted},</if>
        </trim>
    </insert>

    <!--
        功能：批量新增SupplierInvoice
    -->
    <insert id="insertBatch">
        INSERT INTO supplier_invoice(<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="doList" item="do" index="index" separator=",">
            <include refid="Insert_Value_List"/>
        </foreach>
    </insert>

    <!--
        功能：根据id更新SupplierInvoice
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <update id="updateById">
        UPDATE supplier_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="do.invoiceNo != null">invoice_no = #{do.invoiceNo},</if>
            <if test="do.invoiceCode != null">invoice_code = #{do.invoiceCode},</if>
            <if test="do.checkCode != null">check_code = #{do.checkCode},</if>
            <if test="do.invoiceType != null">invoice_type = #{do.invoiceType},</if>
            <if test="do.invoiceDate != null">invoice_date = #{do.invoiceDate},</if>
            <if test="do.sellerName != null">seller_name = #{do.sellerName},</if>
            <if test="do.sellerTaxId != null">seller_tax_id = #{do.sellerTaxId},</if>
            <if test="do.buyer != null">buyer = #{do.buyer},</if>
            <if test="do.buyerTaxId != null">buyer_tax_id = #{do.buyerTaxId},</if>
            <if test="do.rawPrice != null">raw_price = #{do.rawPrice},</if>
            <if test="do.tax != null">tax = #{do.tax},</if>
            <if test="do.taxRate != null">tax_rate = #{do.taxRate},</if>
            <if test="do.price != null">price = #{do.price},</if>
            <if test="do.status != null">status = #{do.status},</if>
            <if test="do.supplierNo != null">supplier_no = #{do.supplierNo},</if>
            <if test="do.creator != null">creator = #{do.creator},</if>
            <if test="do.gmtUpdate != null">gmt_update = #{do.gmtUpdate},</if>
            <if test="do.deleted != null">deleted = #{do.deleted},</if>
        </trim>
        where id = #{do.id}
    </update>

    <!--
        功能：根据id查询SupplierInvoice
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice
        WHERE id = #{id}
        AND deleted = 0
    </select>

    <select id="findSupplierNoCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice
        WHERE invoice_no = #{invoiceNo} and invoice_code = #{invoiceCode}
        AND deleted = 0 limit 1
    </select>

    <select id="findByIdAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice
        WHERE id = #{id} AND status=#{status}
        AND deleted = 0
    </select>


    <!--
        功能：条件分页查询所有未删除记录
        数据量：2000内
        结果集：500内
        频次：500/日
    -->
    <select id="findPageByReq" resultMap="BaseResultMap">
        select DISTINCT si.* from supplier_invoice si inner join supplier_invoice_detail_bill_no dbn on si.id =
        dbn.supplier_invoice_id
        <where>
            <include refid="Where_Column_List"/>
        </where>
        ORDER BY si.id DESC
        limit #{page.offset}, #{page.pageSize}
    </select>

    <!--
        功能：根据条件查询SupplierInvoice总数
        数据量：2000内
        结果集：1内
        频次：500/日
    -->
    <select id="countByReq" resultType="long">
        select COUNT(DISTINCT si.id) from supplier_invoice si inner join supplier_invoice_detail_bill_no dbn on si.id =
        dbn.supplier_invoice_id
        <where>
            <include refid="Where_Column_List"/>
        </where>
    </select>

    <update id="deleteById">
        UPDATE supplier_invoice
        SET deleted=1
        WHERE id = #{id}
          AND deleted = 0
    </update>
</mapper>