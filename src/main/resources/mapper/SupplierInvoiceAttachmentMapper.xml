<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.SupplierInvoiceAttachmentMapper">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.SupplierInvoiceAttachmentDO">
        <id property="id" column="id"/>
        <result property="supplierInvoiceId" column="supplier_invoice_id"/>
        <result property="type" column="type"/>
        <result property="url" column="url"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtUpdate" column="gmt_update"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Insert_Column_List">
        supplier_invoice_id,type,url,gmt_create,gmt_update,deleted
    </sql>

    <sql id="Select_Column_List">
        id,
        <include refid="Insert_Column_List"/>
    </sql>

    <sql id="Insert_Value_List">
        (
        #{do.supplierInvoiceId},#{do.type},#{do.url},#{do.gmtCreate},#{do.gmtUpdate},#{do.deleted}
        )
    </sql>

    <!--
        功能：新增SupplierInvoiceAttachment
    -->
    <insert id="insert">
        INSERT INTO supplier_invoice_attachment(<include refid="Insert_Column_List"/>)
        VALUES
        <include refid="Insert_Value_List"/>
    </insert>

    <!--
        功能：根据id更新SupplierInvoiceAttachment
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <update id="updateById">
        UPDATE supplier_invoice_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="do.supplierInvoiceId != null">supplier_invoice_id = #{do.supplierInvoiceId},</if>
            <if test="do.type != null">type = #{do.type},</if>
            <if test="do.url != null">url = #{do.url},</if>
            <if test="do.gmtUpdate != null">gmt_update = #{do.gmtUpdate},</if>
            <if test="do.deleted != null">deleted = #{do.deleted},</if>
        </trim>
        where id = #{do.id}
    </update>

    <!--
        功能：根据id查询SupplierInvoiceAttachment
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_attachment
        WHERE id = #{id} AND deleted = 0
    </select>

    <!--
        功能：查询发票图片
        数据量：2000内
        结果集：50内
        频次：500/日
    -->
    <select id="findListBySupplierInvoiceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_attachment
        WHERE supplier_invoice_id = #{supplierInvoiceId} AND type = #{type} AND deleted=0
    </select>

    <update id="deleteBySupplierInvoiceId">
        update supplier_invoice_attachment set deleted = 1
        where supplier_invoice_id = #{supplierInvoiceId}
          AND deleted = 0
    </update>
</mapper>