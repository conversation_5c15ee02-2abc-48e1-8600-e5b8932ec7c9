<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.CheckInvoiceDAO">

  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.CheckInvoiceDO">
    <result column="id" jdbcType="INTEGER" property="id"/>
    <result column="conclusion_id" jdbcType="INTEGER" property="conclusionId"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="checker" jdbcType="INTEGER" property="checker"/>
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId"/>
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
    <result column="express_no" jdbcType="VARCHAR" property="expressNo"/>
    <result column="gmt_create" jdbcType="INTEGER" property="gmtCreate"/>
    <result column="gmt_update" jdbcType="INTEGER" property="gmtUpdate"/>
    <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    <result column="operator" jdbcType="INTEGER" property="operator"/>
  </resultMap>
  <resultMap id="CheckInvoiceDTOResult" type="so.dian.invoice.pojo.dto.CheckInvoiceDTO">
    <result column="id" jdbcType="INTEGER" property="id"/>
    <result column="conclusion_id" jdbcType="INTEGER" property="conclusionId"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="checker" jdbcType="INTEGER" property="checker"/>
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId"/>
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
    <result column="express_no" jdbcType="VARCHAR" property="expressNo"/>
    <result column="gmt_create" jdbcType="INTEGER" property="gmtCreate"/>
    <result column="gmt_update" jdbcType="INTEGER" property="gmtUpdate"/>
    <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    <result column="operator" jdbcType="INTEGER" property="operator"/>
    <result column="city_code" jdbcType="INTEGER" property="cityCode"/>
  </resultMap>

  <sql id="base_column">
    id,conclusion_id,remark,status,checker,check_time,invoice_id,invoice_no,invoice_code,express_no,gmt_create,
    gmt_update,operator
  </sql>

  <sql id="invoice_checkInvoice_column">
    ci.id,ci.conclusion_id,ci.remark,ci.status,ci.checker,ci.check_time,ci.invoice_id,ci.invoice_no,ci.invoice_code,ci.express_no,ci.gmt_create,
    ci.gmt_update,ci.operator,si.city_code
  </sql>

  <!--
   查询：分页查询发票质检列表
   频次：100次/日
   数据量：5000
  -->
  <select id="selectCheckInvoiceList" parameterType="so.dian.invoice.pojo.param.InvoiceCheckPageParam"
      resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice
    WHERE
    <include refid="whereCondition"/>
    <if test="status == 1">
      ORDER BY gmt_update DESC
    </if>
    <if test="status == 0 or status == null">
      ORDER BY gmt_create ASC
    </if>
    limit #{offset}, #{pageSize}
  </select>

  <!--
   查询：发票质检列表
   频次：100次/日
   数据量：5000
  -->
  <select id="listCheckInvoice" parameterType="so.dian.invoice.pojo.param.InvoiceCheckPageParam"
      resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice
    WHERE
    <include refid="whereCondition"/>
    ORDER BY gmt_create DESC
  </select>

  <!--
   查询：统计发票质检条数
   频次：100次/日
   数据量：10
  -->
  <select id="count" parameterType="so.dian.invoice.pojo.param.InvoiceCheckPageParam"
      resultType="java.lang.Long">
    SELECT COUNT(1)
    FROM check_invoice ci left join scm_invoice si on ci.invoice_id = si.id
    WHERE
    <include refid="invoiceWhereCondition"/>
  </select>

  <select id="checkInvoicePage" parameterType="so.dian.invoice.pojo.param.InvoiceCheckPageParam" resultMap="CheckInvoiceDTOResult">
    SELECT <include refid="invoice_checkInvoice_column"/>
    FROM check_invoice ci left join scm_invoice si on ci.invoice_id = si.id
    WHERE
    <include refid="invoiceWhereCondition"/>
  </select>

  <!--
   新增：批量新增质检发票
   频次：1次/周
   数据量：1000
  -->
  <insert id="batchInsert" parameterType="list">
    INSERT INTO
    check_invoice(status,operator,invoice_id,invoice_no,invoice_code,express_no,gmt_create,gmt_update)
    VALUES
    <foreach collection="checkInvoiceDOList" index="index" item="item" separator=",">
      (#{item.status},#{item.operator},#{item.invoiceId},#{item.invoiceNo},#{item.invoiceCode},#{item.expressNo},
      #{item.gmtCreate},#{item.gmtUpdate})
    </foreach>
  </insert>

  <!--
   新增：新增质检发票
   频次：10次/日
   数据量：1
  -->
  <insert id="insert" parameterType="so.dian.invoice.pojo.entity.CheckInvoiceDO">
    INSERT INTO
    check_invoice(conclusion_id,remark,status,checker,check_time,invoice_id,invoice_no,invoice_code,express_no,
    gmt_create,gmt_update)
    VALUES
    (#{conclusionId},#{remark},#{status},#{checker},#{checkTime},#{invoiceId},#{invoiceNo},#{invoiceCode},
    #{expressNo},#{gmtCreate},#{gmtUpdate})
  </insert>

  <!--
   更新：质检发票
   频次：100次/日
   数据量：100
  -->
  <update id="update">
    UPDATE check_invoice
    SET conclusion_id = #{conclusionId},status = #{status},checker = #{checker},check_time = #{checkTime},
    remark = #{remark},gmt_update=#{gmtUpdate}
    WHERE id=#{id}
    AND status = #{beforeStatus}
    AND deleted = 0
  </update>

  <!--
   查询：通过id查询质检发票信息
   频次：100次/日
   数据量：10
  -->
  <select id="getById" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice
    WHERE id = #{id}
    AND deleted = 0
  </select>

  <!--
   查询：查看质检结论使用条数
   频次：10次/日
   数据量：10
  -->
  <select id="countByConclusionId" resultType="java.lang.Long">
    SELECT count(1)
    FROM check_invoice
    WHERE conclusion_id = #{conclusionId}
    AND deleted = 0
  </select>

  <sql id="whereCondition">
    deleted = 0
    AND status = #{status}
    <if test="operatorIdList != null and operatorIdList.size()>0">
      AND operator IN
      <foreach collection="operatorIdList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    <if test="checkResult != null">
      AND conclusion_id = #{checkResult}
    </if>
    <if test="checkerId != null">
      AND checker = #{checkerId}
    </if>
    <if test="creatorId != null">
      AND operator = #{creatorId}
    </if>
    <if test="expressNo != null and expressNo != ''">
      AND express_no = #{expressNo}
    </if>
    <if test="invoiceNo != null and invoiceNo != ''">
      AND invoice_no = #{invoiceNo}
    </if>
    <if test="invoiceCode != null and invoiceCode != ''">
      AND invoice_code = #{invoiceCode}
    </if>
    <if test="startCheckTime != null and startCheckTime != ''">
      AND check_time <![CDATA[ >= ]]> #{startCheckTime}
    </if>
    <if test="endCheckTime != null and endCheckTime != ''">
      AND check_time <![CDATA[ <= ]]> #{endCheckTime}
    </if>
    <if test="startCreateTime != null">
      AND gmt_create <![CDATA[ >= ]]> #{startCreateTime}
    </if>
    <if test="endCreateTime != null">
      AND gmt_create <![CDATA[ <= ]]> #{endCreateTime}
    </if>
  </sql>

  <sql id="invoiceWhereCondition">
    ci.deleted = 0
    AND ci.status = #{status}
    <if test="operatorIdList != null and operatorIdList.size()>0">
      AND ci.operator IN
      <foreach collection="operatorIdList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    <if test="cityCodes != null and cityCodes.size()>0">
      AND si.city_code IN
      <foreach collection="cityCodes" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    <if test="checkResult != null">
      AND ci.conclusion_id = #{checkResult}
    </if>
    <if test="checkerId != null">
      AND ci.checker = #{checkerId}
    </if>
    <if test="creatorId != null">
      AND ci.operator = #{creatorId}
    </if>
    <if test="expressNo != null and expressNo != ''">
      AND ci.express_no = #{expressNo}
    </if>
    <if test="invoiceNo != null and invoiceNo != ''">
      AND ci.invoice_no = #{invoiceNo}
    </if>
    <if test="invoiceCode != null and invoiceCode != ''">
      AND ci.invoice_code = #{invoiceCode}
    </if>
    <if test="startCheckTime != null and startCheckTime != ''">
      AND ci.check_time <![CDATA[ >= ]]> #{startCheckTime}
    </if>
    <if test="endCheckTime != null and endCheckTime != ''">
      AND ci.check_time <![CDATA[ <= ]]> #{endCheckTime}
    </if>
    <if test="startCreateTime != null">
      AND ci.gmt_create <![CDATA[ >= ]]> #{startCreateTime}
    </if>
    <if test="endCreateTime != null">
      AND ci.gmt_create <![CDATA[ <= ]]> #{endCreateTime}
    </if>
  </sql>
</mapper>