<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceNewsVoucherDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="outBizId" column="out_biz_id" jdbcType="VARCHAR"/>
            <result property="outBizType" column="out_biz_type" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="retryTime" column="retry_time" jdbcType="INTEGER"/>
            <result property="eventVoucher" column="event_voucher" jdbcType="VARCHAR"/>
            <result property="receiveTime" column="receive_time" jdbcType="BIGINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,out_biz_id,out_biz_type,
        status,retry_time,event_voucher,
        receive_time,deleted,gmt_create,
        gmt_update
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_news_voucher
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_news_voucher
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO" useGeneratedKeys="true">
        insert into invoice_news_voucher
        ( id,out_biz_id,out_biz_type
        ,status,retry_time,event_voucher
        ,receive_time,deleted,gmt_create
        ,gmt_update)
        values (#{id,jdbcType=BIGINT},#{outBizId,jdbcType=VARCHAR},#{outBizType,jdbcType=TINYINT}
        ,#{status,jdbcType=TINYINT},#{retryTime,jdbcType=INTEGER},#{eventVoucher,jdbcType=VARCHAR}
        ,#{receiveTime,jdbcType=BIGINT},#{deleted,jdbcType=TINYINT},#{gmtCreate,jdbcType=BIGINT}
        ,#{gmtUpdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO" useGeneratedKeys="true">
        insert into invoice_news_voucher
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="outBizId != null">out_biz_id,</if>
                <if test="outBizType != null">out_biz_type,</if>
                <if test="status != null">status,</if>
                <if test="retryTime != null">retry_time,</if>
                <if test="eventVoucher != null">event_voucher,</if>
                <if test="receiveTime != null">receive_time,</if>
                <if test="deleted != null">deleted,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="outBizId != null">#{outBizId,jdbcType=VARCHAR},</if>
                <if test="outBizType != null">#{outBizType,jdbcType=TINYINT},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="retryTime != null">#{retryTime,jdbcType=INTEGER},</if>
                <if test="eventVoucher != null">#{eventVoucher,jdbcType=VARCHAR},</if>
                <if test="receiveTime != null">#{receiveTime,jdbcType=BIGINT},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO">
        update invoice_news_voucher
        <set>
                <if test="outBizId != null">
                    out_biz_id = #{outBizId,jdbcType=VARCHAR},
                </if>
                <if test="outBizType != null">
                    out_biz_type = #{outBizType,jdbcType=TINYINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="retryTime != null">
                    retry_time = #{retryTime,jdbcType=INTEGER},
                </if>
                <if test="eventVoucher != null">
                    event_voucher = #{eventVoucher,jdbcType=VARCHAR},
                </if>
                <if test="receiveTime != null">
                    receive_time = #{receiveTime,jdbcType=BIGINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO">
        update invoice_news_voucher
        set 
            out_biz_id =  #{outBizId,jdbcType=VARCHAR},
            out_biz_type =  #{outBizType,jdbcType=TINYINT},
            status =  #{status,jdbcType=TINYINT},
            retry_time =  #{retryTime,jdbcType=INTEGER},
            receive_time =  #{receiveTime,jdbcType=BIGINT},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="getByBizNoAndBizType" resultType="so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO">
        SELECT * FROM invoice_news_voucher
        WHERE deleted = 0 and out_biz_id = #{bizNo} AND out_biz_type = #{bizType}
    </select>

    <select id="list" parameterType="so.dian.invoice.pojo.param.Invoice.manage.InvoiceNewsVoucherParam" resultType="so.dian.invoice.pojo.entity.InvoiceNewsVoucherDO">
        SELECT * FROM invoice_news_voucher
        <where>
            deleted = 0 and status in (1,3)
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="outBizId != null">
                AND out_biz_id = #{outBizId,jdbcType=VARCHAR}
            </if>
            <if test="outBizType != null">
                AND out_biz_type = #{outBizType,jdbcType=TINYINT}
            </if>
            <if test="maxRetryTime != null">
                AND retry_time &lt; #{maxRetryTime,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>
