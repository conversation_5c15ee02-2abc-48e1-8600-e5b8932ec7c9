<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.SupplierInvoiceOperateLogMapper">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.SupplierInvoiceOperateLogDO">
        <id property="id" column="id"/>
        <result property="supplierInvoiceId" column="supplier_invoice_id"/>
        <result property="operateType" column="operate_type"/>
        <result property="content" column="content"/>
        <result property="operatorName" column="operator_name"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operateTime" column="operate_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtUpdate" column="gmt_update"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Insert_Column_List">
        supplier_invoice_id,operate_type,content,operator_name,
operator_id,operate_time,gmt_create,gmt_update,deleted
    </sql>

    <sql id="Select_Column_List">
        id,
        <include refid="Insert_Column_List"/>
    </sql>

    <sql id="Where_Column_List">
    </sql>

    <sql id="Insert_Value_List">
        (
        #{do.supplierInvoiceId},
        #{do.operateType},
        #{do.content},
        #{do.operatorName},
        #{do.operatorId},
        #{do.operateTime},
        #{do.gmtCreate},
        #{do.gmtUpdate},
        #{do.deleted}
        )
    </sql>

    <!--
        功能：新增SupplierInvoiceOperateLog
    -->
    <insert id="insert">
        INSERT INTO supplier_invoice_operate_log(<include refid="Insert_Column_List"/>)
        VALUES
        <include refid="Insert_Value_List"/>
    </insert>

    <!--
        功能：批量新增SupplierInvoiceOperateLog
    -->
    <insert id="insertBatch">
        INSERT INTO supplier_invoice_operate_log(<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="doList" item="do" index="index" separator=",">
            <include refid="Insert_Value_List"/>
        </foreach>
    </insert>

    <!--
        功能：根据id更新SupplierInvoiceOperateLog
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <update id="updateById">
        UPDATE supplier_invoice_operate_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="do.supplierInvoiceId != null">supplier_invoice_id = #{do.supplierInvoiceId},</if>
            <if test="do.operateType != null">operate_type = #{do.operateType},</if>
            <if test="do.content != null">content = #{do.content},</if>
            <if test="do.operatorName != null">operator_name = #{do.operatorName},</if>
            <if test="do.operatorId != null">operator_id = #{do.operatorId},</if>
            <if test="do.operateTime != null">operate_time = #{do.operateTime},</if>
            <if test="do.gmtUpdate != null">gmt_update = #{do.gmtUpdate},</if>
            <if test="do.deleted != null">deleted = #{do.deleted},</if>
        </trim>
        <where>
            id = #{do.id}
        </where>
    </update>

    <!--
        功能：根据id查询SupplierInvoiceOperateLog
        数据量：2000内
        结果集：1内
        频次：50/日
    -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Column_List"/>
        FROM supplier_invoice_operate_log
        WHERE
            id = #{id}
        AND deleted = 0
    </select>

</mapper>