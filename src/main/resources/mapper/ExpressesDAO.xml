<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.ExpressesDAO">

  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.ExpressesDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="express_code" jdbcType="VARCHAR" property="expressCode" />
    <result column="express_name" jdbcType="VARCHAR" property="expressName" />
    <result column="express_tel" jdbcType="VARCHAR" property="expressTel" />

  </resultMap>

  <sql id="Base_Column_List">
    id,
    express_code,
    express_name,
    express_tel,
    create_time,
    update_time,
    deleted
  </sql>

  <!--
  功能：查询
  数据量：500
  频次：500次/天
  -->
  <select id="findExpresses" parameterType="map"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from scm_expresses
        where
            deleted=#{deleted}
           <if test="name !=null">
             and express_name like concat('%',#{name},'%')
           </if>

  </select>


</mapper>