<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceDeductionDAO">
  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceDeductionDO">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
    <result column="business_no" jdbcType="VARCHAR" property="businessNo"/>
    <result column="business_type" jdbcType="INTEGER" property="businessType"/>
    <result column="operate_type" jdbcType="INTEGER" property="operateType"/>
    <result column="creator" jdbcType="BIGINT" property="creator"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    <result column="amount" jdbcType="DECIMAL" property="amount"/>
    <result column="reason" jdbcType="VARCHAR" property="reason"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, invoice_code, invoice_no, business_no, business_type, operate_type,
    creator, create_name, create_time, update_time, deleted, amount, reason
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from invoice_deduction
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from invoice_deduction
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" parameterType="so.dian.invoice.pojo.entity.InvoiceDeductionDO" keyProperty="id">
    insert into invoice_deduction
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="businessNo != null">
        business_no,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="reason != null">
        reason,
      </if>
      create_time,
      update_time,
      deleted
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      now(),
      now(),
      0
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceDeductionDO">
    update invoice_deduction
    <set>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null">
        business_no = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      update_time = now()
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceDeductionDO">
    update invoice_deduction
    set invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      business_no = #{businessNo,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER},
      update_time = now(),
      deleted = #{deleted,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      reason = #{reason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--根据业务单号查询核销的发票列表 -->
  <select id="findByBusinessNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from invoice_deduction
    where deleted = 0
    and business_no = #{businessNo,jdbcType=VARCHAR}
    and business_type = #{businessType,jdbcType=INTEGER}
    <if test="operateType != null and operateType &gt; 0">
      and operate_type = #{operateType,jdbcType=INTEGER}
    </if>
  </select>

  <!--查询核销的发票明细 -->
  <select id="selectInvoiceDeductionList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from invoice_deduction
    where invoice_no = #{invoiceNo,jdbcType=VARCHAR}
    <if test="invoiceCode != null and invoiceCode != ''">
      and invoice_code = #{invoiceCode,jdbcType=VARCHAR}
    </if>
    and business_no = #{businessNo,jdbcType=VARCHAR}
    and business_type = #{businessType,jdbcType=INTEGER}
    and operate_type = #{operateType,jdbcType=INTEGER}
    and deleted = 0
  </select>

  <!--根据业务单号查询付款申请单核销的发票列表 -->
  <select id="findPayInvoiceByBusinessNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from invoice_deduction
    where deleted = 0
    and business_no = #{businessNo,jdbcType=VARCHAR}
    and business_type in (7,10,11,12,13,15,16,17,18,19,20)
    and operate_type = #{operateType,jdbcType=INTEGER}
  </select>


  <!--获取发票核销记录-按单号分组，取最后一条记录。 -->
  <select id="findTopByInvoiceNoAndInvoiceCode" resultMap="BaseResultMap">
    SELECT * FROM
    ( SELECT <include refid="Base_Column_List"/>
    FROM invoice_deduction
    WHERE invoice_code = #{invoiceCode,jdbcType=VARCHAR}
    AND invoice_no = #{invoiceNo,jdbcType=VARCHAR}
    AND deleted = 0
    ORDER BY id DESC ) t1
    GROUP BY t1.business_no
  </select>

  <!--根据发票号和发票code查询发票核销并列表按照创建时间倒序排序-->
  <select id="findByInvoiceNoAndInvoiceCode" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM invoice_deduction
    WHERE invoice_no = #{invoiceNo}
    <if test="invoiceCode != null and invoiceCode != ''">
      and invoice_code = #{invoiceCode,jdbcType=VARCHAR}
    </if>
    AND deleted = 0
    ORDER BY id DESC
  </select>

  <select id="findByCreateTimeAfter" resultMap="BaseResultMap">
    SELECT invoice_code, invoice_no
    FROM invoice_deduction
    WHERE create_time <![CDATA[ >= ]]> #{createTime}
    AND deleted = 0
    GROUP BY
    invoice_code, invoice_no
  </select>

  <select id="selectByInvoiceCodeAndNo" resultType="java.lang.String">
    select business_no
    from invoice_deduction
    where invoice_code = #{invoiceCode,jdbcType=VARCHAR}
    and invoice_no = #{invoiceNo,jdbcType=VARCHAR}
    <if test="operateType != null and operateType &gt; 0">
      and operate_type = #{operateType,jdbcType=INTEGER}
    </if>
    <if test="businessNoList != null and businessNoList.size() > 0">
      and business_no not in
      <foreach close=")" collection="businessNoList" item="businessNo" open="(" separator=",">
        #{businessNo,jdbcType=VARCHAR}
      </foreach>
    </if>
    and deleted = 0
  </select>
  
  <select id="findByIdIn" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM invoice_deduction
    WHERE id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    AND deleted = 0
  </select>
  
  <update id="recoverInvoice">
    update invoice_deduction
    set operate_type = #{target},
    update_time = now()
    where id = #{deductionId}
    and operate_type = #{source}
  </update>
  
</mapper>