<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceManageDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceManageDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="bizNo" column="biz_no" jdbcType="VARCHAR"/>
            <result property="bizType" column="biz_type" jdbcType="TINYINT"/>
            <result property="subjectId" column="subject_id" jdbcType="BIGINT"/>
            <result property="subjectType" column="subject_type" jdbcType="TINYINT"/>
            <result property="totalAmount" column="total_amount" jdbcType="BIGINT"/>
            <result property="paymentTime" column="payment_time" jdbcType="BIGINT"/>
            <result property="bizCreateTime" column="biz_create_time" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_no,biz_type,
        subject_id,subject_type,total_amount,
        payment_time,biz_create_time,status,gmt_create,
        gmt_update,deleted
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_manage
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_manage
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDO" useGeneratedKeys="true">
        insert into invoice_manage
        ( id,biz_no,biz_type
        ,subject_id,subject_type,total_amount
        ,payment_time,biz_create_time,status,gmt_create
        ,gmt_update,deleted)
        values (#{id,jdbcType=BIGINT},#{bizNo,jdbcType=VARCHAR},#{bizType,jdbcType=TINYINT}
        ,#{subjectId,jdbcType=BIGINT},#{subjectType,jdbcType=TINYINT},#{totalAmount,jdbcType=BIGINT}
        ,#{paymentTime,jdbcType=BIGINT},#{bizCreateTime,jdbcType=BIGINT},#{status,jdbcType=TINYINT},#{gmtCreate,jdbcType=BIGINT}
        ,#{gmtUpdate,jdbcType=BIGINT},#{deleted,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDO" useGeneratedKeys="true">
        insert into invoice_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="bizNo != null">biz_no,</if>
                <if test="bizType != null">biz_type,</if>
                <if test="subjectId != null">subject_id,</if>
                <if test="subjectType != null">subject_type,</if>
                <if test="totalAmount != null">total_amount,</if>
                <if test="paymentTime != null">payment_time,</if>
                <if test="bizCreateTime != null">biz_create_time,</if>
                <if test="status != null">status,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
                <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="bizNo != null">#{bizNo,jdbcType=VARCHAR},</if>
                <if test="bizType != null">#{bizType,jdbcType=TINYINT},</if>
                <if test="subjectId != null">#{subjectId,jdbcType=BIGINT},</if>
                <if test="subjectType != null">#{subjectType,jdbcType=TINYINT},</if>
                <if test="totalAmount != null">#{totalAmount,jdbcType=BIGINT},</if>
                <if test="paymentTime != null">#{paymentTime,jdbcType=BIGINT},</if>
                <if test="bizCreateTime != null">#{bizCreateTime,jdbcType=BIGINT},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDO">
        update invoice_manage
        <set>
                <if test="bizNo != null">
                    biz_no = #{bizNo,jdbcType=VARCHAR},
                </if>
                <if test="bizType != null">
                    biz_type = #{bizType,jdbcType=TINYINT},
                </if>
                <if test="subjectId != null">
                    subject_id = #{subjectId,jdbcType=BIGINT},
                </if>
                <if test="subjectType != null">
                    subject_id = #{subjectType,jdbcType=TINYINT},
                </if>
                <if test="totalAmount != null">
                    total_amount = #{totalAmount,jdbcType=BIGINT},
                </if>
                <if test="paymentTime != null">
                    payment_time = #{paymentTime,jdbcType=BIGINT},
                </if>
                <if test="bizCreateTime != null">
                    payment_time = #{bizCreateTime,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDO">
        update invoice_manage
        set 
            biz_no =  #{bizNo,jdbcType=VARCHAR},
            biz_type =  #{bizType,jdbcType=TINYINT},
            subject_id =  #{subjectId,jdbcType=BIGINT},
            subject_type =  #{subjectType,jdbcType=TINYINT},
            total_amount =  #{totalAmount,jdbcType=BIGINT},
            payment_time =  #{paymentTime,jdbcType=BIGINT},
            biz_create_time =  #{bizCreateTime,jdbcType=BIGINT},
            status =  #{status,jdbcType=TINYINT},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT}
    </update>

    <select id="list" parameterType="so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam" resultType="so.dian.invoice.pojo.entity.InvoiceManageDO">
        SELECT * FROM invoice_manage
        <where>
            deleted = 0
            <if test="bizNo != null and bizNo.length() > 0">
                AND biz_no = #{bizNo}
            </if>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="subjectType != null">
                AND subject_type = #{subjectType}
            </if>
            <if test="subjectIds != null and subjectIds.size > 0">
                AND subject_id IN
                <foreach item="subjectId" index="index" collection="subjectIds"
                         open="(" separator="," close=")">
                    #{subjectId}
                </foreach>
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="bizCreateTimeStart != null and bizCreateTimeEnd != null">
                AND biz_create_time BETWEEN #{bizCreateTimeStart} AND #{bizCreateTimeEnd}
            </if>
        </where>
        order by gmt_create desc
    </select>


    <select id="listByIds" resultType="so.dian.invoice.pojo.entity.InvoiceManageDO">
        SELECT * FROM invoice_manage
        WHERE deleted = 0 and id IN
        <foreach item="id" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
        order by gmt_create desc
    </select>

    <select id="getByBizNoAndBizType" resultType="so.dian.invoice.pojo.entity.InvoiceManageDO">
        SELECT * FROM invoice_manage
        WHERE deleted = 0 and biz_no = #{bizNo} AND biz_type = #{bizType}
    </select>
</mapper>
