<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.CheckInvoiceConclusionDAO">

  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.CheckInvoiceConclusionDO">
    <result column="id" jdbcType="INTEGER" property="id"/>
    <result column="is_used" jdbcType="INTEGER" property="isUsed"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="gmt_create" jdbcType="INTEGER" property="gmtCreate"/>
    <result column="gmt_update" jdbcType="INTEGER" property="gmtUpdate"/>
    <result column="deleted" jdbcType="INTEGER" property="deleted"/>
  </resultMap>

  <sql id="base_column">
    id,is_used,code,gmt_create,gmt_update
  </sql>

  <!--
   新增：新增质检结论
   频次：20次/日
   数据量：10
  -->
  <insert id="insert" parameterType="so.dian.invoice.pojo.entity.CheckInvoiceConclusionDO">
    INSERT INTO
    check_invoice_conclusion(is_used,code,gmt_create,gmt_update)
    VALUES
    (#{isUsed},#{code},#{gmtCreate},#{gmtUpdate})
  </insert>

  <!--
   查询：查询质检结论列表
   频次：500次/日
   数据量：50
  -->
  <select id="selectAll" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice_conclusion
    WHERE deleted = 0
  </select>

  <!--
   更新：更新质检结论
   频次：50次/日
   数据量：10
  -->
  <update id="update">
    UPDATE check_invoice_conclusion
    SET code = #{code},gmt_update = #{gmtUpdate}
    WHERE id = #{id}
    AND gmt_update = #{beforeGmtUpdate}
    AND deleted = 0
  </update>

  <!--
     更新：更新质检结论
     频次：10次/日
     数据量：5
  -->
  <update id="logicDeleted">
     UPDATE check_invoice_conclusion
     SET deleted = 1,gmt_update = #{gmtUpdate}
     WHERE id = #{id}
     AND deleted = 0
  </update>

  <!--
   查询：通过ID获取质检结论
   频次：500次/日
   数据量：50
  -->
  <select id="getById" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice_conclusion
    WHERE id=#{id}
    AND deleted = 0
  </select>

  <!--
   查询：通过code查询质检结论
   频次：500次/日
   数据量：50
  -->
  <select id="getByCode" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice_conclusion
    WHERE code = #{code}
    AND deleted = 0
    limit 1
  </select>

  <!--
   更新：更新直接结论为已使用
   频次：10次/日
   数据量：50
  -->
  <update id="updateIsUsedById">
    UPDATE check_invoice_conclusion
    SET is_used = #{isUsed},gmt_update = #{gmtUpdate}
    WHERE id = #{id}
    AND is_used = #{beforeIsUsed}
    AND deleted = 0
  </update>
</mapper>