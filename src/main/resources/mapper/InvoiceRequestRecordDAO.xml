<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceRequestRecordDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="manageId" column="manage_id" jdbcType="BIGINT"/>
            <result property="bizNo" column="biz_no" jdbcType="VARCHAR"/>
            <result property="requestId" column="request_id" jdbcType="BIGINT"/>
            <result property="amount" column="amount" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="financeFeedback" column="finance_feedback" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,manage_id,biz_no,request_id,
        amount,status,finance_feedback,
        deleted,gmt_create,gmt_update
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_request_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_request_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO" useGeneratedKeys="true">
        insert into invoice_request_record
        ( id,manage_id,biz_no,request_id
        ,amount,status,finance_feedback
        ,deleted,gmt_create,gmt_update
        )
        values (#{id,jdbcType=BIGINT},#{manageId,jdbcType=BIGINT},#{bizNo,jdbcType=VARCHAR},#{requestId,jdbcType=BIGINT}
        ,#{amount,jdbcType=BIGINT},#{status,jdbcType=TINYINT},#{financeFeedback,jdbcType=VARCHAR}
        ,#{deleted,jdbcType=TINYINT},#{gmtCreate,jdbcType=BIGINT},#{gmtUpdate,jdbcType=BIGINT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO" useGeneratedKeys="true">
        insert into invoice_request_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="manageId != null">manage_id,</if>
                <if test="bizNo != null">biz_no,</if>
                <if test="requestId != null">request_id,</if>
                <if test="amount != null">amount,</if>
                <if test="status != null">status,</if>
                <if test="financeFeedback != null">finance_feedback,</if>
                <if test="deleted != null">deleted,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="manageId != null">#{manageId,jdbcType=BIGINT},</if>
                <if test="bizNo != null">#{bizNo,jdbcType=VARCHAR},</if>
                <if test="requestId != null">#{requestId,jdbcType=BIGINT},</if>
                <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="financeFeedback != null">#{financeFeedback,jdbcType=VARCHAR},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO">
        update invoice_request_record
        <set>
                <if test="manageId != null">
                    manage_id = #{manageId,jdbcType=BIGINT},
                </if>
                <if test="bizNo != null">
                    biz_no = #{bizNo,jdbcType=VARCHAR},
                </if>
                <if test="requestId != null">
                    request_id = #{requestId,jdbcType=BIGINT},
                </if>
                <if test="amount != null">
                    amount = #{amount,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="financeFeedback != null">
                    finance_feedback = #{financeFeedback,jdbcType=VARCHAR},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO">
        update invoice_request_record
        set 
            manage_id =  #{manageId,jdbcType=BIGINT},
            biz_no =  #{bizNo,jdbcType=VARCHAR},
            request_id =  #{requestId,jdbcType=BIGINT},
            amount =  #{amount,jdbcType=BIGINT},
            status =  #{status,jdbcType=TINYINT},
            finance_feedback =  #{financeFeedback,jdbcType=VARCHAR},
            deleted =  #{deleted,jdbcType=TINYINT},
            gmt_create =  #{gmtCreate,jdbcType=BIGINT},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="list" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO" resultType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO">
        SELECT * FROM invoice_request_record
        <where>
            deleted = 0
            <if test="manageId != null">
                AND manage_id = #{manageId}
            </if>
        </where>
        <!-- 根据实际需求添加更多条件 -->
    </select>

    <select id="listByManageIds" resultType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO">
        SELECT * FROM invoice_request_record
        WHERE deleted = 0 and manage_id IN
        <foreach item="id" index="index" collection="manageIds"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getByRequestId" resultType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDO">
        SELECT * FROM invoice_request_record
        <where>
            deleted = 0
            <if test="requestId != null">
                AND request_id = #{requestId}
            </if>
        </where>
    </select>
</mapper>
