<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceOperateLogDAO">
  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceOperateLogDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, invoice_id,`type`,content, operator_id, operator_name, create_time,update_time,deleted
  </sql>

  <insert id="insert" parameterType="so.dian.invoice.pojo.entity.InvoiceOperateLogDO" keyProperty="id">
    INSERT INTO scm_invoice_operate_log(invoice_id,`type`,content, operator_id, operator_name, create_time,update_time,deleted)
    VALUES (#{invoiceId},#{type},#{content},#{operatorId},#{operatorName},#{createTime},#{updateTime},#{deleted})
  </insert>
  <!--
      功能：批量插入
      场景：批量插入
      频次：10000次/天
      -->
  <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" >
    INSERT INTO scm_invoice_operate_log(invoice_id,`type`,content, operator_id, operator_name, create_time,update_time,deleted)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceId},#{item.type},#{item.content},#{item.operatorId},
      #{item.operatorName},#{item.createTime},#{item.updateTime},#{item.deleted})
    </foreach>
  </insert>
  <!--
      功能：根据发票id查询操作日志
      场景：根据发票id查询操作日志
      频次：1000次/天
      -->
  <select id="findByInvoiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scm_invoice_operate_log
    where deleted=0
    and invoice_id = #{invoiceId}
  </select>

</mapper>