<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceValidateStatusDAO">
  
  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceValidateStatusDO">
      <result column="id" jdbcType="INTEGER" property="id"/>
      <result column="invoice_id" jdbcType="INTEGER" property="invoiceId"/>
      <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
      <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
      <result column="invoice_time" jdbcType="TIMESTAMP" property="invoiceTime"/>
      <result column="validate_time" jdbcType="TIMESTAMP" property="validateTime"/>
      <result column="is_real" jdbcType="INTEGER" property="isReal"/>
      <result column="validate_type" jdbcType="INTEGER" property="validateType"/>
      <result column="validate_code" jdbcType="VARCHAR" property="validateCode"/>
      <result column="validate_result" jdbcType="VARCHAR" property="validateResult"/>
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
      <result column="deleted" jdbcType="INTEGER" property="deleted"/>
  </resultMap>

  <sql id="base_column">
    id,invoice_id,invoice_no,invoice_code,invoice_time,validate_time,is_real,validate_type,validate_code,
    validate_result,update_time,create_time
  </sql>

  <!--
      插入：插入发票验真信息
      频次：0次/日
      数据量：1
   -->
  <insert id="insert" parameterType="so.dian.invoice.pojo.entity.InvoiceValidateStatusDO">
    INSERT INTO
    scm_invoice_validate_status(id,invoice_id,invoice_no,invoice_code,invoice_time,validate_time,is_real,
    validate_type,validate_code,validate_result,update_time,create_time)
    VALUES
    (#{id},#{invoiceId},#{invoiceNo},#{invoiceCode},#{invoiceTime},#{validateTime},#{isReal},
    #{validateType},#{validateCode},#{validateResult},#{updateTime},#{createTime})
  </insert>

  <!--
      插入：批量插入发票验真信息
      频次：1次/15日
      数据量：1000
   -->
  <insert id="batchInsert" parameterType="list">
    INSERT INTO
    scm_invoice_validate_status(invoice_id,invoice_no,invoice_code,invoice_time,validate_time,is_real,
    validate_type,validate_code,validate_result,update_time,create_time)
    VALUES
    <foreach collection="invoiceValidateStatusDOList" index="index" item="item" separator=",">
      (#{item.invoiceId},#{item.invoiceNo},#{item.invoiceCode},#{item.invoiceTime},#{item.validateTime},#{item.isReal},
      #{item.validateType},#{item.validateCode},#{item.validateResult},#{item.updateTime},#{item.createTime})
    </foreach>
  </insert>

  <!--
      查询：根据发票id查询发票验真信息
      频次：100次/日
      数据量：1万+
   -->
  <select id="getById" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM scm_invoice_validate_status
    WHERE id=#{id}
    AND deleted = 0
  </select>

  <!--
      查询：根据发票号码和代码查询发票验真信息
      频次：100次/日
      数据量：1万+
   -->
  <select id="listByInvoiceNoAndInvoiceCode" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM scm_invoice_validate_status
    WHERE invoice_no = #{invoiceNo}
    AND invoice_code = #{invoiceCode}
    AND deleted = 0
  </select>
  <!--
      查询：根据条件查询发票验真信息
      频次：100次/日
      数据量：1万+
   -->
  <select id="listByParam" resultType="java.lang.Long">
    select
    distinct invoice_id
    from scm_invoice_validate_status
    where deleted = 0
    <if test="isReal != null">
      and is_real = #{isReal}
    </if>
    <if test="validateCodeList != null and validateCodeList.size > 0">
      and validate_code in
      <foreach collection="validateCodeList" item="validateCode" open="(" separator="," close=")">
        #{validateCode.code}
      </foreach>
    </if>
    <if test="startCreateTime != null">
      and invoice_time <![CDATA[ >= ]]> #{startCreateTime}
    </if>
    <if test="endCreateTime != null">
      and invoice_time <![CDATA[ < ]]> #{endCreateTime}
    </if>
<!--    <if test="notValidateCodeList != null and notValidateCodeList.size > 0">-->
<!--      and validate_code not in-->
<!--      <foreach collection="notValidateCodeList" item="notValidateCode" open="(" separator="," close=")">-->
<!--        #{notValidateCode}-->
<!--      </foreach>-->
<!--    </if>-->
  </select>
  <select id="getAllValidateInvoice" resultType="so.dian.invoice.pojo.entity.InvoiceValidateStatusDO">
    select a.*
    from (select * from scm_invoice_validate_status order by validate_time desc limit 100000) a
    group by a.invoice_id
  </select>


</mapper>