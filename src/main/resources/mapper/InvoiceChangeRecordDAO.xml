<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceChangeRecordDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceChangeRecordDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="outBizId" column="out_biz_id" jdbcType="VARCHAR"/>
            <result property="outBizType" column="out_biz_type" jdbcType="TINYINT"/>
            <result property="manageId" column="manage_id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="BIGINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,out_biz_id,out_biz_type,
        manage_id,type,amount,
        deleted,gmt_create,gmt_update
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_change_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_change_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDO" useGeneratedKeys="true">
        insert into invoice_change_record
        ( id,out_biz_id,out_biz_type
        ,manage_id,type,amount
        ,deleted,gmt_create,gmt_update
        )
        values (#{id,jdbcType=BIGINT},#{outBizId,jdbcType=VARCHAR},#{outBizType,jdbcType=TINYINT}
        ,#{manageId,jdbcType=BIGINT},#{type,jdbcType=VARCHAR},#{amount,jdbcType=BIGINT}
        ,#{deleted,jdbcType=TINYINT},#{gmtCreate,jdbcType=BIGINT},#{gmtUpdate,jdbcType=BIGINT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDO" useGeneratedKeys="true">
        insert into invoice_change_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="outBizId != null">out_biz_id,</if>
                <if test="outBizType != null">out_biz_type,</if>
                <if test="manageId != null">manage_id,</if>
                <if test="type != null">type,</if>
                <if test="amount != null">amount,</if>
                <if test="deleted != null">deleted,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="outBizId != null">#{outBizId,jdbcType=VARCHAR},</if>
                <if test="outBizType != null">#{outBizType,jdbcType=TINYINT},</if>
                <if test="manageId != null">#{manageId,jdbcType=BIGINT},</if>
                <if test="type != null">#{type,jdbcType=VARCHAR},</if>
                <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDO">
        update invoice_change_record
        <set>
                <if test="outBizId != null">
                    out_biz_id = #{outBizId,jdbcType=VARCHAR},
                </if>
                <if test="outBizType != null">
                    out_biz_type = #{outBizType,jdbcType=TINYINT},
                </if>
                <if test="manageId != null">
                    manage_id = #{manageId,jdbcType=BIGINT},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=VARCHAR},
                </if>
                <if test="amount != null">
                    amount = #{amount,jdbcType=BIGINT},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDO">
        update invoice_change_record
        set 
            out_biz_id =  #{outBizId,jdbcType=VARCHAR},
            out_biz_type =  #{outBizType,jdbcType=TINYINT},
            manage_id =  #{manageId,jdbcType=BIGINT},
            type =  #{type,jdbcType=VARCHAR},
            amount =  #{amount,jdbcType=BIGINT},
            deleted =  #{deleted,jdbcType=TINYINT},
            gmt_create =  #{gmtCreate,jdbcType=BIGINT},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="listByManageId" resultType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDO">
        SELECT * FROM invoice_change_record WHERE deleted = 0 and manage_id = #{manageId} order by gmt_create desc
    </select>

    <select id="getByBizNoAndBizType" resultType="so.dian.invoice.pojo.entity.InvoiceChangeRecordDO">
        SELECT * FROM invoice_change_record
        WHERE deleted = 0 and out_biz_id = #{outBizId} AND out_biz_type = #{outBizType}
    </select>
</mapper>
