<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.CheckInvoiceEmployeeDAO">

  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.CheckInvoiceEmployeeDO">
    <result column="id" jdbcType="INTEGER" property="id"/>
    <result column="user_id" jdbcType="INTEGER" property="userId"/>
    <result column="region" jdbcType="VARCHAR" property="region"/>
    <result column="gmt_create" jdbcType="INTEGER" property="gmtCreate"/>
    <result column="gmt_update" jdbcType="INTEGER" property="gmtUpdate"/>
    <result column="deleted" jdbcType="INTEGER" property="deleted"/>
  </resultMap>

  <sql id="base_column">
    id,user_id,region,gmt_create,gmt_update
  </sql>

  <!--
   查询：查询质检员工大区配置列表
   频次：500次/日
   数据量：50
  -->
  <select id="selectAll" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice_employee
    WHERE deleted = 0
    ORDER BY gmt_create DESC
  </select>

  <!--
   新增：新增员工大区配置
   频次：100次/日
   数据量：50
  -->
  <insert id="batchInsert" parameterType="list">
    INSERT INTO
    check_invoice_employee(user_id,region,gmt_create,gmt_update)
    VALUES
    <foreach collection="checkInvoiceEmployeeDOList" index="index" item="item" separator=",">
      (#{item.userId},#{item.region},#{item.gmtCreate},#{item.gmtUpdate})
    </foreach>
  </insert>

  <!--
   更新：修改员工大区配置
   频次：100次/日
   数据量：10
  -->
  <update id="update">
    UPDATE check_invoice_employee
    SET region = #{region},user_id = #{userId}
    WHERE id = #{id}
    AND deleted = 0
  </update>

  <!--
   查询：通过员工ID查找配置信息
   频次：500次/日
   数据量：50
  -->
  <select id="getByUserId" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice_employee
    WHERE user_id = #{userId}
    AND deleted = 0
  </select>

  <!--
   查询：通过ID获取员工大区配置
   频次：500次/日
   数据量：50
  -->
  <select id="getById" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice_employee
    WHERE id=#{id}
    AND deleted = 0
  </select>

  <!--
   更新：批量逻辑删除
   频次：10次/日
   数据量：10
  -->
  <update id="batchLogicDeleted">
    UPDATE check_invoice_employee
    SET deleted = 1
    WHERE id IN
    <foreach collection="idList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND deleted = 0
  </update>

  <!--
   查询所有的大区
  -->
  <select id="getAllRegionsDistinct" resultType="java.lang.String">
    SELECT DISTINCT(region) FROM check_invoice_employee WHERE deleted = 0
  </select>

  <select id="getByUserIdAndRegion" resultMap="BaseResultMap">
    SELECT
    <include refid="base_column"/>
    FROM check_invoice_employee
    WHERE user_id = #{userId}
    AND region = #{region}
    AND deleted = 0
  </select>
</mapper>