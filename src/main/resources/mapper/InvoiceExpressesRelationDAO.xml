<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceExpressesRelationDAO">

  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_id" jdbcType="VARCHAR" property="invoiceId" />
    <result column="express_id" jdbcType="VARCHAR" property="expressId" />
    <result column="express_name" jdbcType="VARCHAR" property="expressName" />
    <result column="express_tracking_no" jdbcType="VARCHAR" property="expressTrackingNo" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="express_img" jdbcType="VARCHAR" property="expressImg" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="invoice_numbers" jdbcType="INTEGER" property="invoiceNumbers" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id,invoice_id,express_id, express_name,express_tracking_no,
    create_time,update_time,deleted,operator_id,operator_name,express_img,serial_no
  </sql>

  <!--
    功能：发票新增
    数据量：
    频次：5000次/月
    -->
  <insert id="insert" parameterType="so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO" keyProperty="id">
        insert  into scm_invoice_expresses_relation (
            invoice_id,express_id, express_name,express_tracking_no,
            create_time,update_time,deleted
        )values (
            #{invoiceId},#{expressId},#{expressName},#{expressTrackingNo},now(),now(),#{deleted}
        )
  </insert>

  <!--
    功能：发票批量新增
    数据量：
    频次：50次/月
    -->
  <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" >
    insert  into scm_invoice_expresses_relation (
    invoice_id,express_id, express_name,express_tracking_no,
    create_time,update_time,deleted,operator_id,operator_name,express_img,serial_no
    )
    values
    <foreach collection="list" item="obj" index="index" separator="," >
      (
      #{obj.invoiceId},#{obj.expressId},#{obj.expressName},#{obj.expressTrackingNo},now(),now(),#{obj.deleted},#{obj.operatorId},#{obj.operatorName},#{obj.expressImg},#{obj.serialNo}
      )
    </foreach>

  </insert>

  <!--
    功能：查询发票物流
    数据量：
    频次：100次/天
    -->
  <select id="findInvoiceExpressesRelations" parameterType="map" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"></include>
        from
            scm_invoice_expresses_relation
        where
            deleted=#{deleted}
            and
            invoice_id in
            <foreach collection="invoiceIds"  item="item"  open="(" close=")" separator=",">
                #{item}
            </foreach>


  </select>

  <!--
     功能：查询发票是否存在物流
     数据量：
     频次：100次/天
     -->
  <select id="findSimpleInvoiceExpressesRelations" parameterType="map" resultMap="BaseResultMap">
    select
        id,invoice_id
    from
    scm_invoice_expresses_relation
    where
    deleted=#{deleted}
    and
    invoice_id in
    <foreach collection="invoiceIds"  item="item"  open="(" close=")" separator=",">
      #{item}
    </foreach>


  </select>



  <!--
     功能：查询发票是否存在物流
     数据量：
     频次：100次/天
     -->

  <select id="getInvoiceExpressesRelationByInvoiceId" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        scm_invoice_expresses_relation
        where
        invoice_id=#{invoiceId}
        limit 1
  </select>
  <!--
     功能：更新发票是否存在物流
     数据量：
     频次：50次/天
     -->
  <update id="updateInvoiceExpressesRelationById" parameterType="so.dian.invoice.pojo.entity.InvoiceExpressesRelationDO" >

        update
            scm_invoice_expresses_relation
        set
            update_time=now()
            <if test="expressName !=null ">
                ,express_name=#{expressName}
            </if>
            <if test="expressId !=null ">
              ,express_id=#{expressId}
            </if>
            <if test="expressTrackingNo !=null ">
              ,express_tracking_no=#{expressTrackingNo}
            </if>
        where id=#{id}
  </update>

  <!--
     功能：批量更新发票是否存在物流
     数据量：
     频次：50次/天
     -->
  <update id="updateBatchInvoiceExpressesRelationById" parameterType="so.dian.invoice.pojo.dto.InvoiceExpressesUpdateDto" >

    update
    scm_invoice_expresses_relation
    set
    update_time=now()
    <if test="expressName !=null ">
      ,express_name=#{expressName}
    </if>
    <if test="expressId !=null ">
      ,express_id=#{expressId}
    </if>
    <if test="expressTrackingNo !=null ">
      ,express_tracking_no=#{expressTrackingNo}
    </if>
      <if test="operatorId !=null ">
          ,operator_id=#{operatorId}
      </if>
      <if test="operatorName !=null ">
          ,operator_name=#{operatorName}
      </if>
      <if test="expressImg !=null ">
          ,express_img=#{expressImg}
      </if>
      <if test="serialNo !=null ">
          ,serial_no=#{serialNo}
      </if>
    where id  in
    <foreach collection="ids"  item="item"  open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="listInvoiceIdsByExpressNo" resultType="java.lang.Long">
      SELECT invoice_id FROM scm_invoice_expresses_relation WHERE express_tracking_no = #{expressNo} and deleted=0
  </select>

  <select id="findAllByQuery" resultMap="BaseResultMap">
        SELECT
        serial_no, operator_name, update_time, count(invoice_id) as invoice_numbers, express_name, express_tracking_no, express_img
        FROM
            scm_invoice_expresses_relation
        <where>
            <if test="startTime != null ">
                and update_time &gt;= #{startTime}
            </if>
            <if test="endTime != null ">
                and update_time &lt;= #{endTime}
            </if>
            <if test="expressTrackingNo != null and  expressTrackingNo != '' ">
                and express_tracking_no = #{expressTrackingNo}
            </if>
            <if test="operatorName != null and operatorName != '' ">
                and operator_name = #{operatorName}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
        </where>
        GROUP BY
            express_tracking_no,
            operator_name,
            serial_no,
            update_time
        ORDER BY
            update_time desc
        limit #{start},#{pageSize}
  </select>
    <!--
    功能：查询发票寄送条数
    场景：查询发票寄送条数
    频次：1000次/天
    -->
  <select id="countByQuery" resultType="int">
        SELECT
            count(*)
        FROM
            (
                SELECT
                    id
                FROM
                    scm_invoice_expresses_relation
                  <where>
                      <if test="startTime != null ">
                          and update_time &gt;= #{startTime}
                      </if>
                      <if test="endTime != null ">
                          and update_time &lt;= #{endTime}
                      </if>
                      <if test="expressTrackingNo != null and  expressTrackingNo != '' ">
                          and express_tracking_no = #{expressTrackingNo}
                      </if>
                      <if test="operatorName != null and operatorName != '' ">
                          and operator_name = #{operatorName}
                      </if>
                      <if test="operatorId != null ">
                          and operator_id = #{operatorId}
                      </if>
                  </where>
                GROUP BY
                    express_tracking_no,
                    operator_name,
                    serial_no,
                    update_time
            ) as sier
  </select>

  <select id="listInvoiceExpressesRelationByTime" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM scm_invoice_expresses_relation
    WHERE create_time <![CDATA[ >= ]]> #{beginTime}
    AND create_time <![CDATA[ <= ]]> #{endTime}
    AND deleted = 0
  </select>

</mapper>