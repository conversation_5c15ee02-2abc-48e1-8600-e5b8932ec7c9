<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.ChannelInvoiceDao">
  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.ChannelInvoiceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="tax_no" jdbcType="VARCHAR" property="taxNo" />
    <result column="settle_subject_type" jdbcType="INTEGER" property="settleSubjectType" />
    <result column="settle_subject_id" jdbcType="BIGINT" property="settleSubjectId" />
    <result column="province_code" jdbcType="INTEGER" property="provinceCode" />
    <result column="city_code" jdbcType="INTEGER" property="cityCode" />
    <result column="area_code" jdbcType="INTEGER" property="areaCode" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `status`, title, tax_no, settle_subject_type, settle_subject_id, province_code, 
    city_code, area_code, address_detail, receiver, receiver_phone, creator, gmt_create, 
    gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from channel_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from channel_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.ChannelInvoiceDO" useGeneratedKeys="true">
    insert into channel_invoice (`status`, title, tax_no, 
      settle_subject_type, settle_subject_id, province_code, 
      city_code, area_code, address_detail, 
      receiver, receiver_phone, creator, 
      gmt_create, gmt_update, deleted
      )
    values (#{status,jdbcType=INTEGER}, #{title,jdbcType=VARCHAR}, #{taxNo,jdbcType=VARCHAR}, 
      #{settleSubjectType,jdbcType=INTEGER}, #{settleSubjectId,jdbcType=BIGINT}, #{provinceCode,jdbcType=INTEGER}, 
      #{cityCode,jdbcType=INTEGER}, #{areaCode,jdbcType=INTEGER}, #{addressDetail,jdbcType=VARCHAR}, 
      #{receiver,jdbcType=VARCHAR}, #{receiverPhone,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.ChannelInvoiceDO" useGeneratedKeys="true">
    insert into channel_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="taxNo != null">
        tax_no,
      </if>
      <if test="settleSubjectType != null">
        settle_subject_type,
      </if>
      <if test="settleSubjectId != null">
        settle_subject_id,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="addressDetail != null">
        address_detail,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="receiverPhone != null">
        receiver_phone,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null">
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="settleSubjectType != null">
        #{settleSubjectType,jdbcType=INTEGER},
      </if>
      <if test="settleSubjectId != null">
        #{settleSubjectId,jdbcType=BIGINT},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=INTEGER},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=INTEGER},
      </if>
      <if test="addressDetail != null">
        #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.ChannelInvoiceDO">
    update channel_invoice
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null">
        tax_no = #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="settleSubjectType != null">
        settle_subject_type = #{settleSubjectType,jdbcType=INTEGER},
      </if>
      <if test="settleSubjectId != null">
        settle_subject_id = #{settleSubjectId,jdbcType=BIGINT},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=INTEGER},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=INTEGER},
      </if>
      <if test="addressDetail != null">
        address_detail = #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.ChannelInvoiceDO">
    update channel_invoice
    set `status` = #{status,jdbcType=INTEGER},
      title = #{title,jdbcType=VARCHAR},
      tax_no = #{taxNo,jdbcType=VARCHAR},
      settle_subject_type = #{settleSubjectType,jdbcType=INTEGER},
      settle_subject_id = #{settleSubjectId,jdbcType=BIGINT},
      province_code = #{provinceCode,jdbcType=INTEGER},
      city_code = #{cityCode,jdbcType=INTEGER},
      area_code = #{areaCode,jdbcType=INTEGER},
      address_detail = #{addressDetail,jdbcType=VARCHAR},
      receiver = #{receiver,jdbcType=VARCHAR},
      receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectBySettleSubjectId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from channel_invoice
    where settle_subject_id = #{settleSubjectId,jdbcType=BIGINT} and deleted = 0
  </select>
</mapper>