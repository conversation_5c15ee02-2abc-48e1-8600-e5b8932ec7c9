<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceSubjectRelationDAO">
  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="subject_name" jdbcType="VARCHAR" property="subjectName" />
    <result column="relation_subject_name" jdbcType="VARCHAR" property="relationSubjectName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_id, biz_type, subject_name, relation_subject_name, remark, create_time, creator, 
    update_time, updater, deleted, status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scm_invoice_subject_relation
    where id = #{id,jdbcType=INTEGER}
    and deleted=0
  </select>

  <insert id="insertSelective" parameterType="so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO" keyProperty="id">
    insert into scm_invoice_subject_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="subjectName != null">
        subject_name,
      </if>
      <if test="relationSubjectName != null">
        relation_subject_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="subjectName != null">
        #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="relationSubjectName != null">
        #{relationSubjectName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <select id="selectByBizIdAndBizTypeAndSubjectName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from scm_invoice_subject_relation
    where deleted=0
    and status=1
    and biz_id = #{bizId}
    and biz_type = #{bizType}
    and subject_name=#{subjectName}
    order by id desc limit 1
  </select>

  <!-- 根据发票关联主体名称获取发票主体名称列表-->
  <select id="getSubjectNameList" resultType="java.lang.String">
    select subject_name
    from scm_invoice_subject_relation
    where deleted=0
    and status=1
    and relation_subject_name = #{relationSubjectName}
  </select>

  <update id="deleteByIdList">
    update scm_invoice_subject_relation
    set deleted = 1, update_time=now()
    where id in
    <foreach collection="idList" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
  </update>

  <!--
       查询：查询发票主体对应关系总数量
       频次：100次/日
       数据量：10
   -->
  <select id="countInvoiceSubjectRelation" parameterType="so.dian.invoice.pojo.query.InvoiceSubjectRelationQuery"
      resultType="java.lang.Long">
    SELECT COUNT(1) FROM scm_invoice_subject_relation
    <include refid="whereCondition"/>
  </select>

  <!--
        查询：查询发票主体对应关系列表
        频次：100次/日
        数据量：10
    -->
  <select id="selectInvoiceSubjectRelationList" parameterType="so.dian.invoice.pojo.query.InvoiceSubjectRelationQuery"
      resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM scm_invoice_subject_relation
    <include refid="whereCondition"/>
    ORDER BY create_time DESC
    LIMIT #{start},#{pageSize}
  </select>

  <select id="listByMerchantIdAndRelationSubjectName" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM scm_invoice_subject_relation
    WHERE deleted=0
    AND status=1
    AND biz_id=#{merchantId}
    AND relation_subject_name=#{relationSubjectName}
  </select>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" >
    INSERT INTO scm_invoice_subject_relation
    (biz_id, biz_type, subject_name, relation_subject_name, remark, create_time, creator,
    update_time, updater, deleted, status)
    VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.bizId},#{item.bizType},#{item.subjectName},
      #{item.relationSubjectName},#{item.remark},#{item.createTime},
      #{item.creator},#{item.updateTime},#{item.updater},
      #{item.deleted},#{item.status})
    </foreach>
  </insert>

  <update id="updateInvoiceSubjectRelation" parameterType="so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO">
    UPDATE scm_invoice_subject_relation
    SET status=#{status},updater=#{updater},update_time=#{updateTime}
    WHERE id=#{id}
    AND status=#{beforeStatus}
    AND deleted = 0
  </update>

  <sql id="whereCondition">
    WHERE deleted = 0
    <if test="bizId != null">
      AND biz_id = #{bizId}
    </if>
    <if test="subjectName != null">
      AND subject_name = #{subjectName}
    </if>
    <if test="relationSubjectName != null">
      AND relation_subject_name = #{relationSubjectName}
    </if>
    <if test="status != null">
      AND status = #{status}
    </if>
  </sql>

  <!-- 根据商户查询映射主体 -->
  <select id="findByBizIdAndBizType" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM scm_invoice_subject_relation
    WHERE deleted=0
    AND status=1
    AND biz_id=#{bizId}
    AND biz_type=#{bizType}
  </select>

  <!-- 处理特殊字符 -->
  <update id="trimRelation" parameterType="so.dian.invoice.pojo.entity.InvoiceSubjectRelationDO">
    UPDATE scm_invoice_subject_relation
    SET subject_name = #{subjectName},
    relation_subject_name = #{relationSubjectName}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <!--
    查询：批量查询映射主体
   -->
  <select id="batchQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM scm_invoice_subject_relation
    WHERE deleted = 0
    AND id > #{lastId}
    limit 1000
  </select>

  <select id="listByRelationSubjectNameAndBizType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM scm_invoice_subject_relation
    WHERE deleted = 0
    and status=1
    AND biz_type=#{bizType}
    and relation_subject_name = #{relationSubjectName}
  </select>
</mapper>