<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceConfigDAO">

    <insert id="insert" parameterType="so.dian.invoice.pojo.entity.InvoiceConfigDO">
        INSERT INTO `invoice_config`
        (
        `key`,
        `value`,
        `version`,
        `status`,
        `create_time`,
        `update_time`)
        VALUES
        (
        #{key},
        #{value},
        #{version},
        #{status},
        now(),
        #{updateTime});
    </insert>

    <update id="update" parameterType="so.dian.invoice.pojo.entity.InvoiceConfigDO">
        UPDATE `invoice_config`
        SET
        `value` = #{value},
        `version` = #{version},
        `status` = #{status},
        `update_time` = #{updateTime}
        WHERE `id` = #{id};
    </update>

    <select id="getByKey" resultType="so.dian.invoice.pojo.entity.InvoiceConfigDO">
        SELECT * from invoice_config where `key`=#{key} AND status=1 limit 1;
    </select>
</mapper>



