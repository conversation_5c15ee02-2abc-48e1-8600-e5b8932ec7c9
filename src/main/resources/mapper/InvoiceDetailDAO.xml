<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceDetailDAO">
  <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceDetailDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_spec" jdbcType="VARCHAR" property="materialSpec" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="raw_price" jdbcType="DECIMAL" property="rawPrice" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="tax_price" jdbcType="DECIMAL" property="taxPrice" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="supplier_invoice_detail_id" jdbcType="INTEGER" property="supplierInvoiceDetailId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, invoice_no, material_name, material_spec, unit, unit_price, quantity, raw_price,
    tax_rate, tax_price, gmt_create, gmt_modified, is_delete, price, invoice_code,supplier_invoice_detail_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scm_invoice_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectInvoiceDetailList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from scm_invoice_detail
        where invoice_no = #{invoiceNo,jdbcType=VARCHAR}
        <if test="invoiceCode != null and invoiceCode != ''">
            and invoice_code = #{invoiceCode,jdbcType=VARCHAR}
        </if>
        and is_delete = 0
        order by gmt_create desc limit 1
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from scm_invoice_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="so.dian.invoice.pojo.entity.InvoiceDetailDO">
    insert into scm_invoice_detail (id, invoice_no, material_name, 
      material_spec, unit, unit_price, 
      quantity, raw_price, tax_rate,
      tax_price, gmt_create, gmt_modified, 
      is_delete, price, invoice_code,supplier_invoice_detail_id)
    values (#{id,jdbcType=INTEGER}, #{invoiceNo,jdbcType=VARCHAR}, #{materialName,jdbcType=VARCHAR}, 
      #{materialSpec,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{unitPrice,jdbcType=DECIMAL}, 
      #{quantity,jdbcType=INTEGER}, #{rawPrice,jdbcType=DECIMAL}, #{taxRate,jdbcType=DECIMAL},
      #{taxPrice,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, #{invoiceCode,jdbcType=VARCHAR}, #{supplierInvoiceDetailId,jdbcType=INTEGER})
  </insert>
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" >
        insert into scm_invoice_detail (invoice_no, material_name, material_spec,
        unit, unit_price, quantity,
        raw_price, tax_rate, tax_price,
        price, invoice_code,supplier_invoice_detail_id)
        values
        <foreach collection="list" item="obj" index="index" separator="," >
        (#{obj.invoiceNo,jdbcType=VARCHAR}, #{obj.materialName,jdbcType=VARCHAR}, #{obj.materialSpec,jdbcType=VARCHAR},
        #{obj.unit,jdbcType=VARCHAR}, #{obj.unitPrice,jdbcType=DECIMAL}, #{obj.quantity,jdbcType=INTEGER},
        #{obj.rawPrice,jdbcType=DECIMAL}, #{obj.taxRate,jdbcType=DECIMAL}, #{obj.taxPrice,jdbcType=DECIMAL},
        #{obj.price,jdbcType=DECIMAL}, #{obj.invoiceCode,jdbcType=VARCHAR}, #{obj.supplierInvoiceDetailId,jdbcType=INTEGER})
        </foreach>
    </insert>
  <insert id="insertSelective" parameterType="so.dian.invoice.pojo.entity.InvoiceDetailDO" keyProperty="id">
    insert into scm_invoice_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="materialSpec != null">
        material_spec,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="unitPrice != null">
        unit_price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="rawPrice != null">
        raw_price,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="taxPrice != null">
        tax_price,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="price != null">
        price,
      </if>
        <if test="invoiceCode != null">
            invoice_code,
        </if>
        <if test="supplierInvoiceDetailId != null">
            supplier_invoice_detail_id,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialSpec != null">
        #{materialSpec,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="rawPrice != null">
        #{rawPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="taxPrice != null">
        #{taxPrice,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
        <if test="invoiceCode != null">
            #{invoiceCode,jdbcType=VARCHAR},
        </if>
        <if test="supplierInvoiceDetailId != null">
            #{supplierInvoiceDetailId,jdbcType=INTEGER},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceDetailDO">
    update scm_invoice_detail
    <set>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialSpec != null">
        material_spec = #{materialSpec,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="rawPrice != null">
        raw_price = #{rawPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="taxPrice != null">
        tax_price = #{taxPrice,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
        <if test="invoiceCode != null">
            invoice_code = #{invoiceCode,jdbcType=VARCHAR},
        </if>
        <if test="supplierInvoiceDetailId != null">
            supplier_invoice_detail_id = #{supplierInvoiceDetailId,jdbcType=VARCHAR},
        </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
    <update id="updateInvoiceDetailByInvoiceCodeAndNo" parameterType="so.dian.invoice.pojo.entity.InvoiceDetailDO">
        update scm_invoice_detail
        <set>
            <if test="materialName != null">
                material_name = #{materialName,jdbcType=VARCHAR},
            </if>
            <if test="materialSpec != null">
                material_spec = #{materialSpec,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="rawPrice != null">
                raw_price = #{rawPrice,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="taxPrice != null">
                tax_price = #{taxPrice,jdbcType=DECIMAL},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="supplierInvoiceDetailId != null">
                supplier_invoice_detail_id = #{supplierInvoiceDetailId,jdbcType=VARCHAR},
            </if>
        </set>
        where invoice_no = #{invoiceNo,jdbcType=VARCHAR}
        and invoice_code = #{invoiceCode,jdbcType=VARCHAR}
    </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceDetailDO">
    update scm_invoice_detail
    set invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      material_name = #{materialName,jdbcType=VARCHAR},
      material_spec = #{materialSpec,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      unit_price = #{unitPrice,jdbcType=DECIMAL},
      quantity = #{quantity,jdbcType=INTEGER},
      raw_price = #{rawPrice,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      tax_price = #{taxPrice,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      invoice_code = #{invoiceCode,jdbcType=VARCHAR},
        supplier_invoice_detail_id = #{supplierInvoiceDetailId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="listInvoiceDetailByNoAndCode" resultMap="BaseResultMap" parameterType="List">
    select
    <include refid="Base_Column_List" />
    from scm_invoice_detail
    where
    <foreach item="item" collection="invoiceNoAndCodeMapList" separator="OR">
      (invoice_no=#{item.invoiceNo} and invoice_code=#{item.invoiceCode})
    </foreach>
    and is_delete = 0
  </select>
  
  <select id="findSupplierInvoiceDetailId" resultType="java.lang.Integer">
    select supplier_invoice_detail_id
    from scm_invoice_detail
    where
    <foreach item="item" collection="list" separator="OR">
      (invoice_no=#{item.invoiceNo} and invoice_code=#{item.invoiceCode})
    </foreach>
    and is_delete = 0
  </select>
  
</mapper>