<?xml version="1.0" encoding="UTF-8"?>
<!-- scan 配置文件如果发生改变，将会被重新加载  scanPeriod 检测间隔时间-->
<configuration debug="false" scan="true" scanPeriod="60 seconds">
    
    <springProperty scope="context" name="springLevel" source="logging.level.spring" defaultValue="INFO"/>
    <property name="DIAN_MONITOR_LEVEL" value="ERROR"/>
    <property name="LOG_PATH" value="${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}"/>
    
    <!--引入父配置-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    <include resource="included-config.xml"/>
    
    <springProfile name="!local">
        <!--默认日志topic，即 @Slf4j 或 LoggerFactory.getLogger(ClassName.class}) 方式打印的日志-->
        <root level="${springLevel}">
            <appender-ref ref="DIAN_MONITOR_SURPLUS"/>
        </root>
    </springProfile>
    
    <springProfile name="local">
        <root level="${springLevel}">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="dev,stable">
        <root level="${springLevel}">
            <appender-ref ref="DIAN_MONITOR_SURPLUS"/>
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

</configuration>