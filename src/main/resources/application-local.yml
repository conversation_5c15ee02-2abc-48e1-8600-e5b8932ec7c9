spring:
  db:
    driver: com.mysql.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: invoice_oss
    password: AYozo23iUHkiZkq7
    maxActive: 400
    maxWait: 100
    initialSize: 5
    timeBetweenEvictionRunsMillis: 60000
    maxOpenPreparedStatementPerConnectionSize: 100
    minEvictableIdleTimeMillis: 300000
    minIdle: 5
    validationQuery: 'select version()'
    testWhileIdle: false
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    connectionProperties: 'druid.stat.mergeSql:true;druid.stat.slowSqlMillis:5000'
    filters: stat,slf4j
  redis:
    host: oss-redis.dian-stable.com
    password: bW0HkQRbuZKxcEvi
    port: 6379
    timeout: 300000
    maxIdle: 10
    maxWaitMillis: 300000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
      enabled: true
redisson:
  address: oss-redis.dian-stable.com:6379
  password: bW0HkQRbuZKxcEvi
  connect-timeout: 10000
system:
  env: stable
invoice:
  oss:
    end-point: oss-cn-beijing.aliyuncs.com
logging:
  level:
    so.dian.invoice: DEBUG
leo:
  baseUrl: http://o.dian-stable.com/leo
mybatis:
  configuration:
    map-underscore-to-camel-case: true
# 睿琪发票接口
glority:
  appKey: 5c7ce1b9
  appSecret: 3f0734c737bc255bdda7b1c5a681bef4
  host: http://fapiao.glority.cn

# 只针对GlorityClient的熔断配置
hystrix:
  command:
    GlorityClient#get(GlorityParam).execution.isolation.thread.timeoutInMilliseconds: 35000

#xxjob定时任务
xxl:
  job:
    access-token:  # 执行器通讯TOKEN
    admin-addresses: http://xxljob.dian-stable.com   # 调度中心部署跟地址
    executor:
      enabled: true    # 是否开启自动注册
      app-name: invoice-job  # 执行器AppName
      log-path: ${logging.path}  # 执行器运行日志文件存储磁盘路径
      log-retention-days: 3 # 执行器日志文件保存天数
temp.path: /var/tmp/${spring.application.name}/temp/

aliyun:
  oss:
    accessKeyId: LTAI5tMYkNML8km7cJspyAFy
    accessKeySecret: ******************************
    bucketName: xdfis-file
    endPoint: oss-cn-beijing.aliyuncs.com
