spring:
  application:
    name: invoice
  main:
    allow-bean-definition-overriding: true
  jackson:
    serialization:
      write-dates-as-timestamps: true
  cloud:
    bootstrap:
      enabled: true
    nacos:
      config:
        enabled: true
        server-addr: http://nacos-dev.dian.so:80
        file-extension: yaml
        shared-configs:
          - dataId: invoice-alter-1044515110560731136.yaml
          - dataId: global22X.yaml
          - dataId: global-hystrix.yaml
          - dataId: global22X-management.yaml
        name: ${spring.application.name}
#        refreshable-dataids: {dynamic-config.yaml}   # 动态监听的配置需要单独声明
server:
  port: 8080
  tomcat:
    max-http-form-post-size: 100MB
#  servlet:
#    context-path: /credit-platform
mybatis:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
---
spring:
  config:
    activate:
      on-profile: dev # 此处对应Nacos的namespace命令空间,DNA开发/测试环境启动应用,默认会读取namespace:dev 下的配置文件的
  cloud:
    nacos:
      config:
        enabled: true               # enable nacos配置
        namespace: dev              # 配置对应环境的namespace, ns参考下面的图片
        server-addr: http://nacos-dev.dian.so:80
---
spring:
  config:
    activate:
      on-profile: stable
  cloud:
    nacos:
      config:
        enabled: true
        namespace: 4061bdcb-a707-4b81-b689-a035ad009309           #配置对应环境的namespace, ns参考下面的图片
        server-addr: http://nacos-dev.dian.so:80

---
spring:
  config:
    activate:
      on-profile: pre
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: nacos-real.dian.so:80
        namespace: c1f38994-50a4-482f-bd30-95d65188cf9d            #配置对应环境的namespace, ns参考下面的图片

---
spring:
  config:
    activate:
      on-profile: real
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: nacos-real.dian.so:80                            # 配置对应的nacos地址
        namespace: fcf1f0b9-4c31-43e5-80ef-f33a8d5d79dc               # 配置对应环境的namespace, ns参考下面的图片