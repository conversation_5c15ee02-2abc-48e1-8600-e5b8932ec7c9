@Library('jenkins-pipeline-library@main') _
pipeline {
    agent { node { label 'java-8' } }
    options { timeout(time: 30, unit: 'MINUTES') }
    environment {
        REGISTRY = credentials('d4dec53c-d6e1-460c-a629-4151ad2cd8f0')
    }
    stages {
        stage('check env') {
            steps {
                checkEnv()
            }
        }
        stage('build image') {
            when {
                expression { return MASTER_BRANCH == 'false' }
            }
            steps {
                script {
                    env.shortCommitId = env.GIT_COMMIT[0..7]
                    env.startedAtTime = sh(returnStdout: true, script: 'date +%Y%m%d%H%M%S').trim()
                    env.SAVE_ARTIFACT = "${env.CALLBACK_URl}/.callback/artifact/save"
                    env.METRIC_URL = "${CALLBACK_URl}/.callback/metric"
                    env.METRIC_CALLBACKURL = "${env.METRIC_URL}?qid=${JOB_ID}&build_id=${BUILD_ID}"
                    env.commitMessage = sh(returnStdout: true, script: "git log --format=%B -n 1 ${GIT_COMMIT}").trim()
                    imageAddr = "quay.xiaodiankeji.net/${IMG_NAMESPACE}/${APP_NAME}:${env.shortCommitId}_${env.startedAtTime}"
                    body = "{\"jobId\":\"${JOB_ID}\",\"gitCommit\":\"${env.shortCommitId}\",\"env\":\"${ENV}\",\"artifactImage\":\"${imageAddr}\",\"buildNumber\":\"${BUILD_ID}\"}"
                }
                container('java') {
                    sh "mvn package jib:build -DskipTests -Djib.to.auth.username=${REGISTRY_USR} -Djib.to.auth.password=${REGISTRY_PSW} -Djib.to.image=${imageAddr}"
                    sh "curl -s -X POST --connect-timeout 3 -m 10 --retry 5   \'${env.SAVE_ARTIFACT}\'   -H 'content-type: application/json'   -d \'${body}\'"

                }
            }
        }
        stage('parallel') {
            parallel {
                stage('deploy') {
                    when {
                        expression { return MASTER_BRANCH == 'false' }
                    }
                    steps {
                        deploy()
                    }
                }
                stage('snapshot') {
                    // 检查工程是否使用了快照版本的jar
                    steps {
                        snapshot()
                    }
                }
                stage('snyk scan') {
                    // 安全扫描
                    steps {
                        snykScan()
                    }
                }
                stage('unit and sonar') {
                    stages {
                        stage('unit') {
                            // 执行单元测试，不忽略失败的单测，必须保障通过单测
                            steps {
                                unit()
                            }
                        }
                        stage('sonar') {
                            // 执行sonar静态代码扫描
                            steps {
                                sonar()
                            }
                        }
                    }
                }
            }
        }
    }
}