<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>so.dian</groupId>
  <artifactId>invoice</artifactId>
  <version>0.0.1-release</version>
  <packaging>jar</packaging>

  <name>${project.artifactId}</name>
  <description>invoice</description>

  <parent>
    <groupId>so.dian.parent</groupId>
    <artifactId>spring-cloud-ilford-dependencies</artifactId>
    <version>1.1.11</version>
  </parent>

  <properties>
    <!--prometheus-->
    <micrometer-registry-prometheus.version>1.6.13</micrometer-registry-prometheus.version>
    <!--micrometer jvm extras-->
    <micrometer-jvm-extras.version>0.2.0</micrometer-jvm-extras.version>

    <powermock.version>2.0.7</powermock.version>
    <rocketmq.version>2.1.1</rocketmq.version>
    <com.alibaba.easyexcel>3.1.1</com.alibaba.easyexcel>
    <newyork-center-client.version>1.5.6-RELEASE</newyork-center-client.version>
    <astra-client.version>2.0.1-RELEASE</astra-client.version>
    <wutai-client.version>1.1.2-RELEASE</wutai-client.version>
    <sunreaver-api.version>1.2.2-RELEASE</sunreaver-api.version>
    <tiantai-client.version>1.1.36-RELEASE</tiantai-client.version>
    <passport-facade.version>1.0.45-RELEASE</passport-facade.version>

    <credit-platform-api.version>1.1.5.RELEASE</credit-platform-api.version>
    <taishan-client.version>1.0.8-RELEASE</taishan-client.version>
    <songshan-client.version>1.1.12-RELEASE</songshan-client.version>

    <!-- sonar unit test -->
    <sonar.inclusions>
      **/service/impl/**,
      **/facade/**,
      **/manager/**
    </sonar.inclusions>
    <sonar.exclusions>
      **/service/impl/ImageServiceImpl*,
      **/service/impl/AgentEmployeeServiceImpl*,
      **/manager/DingTalkManager*
    </sonar.exclusions>

  </properties>

  <dependencies>
<!--    <dependency>-->
<!--      <groupId>so.dian.passport</groupId>-->
<!--      <artifactId>passport-facade</artifactId>-->
<!--      <version>${passport-facade.version}</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>so.dian.tiantai</groupId>
      <artifactId>tiantai-client</artifactId>
      <version>${tiantai-client.version}</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>sunreaver-api</artifactId>
      <version>${sunreaver-api.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>*</artifactId>
          <groupId>*</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.wutai</groupId>
      <artifactId>wutai-client</artifactId>
      <version>${wutai-client.version}</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>astra-client</artifactId>
      <version>${astra-client.version}</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>newyork-center-client</artifactId>
      <version>${newyork-center-client.version}</version>
    </dependency>
    <dependency>
      <groupId>so.dian.fis</groupId>
      <artifactId>credit-platform-api</artifactId>
      <version>${credit-platform-api.version}</version>
    </dependency>
    <dependency>
      <groupId>so.dian.taishan</groupId>
      <artifactId>taishan-client</artifactId>
      <version>${taishan-client.version}</version>
      <exclusions>
        <exclusion>
          <groupId>so.dian.common</groupId>
          <artifactId>common-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.songshan</groupId>
      <artifactId>songshan-client</artifactId>
      <version>${songshan-client.version}</version>
      <exclusions>
        <exclusion>
          <groupId>so.dian.common</groupId>
          <artifactId>common-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>${com.alibaba.easyexcel}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-spring-boot-starter</artifactId>
      <version>${rocketmq.version}</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>invoice-client</artifactId>
      <version>0.0.7-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>pap-client</artifactId>
      <version>1.2.9-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>customer-api</artifactId>
      <version>1.3.0-RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>oss-framework-api</artifactId>
          <groupId>so.dian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>wp-client</artifactId>
      <version>1.2.5-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-starter-feign</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>dove-client</artifactId>
      <version>0.0.12.RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-web</artifactId>
        </exclusion>
        <exclusion>
          <groupId>de.codecentric</groupId>
          <artifactId>spring-boot-admin-starter-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-actuator</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>so.dian.common</groupId>
      <artifactId>dian-logger</artifactId>
      <version>1.1.6.RELEASE</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- 供应商相关 -->
    <dependency>
      <groupId>so.dian.jinyun</groupId>
      <artifactId>jinyun-client</artifactId>
      <version>1.4.60-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>so.dian.center</groupId>
      <artifactId>dian-common</artifactId>
      <version>1.0.13-RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>spymemcached-provider</artifactId>
          <groupId>com.google.code.simple-spring-memcached</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>logging-log4j</artifactId>
          <groupId>com.facebook.jcommon</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>zuul-api</artifactId>
      <version>0.0.4</version>
      <exclusions>
        <exclusion>
          <groupId>so.dian.hr</groupId>
          <artifactId>hr-service-api</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hutool-all</artifactId>
          <groupId>cn.hutool</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>contract-api</artifactId>
      <version>1.0.9-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-starter-feign</artifactId>
        </exclusion>
        <exclusion>
          <groupId>so.dian</groupId>
          <artifactId>commons-eden</artifactId>
        </exclusion>
        <exclusion>
          <groupId>so.dian</groupId>
          <artifactId>lvy-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.hr</groupId>
      <artifactId>hr-service-api</artifactId>
      <version>1.3.5-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-starter-feign</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>lvy-client</artifactId>
      <version>0.6.3-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-web</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>commons-eden</artifactId>
      <version>2.6-RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>hutool-all</artifactId>
          <groupId>cn.hutool</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.himalaya</groupId>
      <artifactId>himalaya-spring-boot-starter</artifactId>
      <version>1.1.6-RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>xxl-job-core</artifactId>
          <groupId>so.dian.xxl.job</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.eureka</groupId>
          <artifactId>eureka-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
      <version>3.0.5</version>
    </dependency>
    <dependency>
      <groupId>so.dian.yandang</groupId>
      <artifactId>yandang-client</artifactId>
      <version>1.6.3-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>so.dian.agent</groupId>
      <artifactId>agent-service-api</artifactId>
      <version>2.9.7-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.kunlun</groupId>
      <artifactId>kunlun</artifactId>
      <version>2022.01.07-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>so.dian.xdjob</groupId>
      <artifactId>xdjob-boot-22x-starter</artifactId>
      <version>2021.06</version>
    </dependency>
    <dependency>
      <groupId>so.dian.spring.cloud</groupId>
      <artifactId>mesh-spring-cloud-starter</artifactId>
      <version>2.4.6</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.retry</groupId>
      <artifactId>spring-retry</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-all</artifactId>
      <version>4.1.35.Final</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <version>8.3.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0.redhat-00001</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <optional>true</optional>
    </dependency>

    <!-- swagger2 -->
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
      <version>2.9.2</version>
    </dependency>
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger-ui</artifactId>
      <version>2.9.2</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jdk8</artifactId>
      <version>2.9.9</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>2.9.9</version>
    </dependency>
    <dependency>
      <groupId>com.meidalife</groupId>
      <artifactId>cybertron-client</artifactId>
      <version>1.0.0-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>logging-log4j</artifactId>
          <groupId>com.facebook.jcommon</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>3.17</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>2.0.4</version>
    </dependency>
    <dependency>
      <groupId>javax.mail</groupId>
      <artifactId>mail</artifactId>
      <version>1.4.7</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.1</version>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
      <version>3.11.2</version>
    </dependency>

    <dependency>
      <groupId>com.qcloud</groupId>
      <artifactId>qcloud-java-sdk</artifactId>
      <version>2.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.qcloud</groupId>
      <artifactId>cos_api</artifactId>
      <version>4.6</version>
      <exclusions>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.qcloud</groupId>
      <artifactId>cmq-client</artifactId>
      <version>1.0.4</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.11.0</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.apache.shiro/shiro-core -->
    <dependency>
      <groupId>org.apache.shiro</groupId>
      <artifactId>shiro-core</artifactId>
      <version>1.5.3</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/com.squareup.okio/okio -->
    <dependency>
      <groupId>com.squareup.okio</groupId>
      <artifactId>okio</artifactId>
      <version>1.14.0</version>
    </dependency>
    <dependency>
      <groupId>com.xebialabs.overthere</groupId>
      <artifactId>overthere</artifactId>
      <version>5.0.1</version>
    </dependency>
    <!--pagehelper 分页插件-->
    <dependency>
      <groupId>com.github.pagehelper</groupId>
      <artifactId>pagehelper-spring-boot-starter</artifactId>
      <version>1.2.12</version>
    </dependency>
    <dependency>
      <groupId>io.github.no-today</groupId>
      <artifactId>dingtalk-robot</artifactId>
      <version>1.0.RELEASE</version>
    </dependency>
    <!-- 监控相关 -->
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
      <version>${micrometer-registry-prometheus.version}</version>
    </dependency>
    <dependency>
      <groupId>io.github.mweirauch</groupId>
      <artifactId>micrometer-jvm-extras</artifactId>
      <version>${micrometer-jvm-extras.version}</version>
    </dependency>
    <dependency>
      <groupId>org.jolokia</groupId>
      <artifactId>jolokia-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-actuator-autoconfigure</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>


    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>

    <dependency>
      <artifactId>hutool-all</artifactId>
      <groupId>cn.hutool</groupId>
      <version>5.5.9</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-junit4</artifactId>
      <version>${powermock.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito2</artifactId>
      <version>${powermock.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-core</artifactId>
      <version>${powermock.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.3.3</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>so.dian.ci.scanner.maven</groupId>
      <artifactId>ci-scanner-maven-plugin</artifactId>
      <version>1.0.16</version>
    </dependency>

    <dependency>
      <groupId>org.jacoco</groupId>
      <artifactId>jacoco-maven-plugin</artifactId>
      <version>0.8.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>commons-validator</artifactId>
          <groupId>commons-validator</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian</groupId>
      <artifactId>virgo-client</artifactId>
      <version>1.2.3.RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <configuration>
          <executable>true</executable>
          <layout>JAR</layout>
          <excludes>
            <exclude>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>so.dian.ci.scanner.maven</groupId>
        <artifactId>ci-scanner-maven-plugin</artifactId>
        <version>1.0.16</version>
      </plugin>
      <plugin>
        <groupId>com.google.cloud.tools</groupId>
        <artifactId>jib-maven-plugin</artifactId>
        <version>3.4.1</version>
        <configuration>
          <containerizingMode>packaged</containerizingMode>
          <container>
            <entrypoint>/opt/jboss/container/java/run/run-java.sh</entrypoint>
          </container>
          <extraDirectories>
            <paths>
              <path>
                <from>target/</from>
                <includes>${project.build.finalName}.jar</includes>
                <into>/deployments</into>
              </path>
            </paths>
          </extraDirectories>
          <pluginExtensions>
            <pluginExtension>
              <implementation>
                com.google.cloud.tools.jib.maven.extension.layerfilter.JibLayerFilterExtension</implementation>
              <configuration
                implementation="com.google.cloud.tools.jib.maven.extension.layerfilter.Configuration">
                <filters>
                  <filter>
                    <!-- exclude all jib layers, which is basically anything in /app -->
                    <glob>/app/**</glob>
                  </filter>
                  <filter>
                    <!-- this is our fat jar, this should be kept by adding it into its own layer -->
                    <glob>/deployments/${project.build.finalName}.jar</glob>
                    <toLayer>jib-custom-fatJar</toLayer>
                  </filter>
                </filters>
              </configuration>
            </pluginExtension>
          </pluginExtensions>
          <from>
            <image>
              quay.xiaodiankeji.net/openjdk/openjdk-8-runtime-skywalking@sha256:4115001cdff162df855650863d5c23877e3e01032ba538dc34e960b0fadd580e</image>
            <platforms>
              <platform>
                <architecture>arm64</architecture>
                <os>linux</os>
              </platform>
              <platform>
                <architecture>amd64</architecture>
                <os>linux</os>
              </platform>
            </platforms>
          </from>
          <to>
            <image>quay.xiaodiankeji.net/dian-dev/${project.build.finalName}</image>
            <auth>
              <username>${env.REGISTRY_USR}</username>
              <password>${env.REGISTRY_PSW}</password>
            </auth>
          </to>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.google.cloud.tools</groupId>
            <artifactId>jib-layer-filter-extension-maven</artifactId>
            <version>0.3.0</version>
          </dependency>
        </dependencies>
    </plugin>
    <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <version>2.43.0</version>
        <configuration>
          <ratchetFrom>origin/main</ratchetFrom>
          <formats>
            <format>
              <includes>
                <include>*.md</include>
                <include>.gitignore</include>
              </includes>
              <trimTrailingWhitespace></trimTrailingWhitespace>
              <endWithNewline></endWithNewline>
              <indent>
                <tabs>true</tabs>
                <spacesPerTab>4</spacesPerTab>
              </indent>
            </format>
          </formats>
          <java>
            <includes>
              <include>src/main/java/**/*.java</include>
              <include>src/test/java/**/*.java</include>
            </includes>
            <googleJavaFormat>
              <style>GOOGLE</style>
              <reflowLongStrings>true</reflowLongStrings>
            </googleJavaFormat>
            <importOrder>
              <wildcardsLast>false</wildcardsLast>
              <order>java|javax|jakarta,org.springframework,org,com,so.dian,,\#so.dian,\#</order>
              <semanticSort>false</semanticSort>
            </importOrder>
            <removeUnusedImports></removeUnusedImports>
            <formatAnnotations></formatAnnotations>
          </java>
          <pom>
            <includes>
              <include>pom.xml</include>
            </includes>
            <sortPom></sortPom>
          </pom>
        </configuration>
    </plugin>
    <plugin>
      <groupId>org.apache.maven.plugins</groupId>
      <artifactId>maven-pmd-plugin</artifactId>
      <version>3.21.2</version>
    </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.5.0</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.21.2</version>
        <reportSets>
          <reportSet>
            <id>aggregate</id>
            <!-- don't run aggregate in child modules -->
            <reports>
              <report>aggregate-pmd</report>
              <report>aggregate-cpd</report>
            </reports>
            <inherited>false</inherited>
          </reportSet>
          <reportSet>
            <id>default</id>
            <reports>
              <report>pmd</report>
              <report>cpd</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>
</project>