package so.dian.invoice.pojo.enums;

import so.dian.himalaya.able.EnumInterface;

public enum InvoiceTypeEnum implements EnumInterface<InvoiceTypeEnum> {

    NONE(-1, "无票","0"),

    VAT(1, "增值税专用发票", "10100"),
    /**
     * 增值税电子专票没有单独分类。会分类在在10100增值税专用发票中，然后通过'electronic_mark': '1',
     * 是否为电子增票，你们来做一下二次分类
     */
    VAT_ELECTR(14, "增值税电子专用发票", "999999"),
    ORD(2, "增值税普通发票", "10101"),
    ORD_ELECTR(3, "增值税电子普通发票", "10102"),

    ORD_ROLL(4, "增值税普通发票(卷票)", "10103"),
    MACHINE(5, "机打发票", "10400"),

    //-----一辉新增发票类型---------------
    QUOTA(6, "定额发票", "10200"),
    RAODTOLL(7, "过路费发票", "10507"),

    TAXI(8, "出租车发票", "10500"),
    TRAIN(9, "火车票", "10503"),
    VEHICLE(9, "机动车统一发票", "10104"),
    PASSCAR(10, "客运汽车", "10505"),

    AVIATION(11, "航空运输电子客票行程单", "10506"),
    IST(12, "国际小票", "20100"),
    IBT(13, "国际大票", "20103"),
    //-----一辉新增发票类型---------------

    OTHER(0, "其他", "-1"),
    FULL_POWER_MAJOR(15, "电子发票（专用发票）", "10107"),
    FULL_POWER_GENERAL(16, "电子发票（普通发票）", "10108"),
    ;


    InvoiceTypeEnum(int type, String field, String bizType) {
        this.type = type;
        this.field = field;
        this.bizType = bizType;
    }

    /**
     * 自定义的，用作field的id，
     */
    private int type;

    /**
     * 发票名称，文案
     */
    private String field;

    /**
     * 睿琪code
     */
    private String bizType;

    @Override
    public String getDesc() {
        return this.field;
    }

    @Override
    public Integer getCode() {
        return this.type;
    }

    @Override
    public InvoiceTypeEnum getDefault() {
        return null;
    }


    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public static String getField(int type) {

        for (InvoiceTypeEnum status : InvoiceTypeEnum.values()) {
            if (status.getType() == type) {
                return status.getField();
            }
        }

        return null;
    }

    public static Integer getTypeByField(String field) {

        for (InvoiceTypeEnum status : InvoiceTypeEnum.values()) {
            if (status.getField().equals(field)) {
                return status.getType();
            }
        }

        return null;
    }

    public static Integer getTypeByBizType(String bizType) {

        for (InvoiceTypeEnum status : InvoiceTypeEnum.values()) {
            if (status.bizType.equals(bizType)) {
                return status.type;
            }
        }
        return null;
    }

    public static String getBizTypeByType(int type) {
        for (InvoiceTypeEnum status : InvoiceTypeEnum.values()) {
            if (status.getType() == type) {
                return status.getBizType();
            }
        }

        return "";
    }

    public static InvoiceTypeEnum getByType(int type) {
        for (InvoiceTypeEnum status : InvoiceTypeEnum.values()) {
            if (status.getType() == type) {
                return status;
            }
        }
        return null;
    }

}
