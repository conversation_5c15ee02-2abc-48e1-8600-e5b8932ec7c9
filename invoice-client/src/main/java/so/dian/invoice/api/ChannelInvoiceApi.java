package so.dian.invoice.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.invoice.pojo.dtos.ChannelInvoiceDTO;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/10/31 15:32
 * @description:
 */
public interface ChannelInvoiceApi {

    @GetMapping("channel/invoice/get")
    BizResult<ChannelInvoiceDTO> get(@RequestParam("belongSubjectId") Long belongSubjectId, @RequestParam("belongSubjectType") Integer belongSubjectType);
}
